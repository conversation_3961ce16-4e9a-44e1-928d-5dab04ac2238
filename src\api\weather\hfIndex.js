import { service1 } from '@/utils/request'

/**
 * 获取当前天气数据（包含空气质量）
 */
export const getTfList = async (params) => {
    try {
        console.log('调用后端API获取当前天气数据...')
        const response = await service1.get('/v7/tropical/storm-list', { params })
        console.log('获取当前天气数据成功:', response.data)
        return response.data
    } catch (error) {
        console.error('获取当前天气数据失败:', error)
        throw error
    }
}

export const getTfInfo = async (params) => {
    try {
        console.log('调用后端API获取当前天气数据...')
        const response = await service1.get('/v7/tropical/storm-track', { params })
        console.log('获取当前天气数据成功:', response.data)
        return response.data
    } catch (error) {
        console.error('获取当前天气数据失败:', error)
        throw error
    }
}
