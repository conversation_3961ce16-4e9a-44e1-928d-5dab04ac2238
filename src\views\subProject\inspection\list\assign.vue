<!-- 巡检作业流程表单 -->
<template>
    <div class="p-2" style="margin-top: -10px">
        <el-card class="box-card">
            <template v-slot:header>
                <div class="clearfix">
                    <span>巡检信息</span>
                </div>
            </template>
            <!-- 作业单任务基本信息 -->
            <BaseInfoInspection from="task" :id="taskId" />
        </el-card>
        <TaskDelayHistory :taskId="taskId" />
        <el-card class="box-card">
            <template v-slot:header>
                <div class="clearfix">
                    <span>作业进度</span>
                </div>
            </template>
            <div class="text item">
                <el-form ref="projectFormRef" :model="form" :rules="rules" label-width="160px">
                    <!-- <div v-if="form.task.currentStatus == 'START'">
                         <el-row :gutter="gutter">
                            <el-col :span="6">
                                <el-form-item label="班组负责人" prop="assignInspection.memberId">
                                    <ManagersSelector v-model="form.assignInspection.memberId" placeholder="请选择班组人员" clearable />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div> -->

                    <div v-if="form.task.currentStatus == 'START'">
                        <el-row :gutter="gutter">
                            <el-row :gutter="gutter">
                                <el-col :span="12">
                                    <el-form-item label="安全交底图片" prop="safeImages">
                                        <imageUpload v-model="form.assignInspection.safeImages" @update:modelValue="handleSafeUploadChange" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-row>
                    </div>
                    <div v-if="form.task.currentStatus == 'Execute'">
                        <div style="display: flex; justify-content: center; align-items: center">
                            <el-alert type="info" show-icon :closable="false" style="width: 200px">
                                <template #icon>
                                    <el-icon><Iphone /></el-icon>
                                </template>
                                请在移动设备操作
                            </el-alert>
                        </div>
                    </div>
                    <div class="auditOption" v-show="form.task.currentStatus == 'Second_Acceptance'">
                        <!-- @todo 某些环节不需要审批意见，用v-show控制-->
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="是否验收通过">
                                    <el-radio-group v-model="form.nextAssignee.wfOperation">
                                        <el-radio v-for="option in approvalOptions" :key="option.value" :value="option.value">
                                            {{ option.label }}
                                        </el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="意见">
                                    <el-input v-model="form.nextAssignee.opinion" type="textarea" placeholder="请输入内容" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <!-- 当form.nextAssignee.businessData.CANCEL_OPTION == 'CANCEL'时，后端会启动取消流程
                    @todo 这里的表单要做控制
                    -->
                    <!-- <div>
                        <el-form-item label="取消原因">
                            <el-input v-model="form.nextAssignee.businessData.CANCEL_OPTION" type="textarea"
                                placeholder="请输入内容" />
                        </el-form-item>
                    </div> -->
                    <!-- @todo 某些环节不予展示  -->

                    <div class="text item" v-if="form.task.currentStatus != 'END' && form.task.currentStatus != 'TERMINATED'">
                        <el-row justify="center">
                            <el-col style="text-align: center; margin-top: 20px">
                                <!-- START状态：显示提交和变更计划日期按钮 -->

                                <el-button
                                    v-if="form.task.currentStatus != 'Execute'"
                                    type="primary"
                                    :loading="submitLoading"
                                    @click="handleSubmit"
                                    style="margin-right: 12px"
                                    >提交</el-button
                                >

                                <el-button
                                    v-if="form.task.currentStatus == 'Execute'"
                                    type="primary"
                                    :loading="submitLoading"
                                    @click="handleChangePlanDate"
                                    >变更计划日期</el-button
                                >

                                <!-- 其他状态：仅显示提交按钮 -->
                                <!-- <template v-else>
                                    <el-button type="primary" :loading="submitLoading" @click="handleSubmit">提交</el-button>
                                </template> -->
                            </el-col>
                        </el-row>
                    </div>
                </el-form>
            </div>
        </el-card>
        <el-card class="box-card">
            <template v-slot:header>
                <div class="clearfix">
                    <span>审批进度</span>
                </div>
            </template>
            <WorkflowInfo :business-id="taskId" exclude-activity-codes="Confirm" />
        </el-card>

        <!-- 变更计划日期组件 -->
        <ChangeTaskDate
            v-model="changePlanDateVisible"
            :project-id="appStore.projectContext.selectedProjectId"
            :task-id="taskId"
            :current-date="form.task.bgnDate"
            @success="handleChangePlanDateSuccess"
            @navigate-to-list="handleNavigateToList"
        />
        <!-- 变更计划日期组件 -->
        <ChangeTaskDate
            v-model="changePlanDateVisible"
            :project-id="appStore.projectContext.selectedProjectId"
            :task-id="taskId"
            :current-date="form.task.bgnDate"
            taskType="inspect"
            @success="handleChangePlanDateSuccess"
            @navigate-to-list="handleNavigateToList"
        />
    </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useRoute, useRouter } from 'vue-router'
import { AssignInspectionFlowForm, AssignInspectionFlowVo } from '@/api/plan/assignInspection/types'
import { inspectionAssign } from '@/api/plan/task'
import { getTaskAssignmentByTaskId } from '@/api/plan/assignInspection'
import { ElMessage } from 'element-plus'
import BaseInfoInspection from '../../components/BaseInfoInspection.vue'
import WorkflowInfo from '../../components/Workflow/WorkflowInfo.vue'
import TaskDelayHistory from '../../components/TaskDelayHistory.vue'
import ChangeTaskDate from '../../components/ChangeTaskDate.vue'
import ManagersSelector from '../../components/ManagersSelector/index.vue'
// import TeamMembersSelector from '../../components/TeamMembersSelector/index.vue';

const appStore = useAppStore()
const route = useRoute()
const router = useRouter()

// 获取路由参数中的taskId
const taskId = (route.params.id || route.query.id || route.query.taskId) as string

const gutter = ref(50) //设置项目表单两列的距离
const projectFormRef = ref()
const submitLoading = ref(false)
// 变更计划日期相关数据
const changePlanDateVisible = ref(false)
const approvalOptions = ref([
    { label: '通过', value: 'APPROVE' },
    { label: '不通过', value: 'ROLLBACK' } //,
    // @todo 处理延期
    // { label: '延期', value: 'SUSPENDED' }
])
// 表单数据
const form = reactive<AssignInspectionFlowForm>({
    task: {
        tempTask: 'no',
        tempResourceId: ''
    },
    assignInspection: {},
    nextAssignee: {
        nextAssignees: undefined,
        wfOperation: 'APPROVE',
        businessData: {}
    }
})

// 表单验证规则
const rules = reactive({
    name: [{ required: true, message: '请输入巡检任务名称', trigger: 'blur' }],
    memberId: [{ required: true, message: '请选择班组人员', trigger: 'change' }]
})

// 提交表单
const handleSubmit = async () => {
    try {
        // 表单验证
        const valid = await projectFormRef.value?.validate()
        if (!valid) {
            return
        }

        submitLoading.value = true
        const processedForm = {
            ...form
        }
        console.log('提交的巡检任务指派数据:', form)
        // 2.根据当前状态处理nextAssignee
        switch (form.task.currentStatus) {
            // 指派环节需要指定处理人
            case 'Assign':
                processedForm.nextAssignee = {
                    ...form.nextAssignee,
                    nextAssignees: form.nextAssignee?.nextAssignees
                        ? Array.isArray(form.nextAssignee.nextAssignees)
                            ? form.nextAssignee.nextAssignees
                            : [form.nextAssignee.nextAssignees]
                        : []
                }
                break
            default:
                // 其他环节不需要指定处理人
                processedForm.nextAssignee = {
                    ...form.nextAssignee,
                    nextAssignees: []
                }
                break
        }
        // 调用API
        await inspectionAssign(processedForm)

        ElMessage.success('巡检任务指派成功')

        // 可以选择跳转到列表页面或关闭当前页面
        router.push('/subProject/circle/inspection/list')
    } catch (error) {
        console.error('巡检任务指派失败:', error)
        ElMessage.error('巡检任务指派失败，请重试')
    } finally {
        submitLoading.value = false
    }
}

// 变更计划日期相关方法
// 打开变更计划日期对话框
const handleChangePlanDate = () => {
    changePlanDateVisible.value = true
}
/** 处理安全交底图片上传变化 */
const handleSafeUploadChange = (ossIds: string) => {
    form.assignInspection.safeImages = ossIds
}

// 处理变更计划日期成功
const handleChangePlanDateSuccess = (newDate: string) => {
    // 更新本地数据
    form.task.bgnDate = newDate
    console.log('计划日期已更新为:', newDate)
}

// 处理跳转到列表页面
const handleNavigateToList = () => {
    router.push('/subProject/circle/inspection/list')
}

// 获取任务分配数据
const getTaskAssignmentData = async () => {
    try {
        const response = await getTaskAssignmentByTaskId(taskId)
        if (response.data) {
            const data: AssignInspectionFlowVo = response.data

            // 初始化form数据
            if (data.task) {
                Object.assign(form.task, data.task)
                console.log('任务信息:', {
                    id: data.task.id,
                    name: data.task.name,
                    taskType: data.task.taskType,
                    bgnDate: data.task.bgnDate,
                    endDate: data.task.endDate
                })
            }

            if (data.assignInspection) {
                Object.assign(form.assignInspection, data.assignInspection)
                console.log('巡检分配信息:', {
                    id: data.assignInspection.id,
                    taskId: data.assignInspection.taskId,
                    name: data.assignInspection.name,
                    content: data.assignInspection.content,
                    memberId: data.assignInspection.memberId
                })
            }

            console.log('获取任务分配数据成功:', data)
        }
    } catch (error) {
        console.error('获取任务分配数据失败:', error)
        ElMessage.warning('获取任务数据失败，将使用默认数据')
    }
}

onMounted(async () => {
    await getTaskAssignmentData()
})
</script>
<style lang="scss" scoped>
.box-card {
    margin-top: 10px;
}
</style>
