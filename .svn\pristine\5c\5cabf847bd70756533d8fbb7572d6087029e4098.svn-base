<template>
    <div class="p-2 facility-page">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-0">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <!-- <el-form-item label="项目编号" prop="projectId">
				<el-input v-model="queryParams.projectId" placeholder="请输入项目编号" clearable @keyup.enter="handleQuery" />
			  </el-form-item> -->
                        <el-form-item label="管理单元" prop="categoryIdSecond">
                            <el-select v-model="queryParams.unitId" placeholder="请输入管理单元" clearable @keyup.enter="handleQuery">
                                <el-option v-for="(item, key) in manageUntList" :value="item.id" :label="item.name"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="设施分类" prop="categoryIdSecond">
                            <!-- <el-input v-model="queryParams.categoryIdThird" placeholder="请输入设施分类" clearable @keyup.enter="handleQuery" /> -->
                            <el-tree-select
                                clearable
                                @change="handleQuery"
                                v-model="selectedQueryCategoryId"
                                :data="categoryList"
                                :props="{ value: 'id', label: 'name', children: 'children' }"
                                value-key="id"
                                placeholder="请选择父节点编号"
                                check-strictly
                            />
                        </el-form-item>
                        <el-form-item label="设施名称" prop="remark">
                            <el-input v-model="queryParams.remark" placeholder="请输入设施名称" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item label="备注" prop="name">
                            <el-input v-model="queryParams.name" placeholder="请输入设施备注" clearable @keyup.enter="handleQuery" />
                        </el-form-item>

                        <el-form-item label="设施编码" prop="code">
                            <el-input v-model="queryParams.code" placeholder="请输入设施编码" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <!-- <el-form-item label="设施分类id-结构树形" prop="categoryIdFirst">
				<el-input v-model="queryParams.categoryIdFirst" placeholder="请输入设施分类id-结构树形" clearable @keyup.enter="handleQuery" />
			  </el-form-item>
			  <el-form-item label="设施分类id-结构类别" prop="categoryIdSecond">
				<el-input v-model="queryParams.categoryIdSecond" placeholder="请输入设施分类id-结构类别" clearable @keyup.enter="handleQuery" />
			  </el-form-item> -->
                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">搜索</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <el-row :gutter="10" class="mb8 toolbar-actions">
                    <el-col :span="1.5">
                        <el-button class="toolbar-btn btn-add" @click="handleAdd" v-hasPermi="['basic:facility:add']">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button class="toolbar-btn btn-edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['basic:facility:edit']"
                            >修改</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button class="toolbar-btn btn-delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['basic:facility:remove']"
                            >删除</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button class="toolbar-btn" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
                    </el-col>
                </el-row>
            </template>

            <el-table v-loading="loading" :data="facilityList" @selection-change="handleSelectionChange" stripe>
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <!-- <el-table-column label="项目编号" align="center" prop="projectId" /> -->
                <!-- <el-table-column label="管理单元" align="center" prop="unitId" /> -->
                <el-table-column label="设施名称" align="center" prop="remark" />
                <el-table-column label="备注" align="center" prop="name" />

                <el-table-column label="管理单元" align="center" prop="unitId">
                    <template #default="scope">
                        {{ manageUntList.find((unit) => unit.id === scope.row.unitId)?.name }}
                    </template>
                </el-table-column>
                <!-- <el-table-column label="设施编码" align="center" prop="code" />
		  <el-table-column label="流水号" align="center" prop="seq" /> -->

                <!-- <el-table-column label="编码类型，编码类型，字典项：code_type" align="center" prop="codeType">
			<template #default="scope">
			  <dict-tag :options="code_type" :value="scope.row.codeType"/>
			</template>
		  </el-table-column> -->
                <el-table-column label="结构属性" align="center" prop="categoryIdFirst">
                    <template #default="scope">
                        {{ getFirstLevelCategory(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <el-table-column label="结构类别" align="center" prop="categoryIdThird" width="120">
                    <template #default="scope">
                        {{ getSecondLevelCategory(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <el-table-column label="构件类型" align="center" prop="categoryIdThird">
                    <template #default="scope">
                        {{ getCategoryNameById(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <!-- <el-table-column label="时钟范围-bgn，字典项：时钟范围" align="center" prop="timeArrangeBgn" />
		  <el-table-column label="时钟范围-end，字典项：时钟范围" align="center" prop="timeArrangeEnd" />
		  <el-table-column label="起范围，管理编码下拉项" align="center" prop="bgnCode" />
		  <el-table-column label="止范围，管理编码下拉项" align="center" prop="endCode" />
		  <el-table-column label="房间编码" align="center" prop="roomId" /> -->
                <!-- <el-table-column label="模型ID" align="center" prop="modelId" /> -->
                <!-- <el-table-column label="更新日期" align="center" prop="updateDate" width="180">
			<template #default="scope">
			  <span>{{ parseTime(scope.row.updateDate, '{y}-{m}-{d}') }}</span>
			</template>
		  </el-table-column> -->
                <!-- <el-table-column label="车道-id" align="center" prop="roadwayId" />
		  <el-table-column label="" align="center" prop="description" />
		  <el-table-column label="图片" align="center" prop="images" />
		  <el-table-column label="文件" align="center" prop="files" /> -->
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="260">
                    <template #default="scope">
                        <div class="op-actions">
                            <el-button link type="primary" class="op-link op-info" @click="handleInformation(scope.row)">
                                <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="档案" />
                                设施档案
                            </el-button>
                            <el-button
                                link
                                type="primary"
                                class="op-link op-edit"
                                @click="handleUpdate(scope.row)"
                                v-hasPermi="['basic:facility:edit']"
                            >
                                <img class="op-icon" src="@/assets/images/edit-icon.png" alt="修改" />
                                修改
                            </el-button>
                            <el-button
                                link
                                type="danger"
                                class="op-link op-delete"
                                @click="handleDelete(scope.row)"
                                v-hasPermi="['basic:facility:remove']"
                            >
                                <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <!-- 添加或修改设施信息对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="750px" append-to-body draggable>
            <el-form ref="facilityFormRef" :model="form" :rules="rules" label-width="120px">
                <el-tabs v-model="activeTabName">
                    <el-tab-pane label="属性" name="basic">
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="管理单元" prop="unitId">
                                    <el-select v-model="form.unitId" placeholder="请输入管理单元" @change="getLineTree" clearable>
                                        <el-option v-for="(item, key) in manageUntList" :value="item.id" :label="item.name"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="设施分类" prop="categoryIdThird">
                                    <!-- <el-tree-select clearable
                                v-model="form.categoryIdThird"
                                :data="categoryList"
                                :props="{ value: 'id', label: 'name', children: 'children' }"
                                value-key="id"
                                placeholder="请选择父节点编号"
                                check-strictly
                            /> -->
                                    <el-cascader
                                        v-model="form.categoryIdThird"
                                        :options="categoryList"
                                        :props="{ value: 'id', label: 'name', children: 'children', expandTrigger: 'hover', emitPath: false }"
                                    ></el-cascader>
                                    <!-- <el-select v-model="form.categoryIdThird" placeholder="请输入设施分类" /> -->
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="设施名称" prop="name">
                                    <el-input v-model="form.name" placeholder="不填则按照默认规则自动生成" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="编码类型" prop="codeType">
                                    <el-select v-model="form.codeType" placeholder="请选择编码类型">
                                        <el-option v-for="dict in codeTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
                                        <!-- <el-option v-for="dict in code_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option> -->
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="备注" prop="remark">
                                    <el-input v-model="form.remark" placeholder="请输入备注" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="设施编码" prop="code">
                                    <el-input v-model="form.code" placeholder="请输入设施代码" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="时钟范围起始" prop="timeArrangeBgn">
                                    <el-input v-model="form.timeArrangeBgn" placeholder="请输入时钟范围" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="时钟范围截止" prop="timeArrangeEnd">
                                    <el-input v-model="form.timeArrangeEnd" placeholder="请输入时钟范围" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="起范围" prop="bgnCode">
                                    <el-input v-model="form.bgnCode" placeholder="请输入起范围，管理编码下拉项" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="止范围" prop="endCode">
                                    <el-input v-model="form.endCode" placeholder="请输入止范围，管理编码下拉项" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="起始里程" prop="bgnKilometer">
                                    <el-input v-model="form.bgnKilometer" placeholder="请输入起范围，管理编码下拉项" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="终止里程" prop="endKilometer">
                                    <el-input v-model="form.endKilometer" placeholder="请输入止范围，管理编码下拉项" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="模型ID" prop="modelId">
                                    <el-input v-model="form.modelId" placeholder="请输入模型ID" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="更新日期" prop="updateDate">
                                    <el-date-picker
                                        clearable
                                        v-model="form.updateDate"
                                        type="datetime"
                                        value-format="YYYY-MM-DD HH:mm:ss"
                                        placeholder="请选择更新日期"
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12"> </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="24">
                                <el-form-item label="车道" prop="roadwayId">
                                    <el-cascader
                                        v-model="form.roadwayId"
                                        :options="lineTree"
                                        multiple
                                        collapse-tags
                                        collapse-tags-tooltip
                                        :props="{
                                            value: 'id',
                                            label: 'name',
                                            children: 'children',
                                            expandTrigger: 'hover',
                                            emitPath: false
                                        }"
                                        placeholder="请选择车道"
                                    ></el-cascader>
                                    <!-- <el-select v-model="form.roadwayId" placeholder="请输入车道-id" /> -->
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="图片" name="images">
                        <el-form-item prop="images">
                            <image-upload></image-upload>
                        </el-form-item>
                    </el-tab-pane>
                    <el-tab-pane label="文件" name="files">
                        <file-upload></file-upload>
                    </el-tab-pane>
                </el-tabs>

                <!-- <el-form-item label="房间编码" prop="roomId">
			<el-select v-model="form.roomId" placeholder="请选择房间编码" />
		  </el-form-item> -->

                <!-- <el-form-item label="" prop="description">
			<el-input v-model="form.description" placeholder="请输入" />
		  </el-form-item>
		  <el-form-item label="图片" prop="images">
			<el-input v-model="form.images" placeholder="请输入图片" />
		  </el-form-item>
		  <el-form-item label="文件" prop="files">
			<el-input v-model="form.files" placeholder="请输入文件" />
		  </el-form-item> -->
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Facility" lang="ts">
import { useRouter } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { listFacility, getFacility, delFacility, addFacility, updateFacility } from '@/api/subProject/basic/facility'
import { FacilityVO, FacilityQuery, FacilityForm } from '@/api/subProject/basic/facility/types'
import { generateFacilityCode } from '@/api/common/deviceCodeRule'
import { listCodeTypesOfProject } from '@/api/project/projectCode'
import { getProject } from '@/api/project/project'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { TunnelTreeNode } from '@/api/types'
import { listLineToRoadwayTreeByUnitId } from '@/api/project/line'
import { listCategory } from '@/api/common/category'
import { CategoryVO, CategoryQuery } from '@/api/common/category/types'
import { listDevice } from '@/api/subProject/basic/device'
import { DeviceVO } from '@/api/subProject/basic/device/types'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const router = useRouter()
const appStore = useAppStore()
const { code_type } = toRefs<any>(proxy?.useDict('code_type'))
const codeTypeOptions = ref([])
const facilityList = ref<DeviceVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const activeTabName = ref('basic')
const queryFormRef = ref<ElFormInstance>()
const facilityFormRef = ref<ElFormInstance>()
const currentProjectId = ref('')
const manageUntList = ref<ManageUnitVO[]>([])
const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})
const lineTree = ref<TunnelTreeNode[]>([])
const initFormData: FacilityForm = {
    id: undefined,
    projectId: undefined,
    unitId: undefined,
    code: undefined,
    seq: undefined,
    name: undefined,
    remark: undefined,
    codeType: undefined,
    categoryIdFirst: undefined,
    categoryIdSecond: undefined,
    categoryIdThird: undefined,
    timeArrangeBgn: undefined,
    timeArrangeEnd: undefined,
    bgnCode: undefined,
    endCode: undefined,
    bgnKilometer: undefined,
    endKilometer: undefined,
    roomId: undefined,
    modelId: undefined,
    updateDate: undefined,
    roadwayId: undefined,
    description: undefined,
    images: undefined,
    files: undefined
}
const data = reactive<PageData<FacilityForm, FacilityQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        unitId: undefined,
        code: undefined,
        name: undefined,
        remark: undefined,
        categoryPath: undefined,
        kind: 'facility',
        // categoryIdFirst: undefined,
        //categoryIdSecond: undefined,
        //  categoryIdThird: undefined,
        params: {}
    },
    rules: {
        unitId: [{ required: true, message: '管理单元不能为空', trigger: 'blur' }],
        categoryIdThird: [{ required: true, message: '设施分类不能为空', trigger: 'blur' }],
        //  name: [{ required: true, message: '设施名称不能为空', trigger: 'blur' }],
        remark: [{ required: true, message: '备注不能为空', trigger: 'blur' }],
        codeType: [{ required: true, message: '编码不能为空', trigger: 'blur' }],
        bgnCode: [{ required: true, message: '起范围不能为空', trigger: 'blur' }],
        endCode: [{ required: true, message: '止范围不能为空', trigger: 'blur' }],
        bgnKilometer: [{ required: true, message: '起始里程不能为空', trigger: 'blur' }],
        endKilometer: [{ required: true, message: '终止里程不能为空', trigger: 'blur' }]
    }
})
const selectedQueryCategoryId = ref('')
const { queryParams, form, rules } = toRefs(data)
const gutter = ref(10)
/** 查询设施信息列表 */
const getList = async () => {
    loading.value = true
    // 确保在调用接口前设置项目ID
    if (!queryParams.value.projectId) {
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
    }
    queryParams.value.kind = 'facility'
    const res = await listDevice(queryParams.value)
    facilityList.value = res.rows
    total.value = res.total
    loading.value = false
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    facilityFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    if (!selectedQueryCategoryId.value) {
        queryParams.value.categoryPath = ''
    } else {
        const selectFacatity = findCategoryById(categoryList.value, selectedQueryCategoryId.value)
        queryParams.value.categoryPath = selectFacatity.path
        console.log('selectFacatity', selectFacatity.path)
    }

    console.log('categoryPath', queryParams.value.categoryPath)
    queryParams.value.pageNum = 1
    console.log('queryParams', queryParams.value)
    getList()
}

function findCategoryById(list, id) {
    for (const item of list) {
        if (item.id === id) return item
        if (item.children) {
            const found = findCategoryById(item.children, id)
            if (found) return found
        }
    }
    return null
}
/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: FacilityVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    dialog.visible = true
    dialog.title = '添加设施信息'

    // 自动生成设施编码
    nextTick(() => {
        generateCodeForNewFacility()
    })
}

/** 修改按钮操作 */
const handleUpdate = async (row?: FacilityVO) => {
    reset()
    const _id = row?.id || ids.value[0]
    const res = await getFacility(_id)
    Object.assign(form.value, res.data)
    console.log('form.value.unitId', form.value.unitId)
    // 加载车道树
    // getLineTree(form.value.unitId);
    // 加载车道树
    await getLineTree(form.value.unitId)

    dialog.visible = true
    dialog.title = '修改设施信息'
}

/** 提交按钮 */
const submitForm = () => {
    facilityFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true
            if (form.value.id) {
                await updateFacility(form.value).finally(() => (buttonLoading.value = false))
            } else {
                await addFacility(form.value).finally(() => (buttonLoading.value = false))
            }
            proxy?.$modal.msgSuccess('操作成功')
            dialog.visible = false
            await getList()
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: FacilityVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除设施信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delFacility(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'basic/facility/export',
        {
            ...queryParams.value
        },
        `设施列表_${new Date().getTime()}.xlsx`
    )
}

/** 设施档案按钮操作 */
const handleInformation = (row: FacilityVO) => {
    router.push('facilityInformation?id=' + row.id)
}

type CategoryOption = {
    id: number
    name: string
    children?: CategoryOption[]
}
const categoryList = ref<CategoryVO[]>([])
const categoryOptions = ref<CategoryOption[]>([])

const facilityCategoryQueryParams = ref<CategoryQuery>({
    projectId: undefined,
    name: undefined,
    code: undefined,
    kind: 'facility',
    params: {}
})

// 获取第一级分类名称
const getFirstLevelCategory = (categoryId: string) => {
    if (!categoryId || !categoryList.value) return ''

    for (const firstLevel of categoryList.value) {
        for (const secondLevel of firstLevel.children || []) {
            for (const thirdLevel of secondLevel.children || []) {
                if (thirdLevel.id === categoryId) {
                    return firstLevel.name
                }
            }
        }
    }
    return ''
}

// 获取第二级分类名称
const getSecondLevelCategory = (categoryId: string) => {
    if (!categoryId || !categoryList.value) return ''

    for (const firstLevel of categoryList.value) {
        for (const secondLevel of firstLevel.children || []) {
            for (const thirdLevel of secondLevel.children || []) {
                if (thirdLevel.id === categoryId) {
                    return secondLevel.name
                }
            }
        }
    }
    return ''
}

const getCategoryNameById = (id: string) => {
    if (!id) return ''
    const findCategory = (list: any[], targetId: string): string => {
        for (const item of list) {
            if (item.id === targetId) {
                return item.name
            }
            if (item.children && item.children.length) {
                const found = findCategory(item.children, targetId)
                if (found) return found
            }
        }
        return ''
    }
    return findCategory(categoryList.value, id)
}

/** 查询category列表 */
const getFacilityCategoryList = async () => {
    loading.value = true
    facilityCategoryQueryParams.value.projectId = appStore.projectContext.selectedProjectId
    const res = await listCategory(facilityCategoryQueryParams.value)
    const data = proxy?.handleTree<CategoryVO>(res.data, 'id', 'parentId')
    console.log('treeData', data)
    if (data) {
        categoryList.value = data
        loading.value = false
    }
}

/** 查询category下拉树结构 */
const getTreeselect = async () => {
    const res = await listCategory()
    categoryOptions.value = []
    const data: CategoryOption = { id: 0, name: '顶级节点', children: [] }
    data.children = proxy?.handleTree<CategoryOption>(res.data, 'id', 'parentId')
    categoryOptions.value.push(data)
}

const getManageUnitList = async () => {
    currentProjectId.value = appStore.projectContext.selectedProjectId
    manageUntList.value = (await listProjectManageUnit(currentProjectId.value)).data
}
const getCodeTypeOptions = async () => {
    currentProjectId.value = appStore.projectContext.selectedProjectId
    const resp = await listCodeTypesOfProject(currentProjectId.value)
    const codeTypesArrUnderProject = resp.data.map((it) => it.codeType)
    codeTypeOptions.value = code_type.value.filter((item) => codeTypesArrUnderProject.includes(item.value))
}
const getLineTree = async (unitId: string) => {
    lineTree.value = (await listLineToRoadwayTreeByUnitId(unitId)).data
}

/** 自动生成设施编码 */
const generateCodeForNewFacility = async () => {
    if (!form.value.categoryIdThird) {
        return // 分类必须先选择
    }

    const projectId = appStore.projectContext.selectedProjectId
    if (!projectId) {
        proxy?.$modal.msgWarning('请先选择项目')
        return
    }

    try {
        form.value.code = '正在生成编码...'
        const response = await generateFacilityCode(projectId, form.value.categoryIdThird as string)
        form.value.code = response.msg
    } catch (error: any) {
        console.error('生成设施编码失败:', error)

        // 根据错误类型给出不同提示
        if (error.response?.data?.msg) {
            proxy?.$modal.msgWarning(error.response.data.msg)
        } else {
            proxy?.$modal.msgWarning('生成设施编码失败，请检查编码规则配置或手动输入')
        }

        form.value.code = '' // 清空编码，让用户手动输入
    }
}

// 监听分类变化，重新生成编码
watch(
    () => form.value.categoryIdThird,
    () => {
        if (!form.value.id && dialog.visible) {
            // 只在新增模式下生成
            generateCodeForNewFacility()
        }
    }
)

onMounted(() => {
    // 首先设置项目ID
    queryParams.value.projectId = appStore.projectContext.selectedProjectId

    getManageUnitList()
    getCodeTypeOptions()
    getFacilityCategoryList()
    getList()
})
</script>
<style lang="scss" scoped>
.facility-page {
    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder) {
        color: #8291a9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)) {
        color: #ffffff !important;
    }

    :deep(.el-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 仅管理单元选择器：禁止文本光标 */
    :deep(.el-form-item[label='管理单元'] .el-select__wrapper),
    :deep(.el-form-item[label='管理单元'] .el-select) {
        cursor: default !important;
    }

    :deep(.el-form-item[label='管理单元'] .el-select .el-select__selected-item > span) {
        cursor: default !important;
    }

    :deep(.el-form-item[label='管理单元'] .el-input__inner) {
        caret-color: transparent !important;
    }

    /* 搜索/重置按钮（非 link） */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286f3 !important;
        border-color: #4286f3 !important;
        color: #ffffff !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: '';
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #ffffff !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: '';
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #aed7f2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #ffffff !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景（对齐全寿命首页） */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 顶部工具按钮 */
    .toolbar-actions {
        align-items: center;
    }

    :deep(.toolbar-btn) {
        border: none !important;
        color: #ffffff !important;
        font-size: 14px !important;
        height: 40px;
        padding: 0 16px 0 42px;
        border-radius: 8px;
        position: relative;
    }

    :deep(.toolbar-btn::before) {
        content: '';
        position: absolute;
        left: 14px;
        top: 50%;
        transform: translateY(-50%);
        width: 14px;
        height: 14px;
        background-repeat: no-repeat;
        background-size: contain;
    }

    :deep(.btn-add) {
        background-color: #2a59c4 !important;
    }

    :deep(.btn-add::before) {
        background-image: url('@/assets/images/add-icon.png');
    }

    :deep(.btn-edit) {
        background-color: #2ba1a0 !important;
    }

    :deep(.btn-edit::before) {
        background-image: url('@/assets/images/edit-white-icon.png');
    }

    :deep(.btn-delete) {
        background-color: #921121 !important;
    }

    :deep(.btn-delete::before) {
        background-image: url('@/assets/images/delete-white-icon.png');
    }

    /* 操作列图标按钮（对齐全寿命首页） */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px; /* 右侧留白 */
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286f3 !important;
    }

    .op-edit {
        color: #42f3e9 !important;
    }

    .op-delete {
        color: #d62121 !important;
    }
}
</style>
