<template>
    <div class="p-2 security-check-page">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px">
                        <!-- <el-form-item label="项目-id" prop="projectId">
				<el-input v-model="queryParams.projectId" placeholder="请输入项目-id" clearable @keyup.enter="handleQuery" />
			  </el-form-item> -->
                        <el-form-item label="管理单元" prop="unitIds">
                            <el-select
                                v-model="queryParams.unitIds"
                                placeholder="请选择管理单元"
                                clearable
                                collapse-tags
                                collapse-tags-tooltip
                                @change="handleQuery"
                            >
                                <el-option v-for="unit in manageUnits" :key="unit.id" :label="unit.name" :value="unit.id" />
                            </el-select>
                        </el-form-item>

                        <el-form-item label="整改负责人" prop="executerId">
                            <el-select v-model="queryParams.executerId" placeholder="请选择整改负责人" clearable @change="handleQuery">
                                <el-option v-for="user in users" :key="user.userId" :label="user.nickName" :value="user.userId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="整改班组" prop="executeTeamId">
                            <el-select v-model="queryParams.executeTeamId" placeholder="请选择整改班组" clearable @change="handleQuery">
                                <el-option v-for="team in teams" :key="team.id" :label="team.name" :value="team.id" />
                            </el-select>
                        </el-form-item>
                        <!-- <el-form-item label="截止期限" prop="deadLine">
				<el-date-picker clearable
				  v-model="queryParams.deadLine"
				  type="date"
				  value-format="YYYY-MM-DD"
				  placeholder="请选择截止期限"
				/>
			  </el-form-item> -->
                        <!-- <el-form-item label="整改通知单" prop="rectificationOticeFiles">
				<el-input v-model="queryParams.rectificationOticeFiles" placeholder="请输入整改通知单" clearable @keyup.enter="handleQuery" />
			  </el-form-item> -->
                        <!-- <el-form-item label="回复内容" prop="replyContent">
				<el-input v-model="queryParams.replyContent" placeholder="请输入回复内容" clearable @keyup.enter="handleQuery" />
			  </el-form-item>
			  <el-form-item label="整改附件" prop="replyFiles">
				<el-input v-model="queryParams.replyFiles" placeholder="请输入整改附件" clearable @keyup.enter="handleQuery" />
			  </el-form-item> -->

                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">搜索</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <div class="btn-box">
                    <div class="filter">
                        <el-radio-group v-model="filterStatus" @change="handleFilterStatus">
                            <el-radio-button value="ALL" v-if="false">全部</el-radio-button>
                            <el-radio-button value="PENDING">待处理</el-radio-button>
                            <el-radio-button value="COMPLETED">已处理</el-radio-button>
                        </el-radio-group>
                        <el-button type="success" plain icon="Edit" v-if="buttonsEnabled.editSecurityCheck" :disabled="single" @click="handleUpdate()"
                            >修改</el-button
                        >
                        <el-button
                            type="danger"
                            plain
                            icon="Delete"
                            v-if="buttonsEnabled.deleteSecurityCheck"
                            :disabled="multiple"
                            @click="handleDelete()"
                            >删除</el-button
                        >
                    </div>
                    <div class="export">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-if="buttonsEnabled.addSecurityCheck">新增</el-button>
                        <!-- <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['operation:securityCheck:export']"
                            >导出</el-button
                        > -->
                        <right-toolbar v-if="false" v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                    </div>
                </div>
            </template>

            <el-table v-loading="loading" stripe :data="securityCheckList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <el-table-column label="项目-id" align="center" prop="projectId" v-if="false" />
                <el-table-column label="管理单元" align="center" prop="unitIds">
                    <template #default="scope">
                        <span>{{ formatManageUnitNames(scope.row.unitIds) }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="整改通知单" align="center" prop="rectificationOticeFiles" /> -->
                <el-table-column label="整改负责人" align="center" prop="executerName"> </el-table-column>
                <el-table-column label="整改班组" align="center" prop="executeTeamId">
                    <template #default="scope">
                        <span>{{ getTeamName(scope.row.executeTeamId) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="截止期限" align="center" prop="deadLine" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.deadLine, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="备注" align="center" prop="remark" />
		  <el-table-column label="回复内容" align="center" prop="replyContent" />-->

                <el-table-column label="当前状态" align="center" prop="currentStatus">
                    <template #default="scope">
                        <!-- 调试信息 -->
                        <!-- <div style="font-size: 12px; color: #999; margin-bottom: 4px;">
                            原始值: {{ scope.row.currentStatus }}
                        </div>
                        <div style="font-size: 12px; color: #999; margin-bottom: 4px;">
                            字典选项: {{ security_check_status }}
                        </div> -->
                        <dict-tag :options="security_check_status" :value="scope.row.currentStatus" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="350">
                    <template #default="scope">
                        <div class="op-actions">
                            <el-button link type="primary" class="op-link op-info" @click="handleView(scope.row)">
                                <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="查看" />
                                查看
                            </el-button>

                            <!-- 当筛选状态为已处理时，隐藏修改、删除等操作按钮 -->
                            <template v-if="filterStatus !== 'COMPLETED'">
                                <el-button
                                    link
                                    type="primary"
                                    class="op-link op-edit"
                                    v-if="scope.row.currentStatus === 'START'"
                                    @click="handleUpdate(scope.row)"
                                    v-hasPermi="['operation:securityCheck:edit']"
                                >
                                    <img class="op-icon" src="@/assets/images/edit-icon.png" alt="修改" />
                                    修改
                                </el-button>
                                <el-button
                                    link
                                    type="danger"
                                    class="op-link op-delete"
                                    v-if="scope.row.currentStatus === 'START'"
                                    @click="handleDelete(scope.row)"
                                    v-hasPermi="['operation:securityCheck:remove']"
                                >
                                    <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />
                                    删除
                                </el-button>
                                <el-button
                                    link
                                    type="primary"
                                    class="op-link op-edit"
                                    @click="handleAssign(scope.row)"
                                    v-if="scope.row.currentStatus != 'END' && isCurrentUserAssignee(scope.row.assignee)"
                                >
                                    <img class="op-icon" src="@/assets/images/edit-icon.png" alt="处理" />
                                    {{ getButtonLabel(scope.row.currentStatus) }}
                                </el-button>
                            </template>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <!-- 添加或修改安全检查对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
            <el-form ref="securityCheckFormRef" :model="form" :rules="rules" label-width="100px">
                <!-- <el-form-item label="项目-id" prop="projectId">
			<el-input v-model="form.projectId" placeholder="请输入项目-id" />
		  </el-form-item> -->
                <el-form-item label="管理单元" prop="unitIds" required>
                    <el-select
                        v-model="form.unitIds"
                        placeholder="请选择管理单元"
                        clearable
                        multiple
                        collapse-tags
                        collapse-tags-tooltip
                        style="width: 100%"
                    >
                        <el-option v-for="unit in manageUnits" :key="unit.id" :label="unit.name" :value="unit.id" />
                    </el-select>
                </el-form-item>
                <!-- <el-form-item label="整改通知单" prop="rectificationOticeFiles">
                    <el-input v-model="form.rectificationOticeFiles" placeholder="请输入整改通知单" />
                </el-form-item> -->
                <el-form-item label="整改班组" prop="executeTeamId" required>
                    <el-select v-model="form.executeTeamId" placeholder="请选择整改班组" clearable style="width: 100%" @change="handleTeamChange">
                        <el-option v-for="team in teams" :key="team.id" :label="team.name" :value="team.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="整改负责人" prop="executerId" required>
                    <el-select v-model="form.executerId" placeholder="请选择整改负责人" clearable style="width: 100%">
                        <el-option v-for="member in teamMembers" :key="member.userId" :label="member.nickName" :value="member.userId" />
                    </el-select>
                </el-form-item>

                <el-form-item label="截止期限" prop="deadLine" required>
                    <el-date-picker
                        clearable
                        v-model="form.deadLine"
                        type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        placeholder="请选择截止期限"
                        style="width: 100%"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="整改通知单">
                    <file-upload v-model="form.rectificationOticeFiles" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import {
    listSecurityCheck,
    getSecurityCheck,
    delSecurityCheck,
    addSecurityCheck,
    updateSecurityCheck,
    startSecurityCheckWorkflow,
    listSecurityCheckTaskView
} from '@/api/subProject/operation/securityCheck'
import {
    SecurityCheckVO,
    SecurityCheckQuery,
    SecurityCheckForm,
    SecurityCheckFlowForm,
    SecurityCheckTaskViewVO,
    SecurityCheckTaskViewQuery
} from '@/api/subProject/operation/securityCheck/types'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { listProjectAvailableTeam } from '@/api/common/team'
import { TeamVO } from '@/api/common/team/types'
import { listTeamMember } from '@/api/common/teamMember'
import { TeamMemberVO, VTeamMemberVO } from '@/api/common/teamMember/types'
import { listUserByProject, listUserByDeptId, getUserProfile } from '@/api/system/user'
import { UserVO } from '@/api/system/user/types'
import { useRouter } from 'vue-router'
import { ref, reactive, toRefs, onMounted, onBeforeUnmount, watch, getCurrentInstance, ComponentInternalInstance, nextTick } from 'vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { security_check_status, sys_user_sex } = toRefs<any>(proxy?.useDict('security_check_status', 'sys_user_sex'))
const appStore = useAppStore()
const userStore = useUserStore()
const router = useRouter()
const buttonsEnabled = ref({
    addSecurityCheck: appStore.judgePermissionEnabled('securityCheck:add'),
    editSecurityCheck: appStore.judgePermissionEnabled('securityCheck:edit'),
    deleteSecurityCheck: appStore.judgePermissionEnabled('securityCheck:delete')
})
const securityCheckList = ref<SecurityCheckTaskViewVO[]>([])
const manageUnits = ref<ManageUnitVO[]>([])
const teams = ref<TeamVO[]>([])
const users = ref<UserVO[]>([])
const teamMembers = ref<VTeamMemberVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const queryFormRef = ref<ElFormInstance>()
const securityCheckFormRef = ref<ElFormInstance>()
const filterStatus = ref('PENDING')

// 状态缓存相关
const CACHE_KEY = 'security_check_search_state'

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

const data = reactive<PageData<SecurityCheckForm, SecurityCheckTaskViewQuery>>({
    form: {
        id: undefined,
        projectId: appStore.projectContext.selectedProjectId,
        unitIds: [],
        rectificationOticeFiles: undefined,
        executerId: undefined,
        executeTeamId: undefined,
        deadLine: undefined,
        remark: undefined,
        replyContent: undefined,
        replyFiles: undefined,
        currentStatus: undefined
    },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: appStore.projectContext.selectedProjectId,
        unitIds: undefined,
        executerId: undefined,
        executeTeamId: undefined,
        beginDeadLine: undefined,
        endDeadLine: undefined,
        remark: undefined,
        replyContent: undefined,
        currentStatus: undefined,
        instanceId: undefined,
        currentActivityCode: undefined,
        businessType: undefined,
        wfStatus: undefined,
        assignee: undefined,
        todoStatus: undefined,
        beginCreateTime: undefined,
        endCreateTime: undefined,
        params: {}
    },
    rules: {
        unitIds: [
            {
                required: true,
                message: '管理单元不能为空',
                trigger: 'change',
                validator: (rule: any, value: any, callback: any) => {
                    if (!value || (Array.isArray(value) && value.length === 0)) {
                        callback(new Error('管理单元不能为空'))
                    } else {
                        callback()
                    }
                }
            }
        ],
        executeTeamId: [{ required: true, message: '整改班组不能为空', trigger: 'change' }],
        executerId: [{ required: true, message: '整改负责人不能为空', trigger: 'change' }],
        deadLine: [{ required: true, message: '截止期限不能为空', trigger: 'change' }]
    }
})

const { queryParams, form, rules } = toRefs(data)
/** 检查当前用户是否为指定的处理人 */
const isCurrentUserAssignee = (assignee: string | undefined | null) => {
    if (!assignee || !userStore.userId) {
        return false
    }

    // 将assignee按逗号分割，去除空格，检查是否包含当前用户ID
    const assigneeList = assignee
        .split(',')
        .map((id) => id.trim())
        .filter((id) => id)
    const currentUserId = userStore.userId.toString()

    const isAssignee = assigneeList.includes(currentUserId)

    // console.log('检查用户权限:', {
    //     currentUserId,
    //     assignee,
    //     assigneeList,
    //     isAssignee
    // })

    return isAssignee
}
/** 获取管理单元列表 */
const getManageUnits = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取管理单元')
            return
        }

        const response = await listProjectManageUnit(projectId)
        if (response && response.data) {
            manageUnits.value = Array.isArray(response.data) ? response.data : [response.data]
        } else if (response && Array.isArray(response)) {
            manageUnits.value = response
        } else {
            manageUnits.value = []
        }
    } catch (error) {
        console.error('获取管理单元失败:', error)
        manageUnits.value = []
    }
}

/** 获取班组列表 */
const getTeams = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取班组')
            return
        }

        const response = await listProjectAvailableTeam(projectId)
        if (response && Array.isArray(response)) {
            teams.value = response
        } else if (response && response.data) {
            teams.value = Array.isArray(response.data) ? response.data : [response.data]
        } else {
            teams.value = []
        }
    } catch (error) {
        console.error('获取班组失败:', error)
        teams.value = []
    }
}

/** 获取班组成员列表 */
const getTeamMembers = async (teamId: string | number) => {
    try {
        if (!teamId) {
            teamMembers.value = []
            return
        }

        const response = await listTeamMember({
            teamId: teamId,
            pageNum: 1,
            pageSize: 1000
        })

        // 处理分页格式的返回数据
        if (response && response.rows) {
            // 使用类型断言处理实际返回的分页格式
            teamMembers.value = response.rows
        }
    } catch (error) {
        console.error('获取班组成员失败:', error)
        teamMembers.value = []
    }
}

/** 处理班组选择变化 */
const handleTeamChange = async (teamId: string | number) => {
    // 清空之前选择的整改负责人
    form.value.executerId = undefined

    // 获取选中班组的成员列表
    await getTeamMembers(teamId)
}

/** 获取项目用户列表 */
const getUsers = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取用户')
            return
        }

        // 获取当前用户信息
        const currentUser = userStore
        if (!currentUser.userId) {
            console.warn('当前用户信息不存在')
            return
        }

        // 首先获取当前用户的部门信息
        const userProfile = await getUserProfile()
        const currentUserDeptId = userProfile.data.user.deptId

        if (!currentUserDeptId) {
            console.warn('当前用户没有部门信息')
            return
        }

        // 根据部门ID获取该部门下的用户
        const response = await listUserByDeptId(currentUserDeptId)
        if (response && response.data) {
            users.value = Array.isArray(response.data) ? response.data : [response.data]
        } else if (response && Array.isArray(response)) {
            users.value = response
        } else {
            users.value = []
        }
    } catch (error) {
        console.error('获取用户失败:', error)
        users.value = []
    }
}

/** 根据用户ID获取用户名称 */
const getUserName = (userId: string | number | undefined) => {
    if (!userId || !users.value.length) {
        return '--'
    }
    const user = users.value.find((u) => u.userId.toString() === userId.toString())
    return user ? user.nickName : userId.toString()
}

/** 根据班组ID获取班组名称 */
const getTeamName = (teamId: string | number | undefined) => {
    if (!teamId || !teams.value.length) {
        return '--'
    }
    const team = teams.value.find((t) => t.id.toString() === teamId.toString())
    return team ? team.name : teamId.toString()
}

/** 格式化管理单元名称显示 */
const formatManageUnitNames = (unitIds: string | number | Array<string | number>) => {
    if (!unitIds || !manageUnits.value.length) {
        return '--'
    }

    let idsArray: Array<string | number> = []
    if (Array.isArray(unitIds)) {
        idsArray = unitIds
    } else if (typeof unitIds === 'string') {
        idsArray = unitIds.split(',').map((id) => id.trim())
    } else {
        idsArray = [unitIds]
    }

    const names = idsArray.map((id) => {
        const unit = manageUnits.value.find((u) => u.id.toString() === id.toString())
        return unit ? unit.name : id.toString()
    })

    return names.join(', ')
}

/** 查询安全检查列表 */
const getList = async () => {
    loading.value = true

    // 处理查询参数中的管理单元数组
    const queryData = { ...queryParams.value }
    if (Array.isArray(queryData.unitIds)) {
        queryData.unitIds = queryData.unitIds.join(',')
    }

    // 设置待处理/已处理筛选条件
    if (filterStatus.value !== 'ALL') {
        queryData.todoStatus = filterStatus.value
    }

    const res = await listSecurityCheckTaskView(queryData)
    securityCheckList.value = res.rows
    total.value = res.total
    loading.value = false
}

// 缓存搜索状态
const saveSearchState = () => {
    try {
        const state = {
            queryParams: {
                unitIds: queryParams.value.unitIds,
                executerId: queryParams.value.executerId,
                executeTeamId: queryParams.value.executeTeamId,
                remark: queryParams.value.remark
            },
            showSearch: showSearch.value,
            filterStatus: filterStatus.value
        }
        sessionStorage.setItem(CACHE_KEY, JSON.stringify(state))
        console.log('保存安全检查搜索状态:', state)
    } catch (error) {
        console.error('保存安全检查搜索状态失败:', error)
    }
}

// 恢复搜索状态
const restoreSearchState = () => {
    try {
        const cached = sessionStorage.getItem(CACHE_KEY)
        if (cached) {
            const state = JSON.parse(cached)

            // 恢复查询参数
            if (state.queryParams) {
                queryParams.value.unitIds = state.queryParams.unitIds
                queryParams.value.executerId = state.queryParams.executerId
                queryParams.value.executeTeamId = state.queryParams.executeTeamId
                queryParams.value.remark = state.queryParams.remark
            }

            // 恢复搜索框显示状态
            if (state.showSearch !== undefined) {
                showSearch.value = state.showSearch
            }

            // 恢复筛选状态
            if (state.filterStatus) {
                filterStatus.value = state.filterStatus
            }

            console.log('恢复安全检查搜索状态:', state)
        }
    } catch (error) {
        console.error('恢复安全检查搜索状态失败:', error)
    }
}

// 清除缓存状态
const clearSearchState = () => {
    sessionStorage.removeItem(CACHE_KEY)
}

/** 处理筛选状态按钮点击 */
const handleFilterStatus = (status: string) => {
    filterStatus.value = status

    // 筛选时重置页码
    queryParams.value.pageNum = 1
    getList()
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}

/** 表单重置 */
const reset = () => {
    form.value = {
        id: undefined,
        projectId: appStore.projectContext.selectedProjectId,
        unitIds: [],
        rectificationOticeFiles: undefined,
        executerId: undefined,
        executeTeamId: undefined,
        deadLine: undefined,
        remark: undefined,
        replyContent: undefined,
        replyFiles: undefined,
        currentStatus: undefined
    }
    securityCheckFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()

    // 清除缓存状态
    clearSearchState()

    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: SecurityCheckVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    dialog.visible = true
    dialog.title = '添加安全检查'
}

/** 修改按钮操作 */
const handleUpdate = async (row?: SecurityCheckVO) => {
    loading.value = true
    reset()
    const _id = row?.id || ids.value[0]
    try {
        const res = await getSecurityCheck(_id)
        Object.assign(form.value, res.data)
        // 处理管理单元数据
        if (form.value.unitIds && typeof form.value.unitIds === 'string') {
            form.value.unitIds = (form.value.unitIds as string).split(',').map((id) => id.trim())
        }
        dialog.visible = true
        dialog.title = '修改安全检查'
    } catch (error) {
        console.error('获取安全检查详情失败:', error)
    } finally {
        loading.value = false
    }
}

/** 提交按钮 */
const submitForm = () => {
    securityCheckFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true
            try {
                // 处理管理单元数组转换为字符串
                const submitData = { ...form.value }
                if (Array.isArray(submitData.unitIds)) {
                    submitData.unitIds = submitData.unitIds.join(',')
                }

                if (form.value.id) {
                    // 编辑模式，直接更新
                    await updateSecurityCheck(submitData)
                } else {
                    // 新增模式，启动工作流
                    submitData.projectId = appStore.projectContext.selectedProjectId

                    const securityCheckFlowForm: SecurityCheckFlowForm = {
                        securityCheck: submitData
                    }

                    await startSecurityCheckWorkflow(securityCheckFlowForm)
                }

                proxy?.$modal.msgSuccess('操作成功')
                dialog.visible = false
                await getList()
            } catch (error) {
                console.error('提交失败:', error)
                proxy?.$modal.msgError('操作失败')
            } finally {
                buttonLoading.value = false
            }
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: SecurityCheckVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除安全检查编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delSecurityCheck(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'operation/securityCheck/export',
        {
            ...queryParams.value
        },
        `securityCheck_${new Date().getTime()}.xlsx`
    )
}

/** 获取按钮标签 */
const getButtonLabel = (statusValue: string) => {
    // 从security_check_status数据字典中查找对应的标签
    const dictItem = security_check_status.value?.find((item: any) => item.value === statusValue)
    // 如果找到字典项，返回其标签；否则返回原始值
    return dictItem?.label || statusValue
}

/** 查看按钮操作 */
const handleView = (row: SecurityCheckVO) => {
    router.push('securityCheckDetail?id=' + row.id)
}

/** 分配/处理按钮操作 */
const handleAssign = (row: SecurityCheckVO) => {
    router.push('securityCheckAssign?id=' + row.id)
}

onMounted(async () => {
    await Promise.all([getManageUnits(), getTeams(), getUsers()])

    // 恢复搜索状态
    restoreSearchState()

    getList()
})

// 组件卸载前保存状态
onBeforeUnmount(() => {
    saveSearchState()
})

// 监听搜索条件变化，实时保存状态
watch(
    [
        () => queryParams.value.unitIds,
        () => queryParams.value.executerId,
        () => queryParams.value.executeTeamId,
        () => queryParams.value.remark,
        showSearch,
        filterStatus
    ],
    () => {
        // 延迟保存，避免频繁操作
        nextTick(() => {
            saveSearchState()
        })
    },
    { deep: true }
)
</script>

<style lang="scss" scoped>
.security-check-page {

    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-tree-select__wrapper),
    :deep(.el-date-editor .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder),
    :deep(.el-tree-select__placeholder) {
        color: #8291A9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)),
    :deep(.el-tree-select .el-tree-select__selected-item > span:not(.el-tree-select__placeholder)) {
        color: #FFFFFF !important;
    }

    :deep(.el-select__wrapper.is-focused),
    :deep(.el-tree-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 日期选择器特殊样式 */
    :deep(.el-date-editor) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-date-editor .el-input__inner) {
        color: #FFFFFF !important;
        background: transparent !important;
    }

    :deep(.el-range-separator) {
        color: #8291A9 !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #AED7F2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #FFFFFF !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 筛选按钮组样式 */
    .btn-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter {
            flex: 1;
        }

        .export {
            margin-left: auto;
        }
    }

    :deep(.el-radio-group .el-radio-button__inner) {
        background: #232d45 !important;
        border: 1px solid #4286F3 !important;
        color: #8291A9 !important;
        border-radius: 6px !important;
        margin-right: 10px;
        height: 36px;
        line-height: 34px;
        padding: 0 16px;
    }

    :deep(.el-radio-group .el-radio-button.is-active .el-radio-button__inner) {
        background: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    /* 头部操作按钮样式 */
    :deep(.btn-box .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 36px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.btn-box .el-button.el-button--primary) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.btn-box .el-button.el-button--success) {
        background-color: #67C23A !important;
        border-color: #67C23A !important;
        color: #FFFFFF !important;
    }

    :deep(.btn-box .el-button.el-button--warning) {
        background-color: #E6A23C !important;
        border-color: #E6A23C !important;
        color: #FFFFFF !important;
    }

    :deep(.btn-box .el-button.el-button--danger) {
        background-color: #F56C6C !important;
        border-color: #F56C6C !important;
        color: #FFFFFF !important;
    }

    /* 操作列图标按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
        flex-wrap: wrap;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
        margin: 2px 0;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286F3 !important;
    }

    .op-edit {
        color: #42F3E9 !important;
    }

    .op-delete {
        color: #D62121 !important;
    }

    /* 对话框样式优化 */
    :deep(.el-dialog) {
        background: rgba(35, 45, 69, 0.95) !important;
        border-radius: 8px !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    :deep(.el-dialog__header) {
        background: transparent !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    :deep(.el-dialog__title) {
        color: #FFFFFF !important;
    }

    :deep(.el-dialog__body) {
        background: transparent !important;
    }

    :deep(.el-dialog__footer) {
        background: transparent !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    /* 对话框内表单样式 */
    :deep(.el-form-item__label) {
        color: #AED7F2 !important;
    }

    :deep(.el-textarea__inner) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
        color: #FFFFFF !important;
    }

    :deep(.el-textarea__inner::placeholder) {
        color: #8291A9 !important;
    }
}
</style>
