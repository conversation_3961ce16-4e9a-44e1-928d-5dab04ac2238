<!-- 右侧群组 -->
<template>
    <div class="group-right-container">

        <div class="group-right-top">
            <!-- 安全事件 -->
            <StaticsSafeEvent />
         <!-- 本周计划 -->
         <WeekPlan />
        </div>
        <div class="group-right-right">
    <!-- 交通流量 -->

    <TrafficChart />
        <!-- 项目评价 -->
        <StaticsProject />
        </div>

    </div>
</template>
<script setup lang="ts">

import StaticsSafeEvent from './StaticsSafeEvent.vue'
import TrafficChart from './TrafficChart.vue'
import WeekPlan from './WeekPlan.vue'
import StaticsProject from './StaticsProject.vue'
</script>
<style lang="scss" scoped>

.group-right-container {
    position: absolute;
    right: 0;
    top: 0;
    width: 35%;
    height: 100%;
    padding: 10px;
    overflow-y: hidden;
    overflow-x: hidden;
    box-sizing: border-box;
    // background: red;
}
.group-right-top{
    width: 100%;
    height: 35%;
    // background: red;
    display: flex;

}
.group-right-right{
    width: 100%;
    height: 63%;
    // background: red;
    margin-top: 2%;
}
</style>
