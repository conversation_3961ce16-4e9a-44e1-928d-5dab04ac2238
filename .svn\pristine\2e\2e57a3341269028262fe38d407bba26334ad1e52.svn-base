<!-- 安全检查流程表单 -->
<template>
    <div class="p-2" style="margin-top: -10px" v-loading="loading">
        <el-form ref="projectFormRef" :model="form" :rules="rules" label-width="160px">
            <el-card class="box-card">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>安全检查信息</span>
                    </div>
                </template>
                <!-- 安全检查基本信息 -->
                <BaseInfoSecurityCheck :id="id" />
            </el-card>
            <el-card class="box-card">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>作业进度</span>
                    </div>
                </template>
                <div class="text item">
                    <!-- 回复环节（START状态） -->
                    <div v-if="form.securityCheck.currentStatus == 'START'">
                        <el-row :gutter="gutter">
                            <el-col :span="24">
                                <el-form-item label="整改回复说明" prop="nextAssignee.opinion">
                                    <el-input
                                        type="textarea"
                                        v-model="form.nextAssignee.opinion"
                                        :min-height="192"
                                        placeholder="请输入整改回复说明"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="24">
                                <el-form-item label="上传附件">
                                    <file-upload v-model="form.securityCheck.replyFiles" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <!-- 回复环节 -->
                    <div v-if="form.securityCheck.currentStatus == 'Reply'">
                        <el-row :gutter="gutter">
                            <el-col :span="24">
                                <el-form-item label="反馈回复说明" prop="securityCheck.replyContent">
                                    <el-input type="textarea" v-model="form.securityCheck.replyContent" :min-height="192" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="24">
                                <el-form-item label="上传附件">
                                    <file-upload v-model="form.securityCheck.replyFiles" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                <!-- 审核环节 -->
                <div class="auditOption" v-if="form.securityCheck.currentStatus == 'Audit'">
                    <!-- @todo 某些环节不需要审批意见，用v-show控制-->
                    <el-row :gutter="gutter">
                        <el-col :span="12">
                            <el-form-item label="审批状态" prop="nextAssignee.wfOperation">
                                <el-radio-group v-model="form.nextAssignee.wfOperation">
                                    <el-radio v-for="option in approvalOptions" :key="option.value" :value="option.value">
                                        {{ option.label }}
                                    </el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="12">
                            <el-form-item label="审批意见">
                                <el-input v-model="form.nextAssignee.opinion" type="textarea" placeholder="请输入内容" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <div class="text item">
                    <el-row :gutter="gutter">
                        <el-col :span="24">
                            <el-button type="primary" :loading="submitLoading" @click="handleSubmit">提交</el-button>
                            <!-- <el-button @click="handleReset">重置</el-button> -->
                        </el-col>
                    </el-row>
                </div>
            </el-card>
            <el-card class="box-card">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>审批流程</span>
                    </div>
                </template>
                <WorkflowInfo :business-id="id" />
            </el-card>
        </el-form>
    </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getSecurityCheck, securityCheckAssign } from '@/api/subProject/operation/securityCheck'
import { SecurityCheckSubmitFlowBo, SecurityCheckVO } from '@/api/subProject/operation/securityCheck/types'
import { ElMessage } from 'element-plus'
import BaseInfoSecurityCheck from '@/views/subProject/components/BaseInfoSecurityCheck.vue'
import WorkflowInfo from '@/views/subProject/components/Workflow/WorkflowInfo.vue'
import { listUserByProject, listUserByDeptId, getUserProfile } from '@/api/system/user'
import { UserVO } from '@/api/system/user/types'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'

// 添加调试信息
console.log('SecurityCheck assign.vue 组件开始加载')

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

// 获取路由参数中的id
const id = ref((route.params.id || route.query.id) as string)
console.log('路由信息:', route.params, route.query)
console.log('获取到的ID:', id.value)
const gutter = ref(50) //设置项目表单两列的距离
const projectFormRef = ref()
const submitLoading = ref(false)
const loading = ref(true)
const managers = ref<UserVO[]>([])
const users = ref<UserVO[]>([])

// 审核结果选项
const approvalOptions = ref([
    { label: '通过', value: 'APPROVE' },
    { label: '不通过', value: 'ROLLBACK' }
])

const form = reactive<SecurityCheckSubmitFlowBo>({
    securityCheck: {},
    nextAssignee: {
        nextAssignees: undefined, // 单选模式，初始化为undefined
        wfOperation: 'APPROVE' // 添加审批状态字段
    }
})

// 安全检查数据
const securityCheck = ref<SecurityCheckVO>({
    id: '',
    projectId: '',
    unitIds: '',
    rectificationOticeFiles: '',
    executerId: '',
    executeTeamId: '',
    deadLine: '',
    remark: '',
    replyContent: '',
    replyFiles: '',
    currentStatus: '',
    tnlSecurityCheckcol: ''
})

// 表单验证规则
const rules = reactive({
    'nextAssignee.opinion': [{ required: true, message: '请输入整改回复说明', trigger: 'blur' }],
    // 'securityCheck.replyFiles': [{ required: true, message: '请上传附件', trigger: 'change' }],
    'nextAssignee.wfOperation': [{ required: true, message: '请选择审核结果', trigger: 'change' }]
})

// 获取带班负责人列表
const getManagers = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        const res = await listUserByProject(projectId)
        console.log('API返回的带班负责人数据:', res)
        // 兼容不同的API返回结构
        managers.value = Array.isArray(res) ? res : res.rows || res.data || []
        console.log('解析后的项目用户列表:', managers.value)
        if (managers.value.length === 0) {
            console.warn('没有获取到带班负责人数据，请检查项目下是否有对应岗位的用户')
        } else {
            console.log(`成功获取 ${managers.value.length} 个带班负责人`)
        }
    } catch (error) {
        console.error('获取项目用户列表失败:', error)
        managers.value = []
        // 如果获取失败，回退使用安全员列表作为临时方案
        console.log('回退使用安全员列表作为带班负责人选项')
        //ElMessage.warning('获取项目用户列表失败，已回退使用安全员列表')
    }
}

const getSecurityCheckData = async () => {
    try {
        if (!id.value) {
            console.error('未获取到安全检查ID')
            ElMessage.error('未获取到安全检查ID，请检查路由参数')
            return
        }

        console.log('正在获取安全检查数据，ID:', id.value)
        const response = await getSecurityCheck(id.value)
        console.log('获取到的安全检查数据:', response)

        if (response && response.data) {
            form.securityCheck = response.data
            console.log('表单数据已更新:', form.securityCheck)
        } else {
            console.error('API返回数据为空')
            ElMessage.error('获取安全检查数据失败')
        }
    } catch (error) {
        console.error('获取安全检查数据失败:', error)
        ElMessage.error('获取安全检查数据失败，请重试')
    }
}

// 获取用户列表
const getUsers = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取用户')
            return
        }

        // 获取当前用户信息
        const currentUser = userStore
        if (!currentUser.userId) {
            console.warn('当前用户信息不存在')
            return
        }

        // 首先获取当前用户的部门信息
        const userProfile = await getUserProfile()
        const currentUserDeptId = userProfile.data.user.deptId

        if (!currentUserDeptId) {
            console.warn('当前用户没有部门信息')
            return
        }

        // 根据部门ID获取该部门下的用户
        const response = await listUserByDeptId(currentUserDeptId)
        if (response && response.data) {
            users.value = Array.isArray(response.data) ? response.data : [response.data]
        } else if (response && Array.isArray(response)) {
            users.value = response
        } else {
            users.value = []
        }
    } catch (error) {
        console.error('获取用户失败:', error)
        users.value = []
    }
}

// 提交表单
const handleSubmit = async () => {
    try {
        console.log('=== 开始表单提交 ===')
        console.log('当前表单数据:', JSON.stringify(form, null, 2))
        console.log('当前验证规则:', rules)
        console.log('表单引用:', projectFormRef.value)

        // 检查表单引用是否存在
        if (!projectFormRef.value) {
            console.error('表单引用不存在')
            ElMessage.error('表单引用不存在，请刷新页面重试')
            return
        }

        console.log('开始表单验证...')
        const valid = await projectFormRef.value.validate()
        console.log('表单验证结果:', valid)

        if (!valid) {
            console.log('表单验证失败')
            ElMessage.warning('请检查表单填写是否完整')
            return
        }

        console.log('表单验证通过，开始提交数据...')
        submitLoading.value = true

        // 准备提交数据
        const processedForm = {
            ...form
        }
        // START阶段是回复环节，不需要指定处理人
        // 其他环节也不需要指定处理人
        processedForm.nextAssignee = {
            ...form.nextAssignee,
            nextAssignees: []
        }

        console.log('处理后的提交数据:', JSON.stringify(processedForm, null, 2))

        const response = await securityCheckAssign(processedForm)
        console.log('API响应:', response)

        ElMessage.success('安全检查处理提交成功')
        router.push('/subProject/circle/safe/securityCheck')
    } catch (error) {
        console.error('安全检查处理提交失败:', error)
        ElMessage.error('安全检查处理提交失败，请重试')
    } finally {
        submitLoading.value = false
    }
}

onMounted(async () => {
    console.log('页面开始加载，接收到的id参数:', id.value)
    loading.value = true

    try {
        await Promise.all([getSecurityCheckData(), getManagers(), getUsers()])
        console.log('所有数据加载完成')
    } catch (error) {
        console.error('数据加载失败:', error)
    } finally {
        loading.value = false
    }
})
</script>

<style lang="scss" scoped>
.box-card {
    margin-top: 10px;
}
</style>
