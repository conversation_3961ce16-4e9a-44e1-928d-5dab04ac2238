export interface ResourceViewVO {
    /**
     *
     */
    id: string | number

    /**
     *
     */
    projectId: string | number

    /**
     *
     */
    typeId: string | number

    /**
     *
     */
    typeName: string
    properties: string

    /**
     * 物资单位
     */
    unit: string

    /**
     * 物资性质
     */
    nature: string

    /**
     * 规格型号
     */
    specification: string

    /**
     * 物资单价
     */
    price: number

    /**
     * 库存数量
     */
    balanceAmount: number

    /**
     * 储存地
     */
    storage: string

    /**
     * 库存数量
     */
    pictures: string
}

export interface ResourceForm extends BaseEntity {
    /**
     *
     */
    id?: string | number

    /**
     *
     */
    projectId?: string | number

    /**
     *
     */
    typeId?: string | number
    properties?: string
    unit?: string //冗余字段
    nature?: string

    /**
     * 规格型号
     */
    specification?: string

    /**
     * 物资单价
     */
    price?: number

    /**
     * 库存数量
     */
    balanceAmount?: number

    /**
     * 储存地
     */
    storage?: string

    /**
     * 库存数量
     */
    pictures?: string
}

export interface ResourceViewQuery extends PageQuery {
    /**
     *
     */
    projectId?: string | number
    typeName?: string
    /**
     *
     */
    typeId?: string | number

    /**
     * 物资单位
     */
    unit?: string

    /**
     * 物资性质
     */
    nature?: string

    /**
     * 规格型号
     */
    specification?: string

    /**
     * 物资单价
     */
    price?: number

    /**
     * 日期范围参数
     */
    params?: any
}
