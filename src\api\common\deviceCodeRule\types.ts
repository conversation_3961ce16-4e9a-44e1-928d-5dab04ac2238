export interface DeviceCodeRuleVO {
    /**
     *
     */
    id: string | number

    /**
     * 项目-id
     */
    projectId: string | number

    /**
     * 邮编
     */
    zipcode: string

    /**
     * 项目编码
     */
    projectCode: string

    /**
     * E/设备,S/设施
     */
    kind: string

    /**
     * 设施设备分类
     */
    category: string
    categoryPath?: string

    /**
     * 一级分类编码
     */
    categoryFirstCode: string

    /**
     * 二级分类编码
     */
    categorySecondCode: string

    /**
     * 三级分类编码
     */
    categoryThirdCode: string

    /**
     * 前缀编码
     */
    prefix: string

    /**
     * 是否启用
     */
    enabled: string

    /**
     * 当前最大流水号
     */
    currentMaxSeq: number
}

export interface DeviceCodeRuleForm extends BaseEntity {
    /**
     *
     */
    id?: string | number

    /**
     * 项目-id
     */
    projectId?: string | number

    /**
     * 邮编
     */
    zipcode?: string

    /**
     * 项目编码
     */
    projectCode?: string

    /**
     * E/设备,S/设施
     */
    kind?: string

    /**
     * 设施设备分类
     */
    category?: string
    categoryPath?: string

    /**
     * 一级分类编码
     */
    categoryFirstCode?: string

    /**
     * 二级分类编码
     */
    categorySecondCode?: string

    /**
     * 三级分类编码
     */
    categoryThirdCode?: string

    /**
     * 前缀编码
     */
    prefix?: string

    /**
     * 是否启用
     */
    enabled?: string

    /**
     * 当前最大流水号
     */
    currentMaxSeq?: number
}

export interface DeviceCodeRuleQuery extends PageQuery {
    /**
     * 项目-id
     */
    projectId?: string | number

    /**
     * E/设备,S/设施
     */
    kind?: string

    /**
     * 设施设备分类
     */
    //category?: string
    categoryPath?: string

    /**
     * 是否启用
     */
    enabled?: string

    /**
     * 日期范围参数
     */
    params?: any
}
