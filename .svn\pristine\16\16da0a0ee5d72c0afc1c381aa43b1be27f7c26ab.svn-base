export interface TaskResourceItemVO {
    /**
     *
     */
    id: string | number

    /**
     * 项目-id
     */
    projectId: string | number

    /**
     * 计划定义id
     */
    defineId: string | number

    /**
     * 任务id
     */
    taskId: string | number

    /**
     * 巡检路线
     */
    inspectionLineId: string | number

    /**
     * 管理单元
     */
    unitId: string | number

    /**
     * 设施设备分类
     */
    deviceCategoryId: string | number

    /**
     * 设施设备id
     */
    deviceId: string | number
    roadId: string
    roadName: string
    startStake: string
    endStake: string
    jobIdList: string
    dataType: string
}

export interface TaskResourceItemBO {
    /**
     *
     */
    id: string | number

    /**
     * 项目-id
     */
    projectId: string | number

    /**
     * 计划定义id
     */
    defineId: string | number

    /**
     * 任务id
     */
    taskId: string | number

    /**
     * 巡检路线
     */
    inspectionLineId: string | number

    /**
     * 管理单元
     */
    unitId: string

    /**
     * 设施设备分类
     */
    deviceCategoryId: string

    /**
     * 设施设备id
     */
    deviceId: string | number
    roadId: string
    roadName: string
    startStake: string
    endStake: string
    jobIdList: string
    dataType: string
}

export interface TaskResourceItemForm extends BaseEntity {
    /**
     *
     */
    id?: string | number

    /**
     * 项目-id
     */
    projectId?: string | number

    /**
     * 计划定义id
     */
    defineId?: string | number

    /**
     * 任务id
     */
    taskId?: string | number

    /**
     * 巡检路线
     */
    inspectionLineId?: string | number

    /**
     * 管理单元
     */
    unitId?: string | number

    /**
     * 设施设备分类
     */
    deviceCategoryId?: string | number

    /**
     * 设施设备id
     */
    deviceId?: string | number
    roadId: string
    roadName: string
    startStake: string
    endStake: string
    jobIdList: string
    dataType: string
}

export interface TaskResourceItemQuery extends PageQuery {
    /**
     * 项目-id
     */
    projectId?: string | number

    /**
     * 计划定义id
     */
    defineId?: string | number

    /**
     * 任务id
     */
    taskId?: string | number

    /**
     * 日期范围参数
     */
    params?: any
}
