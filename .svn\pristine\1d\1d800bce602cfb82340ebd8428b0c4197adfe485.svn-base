<!-- 拆分结果展示对话框 -->
<template>
    <el-dialog v-model="dialogVisible" title="拆分结果" width="900px" :close-on-click-modal="false" @close="handleClose">
        <div class="split-result-content">
            <!-- 拆分成功提示 -->
            <div class="success-info">
                <el-result icon="success" title="拆分成功" :sub-title="`成功拆分出 ${splitResults.length} 个新计划，原计划已更新`" />
            </div>

            <!-- 拆分统计信息 -->
            <div class="split-summary">
                <el-card>
                    <template #header>
                        <span>拆分统计</span>
                    </template>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-statistic title="拆分出的计划数" :value="splitResults.length" />
                        </el-col>
                        <el-col :span="6">
                            <el-statistic title="原计划保留资源项" :value="originalResultInfo?.resourceItemCount || 0" />
                        </el-col>
                        <el-col :span="6">
                            <el-statistic title="拆分资源项总数" :value="totalSplitResourceCount" />
                        </el-col>
                        <el-col :span="6">
                            <el-statistic title="操作时间" :value="operationTime" />
                        </el-col>
                    </el-row>
                </el-card>
            </div>

            <!-- 原计划信息 -->
            <div class="original-plan" v-if="originalResultInfo">
                <h4>原计划（更新后）</h4>
                <el-table :data="[originalResultInfo]" border>
                    <el-table-column prop="newDefineName" label="计划名称" />
                    <el-table-column prop="newMonthName" label="月度计划名称" />
                    <el-table-column prop="inspectionLineName" label="保留路线" />
                    <el-table-column prop="resourceItemCount" label="资源项数量" align="center" />
                    <el-table-column label="操作" align="center" width="120">
                        <template #default="scope">
                            <el-button type="primary" size="small" @click="handleViewPlan(scope.row.newMonthId)"> 查看 </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 拆分出的计划列表 -->
            <div class="split-plans">
                <h4>拆分出的计划</h4>
                <el-table :data="splitResults" border>
                    <el-table-column type="index" label="序号" width="60" align="center" />
                    <el-table-column prop="newDefineName" label="计划名称" />
                    <el-table-column prop="newMonthName" label="月度计划名称" />
                    <el-table-column prop="inspectionLineName" label="巡检路线" />
                    <el-table-column prop="resourceItemCount" label="资源项数量" align="center" />
                    <el-table-column label="操作" align="center" width="120">
                        <template #default="scope">
                            <el-button type="primary" size="small" @click="handleViewPlan(scope.row.newMonthId)"> 查看 </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 操作提示 -->
            <div class="operation-tips">
                <el-alert title="操作提示" type="info" :closable="false" show-icon>
                    <template #default>
                        <ul>
                            <li>拆分后的计划已自动创建，可以独立进行后续操作</li>
                            <li>原计划保留了未被拆分的巡检路线，可以继续使用</li>
                            <li>所有计划的拆分状态已重新计算，支持进一步拆分</li>
                            <li>点击"查看"按钮可以查看具体计划的详细信息</li>
                        </ul>
                    </template>
                </el-alert>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="handleClose">确定</el-button>
                <el-button @click="handleRefreshList">刷新列表</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'

// 拆分结果数据接口
interface SplitResultInfo {
    newDefineId: string
    newDefineName: string
    newMonthId: string
    newMonthName: string
    inspectionLineId: string
    inspectionLineName: string
    resourceItemCount: number
}

// 组件属性
interface Props {
    visible: boolean
    splitResults: SplitResultInfo[]
    originalResultInfo?: SplitResultInfo
    operationTime: string
}

// 组件事件
interface Emits {
    (e: 'update:visible', visible: boolean): void
    (e: 'close'): void
    (e: 'refresh'): void
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    splitResults: () => [],
    operationTime: ''
})

const emit = defineEmits<Emits>()

const router = useRouter()

// 响应式数据
const dialogVisible = ref(false)

// 计算属性
const totalSplitResourceCount = computed(() => {
    return props.splitResults.reduce((sum, result) => sum + result.resourceItemCount, 0)
})

// 监听visible属性变化
watch(
    () => props.visible,
    (newVal) => {
        dialogVisible.value = newVal
    }
)

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
    emit('update:visible', newVal)
})

// 方法
const handleViewPlan = (monthId: string) => {
    // 跳转到计划详情页面
    router.push({
        path: '/subProject/circle/plan/baseInfo',
        query: {
            taskType: 'inspection',
            id: monthId,
            from: 'split'
        }
    })
}

const handleClose = () => {
    dialogVisible.value = false
    emit('close')
}

const handleRefreshList = () => {
    emit('refresh')
    handleClose()
}
</script>

<style lang="scss" scoped>
.split-result-content {
    .success-info {
        margin-bottom: 24px;
        text-align: center;
    }

    .split-summary {
        margin-bottom: 24px;
    }

    .original-plan,
    .split-plans {
        margin-bottom: 24px;

        h4 {
            margin: 0 0 12px 0;
            color: #303133;
            font-size: 16px;
            font-weight: 600;
        }
    }

    .operation-tips {
        margin-bottom: 16px;

        ul {
            margin: 0;
            padding-left: 20px;

            li {
                margin-bottom: 4px;
                color: #606266;
                line-height: 1.5;
            }
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

:deep(.el-result__title) {
    color: #67c23a;
}

:deep(.el-result__subtitle) {
    color: #606266;
}
</style>
