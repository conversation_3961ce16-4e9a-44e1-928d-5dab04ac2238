<template>
    <div class="p-2 document-management-page">
        <div style="display: flex;gap: 10px;">
            <div style="width: 500px">
                <el-card shadow="never">
                    <template #header>
                        <el-row :gutter="10" class="mb8">
                            <el-col :span="1.5">
                                <el-button type="primary" plain icon="Plus" @click="handleChannelAdd()"
                                    v-hasPermi="[permissions.addChannel]">新增类别</el-button>
                            </el-col>
                            <el-col :span="1.5">
                                <el-button type="info" plain icon="Sort"
                                    @click="handleChaneelToggleExpandAll">展开/折叠</el-button>
                            </el-col>
                            <el-col :span="1.5">
                                <el-button type="default" plain icon="Folder" @click="handleShowAllFiles"
                                    :class="{ 'selected-all-btn': !selectedChannelId }">
                                    全部文件
                                </el-button>
                            </el-col>
                            <!-- <right-toolbar v-model="channelShowSearch" @queryTable="getChannelList"></right-toolbar> -->
                        </el-row>
                    </template>
                    <el-table ref="fileChannelTableRef" v-loading="channelLoading" stripe :data="fileChannelList" row-key="id"
                        :default-expand-all="isExpandAll"
                        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                        @row-click="handleChannelRowClick" :row-style="getChannelRowStyle">
                        <el-table-column label="所属项目" prop="projectId" v-if="false" />
                        <el-table-column label="名称" align="center" prop="name" />
                        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="240">
                            <template #default="scope">
                                <div class="op-actions">
                                    <el-tooltip content="修改" placement="top" v-hasPermi="[permissions.editChannel]">
                                        <el-button link type="primary" class="op-link op-edit" @click="handleChannelUpdate(scope.row)" v-hasPermi="[permissions.editChannel]">
                                            <img class="op-icon" src="@/assets/images/edit-icon.png" alt="修改" />
                                            修改
                                        </el-button>
                                    </el-tooltip>
                                    <el-tooltip content="新增" placement="top" v-hasPermi="[permissions.addChannel]">
                                        <el-button link type="primary" class="op-link op-info" @click="handleChannelAdd(scope.row)" v-hasPermi="[permissions.addChannel]">
                                            <img class="op-icon" src="@/assets/images/add-icon.png" alt="新增" />
                                            新增
                                        </el-button>
                                    </el-tooltip>
                                    <el-tooltip content="删除" placement="top" v-hasPermi="[permissions.deleteChannel]">
                                        <el-button link type="danger" class="op-link op-delete" @click="handleChannelDelete(scope.row)" v-hasPermi="[permissions.deleteChannel]">
                                            <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />
                                            删除
                                        </el-button>
                                    </el-tooltip>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
                <!-- 添加或修改文件夹对话框 -->
                <el-dialog :title="channelDialog.title" v-model="channelDialog.visible" width="500px" append-to-body>
                    <el-form ref="fileChannelFormRef" :model="channelForm" :rules="rules" label-width="80px">

                        <el-form-item label="类别名称" prop="name">
                            <el-input v-model="channelForm.name" placeholder="请输入名称" />
                        </el-form-item>

                        <el-form-item label="默认" prop="isSystem">
                            <el-radio-group v-model="channelForm.isSystem">
                                <!-- <el-radio :label="1">是</el-radio> -->
                                <el-radio :label="0">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="父节点" prop="parentId">
                            <el-tree-select v-model="channelForm.parentId" :data="fileChannelOptions"
                                :props="{ value: 'id', label: 'name', children: 'children' }" placeholder="请选择类别"
                                check-strictly :disabled="isParentIdDisabled" />
                        </el-form-item>

                    </el-form>
                    <template #footer>
                        <div class="dialog-footer">
                            <el-button :loading="channelButtonLoading" type="primary" @click="submitChannelForm">确
                                定</el-button>
                            <el-button @click="cancelChannel">取 消</el-button>
                        </div>
                    </template>
                </el-dialog>
            </div>

            <div style="flex: 1">
                <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
                    :leave-active-class="proxy?.animate.searchAnimate.leave">
                    <div v-show="showSearch" class="mb-[10px]">
                        <el-card shadow="hover">
                            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                                <el-form-item label="文件名称" prop="fileName">
                                    <el-input v-model="queryParams.fileName" placeholder="请输入文件名称" clearable
                                        @keyup.enter="handleQuery" />
                                </el-form-item>
                                <el-form-item label="文件原名" prop="originalName">
                                    <el-input v-model="queryParams.originalName" placeholder="请输入文件原名称" clearable
                                        @keyup.enter="handleQuery" />
                                </el-form-item>
                                <el-form-item class="filter-actions">
                                    <el-button type="primary" @click="handleQuery">搜索</el-button>
                                </el-form-item>
                            </el-form>
                        </el-card>
                    </div>
                </transition>

                <el-card shadow="never">
                    <template #header>
                        <el-row :gutter="10" class="mb8">
                            <el-col :span="1.5">
                                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                                    v-hasPermi="[permissions.addFile]">新增</el-button>
                            </el-col>
                            <el-col :span="1.5" v-if="selectedChannelName">
                                <el-tag type="info" size="large">
                                    当前类别：{{ selectedChannelName }}
                                </el-tag>
                            </el-col>
                            <el-col :span="1.5">
                                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
                                    v-hasPermi="[permissions.editFile]">修改</el-button>
                            </el-col>
                            <el-col :span="1.5">
                                <el-button type="danger" plain icon="Delete" :disabled="multiple"
                                    @click="handleDelete()" v-hasPermi="[permissions.deleteFile]">删除</el-button>
                            </el-col>
                            <!-- <el-col :span="1.5">
                                <el-button type="warning" plain icon="Download" @click="handleExport"
                                    v-hasPermi="['common:file:export']" v-if="false">导出</el-button>
                            </el-col> -->
                            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                        </el-row>
                    </template>

                    <el-table v-loading="loading" stripe :data="fileList" @selection-change="handleSelectionChange">
                        <el-table-column type="selection" width="55" align="center" />
                        <el-table-column label="文件名称" align="center" prop="fileName" />
                        <el-table-column label="所属类别" align="center" prop="channelId">
                            <template #default="scope">
                                {{ getChannelName(scope.row.channelId) }}
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="全局共享" align="center" prop="isGlobal">
                            <template #default="scope">
                                <el-tag :type="scope.row.isGlobal === 1 ? 'success' : 'info'">
                                    {{ scope.row.isGlobal === 1 ? '是' : '否' }}
                                </el-tag>
                            </template>
                        </el-table-column> -->
                        <el-table-column label="文件类型" align="center" prop="fileSuffix">
                            <template #default="scope">
                                <el-tag type="primary">{{ scope.row.fileSuffix?.toUpperCase() }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="文件链接" align="center" prop="fileUrl">
                            <template #default="scope">
                                <div v-if="scope.row.ossIds">
                                    <div v-for="(ossId, index) in scope.row.ossIds.split(',')" :key="index" class="file-link-item">
                                        <el-link
                                            type="primary"
                                            :underline="false"
                                            @click="handleFileDownload(ossId.trim())"
                                            class="file-download-link"
                                        >
                                            <el-icon><Download /></el-icon>
                                            文件{{ scope.row.ossIds.split(',').length > 1 ? (index + 1) : '' }}
                                        </el-link>
                                    </div>
                                    <el-tag type="info" size="small" class="file-count-tag">
                                        {{ scope.row.ossIds.split(',').length }}个文件
                                    </el-tag>
                                </div>
                                <span v-else>-</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="文件描述" align="center" prop="description">
                            <template #default="scope">
                                <el-tooltip v-if="scope.row.description" :content="scope.row.description"
                                    placement="top">
                                    <span>{{ scope.row.description.length > 20 ? scope.row.description.substring(0, 20)
                                        + '...' : scope.row.description }}</span>
                                </el-tooltip>
                                <span v-else>-</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="上传时间" align="center" prop="createTime">
                            <template #default="scope">
                                <span>{{ scope.row.createTime ? parseTime(scope.row.createTime) : '-' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
                            <template #default="scope">
                                <div class="op-actions">
                                    <el-button link type="primary" class="op-link op-edit" @click="handleUpdate(scope.row)" v-hasPermi="[permissions.editFile]">
                                        <img class="op-icon" src="@/assets/images/edit-icon.png" alt="修改" />
                                        修改
                                    </el-button>
                                    <el-button link type="danger" class="op-link op-delete" @click="handleDelete(scope.row)" v-hasPermi="[permissions.deleteFile]">
                                        <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />
                                        删除
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>

                    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                        v-model:limit="queryParams.pageSize" @pagination="getList" />
                </el-card>
                <!-- 添加或修改文件管理对话框 -->
                <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body draggable>
                    <el-form ref="fileFormRef" :model="form" :rules="rules" label-width="120px">
                        <el-form-item label="所属项目" prop="projectId">
                            <el-input v-model="projectName" placeholder="所属项目" disabled />
                        </el-form-item>
                        <el-form-item label="所属类别" prop="channelId">
                            <el-tree-select v-model="form.channelId" :data="fileChannelOptions"
                                :props="{ value: 'id', label: 'name', children: 'children' }" placeholder="请选择所属类别"
                                check-strictly />
                        </el-form-item>
                        <!-- <el-form-item label="全局共享" prop="isGlobal">
                            <el-radio-group v-model="form.isGlobal">
                                <el-radio :label="1">是</el-radio>
                                <el-radio :label="0">否</el-radio>
                            </el-radio-group>
                        </el-form-item> -->
                        <el-form-item label="文件上传" prop="file">
                            <div class="upload-container">
                                <el-radio-group v-model="uploadType" @change="handleUploadTypeChange">
                                    <el-radio :label="0">文件上传</el-radio>
                                    <el-radio :label="1">图片上传</el-radio>
                                </el-radio-group>
                                <div class="upload-component">
                                    <fileUpload v-if="uploadType === 0" v-model="form.ossIds"
                                        :file-type="['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'mp4', 'zip', 'rar']"
                                        :file-size="50" :limit="1" @update:modelValue="handleFileUploadChange" />
                                    <imageUpload v-if="uploadType === 1" v-model="form.ossIds"
                                        :file-type="['png', 'jpg', 'jpeg', 'gif', 'bmp']" :file-size="50" :limit="1"
                                        @update:modelValue="handleImageUploadChange" />
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="文件名称" prop="fileName">
                            <el-input v-model="form.fileName" placeholder="请输入文件名称" />
                        </el-form-item>
                        <el-form-item label="文件描述" prop="description">
                            <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入文件描述" />
                        </el-form-item>
                    </el-form>
                    <template #footer>
                        <div class="dialog-footer">
                            <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                            <el-button @click="cancel">取 消</el-button>
                        </div>
                    </template>
                </el-dialog>
            </div>
        </div>
    </div>
</template>

<script setup name="File" lang="ts">
import { nextTick, ref, reactive, toRefs, getCurrentInstance, onMounted, watch } from 'vue';
import { Download } from '@element-plus/icons-vue';
import { listFileChannel, getFileChannel, delFileChannel, addFileChannel, updateFileChannel } from "@/api/common/fileChannel";
import { FileChannelVO, FileChannelQuery, FileChannelForm } from '@/api/common/fileChannel/types';

import { listFile, getFile, delFile, addFile, updateFile } from '@/api/common/file';
import { FileVO, FileQuery, FileForm } from '@/api/common/file/types';

// 导入上传组件
import fileUpload from '@/components/FileUpload/index.vue';
import imageUpload from '@/components/ImageUpload/index.vue';

// 导入OSS相关API
import { listByIds } from '@/api/system/oss';

// 导入请求工具
import request from '@/utils/request';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const props = defineProps({
    code: {
        //操作类型：read、edit
        type: String,

    },
    projectId: {
        type: String,
        required: false
    },
    isSystem: {
        type: Boolean,
        required: true
    },
    // 类别:document-文档、应急资料-emergency、安全会议-metting
    category: {
        type: String,
        required: false,
        default: "document"
    },
    // 权限码对象，使用字符串键
    permissions: {
        type: Object,
        required: false,
        default: () => ({})
    }
});
const fileList = ref<FileVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
// 文件管理相关变量
const projectName = ref('');
const selectedFile = ref<File | null>(null);

// 上传相关变量
const uploadType = ref(0); // 0: 文件上传, 1: 图片上传

const queryFormRef = ref<ElFormInstance>();
const fileFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
});

const initFormData: FileForm = {
    id: undefined,
    projectId: undefined,
    channelId: undefined,
    isGlobal: 0,
    fileName: undefined,
    originalName: undefined,
    fileSuffix: undefined,
    description: undefined,
    ossIds: undefined, // 添加OSS文件ID字段
    fileUrl: undefined, // 添加文件URL字段
    category: undefined // 添加文件分类字段
};
const data = reactive<PageData<FileForm, FileQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        channelId: undefined,
        isGlobal: undefined,
        fileName: undefined,
        originalName: undefined,
        fileSuffix: undefined,
        params: {}
    },
    rules: {
        channelId: [
            { required: true, message: '请选择所属类别', trigger: 'change' }
        ],
        fileName: [
            { required: true, message: '文件名称不能为空', trigger: 'blur' }
        ],
        isGlobal: [
            { required: true, message: '请选择是否全局共享', trigger: 'change' }
        ],
        ossIds: [
            { required: true, message: '请上传文件', trigger: 'change' }
        ]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询文件管理列表 */
const getList = async () => {
    loading.value = true;

    // 严格验证projectId
    if (!props.projectId || props.projectId.trim() === '') {
        console.warn('getList - projectId为空或无效，跳过查询');
        fileList.value = [];
        total.value = 0;
        loading.value = false;
        return;
    }

    // 设置项目ID到查询参数
    queryParams.value.projectId = props.projectId.trim();

    // 设置category到查询参数 - 这是关键的筛选条件
    if (props.category) {
        queryParams.value.category = props.category;
    }

    // 如果选中了类别，则过滤该类别下的文件
    if (selectedChannelId.value) {
        queryParams.value.channelId = selectedChannelId.value;
    } else {
        // 如果没有选中类别，则清空类别过滤条件
        // 但仍然会通过category字段筛选当前分类的文件
        queryParams.value.channelId = undefined;
    }

    try {
        const res = await listFile(queryParams.value);
        fileList.value = res.rows;
        total.value = res.total;
    } catch (error) {
        console.error('获取文件列表失败:', error);
        fileList.value = [];
        total.value = 0;
        proxy?.$modal.msgError('获取文件列表失败');
    } finally {
        loading.value = false;
    }
};

/** 文件选择处理 */
const handleFileChange = (file: any) => {
    selectedFile.value = file.raw;
    // 自动设置文件名称
    if (!form.value.fileName) {
        form.value.fileName = file.name;
    }
    // 设置文件后缀
    const lastDotIndex = file.name.lastIndexOf('.');
    if (lastDotIndex > 0) {
        form.value.fileSuffix = file.name.substring(lastDotIndex + 1);
        form.value.originalName = file.name;
    }
};

/** 处理上传类型变化 */
const handleUploadTypeChange = () => {
    // 清空已上传的文件
    form.value.ossIds = undefined;
    form.value.fileName = '';
    form.value.originalName = '';
    form.value.fileSuffix = '';
    form.value.fileUrl = '';
};

/** 处理文件上传变化 */
const handleFileUploadChange = async (ossIds: string) => {
    console.log('文件上传变化 - 文件上传:', ossIds);
    await extractFileInfoFromOssIds(ossIds);
};

/** 处理图片上传变化 */
const handleImageUploadChange = async (ossIds: string) => {
    console.log('文件上传变化 - 图片上传:', ossIds);
    await extractFileInfoFromOssIds(ossIds);
};

/** 处理文件下载 */
const handleFileDownload = async (ossId: string) => {
    try {
        console.log('开始下载文件，OSS ID:', ossId);

        // 根据ossId获取文件信息
        const ossRes = await listByIds(ossId);
        if (ossRes.data && ossRes.data.length > 0) {
            const ossFile = ossRes.data[0];
            const downloadUrl = ossFile.url;

            if (downloadUrl) {
                console.log('文件下载URL:', downloadUrl);
                console.log('文件名:', ossFile.originalName || ossFile.fileName);

                // 创建下载链接
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = ossFile.originalName || ossFile.fileName || 'download';
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                proxy?.$modal.msgSuccess('文件下载已开始');
            } else {
                proxy?.$modal.msgError('文件下载链接无效');
            }
        } else {
            proxy?.$modal.msgError('未找到文件信息');
        }
    } catch (error) {
        console.error('文件下载失败:', error);
        proxy?.$modal.msgError('文件下载失败');
    }
};

/** 从OSS文件ID中提取文件信息 */
const extractFileInfoFromOssIds = async (ossIds: string) => {
    if (!ossIds) return;

    try {
        // 将逗号分隔的ossIds转换为数组
        const ossIdArray = ossIds.split(',').filter(id => id.trim());
        if (ossIdArray.length === 0) return;

        // 设置ossIds字段
        form.value.ossIds = ossIds;

        // 控制台输出上传成功后的ossId
        console.log('文件上传成功，OSS ID:', ossIds);
        console.log('文件数量:', ossIdArray.length);

        // 获取第一个文件的OSS信息作为主要文件信息
        const ossRes = await listByIds(ossIdArray[0]);
        if (ossRes.data && ossRes.data.length > 0) {
            const ossFile = ossRes.data[0];

            // 优先使用用户上传的原文件名称，而不是OSS重命名后的名称
            form.value.originalName = ossFile.originalName || '';
            form.value.fileSuffix = ossFile.fileSuffix || '';
            form.value.fileUrl = ossFile.url || '';

            // 文件名称默认使用原文件名称
            form.value.fileName = form.value.originalName || '';

            // 如果上传了多个文件，在文件名后添加数量标识
            if (ossIdArray.length > 1) {
                const originalFileName = form.value.fileName;

                const extension = form.value.fileSuffix ? `.${form.value.fileSuffix}` : '';
                const nameWithoutExt = originalFileName.replace(extension, '');
                form.value.fileName = `${nameWithoutExt}等${ossIdArray.length}个文件${extension}`;
            }
        }

        // 如果有多个文件，获取所有文件的URL信息
        if (ossIdArray.length > 1) {
            try {
                const allOssRes = await listByIds(ossIds);
                if (allOssRes.data && allOssRes.data.length > 0) {
                    // 设置第一个文件的URL作为主要URL
                    form.value.fileUrl = allOssRes.data[0].url || '';
                }
            } catch (error) {
                console.error('获取多文件信息失败:', error);
            }
        }

        console.log('文件信息提取成功:', {
            ossIds: form.value.ossIds,
            fileName: form.value.fileName,
            originalName: form.value.originalName,
            fileSuffix: form.value.fileSuffix,
            fileUrl: form.value.fileUrl,
            fileCount: ossIdArray.length
        });

    } catch (error) {
        console.error('提取文件信息失败:', error);
        proxy?.$modal.msgError('提取文件信息失败');
    }
};

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData };
    fileFormRef.value?.resetFields();
    // 清理文件上传状态
    selectedFile.value = null;
    // 重置上传类型
    uploadType.value = 0;
};

/** 根据ID获取类别名称 */
const getChannelName = (channelId: string | number): string => {
    const findChannelName = (list: FileChannelVO[]): string => {
        for (const item of list) {
            if (item.id === channelId) {
                return item.name;
            }
            if (item.children && item.children.length > 0) {
                const name = findChannelName(item.children);
                if (name) return name;
            }
        }
        return '';
    };
    return findChannelName(fileChannelList.value);
};

/** 处理文件类别行点击 */
const handleChannelRowClick = (row: FileChannelVO) => {
    console.log('Channel row clicked:', row);
    selectedChannelId.value = row.id;
    selectedChannelName.value = row.name;
    // 更新查询参数并重新获取文件列表
    queryParams.value.channelId = row.id;
    getList();
    // 刷新文件类别下拉数据
    getFileChannelOptions();
};

/** 获取文件类别行样式 */
const getChannelRowStyle = ({ row }: { row: FileChannelVO }) => {
    const isSelected = row.id === selectedChannelId.value;
    if (isSelected) {
        return {
            backgroundColor: '#e6f7ff',
            color: '#1890ff',
            fontWeight: 'bold'
        };
    }
    return {};
};

/** 显示全部文件 */
const handleShowAllFiles = () => {
    selectedChannelId.value = '';
    selectedChannelName.value = '';
    // 清空类别过滤条件并重新获取文件列表
    queryParams.value.channelId = undefined;
    getList();
    // 刷新文件类别下拉数据
    getFileChannelOptions();
};

/** 取消按钮 */
const cancel = () => {
    reset();
    dialog.visible = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: FileVO[]) => {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
    reset();
    // 设置项目ID到表单
    if (props.projectId) {
        form.value.projectId = props.projectId;
    }
    // 🔧 设置category到表单 - 这是关键！
    if (props.category) {
        form.value.category = props.category;
    }
    // 如果选中了类别，则设置为默认类别
    if (selectedChannelId.value) {
        form.value.channelId = selectedChannelId.value;
    }
    // 设置默认值
    form.value.isGlobal = 0;
    // 获取文件类别下拉数据
    getFileChannelOptions();
    dialog.visible = true;
    dialog.title = '添加文件';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: FileVO) => {
    reset();
    const _id = row?.id || ids.value[0];
    const res = await getFile(_id);
    Object.assign(form.value, res.data);

    // 根据文件后缀判断上传类型
    if (res.data.fileSuffix) {
        const imageSuffixes = ['png', 'jpg', 'jpeg', 'gif', 'bmp'];
        if (imageSuffixes.includes(res.data.fileSuffix.toLowerCase())) {
            uploadType.value = 1; // 图片上传
        } else {
            uploadType.value = 0; // 文件上传
        }
    }

    // 如果有ossIds，等待上传组件加载完成后再设置
    if (res.data.ossIds) {
        // 延迟设置，确保上传组件已经初始化
        await nextTick();
        // 这里不需要手动设置ossIds，因为上传组件会自动从modelValue中读取
    }

    // 获取文件类别下拉数据
    getFileChannelOptions();
    dialog.visible = true;
    dialog.title = '修改文件';
};

/** 获取文件类别下拉选项 */
const getFileChannelOptions = async () => {
    try {
        // 严格验证projectId
        if (!props.projectId || props.projectId.trim() === '') {
            console.warn('getFileChannelOptions - projectId为空或无效，跳过查询');
            fileChannelOptions.value = [{ id: '0', name: '顶级节点', children: [] }];
            return;
        }

        // 确保传递正确的项目ID参数
        const params: FileChannelQuery = {
            projectId: props.projectId.trim()
        };

        // 如果设置了category参数，则添加到查询条件中
        if (props.category) {
            params.category = props.category;
        }

        const res = await listFileChannel(params);
        fileChannelOptions.value = [];
        const data: FileChannelOption = { id: '0', name: '顶级节点', children: [] };
        data.children = proxy?.handleTree<FileChannelOption>(res.data, "id", "parentId");
        fileChannelOptions.value.push(data);
    } catch (error) {
        console.error('获取文件类别下拉数据失败:', error);
        fileChannelOptions.value = [{ id: '0', name: '顶级节点', children: [] }];
    }
};

/** 提交按钮 */
const submitForm = () => {
    fileFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            // 确保项目ID不为空
            if (!props.projectId) {
                proxy?.$modal.msgError("项目ID不能为空");
                return;
            }

            // 新增时必须上传文件
            if (!form.value.id && !form.value.ossIds) {
                proxy?.$modal.msgError("请上传文件");
                return;
            }

            // 设置项目ID
            form.value.projectId = props.projectId;

            // 🔧 设置category - 这是关键！
            if (props.category) {
                form.value.category = props.category;
            }

            // 🔧 更新控制台输出，包含category字段
            console.log('提交文件信息:', {
                id: form.value.id,
                projectId: form.value.projectId,
                channelId: form.value.channelId,
                category: form.value.category, // 显示category字段
                fileName: form.value.fileName,
                originalName: form.value.originalName,
                fileSuffix: form.value.fileSuffix,
                ossIds: form.value.ossIds,
                fileUrl: form.value.fileUrl,
                description: form.value.description
            });

            buttonLoading.value = true;
            try {
                if (form.value.id) {
                    await updateFile(form.value);
                } else {
                    await addFile(form.value);
                }
                proxy?.$modal.msgSuccess('操作成功');
                dialog.visible = false;
                await getList();
            } catch (error) {
                console.error('操作失败:', error);
                proxy?.$modal.msgError("操作失败");
            } finally {
                buttonLoading.value = false;
            }
        }
    });
};

/** 删除按钮操作 */
const handleDelete = async (row?: FileVO) => {
    try {
        const _ids = row?.id || ids.value;
        const fileName = row?.fileName || '选中的文件';

        await proxy?.$modal.confirm(`是否确认删除文件"${fileName}"？\n\n⚠️ 删除后将无法恢复！`);

        loading.value = true;
        await delFile(_ids);
        proxy?.$modal.msgSuccess('删除成功');
        await getList();
    } catch (error) {
        console.error('删除失败:', error);
        if (error !== 'cancel') {
            proxy?.$modal.msgError("删除失败");
        }
    } finally {
        loading.value = false;
    }
};

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'common/file/export',
        {
            ...queryParams.value
        },
        `file_${new Date().getTime()}.xlsx`
    );
};

const handleTreeNodeClick = () => { };


type FileChannelOption = {
    id: string | number;
    name: string;
    children?: FileChannelOption[];
}


const fileChannelList = ref<FileChannelVO[]>([]);
const fileChannelOptions = ref<FileChannelOption[]>([]);
const channelButtonLoading = ref(false);
const channelShowSearch = ref(true);
const isExpandAll = ref(true);
const channelLoading = ref(false);
// 控制父级选择器的状态
const isParentIdDisabled = ref(false);
const parentIdLabel = ref('类别');
// 文件类别选中状态
const selectedChannelId = ref<string | number>('');
const selectedChannelName = ref('');

const channelQueryFormRef = ref<ElFormInstance>();
const fileChannelFormRef = ref<ElFormInstance>();
const fileChannelTableRef = ref<ElTableInstance>()

const channelDialog = reactive<DialogOption>({
    visible: false,
    title: ''
});


const initChannelFormData: FileChannelForm = {
    id: undefined,
    projectId: undefined,
    name: undefined,
    code: undefined,
    isSystem: 0, // 默认设置为否
    parentId: undefined,
    path: undefined,
    category: undefined,
}

const channelData = reactive<PageData<FileChannelForm, FileChannelQuery>>({
    form: { ...initChannelFormData },
    queryParams: {
        projectId: undefined,
        name: undefined,
        code: undefined,
        isSystem: undefined,
        parentId: undefined,
        path: undefined,
        params: {
        }
    },
    rules: {
        name: [
            { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        isSystem: [
            { required: true, message: "请选择系统类别状态", trigger: "change" }
        ],
        projectId: [
            { required: true, message: "项目ID不能为空", trigger: "blur" }
        ]
    }
});

const { queryParams: channelQueryParams, form: channelForm, rules: channelRules } = toRefs(channelData);

/** 查询文件夹列表 */
const getChannelList = async () => {
    try {
        channelLoading.value = true;

        // 严格验证projectId
        if (!props.projectId || props.projectId.trim() === '') {
            console.warn('getChannelList - projectId为空或无效，跳过查询');
            fileChannelList.value = [];
            return;
        }

        // 确保传递正确的项目ID参数
        const params: FileChannelQuery = {
            projectId: props.projectId.trim()
        };

        // 如果设置了category参数，则添加到查询条件中
        if (props.category) {
            params.category = props.category;
        }

        const res = await listFileChannel(params);
        const data = proxy?.handleTree<FileChannelVO>(res.data, "id", "parentId");
        fileChannelList.value = data || [];
    } catch (error) {
        console.error('获取文件夹列表失败:', error);
        fileChannelList.value = [];
        proxy?.$modal.msgError("获取文件夹列表失败");
    } finally {
        channelLoading.value = false;
    }
}

/** 查询文件夹下拉树结构 */
const getTreeselect = async () => {
    try {
        // 严格验证projectId
        if (!props.projectId || props.projectId.trim() === '') {
            console.warn('getTreeselect - projectId为空或无效，跳过查询');
            fileChannelOptions.value = [{ id: '0', name: '顶级节点', children: [] }];
            return;
        }

        // 确保传递正确的项目ID参数
        const params: FileChannelQuery = {
            projectId: props.projectId.trim()
        };

        // 如果设置了category参数，则添加到查询条件中
        if (props.category) {
            params.category = props.category;
        }

        const res = await listFileChannel(params);
        fileChannelOptions.value = [];
        const data: FileChannelOption = { id: '0', name: '顶级节点', children: [] };
        data.children = proxy?.handleTree<FileChannelOption>(res.data, "id", "parentId");
        fileChannelOptions.value.push(data);
    } catch (error) {
        console.error('获取文件夹树形结构失败:', error);
        fileChannelOptions.value = [{ id: '0', name: '顶级节点', children: [] }];
    }
}

// 取消按钮
const cancelChannel = () => {
    resetChannel();
    channelDialog.visible = false;
}

// 表单重置
const resetChannel = () => {
    channelForm.value = { ...initChannelFormData }
    fileChannelFormRef.value?.resetFields();
    // 重置父级选择器状态
    isParentIdDisabled.value = false;
    parentIdLabel.value = '类别';
}

/** 搜索按钮操作 */
// const handleQuery = () => {
//   getList();
// }

// /** 重置按钮操作 */
// const resetQuery = () => {
//   queryFormRef.value?.resetFields();
//   handleQuery();
// }

/** 新增按钮操作 */
const handleChannelAdd = (row?: FileChannelVO) => {
    resetChannel();
    getTreeselect();

    // 设置项目ID到表单
    if (props.projectId) {
        channelForm.value.projectId = props.projectId;
    }

    // 设置分类值
    channelForm.value.category = props.category;

    // 确保默认值设置
    channelForm.value.isSystem = 0; // 默认设置为否

    if (row != null && row.id) {
        // 点击文件夹上的新增按钮，传递当前id作为parentId
        channelForm.value.parentId = String(row.id);
        isParentIdDisabled.value = true;
        parentIdLabel.value = `类别：${row.name}`;
    } else {
        // 点击顶部新增按钮，设置为顶级节点
        channelForm.value.parentId = '0';
        isParentIdDisabled.value = true;
        parentIdLabel.value = '类别：顶级';
    }

    channelDialog.visible = true;
    channelDialog.title = "添加类别";
}

/** 展开/折叠操作 */
const handleChaneelToggleExpandAll = () => {
    isExpandAll.value = !isExpandAll.value;
    toggleChannelExpandAll(fileChannelList.value, isExpandAll.value)
}

/** 展开/折叠操作 */
const toggleChannelExpandAll = (data: FileChannelVO[], status: boolean) => {
    data.forEach((item) => {
        fileChannelTableRef.value?.toggleRowExpansion(item, status)
        if (item.children && item.children.length > 0) toggleChannelExpandAll(item.children, status)
    })
}

/** 强制刷新树形控件 */
const refreshChannelTree = async () => {
    await getChannelList();
    // 等待DOM更新后重新设置展开状态
    await nextTick();
    if (isExpandAll.value) {
        toggleChannelExpandAll(fileChannelList.value, true);
    }
    // 同时刷新文件类别下拉数据
    await getFileChannelOptions();
}

/** 修改按钮操作 */
const handleChannelUpdate = async (row: FileChannelVO) => {
    resetChannel();
    await getTreeselect();

    const res = await getFileChannel(row.id);
    Object.assign(channelForm.value, res.data);

    // 修改时允许用户修改parentId
    isParentIdDisabled.value = false;
    parentIdLabel.value = '类别';

    channelDialog.visible = true;
    channelDialog.title = "修改类别";
}

/** 提交按钮 */
const submitChannelForm = () => {
    fileChannelFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            // 确保项目ID不为空
            if (!props.projectId) {
                proxy?.$modal.msgError("项目ID不能为空");
                return;
            }

            // 设置项目ID
            channelForm.value.projectId = props.projectId;

            // 确保分类值被设置
            channelForm.value.category = props.category;

            channelButtonLoading.value = true;
            try {
                if (channelForm.value.id) {
                    await updateFileChannel(channelForm.value);
                } else {
                    await addFileChannel(channelForm.value);
                }
                proxy?.$modal.msgSuccess("操作成功");
                channelDialog.visible = false;
                // 强制刷新树形控件和下拉数据
                await refreshChannelTree();
            } catch (error) {
                console.error('操作失败:', error);
                proxy?.$modal.msgError("操作失败");
            } finally {
                channelButtonLoading.value = false;
            }
        }
    });
}

/** 检查是否有下级目录 */
const hasChildren = (channelId: string | number): boolean => {
    const findChildren = (list: FileChannelVO[]): boolean => {
        for (const item of list) {
            if (item.parentId === String(channelId)) {
                return true;
            }
            if (item.children && item.children.length > 0) {
                if (findChildren(item.children)) {
                    return true;
                }
            }
        }
        return false;
    };
    return findChildren(fileChannelList.value);
};

/** 获取下级目录数量 */
const getChildrenCount = (channelId: string | number): number => {
    const countChildren = (list: FileChannelVO[]): number => {
        let count = 0;
        for (const item of list) {
            if (item.parentId === String(channelId)) {
                count++;
                if (item.children && item.children.length > 0) {
                    count += countChildren(item.children);
                }
            }
        }
        return count;
    };
    return countChildren(fileChannelList.value);
};

/** 检查是否有关联文件 */
const hasRelatedFiles = async (channelId: string | number): Promise<boolean> => {
    try {
        // 查询该类别下的文件数量
        const params: FileQuery = {
            projectId: props.projectId,
            channelId: channelId,
            pageNum: 1,
            pageSize: 1 // 只需要检查是否有文件，不需要获取所有文件
        };
        const res = await listFile(params);
        return res.total > 0;
    } catch (error) {
        console.error('检查关联文件失败:', error);
        return false;
    }
};

/** 删除按钮操作 */
const handleChannelDelete = async (row: FileChannelVO) => {
    try {
        // 检查是否有下级目录
        const hasChildChannels = hasChildren(row.id);
        const childrenCount = getChildrenCount(row.id);

        // 检查是否有关联文件
        const hasFiles = await hasRelatedFiles(row.id);

        // 构建提示信息
        let confirmMessage = `是否确认删除类别"${row.name}"？`;
        let warningMessage = '';

        if (hasChildChannels && hasFiles) {
            warningMessage = `⚠️ 该类别下存在多个下级目录和文件，删除后将无法恢复！`;
        } else if (hasChildChannels) {
            warningMessage = `⚠️ 该类别下存在多个下级目录，删除后将无法恢复！`;
        } else if (hasFiles) {
            warningMessage = '⚠️ 该类别下存在文件，删除后将无法恢复！';
        }

        if (warningMessage) {
            confirmMessage = `${confirmMessage}\n\n${warningMessage}`;
        }

        await proxy?.$modal.confirm(confirmMessage);

        channelLoading.value = true;
        await delFileChannel(row.id);
        proxy?.$modal.msgSuccess("删除成功");
        // 强制刷新树形控件和下拉数据
        await refreshChannelTree();

        // 如果删除的是当前选中的类别，清空选中状态
        if (selectedChannelId.value === row.id) {
            selectedChannelId.value = '';
            selectedChannelName.value = '';
            // 重新加载文件列表
            await getList();
        }
    } catch (error) {
        console.error('删除失败:', error);
        if (error !== 'cancel') {
            proxy?.$modal.msgError("删除失败");
        }
    } finally {
        channelLoading.value = false;
    }
}

onMounted(() => {
    // 设置项目名称（这里可以根据实际需求从项目信息中获取）
    projectName.value = '当前项目';
    getList();
    getChannelList();
    // 获取文件类别下拉数据
    getFileChannelOptions();
});

// 监听项目ID变化，重新加载数据
watch(() => props.projectId, async (newProjectId) => {
    if (newProjectId) {
        // 清空选中状态
        selectedChannelId.value = '';
        selectedChannelName.value = '';

        // 重新加载类别树和文件列表
        await getChannelList();
        await getFileChannelOptions();
        await getList();
    }
}, { immediate: false });
</script>

<style lang="scss" scoped>
.document-management-page {

    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-tree-select__wrapper),
    :deep(.el-date-editor .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }


    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button--primary) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button--primary)::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }
    :deep(.el-tag.el-tag--primary) {
        background: rgba(66, 134, 243, 0.1) !important;
        border-color: #4286F3 !important;
        color: #4286F3 !important;
    }

    :deep(.el-tag.el-tag--info) {
        background: rgba(144, 147, 153, 0.1) !important;
        border-color: #909399 !important;
        color: #909399 !important;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }



    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #AED7F2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #FFFFFF !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }



    /* 操作列图标按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
        flex-wrap: wrap;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
        margin: 2px 0;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286F3 !important;
    }

    .op-edit {
        color: #42F3E9 !important;
    }

    .op-delete {
        color: #D62121 !important;
    }




    .selected-all-btn {
        background-color: rgba(66, 134, 243, 0.15) !important;
        color: #4286F3 !important;
        border-color: #4286F3 !important;
    }

    .selected-all-btn:hover {
        background-color: rgba(66, 134, 243, 0.25) !important;
        color: #4286F3 !important;
        border-color: #4286F3 !important;
    }

    /* 文件链接样式优化 */
    .file-link-item {
        margin-bottom: 5px;
    }

    .file-download-link {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        font-size: 13px;
        padding: 2px 6px;
        border-radius: 4px;
        transition: all 0.3s;
        color: #4286F3 !important;
    }


    .file-count-tag {
        margin-top: 5px;
        font-size: 11px;
        background: rgba(144, 147, 153, 0.1) !important;
        border-color: #909399 !important;
        color: #909399 !important;
    }

    :deep(.el-dialog) {
        background: rgba(35, 45, 69, 0.95) !important;
        border-radius: 8px !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }
}
</style>
