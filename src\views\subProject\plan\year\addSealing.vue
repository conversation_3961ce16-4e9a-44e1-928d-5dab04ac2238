<!-- 新增封道计划
workloadComparison 工作量合计数比较，@todo 父组件保存时，需要做这个判断！
-->
<template>
    <div class="p-2">
        <!-- :model="form" :rules="rules" -->
        <el-form ref="taskDefineFormRef" label-width="160px" :model="form" :rules="rules">
            <el-card class="box-card">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>封道计划</span>
                    </div>
                </template>
                <div class="text item">
                    <el-row :gutter="gutter" v-if="isTemp != '1'">
                        <el-col :span="24">
                            <el-form-item label="年份" prop="code">
                                <el-text>{{ form.year }}</el-text>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter" v-if="isTemp == '1'">
                        <el-col :span="8">
                            <el-form-item label="计划作业日期" prop="bgnDate">
                                <el-date-picker
                                    v-model="form.bgnDate"
                                    placeholder="请选择计划作业日期"
                                    format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD"
                                    :clearable="true"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="12">
                            <el-form-item label="计划名称" prop="name">
                                <el-input v-model="form.name" placeholder="请输入年度计划名称" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 封道道路选择控件 -->
                    <SealingRoadSelector
                        v-model:sealingType="sealingType"
                        v-model:showJobButton="showSealingRoadJobButton"
                        v-model:showSealingTypeRadio="showSealingRoadTypeRadio"
                        v-model:taskResourceItems="taskResourceItems"
                        @change="handleSealingRoadChange"
                    />
                    <!-- 此处用频次控件 -->
                    <FrequencySelect
                        :filterUnits="isTemp === '1' ? 'day' : 'month,week,day'"
                        ref="frequencyRef"
                        v-model:unit="form.frequencyType"
                        v-model:unitValue="form.frequency"
                        :max-unit-value="isTemp === '1' ? 1 : undefined"
                        :frequency-selectable="isTemp == '1' ? false : true"
                        :unit-selectable="isTemp == '1' ? false : true"
                        planType="sealing"
                    />
                </div>
            </el-card>

            <el-card class="box-card">
                <!-- <template v-slot:header>
                    <div class="clearfix">
                        <span>封道计划</span>
                    </div> -->
                <!-- </template> -->
                <div class="text item">
                    <el-row :gutter="gutter" justify="center">
                        <el-col :span="24" style="text-align: center">
                            <el-button @click="submitForm">保存</el-button>
                        </el-col>
                    </el-row>
                </div>
            </el-card>
        </el-form>
    </div>
</template>
<script setup lang="ts">
import FrequencySelect from '../../components/Frequency/FrequencySelect.vue'
import SealingRoadSelector from '../../components/SealingRoadSelector/index.vue'
import { addTaskDefine, updateTaskDefine, getTaskDefine, addTempTaskDefine } from '@/api/plan/taskDefine'
import { getYearOfTaskYear } from '@/api/plan/taskYear'
import { TaskDefineQuery, TaskDefineForm } from '@/api/plan/taskDefine/types'
import { TaskResourceItemBo } from '@/api/plan/taskDefine/types'
import { useAppStore } from '@/store/modules/app'
import { useRoute, useRouter } from 'vue-router'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const buttonLoading = ref(false)
const gutter = ref(50) //设置项目表单两列的距离
const appStore = useAppStore()
const router = useRouter()
const route = useRoute()
const yearTaskId = ref('')
const frequencyRef = ref() // 添加对组件的引用
const isTemp = ref('0') //是否是临时任务
const taskDefineFormRef = ref<ElFormInstance>()
const showSealingRoadTypeRadio = ref(true)
const showSealingRoadJobButton = ref(true)
const initFormData: TaskDefineForm = {
    id: undefined,
    projectId: undefined,
    yearTaskId: undefined,
    name: undefined,
    taskType: undefined,
    speciality: undefined,
    year: undefined,
    frequencyType: undefined,
    frequency: undefined,
    frequencyData: undefined,
    bgnDate: undefined,
    endDate: undefined,
    tempTask: undefined,
    maintenanceContent: undefined,
    taskResource: undefined,
    status: undefined,
    publishState: 'temp',
    sealingType: 'full',
    taskResourceItems: []
}

const data = reactive<PageData<TaskDefineForm, TaskDefineQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        yearTaskId: undefined,
        name: undefined,
        taskType: undefined,
        speciality: undefined,
        year: undefined,
        frequencyType: undefined,
        frequency: undefined,
        maintenanceContent: undefined,
        status: undefined,
        params: {}
    },
    rules: {
        name: [{ required: true, message: '年度计划名称不能为空', trigger: 'blur' }],
        bgnDate: [{ required: true, message: '作业计划执行日期不能为空', trigger: 'blur' }],
        frequencyType: [{ required: true, message: '频次类型，字典项：year_frequency不能为空', trigger: 'change' }],
        frequency: [{ required: true, message: '频次不能为空', trigger: 'blur' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

// 创建计算属性来处理双向绑定
const sealingType = computed({
    get: () => {
        console.log('sealingType getter:', form.value.sealingType)
        return form.value.sealingType
    },
    set: (value: string) => {
        console.log('sealingType setter:', value)
        form.value.sealingType = value
    }
})

const taskResourceItems = computed({
    get: () => {
        console.log('taskResourceItems getter:', form.value.taskResourceItems)
        return form.value.taskResourceItems
    },
    set: (value: TaskResourceItemBo[]) => {
        console.log('taskResourceItems setter:', value)
        form.value.taskResourceItems = value
    }
})

// 处理封道线路选择器变化
const handleSealingRoadChange = (data: any) => {
    console.log('封道线路数据变化:', data)
    // 数据已经通过 v-model 自动更新到 form 中
    // 可以在这里添加其他处理逻辑
}

const submitForm = () => {
    form.value.projectId = appStore.projectContext.selectedProjectId
    form.value.taskType = 'sealing'
    form.value.year = getYearOfTaskYear()
    form.value.frequencyType = frequencyRef.value?.unit
    form.value.frequency = frequencyRef.value?.value
    if (isTemp.value == '1') {
        form.value.endDate = form.value.bgnDate
        form.value.tempTask = '1'
    } else {
        form.value.bgnDate = null
        form.value.endDate = null
        form.value.tempTask = '0'
    }
    console.log('form.value.frequency', form.value.frequency)

    form.value.speciality = 'road'
    if (form.value.frequencyType == 'week') {
        form.value.frequencyData = JSON.stringify({
            tableData: frequencyRef.value?.tableData,
            scheduleData: frequencyRef.value?.weekScheduleData
        })
    } else if (form.value.frequencyType == 'month') {
        form.value.frequencyData = JSON.stringify({
            tableData: frequencyRef.value?.tableData,
            scheduleData: frequencyRef.value?.monthScheduleData
        })
    } else if (form.value.frequencyType == 'day') {
        form.value.frequencyData = JSON.stringify({
            tableData: frequencyRef.value?.tableData,
            scheduleData: frequencyRef.value?.dayScheduleData
        })
    }

    taskDefineFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            try {
                buttonLoading.value = true
                form.value.maintenanceContent = 'fd'
                if (form.value.id) {
                    await updateTaskDefine(form.value)
                } else {
                    if (isTemp.value == '1') {
                        form.value.tempTask = 'YES'
                        await addTempTaskDefine(form.value)
                    } else {
                        form.value.tempTask = 'NO'
                        await addTaskDefine(form.value)
                    }
                }
                proxy?.$modal.msgSuccess('操作成功')
                router.push({
                    path: '/subProject/circle/plan/defineList',
                    query: { yearTaskId: yearTaskId.value }
                })
            } catch (error) {
                console.error('保存失败:', error)
                proxy?.$modal.msgError('保存失败')
            } finally {
                buttonLoading.value = false
            }
        }
    })
}

/** 处理封道数据回填 */
const handleSealingDataBackfill = (taskDefine: any) => {
    try {
        // 1. 封道类型回填（已在form.value中设置）
        console.log('回填封道类型:', taskDefine.sealingType)

        // 2. 处理taskResourceItems回填
        if (taskDefine.taskResourceItems && taskDefine.taskResourceItems.length > 0) {
            // 筛选封道相关的资源项
            const sealingItems = taskDefine.taskResourceItems.filter((item: any) => item.dataType === 'road' || item.roadId)

            if (sealingItems.length > 0) {
                // 转换数据格式以适配SealingRoadSelector组件
                const sealingTaskResourceItems = sealingItems.map((item: any) => ({
                    id: item.id || '',
                    projectId: item.projectId || '',
                    defineId: item.defineId || '',
                    taskId: item.taskId || '',
                    inspectionLineId: item.inspectionLineId || '',
                    unitId: item.unitId || '',
                    deviceCategoryId: item.deviceCategoryId || '',
                    deviceId: item.deviceId || '',
                    roadId: item.roadId || '',
                    roadName: item.roadName || '',
                    startStake: item.startStake || '',
                    endStake: item.endStake || '',
                    jobIdList:
                        typeof item.jobIdList === 'string'
                            ? item.jobIdList
                            : Array.isArray(item.jobIdList)
                              ? item.jobIdList.join(',')
                              : item.jobIdList || '',
                    dataType: 'road'
                }))

                // 设置到form中，触发SealingRoadSelector组件更新
                form.value.taskResourceItems = sealingTaskResourceItems as any

                console.log('成功回填封道线路信息和位置桩号:', sealingTaskResourceItems)
            } else {
                // 没有封道数据时，清空
                form.value.taskResourceItems = []
                console.log('未找到封道数据，清空taskResourceItems')
            }
        } else {
            // 没有taskResourceItems时，清空
            form.value.taskResourceItems = []
            console.log('taskResourceItems为空，清空封道数据')
        }
    } catch (error) {
        console.error('回填封道数据失败:', error)
        // 出错时保持默认状态
        form.value.taskResourceItems = []
    }
}

/** 根据ID获取数据 */
const getInfo = async (id: string) => {
    try {
        const res = await getTaskDefine(id)
        // 只复制需要的字段，而不是整个对象
        form.value = {
            ...initFormData,
            id: res.data.id,
            projectId: res.data.projectId,
            yearTaskId: res.data.yearTaskId,
            name: res.data.name,
            taskType: res.data.taskType,
            speciality: res.data.speciality,
            year: res.data.year,
            frequencyType: res.data.frequencyType,
            frequency: res.data.frequency,
            frequencyData: res.data.frequencyData,
            maintenanceContent: res.data.maintenanceContent,
            status: res.data.status,
            // 封道特有字段回填
            sealingType: res.data.sealingType || 'full', // 封道类型回填
            taskResourceItems: (res.data.taskResourceItems || []) as any // 任务资源项回填
        }

        // 处理封道数据回填
        handleSealingDataBackfill(res.data)

        console.log('封道计划编辑模式数据回填完成')

        // 设置频次相关数据
        if (frequencyRef.value && res.data.frequencyData) {
            try {
                // 设置加载状态，避免监听器触发重新计算
                frequencyRef.value.setLoadingState(true)

                const frequencyData = JSON.parse(res.data.frequencyData)

                // 先设置频次数据（tableData、scheduleData等）
                frequencyRef.value.tableData = frequencyData.tableData || []
                if (res.data.frequencyType === 'week') {
                    frequencyRef.value.weekScheduleData = frequencyData.scheduleData
                } else if (res.data.frequencyType === 'month') {
                    frequencyRef.value.monthScheduleData = frequencyData.scheduleData
                } else if (res.data.frequencyType === 'day') {
                    frequencyRef.value.dayScheduleData = frequencyData.scheduleData
                }

                // 然后设置unit和value，此时监听器会被跳过
                frequencyRef.value.unit = res.data.frequencyType
                frequencyRef.value.value = res.data.frequency

                // 数据加载完毕，恢复正常状态
                nextTick(() => {
                    frequencyRef.value.setLoadingState(false)
                })
            } catch (error) {
                console.error('解析频次数据失败:', error)
                // 如果出错，也要恢复状态
                if (frequencyRef.value) {
                    frequencyRef.value.setLoadingState(false)
                }
            }
        }
    } catch (error) {
        console.error('获取任务定义详情失败:', error)
        proxy?.$modal.msgError('获取详情失败')
    }
}

onMounted(async () => {
    try {
        form.value.year = getYearOfTaskYear()
        const queryId = route.query.yearTaskId
        const id = route.query.id
        yearTaskId.value = Array.isArray(queryId) ? queryId[0] : queryId || ''
        isTemp.value = Array.isArray(route.query.isTemp) ? route.query.isTemp[0] : route.query.isTemp || '0'
        if (yearTaskId.value) {
            form.value.yearTaskId = yearTaskId.value
        }

        // 如果有ID，则获取数据
        if (id) {
            await getInfo(id as string)
        }
    } catch (error) {
        console.error('初始化失败:', error)
        proxy?.$modal.msgError('加载数据失败')
    }
})
</script>

<style lang="scss" scoped>
.box-card {
    margin-top: 10px;
}
</style>
