<template>
    <div class="p-2 complaint-management-page">
        <!-- 初始化加载状态 -->
        <div v-if="initialLoading" class="loading-container">
            <el-icon class="is-loading loading-icon">
                <Loading />
            </el-icon>
            <div class="loading-text">正在初始化数据...</div>
        </div>

        <!-- 主要内容区域 -->
        <div v-else>
            <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
                <div v-show="showSearch" class="mb-[10px]">
                    <el-card shadow="hover">
                        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                            <el-form-item label="投诉时间" style="width: 308px">
                                <el-date-picker
                                    v-model="dateRangeComplaintTime"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                    type="daterange"
                                    range-separator="-"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                                />
                            </el-form-item>
                            <!-- <el-form-item label="所属项目" prop="projectId">
              <el-input v-model="queryParams.projectId" placeholder="请输入所属项目" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
                            <el-form-item label="反映来源" prop="complaintSource">
                                <el-select v-model="queryParams.complaintSource" placeholder="请选择反映来源" clearable>
                                    <el-option v-for="dict in complaint_source" :key="dict.value" :label="dict.label" :value="dict.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="投诉类型" prop="complaintType">
                                <el-select v-model="queryParams.complaintType" placeholder="请选择投诉类型" clearable>
                                    <el-option v-for="dict in complaint_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="状态" prop="status">
                                <el-select v-model="queryParams.status" placeholder="请选择投诉类型" clearable> </el-select>
                            </el-form-item>

                            <el-form-item class="filter-actions">
                                <el-button type="primary" @click="handleQuery">搜索</el-button>
                                <el-button @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>
                </div>
            </transition>

            <el-card shadow="never">
                <template #header>
                    <div class="btn-box">
                        <div class="filter">
                            <el-radio-group v-model="filterStatus" @change="handleFilterStatus">
                                <el-radio-button value="PENDING">待处理</el-radio-button>
                                <el-radio-button value="COMPLETED">已处理</el-radio-button>
                                <!-- <el-radio-button value="">全部</el-radio-button> -->
                            </el-radio-group>
                            <el-button type="success" plain icon="Edit" v-if="false" :disabled="single" @click="handleUpdate()">修改</el-button>

                            <!-- v-hasPermi="['operation:complaint:edit']" -->
                            <el-button type="danger" plain icon="Delete" v-if="false" :disabled="multiple" @click="handleDelete()">删除</el-button>
                            <!-- v-hasPermi="['operation:complaint:remove']" -->
                        </div>
                        <div class="export">
                            <!-- v-hasPermi="['operation:complaint:add']" -->
                            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['operation:complaint:add']">新增</el-button>
                            <!-- <el-button type="primary" plain @click="handleConfirm('批量取消')">批量取消</el-button>
                        <el-button type="primary" plain @click="handleConfirm('批量确认取消')">批量确认验收</el-button> -->
                            <!-- <el-button plain @click="() => handleAssign()">查看</el-button>
                            <el-button plain @click="() => handleAssign()">分配</el-button>
							<!-- v-hasPermi="['operation:complaint:export']" -->
                            <!-- <el-button type="warning" plain icon="Download" @click="handleExport"
                                >导出</el-button
                            > -->
                        </div>
                    </div>
                </template>

                <el-table v-loading="loading" stripe :data="complaintList" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column label="" align="center" prop="id" v-if="false" />
                    <el-table-column label="投诉时间" align="center" prop="complaintTime" width="180">
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.complaintTime, '{y}-{m}-{d}') }}</span>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column label="所属项目" align="center" prop="projectId" /> -->
                    <el-table-column label="发生位置" align="center" prop="adminUnit">
                        <template #default="scope">
                            <span>{{ getManageUnitName(scope.row.adminUnit) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="反映来源" align="center" prop="complaintSource">
                        <template #default="scope">
                            <dict-tag :options="complaint_source" :value="scope.row.complaintSource" />
                        </template>
                    </el-table-column>
                    <el-table-column label="投诉类型" align="center" prop="complaintType">
                        <template #default="scope">
                            <dict-tag :options="complaint_type" :value="scope.row.complaintType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="返回时长" align="center" prop="replyDaySpan" />

                    <!-- <el-table-column label="反映人" align="center" prop="reporter" />
        <el-table-column label="性别" align="center" prop="sex">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.sex"/>
          </template>
        </el-table-column>
        <el-table-column label="反映方式" align="center" prop="complaintWay">
          <template #default="scope">
            <dict-tag :options="complaint_way" :value="scope.row.complaintWay"/>
          </template>
        </el-table-column>
        <el-table-column label="联系方式" align="center" prop="contact" />
        <el-table-column label="投诉描述" align="center" prop="description" />
        <el-table-column label="上传附件" align="center" prop="files" /> -->
                    <el-table-column label="当前状态" align="center" prop="currentStatus">
                        <template #default="scope">
                            <dict-tag :options="complaint_status" :value="scope.row.currentStatus" />
                        </template>
                    </el-table-column>
                    <!-- <el-table-column label="审核时间" align="center" prop="auditTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column> -->
                    <!-- <el-table-column label="整改时间" align="center" prop="allocateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.allocateTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="回复时间" align="center" prop="replyTime" />
        <el-table-column label="初步处理意见" align="center" prop="allocateDescription" />
        <el-table-column label="回复描述" align="center" prop="replyDescription" />
        <el-table-column label="回复的附件" align="center" prop="replyFiles" />

        <el-table-column label="整改日期" align="center" prop="correctionTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.correctionTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="整改现场图片" align="center" prop="correctionImagesUrl" width="100">
          <template #default="scope">
            <image-preview :src="scope.row.correctionImagesUrl" :width="50" :height="50"/>
          </template>
        </el-table-column>
        <el-table-column label="上传附件" align="center" prop="correctionFiles" />-->
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="350">
                        <template #default="scope">
                            <div class="op-actions">
                                <el-button link type="primary" class="op-link op-info" @click="handleView(scope.row)">
                                    <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="查看" />
                                    查看
                                </el-button>

                                <!-- 当筛选状态为已处理时，隐藏修改、删除等操作按钮 -->
                                <template v-if="filterStatus !== 'COMPLETED'">
                                    <el-button
                                        link
                                        type="primary"
                                        class="op-link op-edit"
                                        @click="handleUpdate(scope.row)"
                                        v-hasPermi="['operation:complaint:edit']"
                                        v-if="scope.row.currentStatus == 'START'"
                                    >
                                        <img class="op-icon" src="@/assets/images/edit-icon.png" alt="修改" />
                                        修改
                                    </el-button>
                                    <el-button
                                        link
                                        type="danger"
                                        class="op-link op-delete"
                                        @click="handleDelete(scope.row)"
                                        v-if="scope.row.currentStatus == 'START'"
                                        v-hasPermi="['operation:complaint:delete']"
                                    >
                                        <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />
                                        删除
                                    </el-button>
                                    <el-button
                                        link
                                        type="primary"
                                        class="op-link op-edit"
                                        @click="handleAssign(scope.row)"
                                        v-if="scope.row.currentStatus != 'END' && isCurrentUserAssignee(scope.row.assignee)"
                                    >
                                        <img class="op-icon" src="@/assets/images/edit-icon.png" alt="处理" />
                                        {{ getButtonLabel(scope.row.currentStatus) }}
                                    </el-button>
                                </template>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    @pagination="getList"
                />
            </el-card>
        </div>
    </div>
</template>

<script setup name="Complaint" lang="ts">
import { delComplaint, listComplaintTaskView } from '@/api/subProject/operation/complaint'
import { ComplaintVO, ComplaintTaskViewVO, ComplaintTaskViewQuery } from '@/api/subProject/operation/complaint/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import { Loading } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { ref, reactive, toRefs, onMounted, onBeforeUnmount, watch, getCurrentInstance, ComponentInternalInstance, nextTick } from 'vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { complaint_way, sys_user_sex, complaint_type, complaint_source, complaint_status } = toRefs<any>(
    proxy?.useDict('complaint_way', 'sys_user_sex', 'complaint_type', 'complaint_source', 'complaint_status')
)
const appStore = useAppStore()
const userStore = useUserStore()
const router = useRouter()

const complaintList = ref<ComplaintTaskViewVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRangeComplaintTime = ref<[DateModelType, DateModelType]>(['', ''])
const queryFormRef = ref<ElFormInstance>()
const filterStatus = ref('PENDING')

// 状态缓存相关
const CACHE_KEY = 'complaint_list_search_state'

// 管理单元列表
const manageUnits = ref<ManageUnitVO[]>([])
// 初始化加载状态
const initialLoading = ref(true)

const queryParams = ref<ComplaintTaskViewQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    complaintType: undefined,
    complaintSource: undefined,
    status: undefined,
    params: {
        complaintTime: undefined
    }
})
/** 检查当前用户是否为指定的处理人 */
const isCurrentUserAssignee = (assignee: string | undefined | null) => {
    if (!assignee || !userStore.userId) {
        return false
    }

    // 将assignee按逗号分割，去除空格，检查是否包含当前用户ID
    const assigneeList = assignee
        .split(',')
        .map((id) => id.trim())
        .filter((id) => id)
    const currentUserId = userStore.userId.toString()

    const isAssignee = assigneeList.includes(currentUserId)

    // console.log('检查用户权限:', {
    //     currentUserId,
    //     assignee,
    //     assigneeList,
    //     isAssignee
    // })

    return isAssignee
}

const getButtonLabel = (statusValue: string) => {
    // 从complaint_status数据字典中查找对应的标签
    const dictItem = complaint_status.value?.find((item: any) => item.value === statusValue)

    // 如果找到字典项，返回其标签；否则返回原始值
    return dictItem?.label || statusValue
}
// 获取当前项目所有管理单元
const getAllManageUnits = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取管理单元')
            return
        }

        console.log('开始获取项目管理单元，projectId:', projectId)
        const response = await listProjectManageUnit(projectId)

        if (response && response.data) {
            manageUnits.value = Array.isArray(response.data) ? response.data : [response.data]
            console.log('获取管理单元成功:', manageUnits.value)
        } else if (response && Array.isArray(response)) {
            manageUnits.value = response
            console.log('获取管理单元成功:', manageUnits.value)
        } else {
            console.warn('获取管理单元返回数据格式异常:', response)
            manageUnits.value = []
        }
    } catch (error) {
        console.error('获取管理单元失败:', error)
        proxy?.$modal.msgWarning('获取管理单元失败')
        manageUnits.value = []
    }
}

// 根据管理单元ID获取名称
const getManageUnitName = (unitId: string | undefined) => {
    if (!unitId || !manageUnits.value.length) {
        return unitId || '--'
    }
    const unit = manageUnits.value.find((unit) => unit.id === unitId)
    return unit?.name || unitId
}

/** 查询投诉管理列表 */
const getList = async () => {
    loading.value = true
    try {
        queryParams.value.params = {}
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
        queryParams.value.todoStatus = filterStatus.value
        proxy?.addDateRange(queryParams.value, dateRangeComplaintTime.value, 'ComplaintTime')

        console.log('调用listComplaintTaskView，查询参数:', queryParams.value)
        const res = await listComplaintTaskView(queryParams.value)
        console.log('listComplaintTaskView返回结果:', res)

        // 确保返回的数据是数组，并过滤掉无效数据
        if (res && res.rows && Array.isArray(res.rows)) {
            complaintList.value = res.rows.filter((item) => item && item.id)
            total.value = res.total || 0
            console.log('处理后的投诉列表:', complaintList.value)
            console.log('总数:', total.value)
        } else {
            console.warn('接口返回数据格式异常:', res)
            complaintList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('获取投诉列表失败:', error)
        complaintList.value = []
        total.value = 0
        proxy?.$modal.msgError('获取投诉列表失败')
    } finally {
        loading.value = false
    }
}

// 缓存搜索状态
const saveSearchState = () => {
    try {
        const state = {
            queryParams: {
                complaintType: queryParams.value.complaintType,
                complaintSource: queryParams.value.complaintSource,
                status: queryParams.value.status
            },
            dateRangeComplaintTime: dateRangeComplaintTime.value,
            showSearch: showSearch.value,
            filterStatus: filterStatus.value
        }
        sessionStorage.setItem(CACHE_KEY, JSON.stringify(state))
        console.log('保存投诉管理搜索状态:', state)
    } catch (error) {
        console.error('保存投诉管理搜索状态失败:', error)
    }
}

// 恢复搜索状态
const restoreSearchState = () => {
    try {
        const cached = sessionStorage.getItem(CACHE_KEY)
        if (cached) {
            const state = JSON.parse(cached)

            // 恢复查询参数
            if (state.queryParams) {
                queryParams.value.complaintType = state.queryParams.complaintType
                queryParams.value.complaintSource = state.queryParams.complaintSource
                queryParams.value.status = state.queryParams.status
            }

            // 恢复日期范围
            if (state.dateRangeComplaintTime) {
                dateRangeComplaintTime.value = state.dateRangeComplaintTime
            }

            // 恢复搜索框显示状态
            if (state.showSearch !== undefined) {
                showSearch.value = state.showSearch
            }

            // 恢复筛选状态
            if (state.filterStatus) {
                filterStatus.value = state.filterStatus
            }

            console.log('恢复投诉管理搜索状态:', state)
        }
    } catch (error) {
        console.error('恢复投诉管理搜索状态失败:', error)
    }
}

// 清除缓存状态
const clearSearchState = () => {
    sessionStorage.removeItem(CACHE_KEY)
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    dateRangeComplaintTime.value = ['', '']
    queryFormRef.value?.resetFields()

    // 清除缓存状态
    clearSearchState()

    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ComplaintTaskViewVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    router.push('complaintForm')
}
/** 处理筛选状态按钮点击 */
const handleFilterStatus = (status: string) => {
    filterStatus.value = status

    // 根据前端筛选状态设置对应的todo_status查询条件
    if (status === 'PENDING') {
        // 待处理：对应 todo_status = 'ACTIVE' OR todo_status IS NULL
        queryParams.value.todoStatus = 'PENDING'
    } else if (status === 'COMPLETED') {
        // 已处理：对应 todo_status = 'COMPLETED'
        queryParams.value.todoStatus = 'COMPLETED'
    } else {
        // 全部：不传 todo_status 值
        queryParams.value.todoStatus = undefined
    }

    // 筛选时重置页码
    queryParams.value.pageNum = 1
    getList()
}

/** 导航到查看、指派页面 */
const handleAssign = (row?: ComplaintTaskViewVO) => {
    // reset();
    // dialog.visible = true;
    // dialog.title = '添加年度计划目录';
    router.push('complaintAssign?id=' + row?.id)
}

/** 审批按钮操作 */
const handleConfirm = (operationName: string) => {
    // 这个函数暂时保留，可能在后续版本中使用
    console.log('操作名称:', operationName)
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ComplaintVO) => {
    const _id = row?.id || ids.value[0]
    router.push('complaintForm?id=' + _id)
}

/** 删除按钮操作 */
const handleDelete = async (row?: ComplaintTaskViewVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除投诉管理编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delComplaint(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

const handleView = (row?: ComplaintTaskViewVO) => {
    router.push('complaintDetail?id=' + row?.id)
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'operation/complaint/export',
        {
            ...queryParams.value
        },
        `complaint_${new Date().getTime()}.xlsx`
    )
}

onMounted(async () => {
    try {
        initialLoading.value = true
        // 先获取管理单元数据，再获取列表数据
        await getAllManageUnits()

        // 恢复搜索状态
        restoreSearchState()

        await getList()
    } catch (error) {
        console.error('初始化数据失败:', error)
        proxy?.$modal.msgError('初始化数据失败')
    } finally {
        initialLoading.value = false
    }
})

// 组件卸载前保存状态
onBeforeUnmount(() => {
    saveSearchState()
})

// 监听搜索条件变化，实时保存状态
watch(
    [
        () => queryParams.value.complaintType,
        () => queryParams.value.complaintSource,
        () => queryParams.value.status,
        dateRangeComplaintTime,
        showSearch,
        filterStatus
    ],
    () => {
        // 延迟保存，避免频繁操作
        nextTick(() => {
            saveSearchState()
        })
    },
    { deep: true }
)
</script>
<style lang="scss" scoped>
.complaint-management-page {

    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-tree-select__wrapper),
    :deep(.el-date-editor .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder),
    :deep(.el-tree-select__placeholder) {
        color: #8291A9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)),
    :deep(.el-tree-select .el-tree-select__selected-item > span:not(.el-tree-select__placeholder)) {
        color: #FFFFFF !important;
    }

    :deep(.el-select__wrapper.is-focused),
    :deep(.el-tree-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 日期选择器特殊样式 */
    :deep(.el-date-editor) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-date-editor .el-input__inner) {
        color: #FFFFFF !important;
        background: transparent !important;
    }

    :deep(.el-range-separator) {
        color: #8291A9 !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #AED7F2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #FFFFFF !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 筛选按钮组样式 */
    .btn-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter {
            flex: 1;
        }

        .export {
            margin-left: auto;
        }
    }

    :deep(.el-radio-group .el-radio-button__inner) {
        background: #232d45 !important;
        border: 1px solid #4286F3 !important;
        color: #8291A9 !important;
        border-radius: 6px !important;
        margin-right: 10px;
        height: 36px;
        line-height: 34px;
        padding: 0 16px;
    }

    :deep(.el-radio-group .el-radio-button.is-active .el-radio-button__inner) {
        background: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    /* 头部操作按钮样式 */
    :deep(.btn-box .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 36px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.btn-box .el-button.el-button--primary) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.btn-box .el-button.el-button--success) {
        background-color: #67C23A !important;
        border-color: #67C23A !important;
        color: #FFFFFF !important;
    }

    :deep(.btn-box .el-button.el-button--warning) {
        background-color: #E6A23C !important;
        border-color: #E6A23C !important;
        color: #FFFFFF !important;
    }

    :deep(.btn-box .el-button.el-button--danger) {
        background-color: #F56C6C !important;
        border-color: #F56C6C !important;
        color: #FFFFFF !important;
    }

    /* 操作列图标按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
        flex-wrap: wrap;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
        margin: 2px 0;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286F3 !important;
    }

    .op-edit {
        color: #42F3E9 !important;
    }

    .op-delete {
        color: #D62121 !important;
    }

    /* 加载状态样式 */
    .loading-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 400px;
        border-radius: 8px;

        .loading-icon {
            font-size: 32px;
            color: #4286F3;
            margin-bottom: 16px;
        }

        .loading-text {
            color: #8291A9;
            font-size: 14px;
        }
    }
}
</style>
