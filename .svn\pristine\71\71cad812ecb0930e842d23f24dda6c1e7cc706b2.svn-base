export interface WaterDetecterVO {
    /**
     * 站点编号
     */
    id: string | number

    /**
     * 站点名称
     */
    name: string

    /**
     * 纬度
     */
    lat: number

    /**
     * 经度
     */
    lng: number

    /**
     * 警戒水位
     */
    levLmt: number

    /**
     * 上次取数据时间
     */
    lastGetTime: string

    remark: string
    /**
     *
     */
    projectId: string | number
}

export interface WaterDetecterForm extends BaseEntity {
    /**
     * 站点编号
     */
    id?: string | number

    /**
     * 站点名称
     */
    name?: string

    /**
     * 纬度
     */
    lat?: number

    /**
     * 经度
     */
    lng?: number

    /**
     * 警戒水位
     */
    levLmt?: number

    /**
     * 上次取数据时间
     */
    lastGetTime?: string
    remark?: string
    /**
     *
     */
    projectId?: string | number
}

export interface WaterDetecterQuery extends PageQuery {
    /**
     * 站点名称
     */
    name?: string

    /**
     * 纬度
     */
    lat?: number

    /**
     * 经度
     */
    lng?: number

    /**
     * 警戒水位
     */
    levLmt?: number

    /**
     * 上次取数据时间
     */
    lastGetTime?: string

    /**
     *
     */
    projectId?: string | number

    /**
     * 日期范围参数
     */
    params?: any
}

/**
 * 积水监测图表数据
 */
export interface WaterMonitorDataVO {
    /**
     * 点位ID
     */
    id: string

    /**
     * 点位名称
     */
    name: string

    /**
     * X坐标 (0-100的相对位置)
     */
    x: number

    /**
     * Y坐标 (0-100的相对位置)
     */
    y: number

    /**
     * 积水深度值
     */
    value: number

    /**
     * 预警级别: normal/warning/danger
     */
    level: 'normal' | 'warning' | 'danger'

    /**
     * 实际纬度
     */
    lat: number

    /**
     * 实际经度
     */
    lng: number

    /**
     * 最后更新时间
     */
    lastUpdateTime: string

    /**
     * 警戒水位
     */
    warningLimit: number
}
