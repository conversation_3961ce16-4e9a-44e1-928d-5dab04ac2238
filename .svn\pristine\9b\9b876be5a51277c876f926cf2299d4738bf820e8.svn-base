<!--
养护作业表格组件 (CuringTaskTable)

功能：
- 显示养护作业相关信息
- 自动处理数据字典显示
- 内置分页功能
- 支持详情查看
- 内置查询条件筛选（作业单类型：维修作业单、养护作业单）
- 支持按设备ID查询养护作业档案
- 使用档案查询API (QueryDeviceCuringTaskDossierQuery)

使用示例：

1. 基本使用（不包含设备ID查询）：
<CuringTaskTable
    :table-data="taskData"
    :loading="loading"
    :total="total"
    :query-params="queryParams"
    :show-filter="true"
    @view-details="handleViewDetails"
    @pagination="handlePagination"
    @query="handleQuery"
    @reset="handleReset"
/>

2. 设备养护作业档案查询使用：
<CuringTaskTable
    v-model:table-data="taskData"
    v-model:total="total"
    :loading="loading"
    :query-params="queryParams"
    :show-filter="true"
    :device-id="deviceId"
    :project-id="projectId"
    @view-details="handleViewDetails"
    @pagination="handlePagination"
    @query="handleQuery"
    @reset="handleReset"
/>

Props:
- tableData: 表格数据数组
- loading: 加载状态
- total: 总记录数
- queryParams: 分页参数 { pageNum, pageSize }
- showFilter: 是否显示查询条件 (默认true)
- deviceId: 设备ID (可选，用于查询该设备的养护作业)
- projectId: 项目ID (可选，用于设备养护作业查询)
- dateRange: 日期范围 { startDate, endDate } (可选，用于时间范围查询)

Events:
- view-details: 查看详情事件
- pagination: 分页事件
- query: 查询事件
- reset: 重置事件
- update:tableData: 表格数据更新事件（当使用设备ID查询时）
- update:total: 总数更新事件（当使用设备ID查询时）
- start-task: 开始作业事件
- complete-task: 完成作业事件

特性说明：
1. 当提供deviceId和projectId时，组件会自动调用档案查询API
2. 支持按作业单类型条件过滤（维修作业单、养护作业单）
3. 当设备ID变化时，组件会自动重新查询档案
4. 支持时间范围查询，当dateRange变化时自动重新查询档案
5. 支持作业进度显示和状态管理
6. 提供开始作业和完成作业的操作按钮
7. 使用QueryDeviceCuringTaskDossierQuery进行档案查询
-->

<template>
    <el-card shadow="never" class="table-card">
        <!-- 查询条件区域 -->
        <div class="filter-section">
            <div class="filter-row">
                <div class="filter-item">
                    <el-select v-model="filterParams.taskType" placeholder="请选择作业单类型" clearable>
                        <el-option v-for="item in taskTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </div>

                <!-- 查询按钮 -->
                <div class="filter-actions">
                    <el-button type="primary" @click="handleQuery">查询</el-button>
                    <el-button @click="handleReset">重置</el-button>
                </div>
            </div>
        </div>

        <el-table :data="tableData" stripe style="width: 100%" v-loading="loading">
            <!-- 计划作业日期 -->
            <el-table-column prop="taskDate" label="计划作业日期" width="120">
                <template #default="scope">
                    {{ formatDate(scope.row.taskDate) }}
                </template>
            </el-table-column>

            <!-- 作业单名称 -->
            <el-table-column prop="name" label="作业单名称" min-width="200" />

            <!-- 作业单类型 -->
            <el-table-column prop="taskType" label="作业单类型" width="120">
                <template #default="scope">
                    <dict-tag :options="taskTypeOptions" :value="scope.row.taskType" />
                </template>
            </el-table-column>

            <!-- 实际开工日期 -->
            <el-table-column prop="bgnDate" label="实际开工日期" width="120">
                <template #default="scope">
                    {{ formatDate(scope.row.bgnDate) }}
                </template>
            </el-table-column>

            <!-- 实际完工日期 -->
            <el-table-column prop="endDate" label="实际完工日期" width="120">
                <template #default="scope">
                    {{ formatDate(scope.row.endDate) }}
                </template>
            </el-table-column>

            <el-table-column label="操作" width="120" align="center">
                <template #default="scope">
                    <el-button link type="primary" @click="handleViewDetails(scope.row)"> 详情 </el-button>
                    <el-button v-if="scope.row.taskStatus === 'pending'" link type="success" @click="handleStartTask(scope.row)">
                        开始作业
                    </el-button>
                    <el-button v-if="scope.row.taskStatus === 'in_progress'" link type="warning" @click="handleCompleteTask(scope.row)">
                        完成作业
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <pagination
            v-if="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="handlePagination"
        />
    </el-card>
</template>

<script setup lang="ts">
import { ref, computed, getCurrentInstance, toRefs, watch, onMounted } from 'vue'
import type { ComponentInternalInstance } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { queryDeviceRelatedTaskList } from '@/api/statistics/dossier'
import { TaskVO } from '@/api/plan/task/types'
import type { QueryDeviceCuringTaskDossierQuery } from '@/api/statistics/dossier/types'

// 定义组件属性
interface Props {
    deviceId?: string
    dateRange?: {
        startDate: string
        endDate: string
    }
}

// 定义组件事件
interface Emits {
    (e: 'view-details', row: TaskVO): void
    (e: 'pagination', params: { pageNum: number; pageSize: number }): void
    (e: 'query', filterParams: FilterParams): void
    (e: 'reset'): void
    (e: 'update:tableData', data: TaskVO[]): void
    (e: 'update:total', total: number): void
    (e: 'start-task', row: TaskVO): void
    (e: 'complete-task', row: TaskVO): void
}

// 查询条件参数接口
interface FilterParams {
    taskType: string
    deviceId?: string
    projectId?: string
}

const props = withDefaults(defineProps<Props>(), {
    deviceId: '',
    dateRange: () => ({
        startDate: '',
        endDate: ''
    })
})

const appStore = useAppStore()

const emit = defineEmits<Emits>()

const { proxy } = getCurrentInstance() as ComponentInternalInstance

// 内部数据状态
const tableData = ref<TaskVO[]>([])
const loading = ref(false)
const total = ref(0)
const queryParams = ref({
    pageNum: 1,
    pageSize: 10
})

// 查询条件
const filterParams = ref<FilterParams>({
    taskType: '',
    deviceId: props.deviceId,
    projectId: appStore.projectContext.selectedProjectId
})

// 监听设备ID变化
watch(
    () => props.deviceId,
    (newDeviceId) => {
        filterParams.value.deviceId = newDeviceId
        if (newDeviceId && appStore.projectContext.selectedProjectId) {
            // 当设备ID变化时，自动查询该设备的养护作业
            handleQuery()
        }
    }
)

// 监听项目ID变化
watch(
    () => appStore.projectContext.selectedProjectId,
    (newProjectId) => {
        filterParams.value.projectId = newProjectId
        if (newProjectId && props.deviceId) {
            handleQuery()
        }
    }
)

// 监听时间范围变化
watch(
    () => props.dateRange,
    (newDateRange) => {
        if (newDateRange && props.deviceId && appStore.projectContext.selectedProjectId) {
            handleQuery()
        }
    },
    { deep: true }
)

// 组件挂载时执行初始查询
onMounted(() => {
    // 检查是否有必要的参数，如果有则自动执行查询
    if (props.deviceId && appStore.projectContext.selectedProjectId) {
        handleQuery()
    }
})

// 获取数据字典
const { task_type } = toRefs<any>(proxy?.useDict('task_type'))

// 数据字典选项
const taskTypeOptions = computed(() => {
    // 如果数据字典中有值，使用数据字典的值
    if (task_type.value && task_type.value.length > 0) {
        return task_type.value
    }

    // 如果数据字典为空，使用默认的选项
    return [
        { label: '维修作业单', value: 'repair' },
        { label: '养护作业单', value: 'curing' }
    ]
})

// 格式化日期
const formatDate = (date: string) => {
    if (!date) return '-'
    return date
}

// 查看详情
const handleViewDetails = (row: TaskVO) => {
    // emit('view-details', row)
    proxy?.$router.push('/subProject/circle/maintain/maintainTaskDetail?taskId=' + row?.id)
}

// 开始作业
const handleStartTask = (row: TaskVO) => {
    emit('start-task', row)
}

// 完成作业
const handleCompleteTask = (row: TaskVO) => {
    emit('complete-task', row)
}

// 分页处理
const handlePagination = (params: { pageNum: number; pageSize: number }) => {
    queryParams.value.pageNum = params.pageNum
    queryParams.value.pageSize = params.pageSize
    emit('pagination', params)
    // 如果有设备ID和项目ID，分页时也要重新查询
    if (props.deviceId && appStore.projectContext.selectedProjectId) {
        handleQuery()
    }
}

// 查询处理
const handleQuery = async () => {
    if (!props.deviceId || !appStore.projectContext.selectedProjectId) {
        return
    }

    try {
        loading.value = true

        // 构建查询参数 - 使用设备相关任务查询接口，指定维养任务类型
        const queryBo: QueryDeviceCuringTaskDossierQuery = {
            deviceId: props.deviceId,
            projectId: appStore.projectContext.selectedProjectId,
            taskType: 'curing', // 明确指定为维养任务
            pageNum: queryParams.value.pageNum,
            pageSize: queryParams.value.pageSize
        }

        // 根据选择的作业单类型设置taskTypeSub
        if (filterParams.value.taskType) {
            queryBo.taskTypeSub = filterParams.value.taskType
        }

        // 处理日期范围 - 按bgnDate和endDate查询（任务时间范围交集）
        if (props.dateRange && props.dateRange.startDate && props.dateRange.endDate) {
            queryBo.params = {
                beginBgnDate: props.dateRange.startDate,
                endBgnDate: props.dateRange.endDate
            }
        }

        // 调用设备相关任务查询API（通过taskType=curing指定维养任务）
        const response = await queryDeviceRelatedTaskList(queryBo)

        // 处理API响应数据
        if (response && response.rows) {
            tableData.value = response.rows
            total.value = response.total || 0
        } else if (response && Array.isArray(response)) {
            tableData.value = response
            total.value = response.length
        } else {
            tableData.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('查询设备养护作业档案失败:', error)
    } finally {
        loading.value = false
    }
}

// 重置处理
const handleReset = () => {
    filterParams.value = {
        taskType: '',
        deviceId: props.deviceId,
        projectId: appStore.projectContext.selectedProjectId
    }
    emit('reset')
    // 重置后重新查询
    if (props.deviceId && appStore.projectContext.selectedProjectId) {
        handleQuery()
    }
}
</script>

<style lang="scss" scoped>
.table-card {
    margin-top: 16px;
}

.filter-section {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 4px;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.filter-item {
    min-width: 200px;
}

.filter-actions {
    display: flex;
    gap: 8px;
    margin-left: auto;
}

@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-item {
        min-width: auto;
    }

    .filter-actions {
        margin-left: 0;
        margin-top: 16px;
    }
}
</style>
