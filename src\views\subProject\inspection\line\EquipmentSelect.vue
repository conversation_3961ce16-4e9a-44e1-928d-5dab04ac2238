<template>
    <el-dialog
        :draggable="true"
        :title="dialogTitle"
        v-model="dialogVisible"
        width="1200px"
        append-to-body
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <div class="equipment-dialog-content">
            <!-- 查询条件区域 -->
            <div class="search-area">
                <div class="search-filters">
                    <el-select
                        v-model="equipmentQuery.unitId"
                        placeholder="请选择管理单元"
                        clearable
                        @change="handleEquipmentQuery"
                        style="width: 200px; margin-right: 10px"
                    >
                        <el-option v-for="unit in manageUnits" :key="unit.id" :label="unit.name" :value="unit.id" />
                    </el-select>
                    <el-select
                        v-model="equipmentQuery.roomId"
                        placeholder="请选择房间"
                        clearable
                        @change="handleEquipmentQuery"
                        style="width: 200px; margin-right: 10px"
                    >
                        <el-option v-for="room in roomOptions" :key="room.id" :label="room.roomNane" :value="room.id" />
                    </el-select>
                    <el-input
                        v-model="equipmentQuery.name"
                        :placeholder="props.specialtyType === 'electric' ? '设备名称' : '设施名称'"
                        clearable
                        @keyup.enter="handleEquipmentQuery"
                        style="width: 200px; margin-right: 10px"
                    />
                    <el-button type="primary" @click="handleEquipmentQuery">查询</el-button>
                    <el-button @click="resetEquipmentQuery">重置</el-button>
                </div>

                <div class="equipment-tabs">
                    <el-button :type="equipmentTab === 'current' ? 'primary' : 'default'" @click="handleCurrentPageSelect"> 单页全选 </el-button>
                </div>
            </div>

            <!-- 表格区域容器 -->
            <div class="tables-container">
                <!-- 左侧：设备选择区域 -->
                <div class="equipment-select-area">
                    <el-table
                        v-loading="equipmentLoading"
                        :data="availableEquipments"
                        @selection-change="handleEquipmentSelectionChange"
                        style="margin-top: 10px"
                    >
                        <el-table-column type="selection" width="55" align="center" />
                        <el-table-column label="序号" type="index" width="60" align="center" v-if="false" />
                        <el-table-column label="名称" prop="remark" show-overflow-tooltip />
                        <!-- <el-table-column label="备注" prop="remark" show-overflow-tooltip /> -->
                        <el-table-column label="房间编码" prop="roomCode" width="100" />
                        <el-table-column label="管理单元" prop="unitName" width="120" show-overflow-tooltip />
                        <el-table-column label="操作" align="center" width="80">
                            <template #default="scope">
                                <el-button v-if="!isEquipmentSelected(scope.row)" type="text" size="small" @click="addEquipmentToSelected(scope.row)">
                                    选择
                                </el-button>
                                <span v-else class="selected-text">已选择</span>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 添加分页组件 -->
                    <div class="pagination-container">
                        <el-pagination
                            v-model:current-page="pagination.pageNum"
                            v-model:page-size="pagination.pageSize"
                            :total="pagination.total"
                            :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handlePageChange"
                        />
                    </div>
                </div>

                <!-- 右侧：已选设备区域 -->
                <div class="selected-equipment-area">
                    <div class="selected-header">
                        <span>{{ props.specialtyType === 'electric' ? '已选设备' : '已选设施' }}</span>
                        <el-tag type="info" size="small" style="margin-left: 8px"> 共 {{ selectedEquipmentList.length }} 个 </el-tag>
                        <el-button @click="goBackToList" type="text" class="back-btn">
                            <el-icon><ArrowLeft /></el-icon>
                        </el-button>
                    </div>

                    <el-table :data="selectedEquipmentList" style="flex: 1; overflow: hidden">
                        <el-table-column label="序号" type="index" width="60" align="center" />
                        <el-table-column label="名称" prop="name" show-overflow-tooltip />
                        <!-- <el-table-column label="备注" prop="remark" show-overflow-tooltip /> -->
                        <el-table-column label="房间编码" prop="roomCode" width="100" />
                        <el-table-column label="管理单元" prop="unitName" width="120" show-overflow-tooltip />
                        <el-table-column label="操作" align="center" width="80">
                            <template #default="scope">
                                <el-button type="text" size="small" @click="removeEquipmentFromSelected(scope.row)" style="color: #f56c6c">
                                    移除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import type { ManageUnitVO } from '@/api/project/manageUnit/types'
import { listRoom } from '@/api/project/room'
import type { RoomVO } from '@/api/project/room/types'
import { listDevice } from '@/api/subProject/basic/device'
import type { DeviceVO } from '@/api/subProject/basic/device/types'
import { useAppStore } from '@/store/modules/app'
import { saveLineDevices } from '@/api/subProject/inspection/inspectionLine'

// 定义组件属性
interface Props {
    visible: boolean
    lineData?: any
    selectedEquipments?: any[]
    specialtyType?: string // 专业类型
}

// 定义事件
interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'confirm', equipments: any[]): void
    (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    selectedEquipments: () => []
})

const emit = defineEmits<Emits>()

// 获取应用状态
const appStore = useAppStore()

// 响应式数据
const equipmentLoading = ref(false)
const equipmentTab = ref('current')

// 添加分页相关的响应式数据
const pagination = reactive({
    total: 0,
    pageSize: 10,
    pageNum: 1
})

// 设备选择相关数据
const manageUnits = ref<ManageUnitVO[]>([])
const roomOptions = ref<RoomVO[]>([])
const availableEquipments = ref<DeviceVO[]>([])
const selectedEquipmentList = ref<DeviceVO[]>([])
const equipmentQuery = reactive({
    unitId: undefined,
    roomId: undefined,
    name: undefined
})

// 计算属性：对话框显示状态
const dialogVisible = computed({
    get: () => props.visible,
    set: (value: boolean) => emit('update:visible', value)
})

// 计算属性：对话框标题
const dialogTitle = computed(() => {
    if (props.specialtyType === 'electric') {
        return '选择设备'
    } else if (props.specialtyType) {
        return '选择设施'
    } else {
        return '选择设备/设施'
    }
})

// 监听选中设备变化
watch(
    () => props.selectedEquipments,
    (newVal) => {
        selectedEquipmentList.value = [...(newVal || [])]
    },
    { immediate: true, deep: true }
)

// 监听对话框显示状态
watch(
    () => props.visible,
    (newVal) => {
        if (newVal) {
            loadAvailableEquipments()
            // loadSelectedEquipments(); // 已经通过watch监听props.selectedEquipments处理了
        }
    }
)

// 监听管理单元变化，加载对应房间
watch(
    () => equipmentQuery.unitId,
    (newUnitId) => {
        if (newUnitId) {
            loadRoomsByUnit(newUnitId)
        } else {
            roomOptions.value = []
        }
        // 清空房间选择
        equipmentQuery.roomId = undefined
    }
)

// 方法定义
const handleEquipmentQuery = async () => {
    equipmentLoading.value = true
    try {
        await loadEquipmentData()
        console.log('查询设备列表')
    } catch (error) {
        console.error('查询设备失败:', error)
    } finally {
        equipmentLoading.value = false
    }
}

const resetEquipmentQuery = () => {
    equipmentQuery.unitId = undefined
    equipmentQuery.roomId = undefined
    equipmentQuery.name = undefined
    handleEquipmentQuery()
}

const handleEquipmentSelectionChange = (selection: any[]) => {
    console.log('设备选择变化:', selection)
}

const addEquipmentToSelected = (equipment: any) => {
    // 检查是否已存在（使用字符串比较确保类型一致）
    const exists = selectedEquipmentList.value.find((item) => String(item.id) === String(equipment.id))
    if (!exists) {
        selectedEquipmentList.value.push(equipment)
        console.log('添加设备到已选列表:', equipment)
    }
}

const removeEquipmentFromSelected = (equipment: any) => {
    const index = selectedEquipmentList.value.findIndex((item) => String(item.id) === String(equipment.id))
    if (index > -1) {
        selectedEquipmentList.value.splice(index, 1)
        console.log('从已选列表移除设备:', equipment)
    }
}

const goBackToList = () => {
    console.log('返回列表')
}

const loadAvailableEquipments = async () => {
    equipmentLoading.value = true
    try {
        // 加载当前项目的管理单元
        await loadManageUnits()

        // 加载真实的设备数据
        await loadEquipmentData()

        // 房间选项会在选择管理单元时动态加载
        roomOptions.value = []
    } catch (error) {
        console.error('加载设备失败:', error)
    } finally {
        equipmentLoading.value = false
    }
}

// 加载管理单元数据
const loadManageUnits = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取管理单元')
            return
        }

        console.log('开始获取项目管理单元，projectId:', projectId)
        const response = await listProjectManageUnit(projectId)

        if (response && response.data) {
            manageUnits.value = Array.isArray(response.data) ? response.data : [response.data]
            console.log('获取管理单元成功:', manageUnits.value)
        } else if (response && Array.isArray(response)) {
            manageUnits.value = response
            console.log('获取管理单元成功:', manageUnits.value)
        } else {
            console.warn('获取管理单元返回数据格式异常:', response)
            manageUnits.value = []
        }
    } catch (error) {
        console.error('获取管理单元失败:', error)
        ElMessage.warning('获取管理单元失败')
        manageUnits.value = []
    }
}

// 根据管理单元加载房间数据
const loadRoomsByUnit = async (unitId: string | number) => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取房间')
            return
        }

        console.log('开始获取管理单元房间，unitId:', unitId, 'projectId:', projectId)
        const queryParams = {
            projectId: projectId,
            unitId: unitId,
            pageNum: 1,
            pageSize: 1000 // 获取所有房间
        }

        const response = await listRoom(queryParams)
        console.log('API返回的房间数据:', response)

        // 兼容不同的API返回结构
        if (response && response.rows) {
            roomOptions.value = response.rows
            console.log('获取房间成功:', roomOptions.value)
        } else if (response && response.data) {
            roomOptions.value = Array.isArray(response.data) ? response.data : [response.data]
            console.log('获取房间成功:', roomOptions.value)
        } else if (response && Array.isArray(response)) {
            roomOptions.value = response
            console.log('获取房间成功:', roomOptions.value)
        } else {
            console.warn('获取房间返回数据格式异常:', response)
            roomOptions.value = []
        }

        if (roomOptions.value.length === 0) {
            console.warn('该管理单元下没有房间数据')
        } else {
            console.log(`成功获取 ${roomOptions.value.length} 个房间`)
        }
    } catch (error) {
        console.error('获取房间失败:', error)
        ElMessage.warning('获取房间失败')
        roomOptions.value = []
    }
}

// 加载设备数据
const loadEquipmentData = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取设备')
            return
        }

        console.log('开始获取设备数据，查询参数:', {
            projectId,
            unitId: equipmentQuery.unitId,
            roomId: equipmentQuery.roomId,
            name: equipmentQuery.name,
            pageNum: pagination.pageNum,
            pageSize: pagination.pageSize
        })

        // 根据专业类型决定查询设备还是设施
        let deviceKind = ''
        if (props.specialtyType === 'electric') {
            deviceKind = 'equipment' // 机电系统查询设备
        } else {
            deviceKind = 'facility' // 其他专业类型查询设施
        }

        const queryParams = {
            projectId: projectId,
            unitId: equipmentQuery.unitId,
            roomId: equipmentQuery.roomId,
            name: equipmentQuery.name,
            specialty: props.specialtyType,
            pageNum: pagination.pageNum,
            pageSize: pagination.pageSize,
            kind: deviceKind
        }

        console.log('根据专业类型查询设备:', {
            specialtyType: props.specialtyType,
            deviceKind: deviceKind,
            isElectric: props.specialtyType === 'electric',
            queryParams
        })

        const response = await listDevice(queryParams)
        console.log('API返回的设备数据:', response)

        if (response && response.rows) {
            availableEquipments.value = response.rows
            pagination.total = response.total || 0
            console.log('获取设备成功:', availableEquipments.value)
        } else {
            console.warn('获取设备返回数据格式异常:', response)
            availableEquipments.value = []
            pagination.total = 0
        }

        if (availableEquipments.value.length === 0) {
            console.warn('没有找到符合条件的设备')
        } else {
            console.log(`成功获取 ${availableEquipments.value.length} 个设备，总数：${pagination.total}`)
        }
    } catch (error) {
        console.error('获取设备失败:', error)
        ElMessage.warning('获取设备失败')
        availableEquipments.value = []
        pagination.total = 0
    }
}

// 添加分页处理方法
const handlePageChange = (page: number) => {
    pagination.pageNum = page
    loadEquipmentData()
}

const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageNum = 1
    loadEquipmentData()
}

const loadSelectedEquipments = () => {
    try {
        // 如果有传入的已选设备，使用传入的数据
        if (props.selectedEquipments && props.selectedEquipments.length > 0) {
            selectedEquipmentList.value = [...props.selectedEquipments]
        } else {
            selectedEquipmentList.value = []
        }
        console.log('加载线路已选设备:', props.lineData?.id)
    } catch (error) {
        console.error('加载已选设备失败:', error)
    }
}

const handleCancel = () => {
    emit('cancel')
    emit('update:visible', false)
}

const handleConfirm = async () => {
    try {
        console.log('确认选择设备:', selectedEquipmentList.value)

        // 如果有线路数据，保存设备配置到后端
        if (props.lineData?.id) {
            const deviceIds = selectedEquipmentList.value.map((device) => String(device.id))
            console.log('保存设备配置到线路:', props.lineData.id, '设备IDs:', deviceIds)

            await saveLineDevices(props.lineData.id, deviceIds)
            ElMessage.success('设备配置保存成功')
        }

        emit('confirm', selectedEquipmentList.value)
        emit('update:visible', false)
    } catch (error) {
        console.error('保存设备配置失败:', error)
        ElMessage.error('保存设备配置失败')
    }
}

const handleClose = () => {
    emit('update:visible', false)
}

const handleCurrentPageSelect = () => {
    availableEquipments.value.forEach((equipment) => {
        // 检查是否已存在
        const exists = selectedEquipmentList.value.find((item) => String(item.id) === String(equipment.id))
        if (!exists) {
            selectedEquipmentList.value.push(equipment)
        }
    })

    // 显示成功消息
    const deviceType = props.specialtyType === 'electric' ? '设备' : '设施'
    ElMessage.success(`已添加当前页${deviceType}`)
    /*
    // 切换状态
    equipmentTab.value = equipmentTab.value === 'current' ? '' : 'current';

    if (equipmentTab.value === 'current') {
        // 将当前页面的所有设备添加到已选列表
        availableEquipments.value.forEach(equipment => {
            // 检查是否已存在
            const exists = selectedEquipmentList.value.find(item => String(item.id) === String(equipment.id));
            if (!exists) {
                selectedEquipmentList.value.push(equipment);
            }
        });

        // 显示成功消息
        const deviceType = props.specialtyType === 'electric' ? '设备' : '设施';
        ElMessage.success(`已添加当前页${deviceType}`);
    } else {
        // 从已选列表中移除当前页面的所有设备
        availableEquipments.value.forEach(equipment => {
            const index = selectedEquipmentList.value.findIndex(item => String(item.id) === String(equipment.id));
            if (index > -1) {
                selectedEquipmentList.value.splice(index, 1);
            }
        });

        // 显示成功消息
        const deviceType = props.specialtyType === 'electric' ? '设备' : '设施';
        ElMessage.success(`已移除当前页${deviceType}`);
    }*/
}

// 判断设备是否已被选择
const isEquipmentSelected = (equipment: any) => {
    return selectedEquipmentList.value.some((item) => String(item.id) === String(equipment.id))
}
</script>

<style lang="scss" scoped>
// 设备选择对话框样式
.equipment-dialog-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 500px;
    overflow: hidden;
}

// 查询区域样式
.search-area {
    flex-shrink: 0;
}

// 表格区域容器
.tables-container {
    display: flex;
    gap: 20px;
    flex: 1;
    overflow: hidden;
}

.equipment-select-area {
    width: 50%;
    border-right: 1px solid #ebeef5;
    padding-right: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.selected-equipment-area {
    width: 50%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.search-filters {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
    flex-shrink: 0;
}

.equipment-tabs {
    margin-bottom: 16px;
    flex-shrink: 0;
}

// 表格容器样式
:deep(.el-table) {
    flex: 1;
    overflow: hidden;

    .el-table__body-wrapper {
        overflow-y: auto;
        height: calc(100% - 40px);
    }
}

.selected-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-weight: 500;
    flex-shrink: 0;
}

.back-btn {
    padding: 4px;
}

.no-data {
    color: #909399;
    font-size: 14px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
}

.selected-text {
    color: #909399;
    font-size: 12px;
}

.dialog-footer {
    display: flex;
    justify-content: center;
    gap: 12px;
}
</style>
