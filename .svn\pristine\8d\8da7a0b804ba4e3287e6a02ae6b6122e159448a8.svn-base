<!--用于显示维养作业单详情或者维养周计划详情-->

<template>
    <div class="p-2">
        <el-card class="box-card">
            <template v-slot:header>
                <div class="clearfix">
                    <span>事件详情</span>
                </div>
            </template>
            <!-- 作业单任务基本信息 -->

            <BaseInfoEvent :id="id" />
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import BaseInfoEvent from '../../components/BaseInfoEvent.vue'

const route = useRoute()
const id = ref((route.query.id as string) || '')
</script>
