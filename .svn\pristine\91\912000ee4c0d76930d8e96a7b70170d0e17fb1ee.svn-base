<!-- 缺陷任务基本信息 -->
<template>
    <div class="text item" v-loading="detailLoading">
        <el-row :gutter="gutter">
            <el-col :span="24">
                <el-form-item label="发现时间" prop="code">
                    <el-text>{{ defectDetail?.disoverTime || '暂无数据' }}</el-text>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="gutter">
            <el-col :span="24">
                <el-form-item label="发现人" prop="code">
                    <el-text>{{ defectDetail?.disoverUserId || '暂无数据' }}</el-text>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="gutter">
            <el-col :span="24">
                <el-form-item label="缺陷来源" prop="code">
                    <el-text>{{ getDictLabel(defect_source, defectDetail?.source) }}</el-text>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="gutter">
            <el-col :span="24">
                <el-form-item label="专业类型" prop="code">
                    <el-text>{{ getDictLabel(tnl_specialty, defectDetail?.specialty) }}</el-text>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="gutter">
            <el-col :span="12">
                <el-form-item label="缺陷对象" prop="code">
                    <el-text>{{ defectDetail?.deviceName || '暂无数据' }}</el-text>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <!-- <el-form-item label="查看已有缺陷" prop="code">
                    <el-text></el-text>
                </el-form-item> -->
            </el-col>
        </el-row>
        <el-row :gutter="gutter">
            <el-col :span="8">
                <el-form-item :label="getLevel1Label()" prop="code">
                    <el-text>{{ categoryLevel1 || '暂无数据' }}</el-text>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item :label="getLevel2Label()" prop="code">
                    <el-text>{{ categoryLevel2 || '暂无数据' }}</el-text>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item :label="getLevel3Label()" prop="code">
                    <el-text>{{ categoryLevel3 || '暂无数据' }}</el-text>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="gutter">
            <el-col :span="24">
                <el-form-item label="缺陷位置" prop="code">
                    <el-text>{{ defectDetail?.unitName || '暂无数据' }}</el-text>
                </el-form-item>
            </el-col>
        </el-row>
        <!-- <el-row :gutter="gutter">
            <el-col :span="24">
                <el-form-item label="房间编码" prop="code">
                    <el-text>暂无数据</el-text>
                </el-form-item>
            </el-col>
        </el-row> -->
        <el-row :gutter="gutter">
            <el-col :span="24">
                <el-form-item label="缺陷类型" prop="code">
                    <el-text>{{ defectTypeName || '暂无数据' }}</el-text>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="gutter">
            <el-col :span="24">
                <el-form-item label="缺陷描述" prop="code">
                    <el-text>{{ defectDetail?.description || '暂无数据' }}</el-text>
                </el-form-item>
            </el-col>
        </el-row>

        <!-- 缺陷特征指标显示区域 -->
        <el-row :gutter="gutter" v-if="defectDetail?.feature">
            <el-col :span="24">
                <el-form-item label="缺陷描述" prop="feature">
                    <el-text>{{ defectDetail?.feature || '暂无数据' }}</el-text>
                </el-form-item>
            </el-col>
        </el-row>

        <!-- 当前选中的缺陷特征指标 -->
        <!-- <el-row :gutter="gutter" v-if="defectDetail?.feature && defectDetail.feature.trim()">
            <el-col :span="24">
                <el-form-item label="选中的缺陷特征" prop="code">
                    <div style="padding: 8px 12px; background-color: #e7f5ff; border-radius: 4px; border-left: 3px solid #1890ff">
                        <el-text style="color: #1890ff; font-weight: 500">{{ defectDetail.feature }}</el-text>
                    </div>
                </el-form-item>
            </el-col>
        </el-row> -->
        <el-row :gutter="gutter">
            <el-col :span="24">
                <el-form-item label="图片" prop="code">
                    <MultiImageViewer :oss-ids="defectDetail?.images || ''" :max-display="6" :grid-cols="3" />
                </el-form-item>
            </el-col>
        </el-row>
        <!-- 关联的作业单信息 -->
        <el-row :gutter="gutter" v-if="defectDetail?.taskId">
            <el-col :span="24">
                <el-form-item label="关联作业单" prop="code">
                    <div style="display: flex; align-items: center; gap: 8px">
                        <el-text>{{ taskName || '加载中...' }}</el-text>
                        <el-link type="primary" @click="handleViewTask" style="margin-left: 8px">
                            <el-icon><View /></el-icon>
                            查看详情
                        </el-link>
                    </div>
                </el-form-item>
            </el-col>
        </el-row>
    </div>
</template>
<script setup lang="ts">
import { ref, getCurrentInstance, toRefs, watch, ComponentInternalInstance } from 'vue'
import { useRouter } from 'vue-router'
import { View } from '@element-plus/icons-vue'
import { getDefectData, getDefectViewData } from '@/api/subProject/operation/defect'
import { DiscoveredDefectVO, DiscoveredDefectViewVO } from '@/api/subProject/operation/defect/types'
import MultiImageViewer from '@/components/MultiImageViewer/index.vue'
import { getCategoryParentHierarchy } from '@/api/common/category'
import { CategoryHierarchyVO } from '@/api/common/category/types'
import { getDefect } from '@/api/common/defect'
import { DefectFeatureItemVO } from '@/api/common/defect/types'
import { getTask } from '@/api/plan/task'
import { TaskVO } from '@/api/plan/task/types'

// 定义props
interface Props {
    id?: string | number
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    id: '',
    loading: false
})

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const router = useRouter()
const { tnl_specialty, defect_source } = toRefs<any>(proxy?.useDict('tnl_specialty', 'defect_source'))
const gutter = ref(50) //设置项目表单两列的距离

// 缺陷详情数据
const defectDetail = ref<DiscoveredDefectViewVO | null>(null)
const detailLoading = ref(false)

// 分类层级信息
const categoryLevel1 = ref('') // 机电分系统
const categoryLevel2 = ref('') // 机电子系统
const categoryLevel3 = ref('') // 设备类型

// 缺陷类型相关信息
const defectTypeName = ref('') // 缺陷类型名称
const defectFeatureItems = ref<DefectFeatureItemVO[]>([]) // 缺陷特征指标

// 作业单相关信息
const taskName = ref('') // 作业单名称
const taskInfo = ref<TaskVO | null>(null) // 作业单详细信息

/** 根据数据字典获取标签 */
const getDictLabel = (dictOptions: any[], value: string) => {
    if (!dictOptions || !value) return value || '暂无数据'
    const option = dictOptions.find((item) => item.value === value)
    return option ? option.label : value
}
/** 查看关联的作业单详情 */
const handleViewTask = () => {
    if (!defectDetail.value?.taskId) {
        console.warn('BaseInfoDefect: 缺陷没有关联的作业单ID')
        proxy?.$modal.msgWarning('该缺陷没有关联的作业单')
        return
    }

    console.log('BaseInfoDefect: 跳转到作业单详情页面，taskId:', defectDetail.value.taskId)

    try {
        const taskId = defectDetail.value.taskId
        const taskType = taskInfo.value?.taskType || 'curing' // 默认为维养类型

        // 根据作业单类型跳转到不同的详情页面
        switch (taskType) {
            case 'curing':
                router.push({
                    path: '/subProject/circle/maintain/maintainTaskDetail',
                    query: { taskId: taskId.toString() }
                })
                break
            case 'inspect':
                router.push({
                    path: '/subProject/circle/inspection/inspectionTaskDetail',
                    query: { taskId: taskId.toString() }
                })
                break
            case 'sealing':
                router.push({
                    path: '/subProject/circle/maintain/sealingTaskDetail',
                    query: { taskId: taskId.toString() }
                })
                break
            default:
                // 默认跳转到维养作业单详情
                router.push({
                    path: '/subProject/circle/maintain/maintainTaskDetail',
                    query: { taskId: taskId.toString() }
                })
                break
        }

        console.log('BaseInfoDefect: 成功跳转到作业单详情页面，类型:', taskType)
    } catch (error) {
        console.error('BaseInfoDefect: 跳转到作业单详情页面失败:', error)
        proxy?.$modal.msgError('跳转失败，请重试')
    }
}

/** 根据专业类型获取一级分类标签 */
const getLevel1Label = () => {
    if (defectDetail.value?.specialty === 'electric') {
        return '机电分系统'
    }
    return '结构属性'
}

/** 根据专业类型获取二级分类标签 */
const getLevel2Label = () => {
    if (defectDetail.value?.specialty === 'electric') {
        return '机电子系统'
    }
    return '结构类别'
}

/** 根据专业类型获取三级分类标签 */
const getLevel3Label = () => {
    if (defectDetail.value?.specialty === 'electric') {
        return '设备类型'
    }
    return '构件类型'
}

/** 获取分类层级信息 */
const getCategoryHierarchyInfo = async (categoryId: string | number) => {
    try {
        const res = await getCategoryParentHierarchy(categoryId)
        const hierarchy: CategoryHierarchyVO = res.data

        // 根据层级结构设置显示信息
        if (hierarchy.grandParent) {
            categoryLevel1.value = hierarchy.grandParent.name || ''
        } else {
            categoryLevel1.value = ''
        }

        if (hierarchy.parent) {
            categoryLevel2.value = hierarchy.parent.name || ''
        } else {
            categoryLevel2.value = ''
        }

        if (hierarchy.current) {
            categoryLevel3.value = hierarchy.current.name || ''
        } else {
            categoryLevel3.value = ''
        }

        console.log('分类层级信息获取成功:', {
            level1: categoryLevel1.value,
            level2: categoryLevel2.value,
            level3: categoryLevel3.value,
            hierarchy
        })
    } catch (error) {
        console.error('获取分类层级信息失败:', error)
        // 失败时清空分类层级信息
        categoryLevel1.value = ''
        categoryLevel2.value = ''
        categoryLevel3.value = ''
    }
}

/** 获取缺陷类型详细信息 */
const getDefectTypeDetail = async (defectTypeId: string) => {
    try {
        const response = await getDefect(defectTypeId)
        const defectInfo = response.data as any

        // 设置缺陷类型名称
        defectTypeName.value = defectInfo.typeName || ''

        // 解析缺陷特征指标
        if (defectInfo.defectFeatureItems && defectInfo.defectFeatureItems.length > 0) {
            defectFeatureItems.value = defectInfo.defectFeatureItems
        } else if (defectInfo.feature) {
            try {
                const featureData = JSON.parse(defectInfo.feature)
                defectFeatureItems.value = featureData || []
            } catch (parseError) {
                console.error('解析缺陷特征指标JSON失败:', parseError)
                defectFeatureItems.value = []
            }
        } else {
            defectFeatureItems.value = []
        }

        console.log('获取缺陷类型详细信息成功:', {
            typeName: defectTypeName.value,
            featureItemsCount: defectFeatureItems.value.length
        })
    } catch (error) {
        console.error('获取缺陷类型详细信息失败:', error)
        defectTypeName.value = ''
        defectFeatureItems.value = []
    }
}

/** 获取作业单详细信息 */
const getTaskDetail = async (taskId: string | number) => {
    try {
        const response = await getTask(taskId)
        const task = response.data

        // 设置作业单名称和详细信息
        taskName.value = task.name || ''
        taskInfo.value = task

        console.log('获取作业单详细信息成功:', {
            taskId,
            taskName: taskName.value
        })
    } catch (error) {
        console.error('获取作业单详细信息失败:', error)
        taskName.value = ''
        taskInfo.value = null
    }
}

/** 获取缺陷详情 */
const getDefectDetail = async (id: string | number) => {
    if (!id) {
        console.warn('BaseInfoDefect: 缺陷ID为空，无法获取详情')
        detailLoading.value = false
        return
    }

    console.log('BaseInfoDefect: 开始获取缺陷详情，ID:', id)

    try {
        detailLoading.value = true
        console.log('BaseInfoDefect: 设置加载状态为true')

        const res = await getDefectViewData(id)
        console.log('BaseInfoDefect: API响应:', res)

        defectDetail.value = res.data

        // 获取分类层级信息
        if (defectDetail.value?.categoryId) {
            await getCategoryHierarchyInfo(defectDetail.value.categoryId)
        } else {
            // 如果没有分类ID，清空分类层级信息
            categoryLevel1.value = ''
            categoryLevel2.value = ''
            categoryLevel3.value = ''
        }

        // 获取缺陷类型详细信息
        if (defectDetail.value?.defectType) {
            await getDefectTypeDetail(defectDetail.value.defectType)
        } else {
            // 如果没有缺陷类型ID，清空相关信息
            defectTypeName.value = ''
            defectFeatureItems.value = []
        }

        // 获取作业单详细信息
        if (defectDetail.value?.taskId) {
            await getTaskDetail(defectDetail.value.taskId)
        } else {
            // 如果没有作业单ID，清空相关信息
            taskName.value = ''
            taskInfo.value = null
        }

        console.log('BaseInfoDefect: 获取缺陷详情成功:', defectDetail.value)
    } catch (error) {
        console.error('BaseInfoDefect: 获取缺陷详情失败:', error)
        proxy?.$modal.msgError('获取缺陷详情失败')
        defectDetail.value = null
    } finally {
        console.log('BaseInfoDefect: 设置加载状态为false')
        detailLoading.value = false
    }
}

// 监听id变化，自动获取缺陷详情
watch(
    () => props.id,
    (newId, oldId) => {
        console.log('BaseInfoDefect: id变化监听触发', { newId, oldId })
        if (newId) {
            console.log('BaseInfoDefect: 开始调用getDefectDetail')
            getDefectDetail(newId)
        } else {
            console.log('BaseInfoDefect: newId为空，不调用API')
            detailLoading.value = false
        }
    },
    { immediate: true }
)
</script>
