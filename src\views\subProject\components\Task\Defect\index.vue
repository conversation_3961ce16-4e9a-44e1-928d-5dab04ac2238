<!-- 缺陷待办 -->
<template>
    <el-card shadow="hover">
        <div class="defect-container">
            <!-- 标题栏 -->
            <div class="header">
                <div class="title">缺陷待办</div>
                <div class="count">{{ defectCount }} 条</div>
                <div class="filter">
                    <el-date-picker
                        v-model="date"
                        type="date"
                        placeholder="选择日期"
                        :clearable="false"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        @change="onDateChange"
                    />
                </div>
            </div>

            <!-- 表格 -->
            <div class="table-container">
                <el-table :data="defectList" style="width: 100%" :border="false" :show-header="true" v-loading="loading">
                    <el-table-column type="index" label="序号" width="80" :index="indexMethod" />
                    <!-- @tod：作业单名称要改成实际的待办名称（name） -->
                    <el-table-column prop="source" label="作业单名称">
                        <template #default="scope">
                            {{ scope.row.source }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="planJobDate" label="计划作业日期" width="180">
                        <template #default="scope">
                            <span>{{ formatDate(scope.row.planJobDate) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="whileEvening" label="班次" width="100" />
                    <el-table-column prop="currentStatus" label="状态" width="100">
                        <template #default="scope">
                            <span class="defect-name-link" @click="goDefectDetail(scope.row.id)">
                                {{ scope.row.currentStatus }}
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    @pagination="getList"
                />
            </div>
        </div>
    </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { listDiscoveredDefect } from '@/api/subProject/operation/defect'
import { DiscoveredDefectViewVO, DiscoveredDefectQuery } from '@/api/subProject/operation/defect/types'
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'
import { useRouter } from 'vue-router'

// 定义组件属性
const props = defineProps({
    date: {
        type: String,
        default: ''
    }
})

const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()

// 内部日期状态
const date = ref(props.date)

// 监听props变化
watch(
    () => props.date,
    (newValue) => {
        date.value = newValue
        loadDefectData()
    }
)

// 表格数据
const defectList = ref<DiscoveredDefectViewVO[]>([])

// 加载状态
const loading = ref(false)

// 总记录数
const total = ref(0)

// 查询参数
const queryParams = ref<DiscoveredDefectQuery>({
    pageNum: 1,
    pageSize: 10
})

// 计算缺陷数量
const defectCount = computed(() => {
    return total.value
})

// 序号方法
const indexMethod = (index: number) => {
    return (queryParams.value.pageNum! - 1) * queryParams.value.pageSize! + index + 1
}

// 跳转到缺陷详情
const goDefectDetail = (id: string) => {
    router.push({
        path: '/subProject/circle/maintain/defect/assign',
        query: { id: id }
    })
}

// 日期格式化函数
const formatDate = (date: string) => {
    if (!date) return ''
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
}

// 查询缺陷列表（分页版本）
const getList = async () => {
    try {
        loading.value = true

        // 设置查询参数
        const query: DiscoveredDefectQuery = {
            pageNum: queryParams.value.pageNum,
            pageSize: queryParams.value.pageSize,
            projectId: appStore.projectContext.selectedProjectId,
            todoStatus: 'ACTIVE', // 与普通任务保持一致
            params: {
                assignee: userStore.userId, // 按当前用户筛选
                ...(date.value && { planJobDate: date.value }) // 只有当日期有值时才添加
            }
        }

        const response = await listDiscoveredDefect(query)
        defectList.value = response.rows || []
        total.value = response.total || 0
    } catch (error) {
        console.error('加载缺陷数据失败:', error)
        defectList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

// 加载缺陷数据（兼容原有调用）
const loadDefectData = async () => {
    // 重置到第一页
    queryParams.value.pageNum = 1
    await getList()
}

// 日期变化处理
const onDateChange = () => {
    loadDefectData()
}

// 组件挂载时加载数据
onMounted(() => {
    loadDefectData()
})
</script>

<style lang="scss" scoped>
.defect-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .header {
        display: flex;
        align-items: center;
        padding: 16px 0px;

        .title {
            font-size: 16px;
            font-weight: bold;
            color: #fff;
        }

        .count {
            margin-left: 10px;
            font-size: 14px;
            color: #909399;
        }

        .filter {
            margin-left: 20px;
        }
    }

    .table-container {
        flex: 1;
        padding: 10px 0px;
        overflow-y: auto;

        .defect-name-link {
            color: #409eff;
            cursor: pointer;

            &:hover {
                color: #66b1ff;
                text-decoration: underline;
            }
        }

        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .status-progress {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .status-done {
            background-color: #f6ffed;
            color: #52c41a;
        }
    }
}
</style>
