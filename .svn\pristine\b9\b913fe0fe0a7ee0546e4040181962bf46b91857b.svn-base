<template>
    <div v-if="visible" class="event-dialog-overlay" @click="handleOverlayClick">
        <div class="event-dialog" @click.stop>
            <!-- 关闭按钮 -->
            <div class="close-btn" @click="handleClose">×</div>

            <!-- 事件表格 -->
            <el-table
                :data="displayedEvents"
                v-loading="loading"
                element-loading-text="加载中..."
                element-loading-background="rgba(0, 0, 0, 0.8)"
                element-loading-text-color="#ffffff"
                style="width: 100%; max-height: 500px"
                class="event-table"
                :header-cell-style="{ background: 'transparent', color: '#ffffff', textAlign: 'center' }"
                :cell-style="{ background: 'transparent', color: '#ffffff', borderColor: 'rgba(0, 150, 255, 0.1)' }"
                :row-style="{ background: 'transparent', height: 'auto' }"
            >
                <el-table-column prop="happenTime" label="事件时间" width="160" align="center">
                    <template #default="{ row }">
                        <span class="event-time-link" @click="handleEventTimeClick(row)">
                            {{ formatTime(row.happenTime) }}
                        </span>
                    </template>
                </el-table-column>

                <el-table-column prop="eventClassName" label="事件类型" width="120" align="center">
                    <template #default="{ row }">
                        {{ row.eventClassName || '未知类型' }}
                    </template>
                </el-table-column>

                <el-table-column prop="liveDescription" label="事件描述" min-width="200" align="left">
                    <template #default="{ row }">
                        {{ row.liveDescription || '暂无描述' }}
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 事件详情对话框 -->
        <EventDetailDialog :visible="detailDialogVisible" :event-id="selectedEventId" @close="handleDetailDialogClose" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { listNoPageEvents } from '@/api/subProject/operation/event/index'
import { EventVO } from '@/api/subProject/operation/event/types'
import { getCategory } from '@/api/common/category/index'
import EventDetailDialog from './EventDetailDialog.vue'

// 扩展的事件接口，包含事件类型名称
interface ProcessedEventVO extends EventVO {
    eventClassName?: string
}

interface Props {
    visible: boolean
    projectId?: string | number
    tunnelName?: string
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    projectId: '',
    tunnelName: ''
})

const emit = defineEmits<{
    close: []
}>()

// 响应式数据
const eventList = ref<ProcessedEventVO[]>([])
const loading = ref(false)
const eventClassNames = ref<Map<string, string>>(new Map())

// 详情对话框状态
const detailDialogVisible = ref(false)
const selectedEventId = ref<string | number>('')

// 限制显示前5条数据
const displayedEvents = computed(() => {
    return eventList.value.slice(0, 5)
})

// 获取事件类型名称
const getEventClassName = async (eventClass: string): Promise<string> => {
    if (!eventClass) return '未知类型'

    // 检查缓存
    if (eventClassNames.value.has(eventClass)) {
        return eventClassNames.value.get(eventClass)!
    }

    try {
        const response = await getCategory(eventClass)
        const categoryName = response.data?.name || '未知类型'

        // 缓存结果
        eventClassNames.value.set(eventClass, categoryName)
        return categoryName
    } catch (error) {
        console.error('获取事件类型名称失败:', error)
        return '未知类型'
    }
}

// 处理事件数据，包括获取事件类型名称
const processEventData = async (events: EventVO[]): Promise<ProcessedEventVO[]> => {
    const processedEvents = await Promise.all(
        events.map(async (event) => ({
            ...event,
            eventClassName: await getEventClassName(event.eventClass)
        }))
    )

    return processedEvents
}

// 获取事件数据
const fetchEventData = async () => {
    if (!props.tunnelName) {
        eventList.value = []
        return
    }

    loading.value = true
    try {
        console.log('获取事件数据:', { tunnelName: props.tunnelName })
        const response = await listNoPageEvents({
            tunnelName: props.tunnelName,
            pageNum: 1,
            pageSize: 5 // 获取大量数据用于客户端分页
        })

        const events = response.data || []
        console.log('获取到事件数据:', events.length, '条')

        // 按发生时间倒序排序
        events.sort((a, b) => {
            const timeA = new Date(a.happenTime || '').getTime()
            const timeB = new Date(b.happenTime || '').getTime()
            return timeB - timeA
        })

        // 处理事件数据，获取事件类型名称
        const processedEvents = await processEventData(events)
        eventList.value = processedEvents
    } catch (error) {
        console.error('获取事件数据失败:', error)
        eventList.value = []
    } finally {
        loading.value = false
    }
}

// 时间格式化
const formatTime = (time?: string) => {
    if (!time) return '暂无时间'

    try {
        const date = new Date(time)
        if (isNaN(date.getTime())) {
            return time
        }

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}`
    } catch (error) {
        return time
    }
}

// 处理事件时间点击
const handleEventTimeClick = (row: ProcessedEventVO) => {
    if (!row.id) {
        console.warn('事件ID不存在，无法打开详情')
        return
    }

    // 打开事件详情对话框
    selectedEventId.value = row.id
    detailDialogVisible.value = true
}
// 处理详情对话框关闭
const handleDetailDialogClose = () => {
    detailDialogVisible.value = false
    selectedEventId.value = ''
}

// 点击关闭按钮
const handleClose = () => {
    emit('close')
}
// 监听参数变化
watch(
    [() => props.visible, () => props.tunnelName],
    ([visible, tunnelName]) => {
        if (visible && tunnelName) {
            fetchEventData()
        }
    },
    { immediate: true }
)

// 点击遮罩层关闭对话框
const handleOverlayClick = () => {
    emit('close')
}
</script>

<style lang="scss" scoped>
.event-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    //background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.event-dialog {
    border-radius: 8px;
    min-width: 600px;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);

    // background: red;
    .close-btn {
        position: absolute;
        top: 2px;
        right: 15px;
        color: #ffffff;
        font-size: 28px;
        cursor: pointer;
        line-height: 1;
        z-index: 10;
        // background: rgba(0, 0, 0, 0.3);
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        // background: red;
        &:hover {
            opacity: 0.8;
            background: rgba(0, 0, 0, 0.6);
        }
    }
}

// el-table 自定义样式
.event-table {
    background: transparent !important;
    border-radius: 6px;
    overflow: hidden;
    // background: red;
    // 表格整体样式
    :deep(.el-table__inner-wrapper) {
        background: transparent;
        // border-radius: 6px;
    }

    :deep(.el-table__header-wrapper) {
        background: transparent;
    }

    :deep(.el-table__body-wrapper) {
        background: rgba(0, 20, 50, 0.8);
        max-height: 400px;
        overflow-y: auto !important;

        // 自定义滚动条
        &::-webkit-scrollbar {
            width: 8px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(0, 150, 255, 0.1);
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(0, 150, 255, 0.6);
            border-radius: 4px;

            &:hover {
                background: rgba(0, 150, 255, 0.8);
            }
        }

        // 火狐浏览器滚动条样式
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 150, 255, 0.6) rgba(0, 150, 255, 0.1);
    }

    // 表头样式
    :deep(.el-table__header) {
        th {
            height: 60px !important;
            // background: linear-gradient(90deg, rgba(255,255,255,0) 0%, #FFFFFF 50%, rgba(255,255,255,0) 100%) !important;
            background: #11274b !important;
            border-bottom: 1px solid rgba(0, 150, 255, 0.2) !important;

            .cell {
                color: #333333 !important;
                font-size: 18px !important;
                font-weight: bold !important;
                color: #fff !important;
                padding: 10px 5px !important;
                line-height: 1.4 !important;
            }
        }
    }

    // 表格行样式
    :deep(.el-table__body) {
        tr {
            background: transparent !important;

            // 奇数行背景色
            &:nth-child(odd) {
                background: rgba(10, 19, 15, 0.3) !important;
            }

            // 偶数行背景色
            &:nth-child(even) {
                background: rgba(17, 39, 75, 0.6) !important;
            }

            &:hover {
                background: rgba(0, 150, 255, 0.2) !important;
            }

            td {
                background: inherit !important;
                border-bottom: 1px solid rgba(0, 150, 255, 0.1) !important;
                height: auto !important;

                .cell {
                    color: #ffffff !important;
                    font-size: 16px !important;
                    line-height: 1.4 !important;
                    white-space: pre-wrap !important;
                    word-break: break-word !important;
                    word-wrap: break-word !important;
                    overflow-wrap: break-word !important;
                    padding: 12px 8px !important;
                }
            }
        }
    }

    // 空数据样式
    :deep(.el-table__empty-block) {
        background: transparent !important;

        .el-table__empty-text {
            color: rgba(255, 255, 255, 0.6) !important;
        }
    }
}

// 事件时间链接样式
.event-time-link {
    color: #00d4ff !important;
    cursor: pointer;
    text-decoration: underline;
    transition: all 0.3s ease;

    &:hover {
        color: #ffffff !important;
        text-shadow: 0 0 8px rgba(0, 212, 255, 0.8);
        transform: scale(1.05);
    }

    &:active {
        transform: scale(0.95);
    }
}

// 加载状态样式
.loading-container {
    padding: 40px 20px;
    text-align: center;

    .loading-text {
        color: #ffffff;
        font-size: 18px;
        opacity: 0.8;
    }
}

// 无数据提示样式
.no-data-container {
    padding: 40px 20px;
    text-align: center;

    .no-data-text {
        color: #ffffff;
        font-size: 18px;
        opacity: 0.6;
    }
}
</style>
