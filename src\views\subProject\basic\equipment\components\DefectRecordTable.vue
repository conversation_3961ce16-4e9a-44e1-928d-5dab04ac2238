<!--
缺陷记录表格组件 (DefectRecordTable)

功能：
- 显示缺陷记录相关信息
- 自动处理数据字典显示
- 内置分页功能
- 内置查询条件筛选（缺陷来源、养护策略）
- 支持按设备ID查询缺陷记录
- 支持时间范围筛选

使用示例：

<DefectRecordTable
    :device-id="deviceId"
    :project-id="projectId"
    :date-range="{ startDate: '2024-01-01', endDate: '2024-12-31' }"
    @process-defect="handleProcessDefect"
/>

Props:
- deviceId: 设备ID (可选，用于查询该设备的缺陷记录)
- projectId: 项目ID (可选，用于设备缺陷记录查询)
- dateRange: 时间范围 { startDate, endDate } (可选，用于时间范围筛选)

Events:
- process-defect: 处理缺陷事件

特性说明：
1. 当提供deviceId和projectId时，组件会自动调用相关API
2. 支持按缺陷来源、养护策略条件过滤
3. 支持时间范围筛选，当dateRange变化时自动重新查询
4. 当设备ID变化时，组件会自动重新查询
5. 支持缺陷等级显示和状态管理
6. 组件内部管理数据状态，外部只需传入核心参数
-->

<template>
    <el-card shadow="never" class="table-card">
        <!-- 查询条件区域 -->
        <div v-if="showFilter" class="filter-section">
            <div class="filter-row">
                <div class="filter-item">
                    <el-select v-model="queryParams.source" placeholder="请选择缺陷来源" clearable>
                        <el-option v-for="item in defectSourceOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </div>

                <div class="filter-item">
                    <el-select v-model="queryParams.strategy" placeholder="请选择养护策略" clearable>
                        <el-option v-for="item in maintenanceStrategyOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </div>

                <div class="filter-item">
                    <el-select v-model="queryParams.defectType" placeholder="请选择缺陷分类" clearable>
                        <el-option v-for="item in defectCategories" :key="item.id" :label="item.typeName" :value="item.id" />
                    </el-select>
                </div>

                <!-- 查询按钮 -->
                <div class="filter-actions">
                    <el-button type="primary" @click="handleQuery">查询</el-button>
                    <el-button
                        @click="
                            () => {
                                queryParams.source = undefined
                                queryParams.strategy = undefined
                                queryParams.defectType = undefined
                                handleQuery()
                            }
                        "
                        >重置</el-button
                    >
                </div>
            </div>
        </div>

        <el-table :data="tableData" stripe style="width: 100%" v-loading="loading">
            <!-- 发现时间 -->
            <el-table-column prop="disoverTime" label="发现时间">
                <template #default="scope">
                    {{ formatDate(scope.row.disoverTime) }}
                </template>
            </el-table-column>
            <el-table-column prop="disoverUserId" label="发现人"> </el-table-column>
            <!-- 缺陷来源 -->
            <el-table-column prop="source" label="缺陷来源">
                <template #default="scope">
                    <dict-tag :options="defectSourceOptions" :value="scope.row.source" />
                </template>
            </el-table-column>
            <!-- 缺陷类型 -->
            <el-table-column prop="defectType" label="缺陷类型">
                <template #default="scope">
                    {{ getDefectTypeName(scope.row.defectType) }}
                </template>
            </el-table-column>

            <!-- 养护策略 -->
            <el-table-column prop="strategy" label="养护策略">
                <template #default="scope">
                    <dict-tag :options="maintenanceStrategyOptions" :value="scope.row.strategy" />
                </template>
            </el-table-column>

            <!-- 当前状态 -->
            <el-table-column prop="currentStatus" label="当前状态">
                <template #default="scope">
                    <span v-if="scope.row.canceled === 'Y'"> 已销项 </span>
                    <span v-else-if="['observe', 'plan', 'mediumrepair', 'special'].includes(scope.row.strategy)"> 暂不处理 </span>
                    <span v-else-if="['repair', 'arrange'].includes(scope.row.strategy)">
                        <span v-if="scope.row.taskRepairStatus == 'ACTIVE'"> 处理中 </span>
                        <span v-else-if="scope.row.taskRepairStatus == 'COMPLETED'"> 已处理 </span>
                        <span v-else> 处理中 </span>
                    </span>

                    <dict-tag v-else :options="defect_status" :value="scope.row.currentStatus" />
                </template>
            </el-table-column>

            <el-table-column label="操作" width="120" align="center">
                <template #default="scope">
                    <el-button link type="primary" @click="handleViewDetails(scope.row)"> 详情 </el-button>
                    <el-button v-if="scope.row.currentStatus === 'pending'" link type="success" @click="handleProcessDefect(scope.row)">
                        处理
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="handlePagination"
        />
    </el-card>
</template>

<script setup lang="ts">
import { ref, computed, getCurrentInstance, toRefs, watch, reactive, onMounted } from 'vue'
import type { ComponentInternalInstance } from 'vue'
import { listDiscoveredDefect2 } from '@/api/subProject/operation/defect'
import type { DiscoveredDefectVO, DiscoveredDefectQuery } from '@/api/subProject/operation/defect/types'
import { listDefect } from '@/api/common/defect'
import type { DefectVO } from '@/api/common/defect/types'

// 定义组件属性
interface Props {
    deviceId?: string // 设备ID属性
    projectId?: string // 项目ID属性
    dateRange?: {
        startDate: string
        endDate: string
    } // 时间范围参数
}

// 定义组件事件
interface Emits {
    (e: 'process-defect', row: DiscoveredDefectVO): void
    (e: 'pagination', params: { pageNum: number; pageSize: number }): void
}

const props = withDefaults(defineProps<Props>(), {
    deviceId: '',
    projectId: '',
    dateRange: undefined
})

const emit = defineEmits<Emits>()

const { proxy } = getCurrentInstance() as ComponentInternalInstance

// 内部数据状态
const loading = ref(false)
const total = ref(0)
const tableData = ref<DiscoveredDefectVO[]>([])
const showFilter = ref(true)

// 缺陷分类列表
const defectCategories = ref<DefectVO[]>([])

// 查询参数 - 参考index.vue的方式
const queryParams = reactive<DiscoveredDefectQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: props.projectId,
    deviceId: props.deviceId,
    source: undefined,
    strategy: undefined,
    params: {
        disoverTime: undefined
    }
})

// 获取数据字典
const { defect_source, maintenance_strategy, defect_status } = toRefs<any>(proxy?.useDict('defect_source', 'maintenance_strategy', 'defect_status'))

// 数据字典选项
const defectSourceOptions = computed(() => {
    return defect_source.value || []
})

const maintenanceStrategyOptions = computed(() => {
    return maintenance_strategy.value || []
})

// 缺陷分类选项 - 将缺陷分类数据转换为数据字典格式
const defectCategoryOptions = computed(() => {
    return defectCategories.value.map((item) => ({
        label: item.typeName,
        value: item.id.toString()
    }))
})

// 格式化日期
const formatDate = (date: string) => {
    if (!date) return '-'
    return date
}

// 获取处理状态类型
const getProcessStatusType = (status: string) => {
    switch (status) {
        case 'completed':
            return 'success'
        case 'in_progress':
            return 'warning'
        case 'pending':
            return 'info'
        case 'failed':
            return 'danger'
        default:
            return 'info'
    }
}

// 获取处理状态文本
const getProcessStatusText = (status: string) => {
    switch (status) {
        case 'completed':
            return '已完成'
        case 'in_progress':
            return '处理中'
        case 'pending':
            return '待处理'
        case 'failed':
            return '处理失败'
        default:
            return '未知'
    }
}
// 查看详情
const handleViewDetails = (row: DefectVO) => {
    // emit('view-details', row)
    proxy?.$router.push(`/subProject/circle/maintain/defectDetail?id=${row.id}`)
    //  proxy?.$router.push('/subProject/circle/maintain/maintainTaskDetail?taskId=' + row?.id)
}
// 获取缺陷类型名称
const getDefectTypeName = (defectTypeId: string) => {
    const defect = defectCategories.value.find((item) => item.id.toString() === defectTypeId)
    return defect ? defect.typeName : defectTypeId
}

// 处理缺陷
const handleProcessDefect = (row: DiscoveredDefectVO) => {
    emit('process-defect', row)
}

// 获取缺陷分类列表
const getDefectCategories = async () => {
    try {
        const response = await listDefect()

        defectCategories.value = response.rows || []
        console.log('获取缺陷分类列表成功:', {
            count: defectCategories.value.length,
            categories: defectCategories.value.map((item) => ({
                id: item.id,
                specialty: item.specialty,
                typeName: item.typeName,
                definition: item.definition
            }))
        })
    } catch (error) {
        console.error('获取缺陷分类列表失败:', error)
        defectCategories.value = []
    }
}

// 查询缺陷记录 - 参考index.vue的getList方法
const handleQuery = async () => {
    loading.value = true

    // 重置查询参数
    queryParams.params = {}
    queryParams.projectId = props.projectId
    queryParams.deviceId = props.deviceId

    // 处理日期范围 - 将开始和结束日期分别加上begin和end前缀
    if (props.dateRange?.startDate && props.dateRange?.endDate) {
        queryParams.params.beginDisoverTime = props.dateRange.startDate
        queryParams.params.endDisoverTime = props.dateRange.endDate
    }

    try {
        const res = await listDiscoveredDefect2(queryParams)
        tableData.value = res?.rows || []
        total.value = res?.total || 0
    } finally {
        loading.value = false
    }
}

// 分页事件处理
const handlePagination = (params: { pageNum: number; pageSize: number }) => {
    queryParams.pageNum = params.pageNum
    queryParams.pageSize = params.pageSize
    handleQuery()
}

// 监听属性变化
watch([() => props.deviceId, () => props.projectId, () => props.dateRange, () => queryParams.pageNum, () => queryParams.pageSize], handleQuery, {
    immediate: true
})

// 组件挂载时获取缺陷分类列表
onMounted(() => {
    getDefectCategories()
})
</script>

<style lang="scss" scoped>
.table-card {
    margin-top: 16px;
}

.filter-section {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 4px;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.filter-item {
    min-width: 200px;
}

.filter-actions {
    display: flex;
    gap: 8px;
    margin-left: auto;
}

@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-item {
        min-width: auto;
    }

    .filter-actions {
        margin-left: 0;
        margin-top: 16px;
    }
}
</style>
