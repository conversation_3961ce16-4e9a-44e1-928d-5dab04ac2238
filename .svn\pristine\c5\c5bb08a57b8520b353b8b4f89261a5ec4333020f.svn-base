<!-- 领航页V2 -->
<template>
    <div class="navigation-container">
            <ProjectMapBaidu />
            <Navigation />
            <GroupRight/>
    </div>
</template>

<script setup lang="ts">
import ProjectMapBaidu from './ditu.vue'
// 引入原来的领航页做为当前页面组件
import Navigation from './index.vue'
import GroupRight from './components/GroupRight.vue'
</script>

<style lang="scss" scoped>
.navigation-container {
    width: 7680px;
    height: 100vh;
    overflow: hidden;
    position: relative;
    background: url('@/assets/images/bj.png');
    background-size: 100% 100%;
    background-position: center center;
    background-repeat: no-repeat;


}



</style>
