<template>
    <!-- 自定义对话框遮罩层 -->
    <div v-if="visible" class="custom-dialog-overlay" @click="handleOverlayClick">
        <!-- 对话框容器 -->
        <div class="custom-dialog" @click.stop>
            <!-- 对话框头部 -->
            <div class="custom-dialog-header">
                <h3 class="custom-dialog-title">事件详情</h3>
                <button class="custom-dialog-close" @click="handleClose">
                    <svg viewBox="0 0 1024 1024" width="16" height="16">
                        <path
                            d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"
                            fill="currentColor"
                        />
                    </svg>
                </button>
            </div>

            <!-- 对话框内容 -->
            <div class="custom-dialog-body">
                <BaseInfoEvent :id="String(eventId)" theme="bigscreen" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import BaseInfoEvent from '@/views/subProject/components/BaseInfoEvent.vue'

interface Props {
    visible: boolean
    eventId: string | number
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    eventId: ''
})

const emit = defineEmits<{
    close: []
}>()

// 内部可见状态
const visible = ref(false)

// 监听外部 visible 变化
watch(
    () => props.visible,
    (newVal) => {
        visible.value = newVal
    },
    { immediate: true }
)

// 处理关闭事件
const handleClose = () => {
    visible.value = false
    emit('close')
}

// 点击遮罩层关闭对话框
const handleOverlayClick = () => {
    handleClose()
}
</script>

<style lang="scss" scoped>
// 自定义对话框遮罩层
.custom-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 4000;
    backdrop-filter: blur(4px);
}

// 自定义对话框容器
.custom-dialog {
    width: 1200px;
    max-width: 90vw;
    max-height: 90vh;
    background: linear-gradient(135deg, rgba(0, 20, 50, 0.95) 0%, rgba(17, 39, 75, 0.95) 100%);
    border: 1px solid rgba(0, 150, 255, 0.4);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: dialogFadeIn 0.3s ease-out;
}

// 对话框头部
.custom-dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background-color: rgba(17, 39, 75, 0.9);
    border-bottom: 1px solid rgba(0, 150, 255, 0.3);
    flex-shrink: 0;
}

// 对话框标题
.custom-dialog-title {
    margin: 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: bold;
}

// 关闭按钮
.custom-dialog-close {
    background: none;
    border: none;
    color: #ffffff;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
        color: #00d4ff;
        background-color: rgba(0, 150, 255, 0.1);
    }

    svg {
        width: 16px;
        height: 16px;
    }
}

// 对话框内容区域
.custom-dialog-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    color: #ffffff;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(0, 150, 255, 0.6);
        border-radius: 3px;

        &:hover {
            background: rgba(0, 150, 255, 0.8);
        }
    }
}

// 对话框动画
@keyframes dialogFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

// 响应式设计
@media (max-width: 768px) {
    .custom-dialog {
        width: 95vw;
        max-height: 95vh;
    }

    .custom-dialog-header {
        padding: 12px 16px;
    }

    .custom-dialog-title {
        font-size: 16px;
    }

    .custom-dialog-body {
        padding: 16px;
    }
}

// 大屏适配
@media (min-width: 6000px) {
    .custom-dialog-body {
        font-size: 16px;
    }
}
</style>
