@use './variables.module.scss' as *;

#app {
	.main-container {
		height: 100%;
		transition: margin-left 0.28s;
		margin-left: $base-sidebar-width;
		position: relative;
	}

	.sidebarHide {
		margin-left: 0 !important;
	}

	.sidebar-container {
		-webkit-transition: width 0.28s;
		transition: width 0.28s;
		width: $base-sidebar-width !important;
		background-color: $base-menu-background;
		height: calc(100% - 78px);//减去顶部的高度
		position: fixed;
		font-size: 0;
		top: 0;
		bottom: 0;
		left: 0;
		z-index: 1001;
		overflow: hidden;
		-webkit-box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
		box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);

		// reset element-ui css
		.horizontal-collapse-transition {
			transition:
				0s width ease-in-out,
				0s padding-left ease-in-out,
				0s padding-right ease-in-out;
		}

		.scrollbar-wrapper {
			overflow-x: hidden !important;
		}

		.el-scrollbar__bar.is-vertical {
			right: 0;
		}

		.el-scrollbar {
			height: 100%;
		}

		&.has-logo {
			.el-scrollbar {
				height: calc(100% - 50px);
			}
		}

		.is-horizontal {
			display: none;
		}

		a {
			display: inline-block;
			width: 100%;
			overflow: hidden;
		}

		.svg-icon {
			margin-right: 16px;
		}

		.el-menu {
			border: none;
			height: 100%;
			width: 100% !important;
		}

		.el-menu-item,
		.menu-title {
			overflow: hidden !important;
			text-overflow: ellipsis !important;
			white-space: nowrap !important;
		}

		.el-menu-item .el-menu-tooltip__trigger {
			display: inline-block !important;
		}

		// menu hover
		.theme-dark .sub-menu-title-noDropdown,
		.theme-dark .el-sub-menu__title {
			&:hover {
				background-color: $base-sub-menu-title-hover !important;
			}
		}
		.sub-menu-title-noDropdown,
		.el-sub-menu__title {
			&:hover {
				background-color: rgba(0, 0, 0, 0.05) !important;
			}
		}

		& .theme-dark .is-active > .el-sub-menu__title {
			color: $base-menu-color-active !important;
		}

		& .nest-menu .el-sub-menu > .el-sub-menu__title,
		& .el-sub-menu .el-menu-item {
			min-width: $base-sidebar-width !important;
			&:hover {
				background-color: rgba(0, 0, 0, 0.1) !important;
			}
		}

		& .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
		& .theme-dark .el-sub-menu .el-menu-item {
			background-color: $base-sub-menu-background !important;

			&:hover {
				background-color: $base-sub-menu-hover !important;
			}
		}

		& .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
		& .theme-dark .el-menu-item {
			&:hover {
				// you can use $sub-menuHover
				background-color: $base-menu-hover !important;
			}
		}
		& .nest-menu .el-sub-menu > .el-sub-menu__title,
		& .el-menu-item {
			&:hover {
				// you can use $sub-menuHover
				background-color: rgba(0, 0, 0, 0.04) !important;
			}
		}

		// 选中状态的渐变背景
		.el-menu-item.is-active {
            background: linear-gradient(180deg, #202A36 0%, #4286F3 100%);
            box-shadow: 0px 3px 7px 0px rgba(0,0,0,0.28);
color: #fff !important;
			.svg-icon {
				color: #fff !important;
			}
		}

		// 深色模式下的选中状态
		.theme-dark .el-menu-item.is-active {
			background: linear-gradient(180deg, #202A36 0%, #4286F3 100%);
box-shadow: 0px 3px 7px 0px rgba(0,0,0,0.28);
color: #fff !important;
			.svg-icon {
				color: #fff !important;
			}
		}
	}

	.hideSidebar {
		.sidebar-container {
			width: 54px !important;
		}

		.main-container {
			margin-left: 54px;
		}

		.sub-menu-title-noDropdown {
			padding: 0 !important;
			position: relative;

			.el-tooltip {
				padding: 0 !important;

				.svg-icon {
					margin-left: 20px;
				}
			}
		}

		.el-sub-menu {
			overflow: hidden;

			& > .el-sub-menu__title {
				padding: 0 !important;

				.svg-icon {
					margin-left: 20px;
				}
			}
		}

		.el-menu--collapse {
			.el-sub-menu {
				& > .el-sub-menu__title {
					& > span {
						height: 0;
						width: 0;
						overflow: hidden;
						visibility: hidden;
						display: inline-block;
					}
					& > i {
						height: 0;
						width: 0;
						overflow: hidden;
						visibility: hidden;
						display: inline-block;
					}
				}
			}
		}
	}

	.el-menu--collapse .el-menu .el-sub-menu {
		min-width: $base-sidebar-width !important;
	}

	// mobile responsive
	.mobile {
		.main-container {
			margin-left: 0px;
		}

		.sidebar-container {
			transition: transform 0.28s;
			width: $base-sidebar-width !important;
		}

		&.hideSidebar {
			.sidebar-container {
				pointer-events: none;
				transition-duration: 0.3s;
				transform: translate3d(-$base-sidebar-width, 0, 0);
			}
		}
	}

	.withoutAnimation {
		.main-container,
		.sidebar-container {
			transition: none;
		}
	}
}

// when menu collapsed
.el-menu--vertical {
	& > .el-menu {
		.svg-icon {
			margin-right: 16px;
		}
	}
}
