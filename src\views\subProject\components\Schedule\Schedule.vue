<!-- 排班管理组件 -->
<template>
    <div class="schedule-container">
        <!-- 头部操作区 -->
        <div class="header-actions">
            <el-card>
                <div class="action-buttons">
                    <el-button type="primary" @click="handleAddSchedule">
                        <el-icon>
                            <Plus />
                        </el-icon>
                        手动录入
                    </el-button>
                    <!-- 暂时隐藏导入和下载模板功能-->
                    <el-button type="success" @click="handleExcelImport">
                        <el-icon>
                            <Upload />
                        </el-icon>
                        Excel导入
                    </el-button>
                    <el-button type="danger" @click="handleBatchDelete">
                        <el-icon>
                            <Delete />
                        </el-icon>
                        批量删除当月数据
                    </el-button>
                    <!-- <el-button type="info" @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            下载模板
          </el-button> -->
                </div>

                <!-- 筛选条件 -->
                <div class="filter-section">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-select v-model="filters.team" placeholder="选择班组" clearable filterable allow-create default-first-option>
                                <el-option label="全部班组" value="" />
                                <el-option v-for="team in teamOptions" :key="team" :label="team" :value="team" />
                            </el-select>
                        </el-col>
                        <el-col :span="6">
                            <el-select v-model="filters.shift" placeholder="选择班次" clearable>
                                <el-option label="全部班次" value="" />
                                <el-option label="白班" value="白班" />
                                <el-option label="晚班" value="晚班" />
                            </el-select>
                        </el-col>
                        <el-col :span="6">
                            <el-input v-model="filters.person" placeholder="输入人员姓名" clearable />
                        </el-col>
                        <el-col :span="6">
                            <el-button type="primary" @click="applyFilters">筛选</el-button>
                            <el-button @click="resetFilters">重置</el-button>
                        </el-col>
                    </el-row>
                </div>
            </el-card>
        </div>

        <!-- 月历展示区 -->
        <div class="calendar-section">
            <el-card>
                <template #header>
                    <div class="calendar-header">
                        <el-date-picker
                            v-model="currentMonth"
                            type="month"
                            placeholder="选择月份"
                            format="YYYY年MM月"
                            value-format="YYYY-MM"
                            @change="handleMonthChange"
                        />
                        <div class="legend">
                            <span class="legend-item">
                                <span class="legend-color day-shift"></span>
                                白班
                            </span>
                            <span class="legend-item">
                                <span class="legend-color night-shift"></span>
                                晚班
                            </span>
                            <span class="legend-item">
                                <span class="legend-color leader"></span>
                                班组长
                            </span>
                        </div>
                    </div>
                </template>

                <div class="calendar-grid">
                    <div class="calendar-weekdays">
                        <div v-for="day in weekdays" :key="day" class="weekday">周{{ day }}</div>
                    </div>
                    <div class="calendar-days">
                        <div
                            v-for="day in calendarDays"
                            :key="day.date"
                            class="calendar-day"
                            :class="{
                                'other-month': day.isOtherMonth,
                                'today': day.isToday,
                                'has-schedule': day.schedules.length > 0
                            }"
                            @click="handleDayClick(day)"
                        >
                            <div class="day-number">{{ day.day }}</div>
                            <div class="day-schedules">
                                <div
                                    v-for="schedule in day.schedules.slice(0, 3)"
                                    :key="schedule.id"
                                    class="schedule-item"
                                    :class="{
                                        'day-shift': schedule.shiftType === '白班',
                                        'night-shift': schedule.shiftType === '晚班' || schedule.shiftType === '夜班',
                                        'leader': schedule.isLeader
                                    }"
                                >
                                    <span class="schedule-name">
                                        {{ schedule.employeeName }}
                                        <span v-if="schedule.isLeader" class="leader-dot"></span>
                                    </span>
                                    <span class="schedule-team">{{ schedule.teamName }}</span>
                                </div>
                                <div v-if="day.schedules.length > 3" class="more-schedules">+{{ day.schedules.length - 3 }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-card>
        </div>

        <!-- 排班录入对话框 -->
        <el-dialog v-model="showAddDialog" title="排班录入" width="600px" @close="handleDialogClose">
            <el-form ref="formRef" :model="scheduleForm" :rules="formRules" label-width="100px">
                <el-form-item label="日期" prop="scheduleDate">
                    <el-date-picker
                        v-model="scheduleForm.scheduleDate"
                        type="date"
                        placeholder="选择日期"
                        value-format="YYYY-MM-DD"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item label="班次" prop="shiftType">
                    <el-radio-group v-model="scheduleForm.shiftType">
                        <el-radio value="白班">白班</el-radio>
                        <el-radio value="晚班">晚班</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="班组" prop="teamName">
                    <el-select
                        v-model="scheduleForm.teamName"
                        placeholder="选择班组"
                        filterable
                        allow-create
                        default-first-option
                        style="width: 100%"
                    >
                        <el-option v-for="team in teamOptions" :key="team" :label="team" :value="team" />
                    </el-select>
                </el-form-item>

                <el-form-item label="姓名" prop="employeeName">
                    <el-input v-model="scheduleForm.employeeName" placeholder="请输入姓名" />
                </el-form-item>

                <el-form-item label="手机号" prop="phone">
                    <el-input v-model="scheduleForm.phone" placeholder="请输入手机号（选填）" />
                </el-form-item>

                <el-form-item label="是否班组长" prop="isLeader">
                    <el-radio-group v-model="scheduleForm.isLeader">
                        <el-radio :value="true">是</el-radio>
                        <el-radio :value="false">否</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="值班类型" prop="dutyType">
                    <el-select
                        v-model="scheduleForm.dutyType"
                        placeholder="选择值班类型"
                        filterable
                        allow-create
                        default-first-option
                        style="width: 100%"
                    >
                        <el-option v-for="type in dutyTypeOptions" :key="type" :label="type" :value="type" />
                    </el-select>
                </el-form-item>
            </el-form>

            <template #footer>
                <el-button @click="showAddDialog = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </template>
        </el-dialog>

        <!-- 日程详情对话框 -->
        <el-dialog v-model="showDetailDialog" :title="`${selectedDay?.date} 排班详情`" width="800px">
            <template #header>
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                    <span>{{ selectedDay?.date }} 排班详情</span>
                    <el-button type="primary" size="small" @click="handleAddScheduleForDay(selectedDay?.date)">
                        <el-icon>
                            <Plus />
                        </el-icon>
                        新增排班
                    </el-button>
                </div>
            </template>

            <el-table :data="selectedDay?.schedules || []" style="width: 100%">
                <el-table-column prop="employeeName" label="姓名" width="100" />
                <el-table-column prop="teamName" label="班组" width="120" />
                <el-table-column prop="shiftType" label="班次" width="80">
                    <template #default="scope">
                        <el-tag :type="scope.row.shiftType === '白班' ? 'primary' : 'warning'">
                            {{ scope.row.shiftType }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="phone" label="手机号" width="120" />
                <el-table-column prop="isLeader" label="班组长" width="80">
                    <template #default="scope">
                        <el-tag v-if="scope.row.isLeader" type="success">是</el-tag>
                        <el-tag v-else type="danger">否</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="dutyType" label="值班类型" width="100" />
                <!-- <el-table-column prop="category" label="排班类别" width="100">
                    <template #default="scope">
                        <el-tag :type="scope.row.category === 'emergency' ? 'danger' : 'primary'">
                            {{ scope.row.category === 'emergency' ? '应急排班' : '正常排班' }}
                        </el-tag>
                    </template>
                </el-table-column> -->
                <el-table-column label="操作" width="160">
                    <template #default="scope">
                        <el-button type="primary" size="small" @click="editSchedule(scope.row)">编辑</el-button>
                        <el-button type="danger" size="small" @click="deleteSchedule(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>

        <!-- 导入组件 - 根据category动态选择 -->
        <ScheduleImport
            v-if="category === 'emergency'"
            v-model="showImportDialog"
            :category="category"
            :project-id="projectId"
            @success="handleImportSuccess"
        />
        <ScheduleImportNormal
            v-else-if="category === 'normal'"
            v-model="showImportDialog"
            :category="category"
            :project-id="projectId"
            @success="handleImportSuccess"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, Download, UploadFilled, Delete } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useAppStore } from '@/store/modules/app'

import {
    getCalendarData,
    addSchedule,
    updateSchedule,
    delSchedule,
    getAllTeams,
    getAllDutyTypes,
    batchDeleteByCondition
} from '@/api/common/schedule'
import type { ScheduleVO, ScheduleForm } from '@/api/common/schedule/types'
import ScheduleImport from './ScheduleImport.vue'
import ScheduleImportNormal from './ScheduleImportNormal.vue'

// 定义组件属性
interface Props {
    category?: string
    projectId?: string | number
}

// 定义组件属性，设置默认值
const props = withDefaults(defineProps<Props>(), {
    category: 'normal',
    projectId: undefined
})

// 接口定义
interface CalendarDay {
    date: string
    day: number
    isOtherMonth: boolean
    isToday: boolean
    schedules: ScheduleVO[]
}

// 响应式数据
const appStore = useAppStore()
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const showImportDialog = ref(false)
const formRef = ref<FormInstance>()
const currentMonth = ref(
    (() => {
        const now = new Date()
        return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
    })()
)
const selectedDay = ref<CalendarDay | null>(null)

// 筛选条件
const filters = reactive({
    team: '',
    person: '',
    shift: ''
})

// 排班表单
const scheduleForm = reactive<Partial<ScheduleForm>>({
    scheduleDate: '',
    shiftType: '白班',
    teamName: '',
    employeeName: '',
    phone: '',
    isLeader: false,
    dutyType: '正常',
    category: props.category
})

// 日期验证函数
const validateDate = (rule: any, value: any, callback: any) => {
    if (!value) {
        callback(new Error('请选择日期'))
        return
    }

    // 检查日期格式是否正确
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/
    if (!dateRegex.test(value)) {
        callback(new Error('日期格式不正确'))
        return
    }

    // 检查日期是否有效
    const date = new Date(value)
    if (isNaN(date.getTime())) {
        callback(new Error('无效的日期'))
        return
    }

    // 检查日期字符串是否与实际日期匹配（防止类似2025-06-31这样的无效日期）
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    const expectedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`

    if (value !== expectedDate) {
        callback(new Error('无效的日期'))
        return
    }

    callback()
}

// 表单验证规则
const formRules: FormRules = {
    scheduleDate: [
        { required: true, message: '请选择日期', trigger: 'change' },
        { validator: validateDate, trigger: 'change' }
    ],
    shiftType: [{ required: true, message: '请选择班次', trigger: 'change' }],
    teamName: [{ required: true, message: '请输入班组', trigger: 'blur' }],
    employeeName: [
        { required: true, message: '请输入姓名', trigger: 'blur' },
        { min: 2, max: 10, message: '姓名长度应在2-10个字符之间', trigger: 'blur' }
    ],
    phone: [
        {
            pattern: /^$|^[0-9]{8,12}$/,
            message: '手机号应为11位数字，可不填',
            trigger: 'blur'
        }
    ],
    isLeader: [{ required: true, message: '请选择是否为班组长', trigger: 'change' }],
    dutyType: [{ required: true, message: '请选择值班类型', trigger: 'change' }]
}

// 响应式数据
const schedules = ref<ScheduleVO[]>([])

// 选项数据
const teamOptions = ref<string[]>([])
const dutyTypeOptions = ref<string[]>([])

// 计算属性
const weekdays = ['一', '二', '三', '四', '五', '六', '日']

// 在 calendarDays 计算属性之前添加一个计算属性来获取日历的日期范围
const calendarDateRange = computed(() => {
    const year = parseInt(currentMonth.value.split('-')[0])
    const month = parseInt(currentMonth.value.split('-')[1])

    const firstDay = new Date(year, month - 1, 1)
    const firstDayWeek = firstDay.getDay()

    // 计算日历的开始日期（包含上个月的日期）
    const startDate = new Date(year, month - 1, 1 - firstDayWeek)

    // 计算日历的结束日期（包含下个月的日期，确保42天）
    const endDate = new Date(startDate)
    endDate.setDate(startDate.getDate() + 41)

    return {
        startDate: startDate.toISOString().slice(0, 10),
        endDate: endDate.toISOString().slice(0, 10)
    }
})

const calendarDays = computed(() => {
    const year = parseInt(currentMonth.value.split('-')[0])
    const month = parseInt(currentMonth.value.split('-')[1])

    const firstDay = new Date(year, month - 1, 1)
    const lastDay = new Date(year, month, 0)
    const firstDayWeek = firstDay.getDay()
    const daysInMonth = lastDay.getDate()

    const days: CalendarDay[] = []
    const today = new Date().toISOString().slice(0, 10)

    // 调整为周一开始：0(周日)变为6，1(周一)变为0，2(周二)变为1...
    const adjustedFirstDayWeek = firstDayWeek === 0 ? 6 : firstDayWeek - 1

    // 上个月的日期
    const prevMonth = new Date(year, month - 1, 0) // 获取上个月的最后一天
    const prevMonthDays = prevMonth.getDate()
    for (let i = adjustedFirstDayWeek - 1; i >= 0; i--) {
        const day = prevMonthDays - i
        const prevYear = month === 1 ? year - 1 : year
        const prevMonthNum = month === 1 ? 12 : month - 1
        const date = `${prevYear}-${String(prevMonthNum).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        days.push({
            date,
            day,
            isOtherMonth: true,
            isToday: date === today,
            schedules: getSchedulesByDate(date)
        })
    }

    // 当前月的日期
    for (let day = 1; day <= daysInMonth; day++) {
        const date = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        days.push({
            date,
            day,
            isOtherMonth: false,
            isToday: date === today,
            schedules: getSchedulesByDate(date)
        })
    }

    // 下个月的日期
    const remainingDays = 42 - days.length
    const nextYear = month === 12 ? year + 1 : year
    const nextMonth = month === 12 ? 1 : month + 1
    for (let day = 1; day <= remainingDays; day++) {
        const date = `${nextYear}-${String(nextMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        days.push({
            date,
            day,
            isOtherMonth: true,
            isToday: date === today,
            schedules: getSchedulesByDate(date)
        })
    }

    return days
})

// 方法
const getSchedulesByDate = (date: string) => {
    let daySchedules = schedules.value.filter((s) => s.scheduleDate === date)

    // 应用筛选条件
    if (filters.team) {
        daySchedules = daySchedules.filter((s) => s.teamName === filters.team)
    }
    if (filters.person) {
        daySchedules = daySchedules.filter((s) => s.employeeName.includes(filters.person))
    }
    if (filters.shift) {
        daySchedules = daySchedules.filter((s) => s.shiftType === filters.shift)
    }

    // 确保只显示当前类别的排班
    daySchedules = daySchedules.filter((s) => s.category === props.category)

    return daySchedules
}

const handleMonthChange = async () => {
    await loadCalendarData()
}

const handleDayClick = (day: CalendarDay) => {
    selectedDay.value = day
    showDetailDialog.value = true
}

// 新增排班（通用）
const handleAddSchedule = () => {
    resetForm()
    showAddDialog.value = true
}

// 为指定日期新增排班
const handleAddScheduleForDay = (date?: string) => {
    resetForm()
    if (date) {
        scheduleForm.scheduleDate = date
    }
    showDetailDialog.value = false
    showAddDialog.value = true
}

const handleSubmit = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()

        // 额外验证日期有效性
        if (scheduleForm.scheduleDate) {
            const date = new Date(scheduleForm.scheduleDate)
            if (isNaN(date.getTime())) {
                ElMessage.error('无效的日期格式')
                return
            }

            // 检查日期字符串是否与实际日期匹配
            const year = date.getFullYear()
            const month = date.getMonth() + 1
            const day = date.getDate()
            const expectedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`

            if (scheduleForm.scheduleDate !== expectedDate) {
                ElMessage.error('无效的日期')
                return
            }
        }

        // 设置项目ID
        const formData = { ...scheduleForm } as ScheduleForm
        formData.projectId = props.projectId || appStore.projectContext.selectedProjectId

        if (scheduleForm.id) {
            // 修改
            await updateSchedule(formData)
            ElMessage.success('排班修改成功')
        } else {
            // 新增
            await addSchedule(formData)
            ElMessage.success('排班录入成功')
        }

        showAddDialog.value = false
        await loadCalendarData()
        await loadOptions()
        // 在最后重置表单，确保下次打开时是清空状态
        resetForm()
    } catch (error) {
        console.error('表单验证失败:', error)
    }
}

const resetForm = () => {
    // 先重置表单验证状态
    formRef.value?.resetFields()

    // 然后清空所有表单数据
    scheduleForm.id = undefined
    scheduleForm.projectId = undefined
    scheduleForm.scheduleDate = ''
    scheduleForm.shiftType = '白班'
    scheduleForm.teamName = ''
    scheduleForm.employeeName = ''
    scheduleForm.phone = ''
    scheduleForm.isLeader = false
    scheduleForm.dutyType = '正常'
    scheduleForm.category = props.category
}

const handleDialogClose = () => {
    // 对话框关闭时重置表单
    resetForm()
}

const editSchedule = (schedule: ScheduleVO) => {
    Object.assign(scheduleForm, {
        id: schedule.id,
        projectId: schedule.projectId,
        scheduleDate: schedule.scheduleDate,
        shiftType: schedule.shiftType,
        teamName: schedule.teamName,
        employeeName: schedule.employeeName,
        phone: schedule.phone,
        isLeader: schedule.isLeader,
        dutyType: schedule.dutyType,
        category: schedule.category || props.category
    })
    showDetailDialog.value = false
    showAddDialog.value = true
}

const deleteSchedule = async (id: string | number) => {
    try {
        await ElMessageBox.confirm('确定要删除这条排班记录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })

        await delSchedule(id)
        ElMessage.success('删除成功')
        await loadCalendarData()
    } catch (error) {
        // 用户取消删除
    }
}

const applyFilters = async () => {
    // 筛选逻辑已在计算属性中实现
    //ElMessage.success('筛选条件已应用');
    await loadCalendarData()
}

const resetFilters = async () => {
    filters.team = ''
    filters.person = ''
    filters.shift = ''
    //ElMessage.success('筛选条件已重置');
    await loadCalendarData()
}

// Excel导入相关方法
const handleExcelImport = () => {
    showImportDialog.value = true
}

const handleImportSuccess = () => {
    // 导入成功后刷新数据
    loadCalendarData()
    ElMessage.success('排班数据导入成功')
}

// 批量删除当月数据
const handleBatchDelete = async () => {
    try {
        const currentProjectId = props.projectId || appStore.projectContext.selectedProjectId
        if (!currentProjectId) {
            ElMessage.warning('请先选择项目')
            return
        }

        const confirmText = `确定要删除项目【${currentMonth.value}】下的所有排班数据吗？\n\n此操作不可恢复，请谨慎操作！`

        await ElMessageBox.confirm(confirmText, '批量删除确认', {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: false
        })

        // 执行批量删除
        await batchDeleteByCondition(String(currentProjectId), props.category, currentMonth.value)

        ElMessage.success('批量删除成功')

        // 刷新数据
        await loadCalendarData()
    } catch (error: any) {
        if (error !== 'cancel') {
            console.error('批量删除失败:', error)
            ElMessage.error('批量删除失败: ' + (error.message || error))
        }
    }
}

// 数据加载方法
const loadCalendarData = async () => {
    try {
        const dateRange = calendarDateRange.value
        const query = {
            startDate: dateRange.startDate,
            endDate: dateRange.endDate,
            projectId: appStore.projectContext.selectedProjectId, //props.projectId,
            category: props.category
        }

        const { data } = await getCalendarData(query)
        schedules.value = data
    } catch (error) {
        console.error('加载日历数据失败:', error)
        ElMessage.error('加载日历数据失败')
    }
}

const loadOptions = async () => {
    try {
        const [teamsRes, dutyTypesRes] = await Promise.all([getAllTeams(), getAllDutyTypes()])

        teamOptions.value = teamsRes.data
        dutyTypeOptions.value = dutyTypesRes.data
    } catch (error) {
        console.error('加载选项数据失败:', error)
    }
}

onMounted(async () => {
    // 检查项目ID
    const currentProjectId = props.projectId || appStore.projectContext.selectedProjectId
    if (!currentProjectId) {
        console.warn('排班页面 - 未选择项目，请先选择项目')
        ElMessage.warning('请先选择项目')
        return
    }

    await loadCalendarData()
    await loadOptions()
})
</script>

<style scoped lang="scss">
.schedule-container {
    padding: 20px;

    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    // :deep(.el-input__wrapper),
    // :deep(.el-select__wrapper),
    // :deep(.el-tree-select__wrapper),
    // :deep(.el-date-picker .el-input__wrapper) {
    //     // background: ;
    //     border-radius: 6px !important;
    //     box-shadow: none !important;
    //     min-height: 36px;
    //     height: 36px;
    //     padding: 5px 10px;
    // }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder),
    :deep(.el-tree-select__placeholder) {
        color: #8291A9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)),
    :deep(.el-tree-select .el-tree-select__selected-item > span:not(.el-tree-select__placeholder)) {
        color: #FFFFFF !important;
    }

    :deep(.el-select__wrapper.is-focused),
    :deep(.el-tree-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 日期选择器特殊样式 */
    :deep(.el-date-picker) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-date-picker .el-input__inner) {
        color: #FFFFFF !important;
        background: transparent !important;
    }

    :deep(.el-range-separator) {
        color: #8291A9 !important;
    }
}

.header-actions {
    margin-bottom: 20px;

    .action-buttons {
        margin-bottom: 20px;

        /* 头部操作按钮样式 */
        :deep(.el-button:not(.is-link)) {
            border-radius: 6px !important;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 14px !important;
            margin-right: 10px;
        }

        :deep(.el-button.el-button--primary) {
            background-color: #4286F3 !important;
            border-color: #4286F3 !important;
            color: #FFFFFF !important;
        }

        :deep(.el-button.el-button--success) {
            background-color: #67C23A !important;
            border-color: #67C23A !important;
            color: #FFFFFF !important;
        }

        :deep(.el-button.el-button--danger) {
            background-color: #F56C6C !important;
            border-color: #F56C6C !important;
            color: #FFFFFF !important;
        }

        :deep(.el-button:not(.el-button--primary):not(.el-button--success):not(.el-button--danger)) {
            background-color: #808892 !important;
            border-color: #808892 !important;
            color: #FFFFFF !important;
        }
    }

    .filter-section {
        .el-select {
            width: 100%;
        }

        /* 筛选操作按钮样式 */
        :deep(.el-button:not(.is-link)) {
            border-radius: 6px !important;
            height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 14px !important;
            margin-right: 10px;
        }

        :deep(.el-button.el-button--primary:not(.is-link)) {
            background-color: #4286F3 !important;
            border-color: #4286F3 !important;
            color: #FFFFFF !important;
        }

        :deep(.el-button:not(.el-button--primary):not(.is-link)) {
            background-color: #808892 !important;
            border-color: #808892 !important;
            color: #FFFFFF !important;
        }
    }
}

.calendar-section {
    :deep(.el-card__body) {
        padding: 0;
    }
    //background: rgba(50, 142, 234, 0.7);
    //backdrop-filter: blur(10px);
    //-webkit-backdrop-filter: blur(10px);
    //border-radius: 8px;

    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        //background: rgba(255, 255, 255, 0.7);

        .legend {
            display: flex;
            gap: 20px;

            .legend-item {
                display: flex;
                align-items: center;
                gap: 5px;

                .legend-color {
                    width: 12px;
                    height: 12px;
                    //border-radius: 2px;

                    &.day-shift {
                        background-color: #3380ff;
                    }

                    &.night-shift {
                        background-color: #ed00f7;
                    }

                    &.leader {
                        background-color: #fff;
                        border-radius: 50%;
                    }
                }
            }
        }
    }

    .calendar-grid {
        //background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #2d4a7a 100%);
        border-radius: 8px;
        padding: 10px 10px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        border: 0px solid rgba(255, 255, 255, 0.05);

        .calendar-weekdays {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;
            margin-bottom: 5px;

            .weekday {
                padding: 10px 10px;
                text-align: center;
                font-weight: bold;
                color: #ffffff;
                background: url('@/assets/images/<EMAIL>');
                background-size: cover;
                background-position: center bottom; /* 水平居中，垂直靠底部基线对齐 */
                background-repeat: no-repeat;
                border: 0.5px solid #1a2332;
                font-size: 28px;
                //line-height: 20px;
                border-radius: 12px;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
                position: relative;
            }
        }

        .calendar-days {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;

            .calendar-day {
                min-height: 150px;
                padding: 8px;
                background-color: #1a2332;
                //border: 0.5px solid #0f1419;
                cursor: pointer;
                //transition: background-color 0.2s;
                transition: all 0.3s ease; /* 所有属性平滑过渡 */
                border-radius: 12px;
                box-shadow:
                    0 0 3px rgba(59, 130, 246, 0.2),
                    0 0 6px rgba(59, 130, 246, 0.1);

                &:hover {
                    background-color: #243447;
                    /* 悬停时增强的光晕效果 */
                    box-shadow:
                        0 0 8px rgba(59, 130, 246, 0.6),
                        0 0 15px rgba(59, 130, 246, 0.4),
                        0 0 20px rgba(59, 130, 246, 0.2);

                    border-color: rgba(59, 130, 246, 0.6);
                }

                &.other-month {
                    color: #4a5568;
                    background-color: #0f1419;
                }

                &.today {
                    border: 2px solid #4299e1;
                    background-color: #2d3748;

                    .day-number {
                        color: #4299e1;
                        font-weight: bold;
                    }
                }

                .day-number {
                    font-weight: bold;
                    margin-bottom: 5px;
                    color: #cbd5e0;
                    font-size: 14px;
                }

                .day-schedules {
                    .schedule-item {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 5px 6px;
                        margin-bottom: 4px;
                        //border-radius: 4px;
                        font-size: 12px;
                        //box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

                        // 白班样式
                        &.day-shift {
                            /* 设置背景色并调整透明度为0.6 */
                            background-color: rgba(54, 112, 207, 20%);
                            color: white;
                            /* 为伪元素留出空间 */
                            padding-left: 20px;
                            position: relative; /* 确保伪元素相对于当前元素定位 */
                            &::before {
                                content: ''; /* 伪元素必须设置content属性 */
                                position: absolute;
                                left: 0;
                                top: 0;
                                bottom: 0;
                                width: 5px;
                                /* 相同底色但不设置透明度 */
                                background-color: rgba(51, 128, 255, 1);
                            }
                        }
                        // 晚班样式
                        &.night-shift {
                            /* 设置背景色并调整透明度为0.6 */
                            background-color: rgba(237, 0, 247, 20%);
                            color: white;
                            /* 为左侧伪元素留出空间 */
                            padding-left: 20px;
                            position: relative; /* 作为伪元素的定位容器 */
                            &::before {
                                content: ''; /* 伪元素必须的空内容 */
                                position: absolute;
                                left: 0;
                                top: 0;
                                bottom: 0;
                                width: 5px;
                                /* 与主体背景同色但不设置透明度 */
                                background-color: rgba(237, 0, 247, 1);
                            }
                        }

                        &.leader {
                            //border: 1px solid rgba(255, 255, 255, 0.3);
                            box-shadow: 0 2px 8px rgba(255, 255, 255, 0.15);
                            //background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
                        }

                        .schedule-name {
                            font-weight: 600;
                            display: flex;
                            align-items: center;
                            gap: 4px;

                            .leader-dot {
                                width: 6px;
                                height: 6px;
                                background-color: #fff;
                                border-radius: 50%;
                                display: inline-block;
                                flex-shrink: 0;
                            }
                        }

                        .schedule-team {
                            font-size: 10px;
                            opacity: 0.9;
                            font-weight: 500;
                            color: #a0aec0;
                        }
                    }

                    .more-schedules {
                        text-align: center;
                        font-size: 12px;
                        color: #a0aec0;
                        padding: 2px;
                        font-weight: 500;
                    }
                }
            }
        }
    }
}


.el-dialog {
    .el-form {
        .el-form-item {
            margin-bottom: 20px;
        }
    }
}
</style>
