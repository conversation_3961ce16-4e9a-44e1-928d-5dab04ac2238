<!-- 任务列表（月度视角）选择控件 -->
<template>
    <div class="task-month-list-container">
        <!-- 顶部日期选择区域 -->
        <div class="search-container">
            <div class="search-left">
                <el-date-picker
                    v-model="selectedMonth"
                    type="month"
                    format="YYYY年MM月"
                    value-format="YYYY-MM"
                    placeholder="选择年月"
                    @change="handleMonthChange"
                />
                <el-input v-model="planName" placeholder="请输入计划名称关键字" class="search-input" />
                <!-- <el-input v-model="frequency" placeholder="频次" type="number" :min="1" class="search-input" /> -->
                <el-button type="primary" @click="handleQuery">查询</el-button>
            </div>

            <!-- 筛选按钮区域 -->
            <div class="search-right">
                <div class="task-filters">
                    <!-- 任务类型筛选 -->
                    <div class="filter-group">
                        <div class="filter-buttons">
                            <button
                                v-for="option in taskTypeOptions"
                                :key="option.value"
                                :class="['filter-btn', { active: selectedTaskType === option.value }]"
                                @click="selectedTaskType = option.value"
                            >
                                {{ option.label }}
                            </button>
                        </div>
                    </div>

                    <!-- 任务状态筛选 -->
                    <div class="filter-group">
                        <div class="filter-buttons">
                            <button
                                v-for="option in taskStatusOptions"
                                :key="option.value"
                                :class="['filter-btn', { active: selectedTaskStatus === option.value }]"
                                @click="selectedTaskStatus = option.value"
                            >
                                {{ option.label }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 周任务列表区域 -->
        <div class="weeks-container">
            <!-- 当整个月都没有任务时显示提示 -->
            <div v-if="weeksList.length === 0 || weeksList.every((week) => week.tasks.length === 0)" class="no-month-task-tip">该月份暂无任务</div>

            <div v-for="(week, weekIndex) in weeksList" :key="weekIndex" class="week-column">
                <div class="week-header">
                    <span>{{ `第${getChineseNumber(weekIndex + 1)}周 ${week.startDate} 至 ${week.endDate}` }}</span>
                </div>
                <div
                    class="week-tasks"
                    :data-week-index="weekIndex"
                    :ref="
                        (el) => {
                            if (el) weekTaskRefs[weekIndex] = el
                        }
                    "
                    @dragover.prevent
                    @dragenter.prevent
                    @drop="handleDrop($event, weekIndex)"
                >
                    <!-- 当该周没有任务时显示提示 -->
                    <div v-if="getFilteredTasks(week.tasks).length === 0" class="no-task-tip">该周暂无任务</div>

                    <div
                        v-for="task in getFilteredTasks(week.tasks)"
                        :key="task.id"
                        class="task-item"
                        :class="{
                            'inspect': task.taskType === 'inspect',
                            'sealing': task.taskType === 'sealing'
                        }"
                        :draggable="!published"
                        @dragstart="handleDragStart($event, task, weekIndex)"
                        @dragover.prevent
                        @dragenter.prevent
                        @drop="handleDrop($event, weekIndex)"
                        @contextmenu.prevent="!published && showContextMenu($event, task, weekIndex)"
                        @click="handleTaskClick(task)"
                    >
                        <div class="task-content">
                            <span v-if="task.canSplited" class="split-icon" title="可拆分">
                                <el-icon><Plus /></el-icon>&nbsp;
                            </span>
                            <span v-if="!task.canSplited" class="split-icon">
                                <el-icon><Minus /></el-icon>&nbsp;
                            </span>
                            <span
                                class="task-name"
                                :class="{
                                    'disabled-task': published,
                                    'task-published': task.publishState === 'published',
                                    'task-unpublished': task.publishState === 'publish',
                                    'task-completed': task.currentStatus === 'END'
                                }"
                            >
                                {{ task.name }}
                            </span>
                        </div>
                        <div class="task-icons">
                            <!-- 可拆分状态 -->
                            <div class="splitable-status" v-if="false">
                                <template v-if="task.canSplited" title="可拆分">
                                    <img src="@/assets/images/<EMAIL>" class="status-icon schedulable" alt="可拆分" />
                                    <span>可拆分</span>
                                </template>
                                <template v-if="!task.canSplited" title="不可拆分">
                                    <img src="@/assets/images/<EMAIL>" class="status-icon" alt="不可拆分" />
                                    <span>不可拆分</span>
                                </template>
                            </div>
                            <!-- 完成状态 -->
                            <div class="completed-status">
                                <!-- 1. 最终状态优先：已完成 -->
                                <div v-if="task.currentStatus === 'END'" title="已完成">
                                    <img src="@/assets/images/<EMAIL>" class="status-icon schedulable" alt="已完成" />
                                    <span style="color: chartreuse">已完成</span>
                                </div>

                                <!-- 2. 最终状态优先：已终止 -->
                                <div v-else-if="task.currentStatus === 'TERMINATED'">
                                    <img src="@/assets/images/<EMAIL>" class="status-icon schedulable" alt="已终止" />
                                    <span style="color: red">已终止</span>
                                </div>

                                <!-- 3. 未发布状态 -->
                                <div v-else-if="task.publishState === 'publish'" title="待发布">
                                    <img src="@/assets/images/<EMAIL>" class="status-icon schedulable" alt="待发布" />
                                    <span>待发布</span>
                                </div>

                                <!-- 4. 已发布且进行中 -->
                                <div v-else-if="task.publishState === 'published'" title="进行中">
                                    <img src="@/assets/images/<EMAIL>" class="status-icon schedulable" alt="进行中" />
                                    <span>进行中</span>
                                </div>

                                <!-- 5. 兜底状态 -->
                                <div v-else title="未知状态">
                                    <img src="@/assets/images/<EMAIL>" class="status-icon schedulable" alt="未知" />
                                    <span style="color: gray">未知状态</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="btn-container">
            <span :class="{ 'blue': published }" style="margin-right: 10px">发布状态：{{ published ? '已发布' : '未发布' }}</span>
            <el-button
                type="primary"
                style="width: 100px"
                :disabled="published || isPublishing"
                @click="handlePublish"
                v-if="buttonsEnabled.publish"
                :loading="isPublishing"
            >
                {{ published ? '已发布' : isPublishing ? '发布中...' : '发布' }}
            </el-button>
        </div>
        <div class="remark">
            <ul>
                <li>说明：</li>
                <!-- <li>
                    1.<span style="color: white">白色 </span>表示未发布的月计划，<span style="color: #409eff"> 蓝色 </span>表示已发布的月计划，<span
                        style="color: gray"
                    >
                        灰色 </span
                    >表示已完成的月计划；
                </li> -->
                <li>【拆分说明】：月计划发布之前进行拆分，" + "表示可拆分，" - "表示不可拆分；</li>
                <!-- <li>3.排班：字体不加粗表示可排班，字体<span style="font-weight: bold"> 加粗 </span>表示不可排班；</li> -->
            </ul>
        </div>
        <!-- 右键菜单 -->
        <div v-show="contextMenuData.visible" class="context-menu" :style="{ left: contextMenuData.x + 'px', top: contextMenuData.y + 'px' }">
            <div v-if="contextMenuData.task && contextMenuData.task.canSplited" class="menu-item" @click="openSplitDialog">拆分</div>
        </div>

        <!-- 计划拆分组件 -->
        <PlanSplit ref="planSplitRef" @refresh="handleMonthChange" @split-success="handleSplitSuccess" />
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Minus } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { queryByProjectAndTime } from '@/api/plan/taskMonthView/index'
import { updateTaskMonth, publishTask } from '@/api/plan/taskMonth/index'
import { useAppStore } from '@/store/modules/app'
import PlanSplit from './TaskMonthSpliter/index.vue'
import type { TaskSplitResult } from '@/api/plan/taskMonth/types'

const appStore = useAppStore()
const router = useRouter()
const route = useRoute()

// 拆分组件引用
const planSplitRef = ref()

// 从路由参数获取年月，构建默认的selectedMonth值
const getDefaultSelectedMonth = () => {
    const { year, month } = route.query
    if (year && month) {
        // 确保月份是两位数格式
        const formattedMonth = String(month).padStart(2, '0')
        return `${year}-${formattedMonth}`
    }
    // 如果没有路由参数，使用当前年月
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentMonth = String(now.getMonth() + 1).padStart(2, '0')
    return `${currentYear}-${currentMonth}`
}
const buttonsEnabled = ref({
    publish: appStore.judgePermissionEnabled('monthplan:publish'),
    split: appStore.judgePermissionEnabled('monthplan:split'),
    arrange: appStore.judgePermissionEnabled('monthplan:arrange')
})

// 发布状态,true-已发布，false-未发布,只有未发布状态的任务可拆分和拖拽排序
// 全部项都是发布状态时，发布状态为true，否则为false
const published = ref(false)
// 发布按钮加载状态，防止重复点击
const isPublishing = ref(false)
// 当前选择的月份
const selectedMonth = ref(getDefaultSelectedMonth())
// 计划名称搜索
const planName = ref('')
// 频次搜索
const frequency = ref<number>()

// 周列表数据
const weeksList = ref([])

// 周任务DOM引用
const weekTaskRefs = ref({})

// 防重复刷新标志
const isRefreshing = ref(false)

// 筛选相关数据
const selectedTaskType = ref('all')
const selectedTaskStatus = ref('all')

// 任务类型选项
const taskTypeOptions = [
    { label: '全部', value: 'all' },
    { label: '维养', value: 'curing' },
    { label: '巡检', value: 'inspect' },
    { label: '封道', value: 'sealing' }
]

// 任务状态选项
const taskStatusOptions = [
    { label: '全部', value: 'all' },
    { label: '已完成', value: 'completed' },
    { label: '未完成', value: 'uncompleted' }
]

// 筛选任务函数
const getFilteredTasks = (tasks) => {
    return tasks.filter((task) => {
        // 任务类型筛选
        const typeMatch = selectedTaskType.value === 'all' || task.taskType === selectedTaskType.value

        // 任务状态筛选
        let statusMatch = true
        if (selectedTaskStatus.value === 'completed') {
            statusMatch = task.currentStatus === 'END'
        } else if (selectedTaskStatus.value === 'uncompleted') {
            statusMatch = task.currentStatus !== 'END'
        }

        return typeMatch && statusMatch
    })
}

// 拖拽数据
const dragData = reactive({
    task: null,
    sourceWeekIndex: -1
})

// 右键菜单数据
const contextMenuData = reactive({
    visible: false,
    x: 0,
    y: 0,
    task: null,
    weekIndex: -1
})

// 注意：原有的拆分对话框数据已移除，现在使用新的PlanSplit组件

// 将数字转换为中文数字
const getChineseNumber = (num: number): string => {
    const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
    if (num <= 10) {
        return chineseNumbers[num]
    }
    // 处理11-19的情况
    if (num < 20) {
        return '十' + chineseNumbers[num - 10]
    }
    // 处理20及以上的情况（一般月视图不会超过这个范围）
    const tens = Math.floor(num / 10)
    const ones = num % 10
    if (ones === 0) {
        return chineseNumbers[tens] + '十'
    } else {
        return chineseNumbers[tens] + '十' + chineseNumbers[ones]
    }
}

// 计算当月的周数和日期范围
const calculateWeeks = (yearMonth) => {
    const [year, month] = yearMonth.split('-').map(Number)
    const firstDay = new Date(year, month - 1, 1)
    const lastDay = new Date(year, month, 0)

    // 获取当月第一天是星期几 (0-6, 0是星期日)
    const firstDayOfWeek = firstDay.getDay()

    // 计算第一周的开始日期（如果第一天不是周一，则向后推到当月第一个周一）
    const firstWeekStart = new Date(firstDay)
    // 调整到当月第一个周一 (1是周一)
    // 如果第一天是周一(1)，不调整；否则向后推到第一个周一
    const adjust = firstDayOfWeek === 1 ? 0 : (8 - firstDayOfWeek) % 7
    firstWeekStart.setDate(firstWeekStart.getDate() + adjust)

    const weeks = []
    const currentDate = new Date(firstWeekStart)

    // 生成周数据，直到超过当月最后一天
    while (currentDate <= lastDay || weeks.length === 0) {
        const weekStartDate = new Date(currentDate)
        const weekEndDate = new Date(currentDate)
        weekEndDate.setDate(weekEndDate.getDate() + 6)

        // 格式化日期为 MM-DD 格式
        const formatDate = (date) => {
            return `${date.getMonth() + 1}-${date.getDate()}`
        }

        weeks.push({
            startDate: formatDate(weekStartDate),
            endDate: formatDate(weekEndDate),
            tasks: [] // 初始化空任务列表
        })

        // 移动到下一周的周一
        currentDate.setDate(currentDate.getDate() + 7)
    }

    return weeks
}

const generateApiTasks = async (yearMonth) => {
    const tasks = []
    //const weekCount = weeksList.value.length;

    try {
        // 调用api获取任务数据，并返回任务列表
        const projectId = appStore.projectContext.selectedProjectId
        const res = await queryByProjectAndTime({
            projectId: projectId,
            year: parseInt(yearMonth.split('-')[0]),
            month: parseInt(yearMonth.split('-')[1]),
            name: planName.value
        })
        // 如果有一项未发布，则发布状态为false
        let isPublished = true
        let checked = false
        res.data.forEach((item) => {
            // 过滤临时任务：排除tempTask为'YES'的任务，兼容老数据（空字符串或null视为正常任务）
            if (item.tempTask === 'YES') {
                return // 跳过临时任务
            }

            tasks.push({
                id: item.id,
                name: item.name + '（' + item.frequency + '次/' + item.frequencyType + '）',
                week: item.week,
                sort: item.sort,
                publishState: item.publishState,
                currentStatus: item.currentStatus,
                taskType: item.taskType,
                defineId: item.defineId, // 添加任务定义ID
                //canSplited: true,
                canSplited: item.splitable == 1 && item.publishState == 'publish' // 基于后端splitable字段判断且未发布时可拆分，如果splitable字段不存在则回退到taskType判断
            })
            checked = true
            if (item.publishState != 'published') {
                isPublished = false
            }
        })
        if (checked) {
            published.value = isPublished
        }
        console.log('获取任务数据成功:', tasks)
    } catch (error) {
        console.error('获取任务数据失败:', error)
        ElMessage.error('获取任务数据失败')
    }

    return tasks
}

// 查询按钮点击事件
const handleQuery = async () => {
    if (!selectedMonth.value) {
        ElMessage.warning('请先选择年月')
        return
    }

    // 重新加载数据
    await handleMonthChange()
}

// 更新月度任务实体
const updateMonthTaskEntity = async (taskId: string | number, week: number, sort: number) => {
    try {
        // 构建更新数据
        const updateData = {
            id: taskId,
            week: week,
            sort: sort
        }

        // 调用后端更新接口
        await updateTaskMonth(updateData)
        ElMessage.success('任务更新成功')
        console.log('任务更新成功:', {
            taskId: taskId,
            week: week,
            sort: sort
        })
    } catch (error: any) {
        console.error('更新任务失败:', error)
        ElMessage.error('更新任务失败: ' + (error?.message || '未知错误'))
    }
}

// 处理月份变更
const handleMonthChange = async () => {
    if (!selectedMonth.value) return

    // 防重复刷新检查
    if (isRefreshing.value) {
        console.log('正在刷新中，跳过重复请求')
        return
    }

    try {
        isRefreshing.value = true
        console.log('开始刷新任务列表...')

        // 计算周列表（这会重新初始化weeksList，清空所有tasks）
        weeksList.value = calculateWeeks(selectedMonth.value)

        // 从api获取任务数据
        const tasks = await generateApiTasks(selectedMonth.value)
        console.log(`获取到 ${tasks.length} 个任务`)

        // 将任务分配到对应的周
        tasks.forEach((task) => {
            const weekIndex = task.week - 1
            if (weekIndex >= 0 && weekIndex < weeksList.value.length) {
                weeksList.value[weekIndex].tasks.push(task)
                console.log(`任务 ${task.name} 分配到第 ${task.week} 周`)
            }
        })

        // 按排序号排序每周的任务
        weeksList.value.forEach((week) => {
            week.tasks.sort((a, b) => a.sort - b.sort)
        })

        console.log('任务列表刷新完成')

        // 等待DOM更新后初始化拖拽
        nextTick(() => {
            initDragEvents()
        })
    } finally {
        // 延迟重置刷新标志，避免快速连续调用
        setTimeout(() => {
            isRefreshing.value = false
        }, 500)
    }
}

// 初始化拖拽事件
const initDragEvents = () => {
    // 拖拽事件已在模板中通过@dragstart等指令绑定
}

// 处理拖拽开始
const handleDragStart = (event, task, weekIndex) => {
    if (!buttonsEnabled.value.arrange) return
    dragData.task = task
    dragData.sourceWeekIndex = weekIndex

    // 设置拖拽数据
    event.dataTransfer.setData(
        'text/plain',
        JSON.stringify({
            taskId: task.id,
            sourceWeekIndex: weekIndex
        })
    )

    // 设置拖拽效果
    event.dataTransfer.effectAllowed = 'move'
}

// 处理拖拽放置
const handleDrop = async (event, targetWeekIndex) => {
    event.preventDefault()

    if (!dragData.task) return

    const sourceWeekIndex = dragData.sourceWeekIndex
    const task = dragData.task

    // 如果是同一周内拖拽，只改变排序
    if (sourceWeekIndex === targetWeekIndex) {
        // 获取目标位置的任务索引
        const targetElement = event.target.closest('.task-item')
        if (targetElement) {
            const taskElements = Array.from(weekTaskRefs.value[targetWeekIndex].querySelectorAll('.task-item'))
            const targetIndex = taskElements.indexOf(targetElement)

            // 重新排序任务
            const tasks = [...weeksList.value[targetWeekIndex].tasks]
            const sourceIndex = tasks.findIndex((t) => t.id === task.id)

            if (sourceIndex !== -1) {
                const [movedTask] = tasks.splice(sourceIndex, 1)
                tasks.splice(targetIndex, 0, movedTask)

                // 保存原始排序号，用于比较变化
                const originalSortOrders = tasks.map((t) => ({ id: t.id, originalSort: t.sort }))

                // 更新排序号
                tasks.forEach((t, index) => {
                    t.sort = index + 1
                })

                // 输出排序变化的任务
                const changedTasks = tasks.filter((t, index) => {
                    const original = originalSortOrders.find((o) => o.id === t.id)
                    return original && original.originalSort !== t.sort
                })

                if (changedTasks.length > 0) {
                    console.log(
                        '同周内任务排序变化：',
                        changedTasks.map((t) => ({
                            id: t.id,
                            week: t.week,
                            name: t.name,
                            newSort: t.sort
                        }))
                    )

                    // 批量更新排序变化的任务
                    changedTasks.forEach(async (changedTask) => {
                        await updateMonthTaskEntity(changedTask.id, changedTask.week, changedTask.sort)
                    })
                }

                weeksList.value[targetWeekIndex].tasks = tasks
            }
        }
    } else {
        // 跨周拖拽，改变任务所属周
        // 从源周移除任务
        const sourceWeekTasks = [...weeksList.value[sourceWeekIndex].tasks]
        const taskIndex = sourceWeekTasks.findIndex((t) => t.id === task.id)

        if (taskIndex !== -1) {
            const [movedTask] = sourceWeekTasks.splice(taskIndex, 1)
            const originalWeek = movedTask.week
            const originalSort = movedTask.sort

            weeksList.value[sourceWeekIndex].tasks = sourceWeekTasks

            // 更新任务的周数
            movedTask.week = targetWeekIndex + 1

            // 添加到目标周的指定位置
            const targetWeekTasks = [...weeksList.value[targetWeekIndex].tasks]

            // 获取目标位置的任务索引
            let insertIndex = targetWeekTasks.length // 默认添加到末尾

            // 检查目标周是否有任务项
            if (targetWeekTasks.length > 0) {
                const targetElement = event.target.closest('.task-item')
                if (targetElement) {
                    const taskElements = Array.from(weekTaskRefs.value[targetWeekIndex].querySelectorAll('.task-item'))
                    const targetIndex = taskElements.indexOf(targetElement)
                    if (targetIndex !== -1) {
                        insertIndex = targetIndex
                    }
                }
            }
            // 注意：如果目标周没有任务项，insertIndex保持为0，任务将被添加到列表开头

            // 在指定位置插入任务
            targetWeekTasks.splice(insertIndex, 0, movedTask)

            // 更新排序号
            targetWeekTasks.forEach((t, index) => {
                t.sort = index + 1
            })

            weeksList.value[targetWeekIndex].tasks = targetWeekTasks

            // 输出跨周移动的任务信息
            console.log('跨周任务移动：', {
                id: movedTask.id,
                name: movedTask.name,
                fromWeek: originalWeek,
                toWeek: movedTask.week,
                fromSort: originalSort,
                toSort: movedTask.sort
            })
            // 需要将任务信息保存到数据库
            await updateMonthTaskEntity(movedTask.id, movedTask.week, movedTask.sort)

            // 保存源周任务的原始排序号
            const originalSortOrders = sourceWeekTasks.map((t) => ({ id: t.id, originalSort: t.sort }))

            // 更新源周的排序号
            sourceWeekTasks.forEach((t, index) => {
                t.sort = index + 1
            })

            // 输出源周排序变化的任务
            const changedSourceTasks = sourceWeekTasks.filter((t, index) => {
                const original = originalSortOrders.find((o) => o.id === t.id)
                return original && original.originalSort !== t.sort
            })

            if (changedSourceTasks.length > 0) {
                console.log(
                    '源周任务排序变化：',
                    changedSourceTasks.map((t) => ({
                        id: t.id,
                        week: t.week,
                        name: t.name,
                        newSort: t.sort
                    }))
                )

                // 批量更新源周排序变化的任务
                changedSourceTasks.forEach(async (changedTask) => {
                    await updateMonthTaskEntity(changedTask.id, changedTask.week, changedTask.sort)
                })
            }
        }
    }

    // 清除拖拽数据
    dragData.task = null
    dragData.sourceWeekIndex = -1
}

// 显示右键菜单
const showContextMenu = (event, task, weekIndex) => {
    console.log('showContextMenu', task)

    event.preventDefault() // 阻止默认右键菜单
    console.log('task.canSplited', task.canSplited)
    // 只有可拆分的任务且未发布状态才显示右键菜单
    if (task.canSplited && buttonsEnabled.value.split) {
        contextMenuData.visible = true
        contextMenuData.x = event.clientX
        contextMenuData.y = event.clientY
        contextMenuData.task = task
        contextMenuData.weekIndex = weekIndex

        // 添加全局点击事件，用于关闭右键菜单
        document.addEventListener('click', closeContextMenu)
    }
}

// 关闭右键菜单
const closeContextMenu = () => {
    contextMenuData.visible = false
    document.removeEventListener('click', closeContextMenu)
}

// 打开拆分对话框
const openSplitDialog = async () => {
    const task = contextMenuData.task
    if (!task) return

    // 关闭右键菜单
    closeContextMenu()

    // 构建计划信息
    const planInfo = {
        id: task.id,
        name: task.name,
        taskType: task.taskType,
        year: parseInt(selectedMonth.value.split('-')[0]),
        month: parseInt(selectedMonth.value.split('-')[1]),
        defineId: task.defineId, // 从任务对象中获取defineId
        projectId: appStore.projectContext.selectedProjectId?.toString() || ''
    }

    // 调用拆分组件
    if (planSplitRef.value) {
        if (task.taskType === 'curing') {
            // 调用维养拆分
            await planSplitRef.value.openMaintenanceSplit(planInfo)
        } else {
            // 调用巡检拆分
            await planSplitRef.value.openInspectionSplit(planInfo, true) // 第二个参数表示跳过状态检查
        }
    }
}

const handleTaskClick = (task) => {
    console.log('task', task)
    router.push({
        path: '/subProject/circle/plan/baseInfo',
        query: {
            taskType: task.taskType,
            id: task.id,
            from: 'month'
        }
    })
}

// 处理拆分成功
const handleSplitSuccess = (result: TaskSplitResult) => {
    console.log('拆分成功:', result)
    ElMessage.success(`拆分成功！拆分出 ${result.splitCount} 个新计划`)

    // 刷新任务列表
    handleMonthChange()
}

const handlePublish = async () => {
    // 防止重复点击
    if (isPublishing.value) {
        return
    }

    try {
        // 设置发布状态为进行中
        isPublishing.value = true

        // 检查必要参数
        if (!selectedMonth.value) {
            ElMessage.warning('请先选择年月')
            isPublishing.value = false
            return
        }

        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            ElMessage.warning('请先选择项目')
            isPublishing.value = false
            return
        }

        // 解析年月
        const [year, month] = selectedMonth.value.split('-').map(Number)

        // 检查是否有筛选条件
        const hasFilter = planName.value && planName.value.trim()
        let confirmMessage = `确认发布 ${year}年${month}月 的月度任务计划吗？`
        let taskMonthIds = undefined

        if (hasFilter) {
            // 收集当前显示的所有任务ID（筛选后的任务）
            const currentTaskIds = []
            weeksList.value.forEach((week) => {
                week.tasks.forEach((task) => {
                    currentTaskIds.push(task.id)
                })
            })

            if (currentTaskIds.length === 0) {
                ElMessage.warning('没有找到符合筛选条件的任务')
                isPublishing.value = false
                return
            }

            taskMonthIds = currentTaskIds
            confirmMessage = `确认发布 ${year}年${month}月 筛选出的 ${currentTaskIds.length} 个任务吗？`
        }

        // 确认发布操作
        const confirmResult = await ElMessageBox.confirm(confirmMessage, '发布确认', {
            confirmButtonText: '确认发布',
            cancelButtonText: '取消',
            type: 'warning'
        }).catch(() => false)

        if (!confirmResult) {
            // 用户取消发布，重置状态
            isPublishing.value = false
            return
        }

        // 显示加载状态
        ElMessage.info('正在发布任务...')

        // 构建发布参数 (PublishTaskParams类型)
        const publishParams: any = {
            year,
            month,
            projectId
        }

        // 如果有筛选条件，添加任务ID数组
        if (taskMonthIds && taskMonthIds.length > 0) {
            publishParams.taskMonthIds = taskMonthIds
        }

        // 调用发布接口
        await publishTask(publishParams)

        if (hasFilter) {
            ElMessage.success(`任务发布成功！已发布 ${taskMonthIds.length} 个任务`)
            console.log('筛选发布成功:', {
                year: year,
                month: month,
                projectId: projectId,
                filter: planName.value,
                taskMonthIds: taskMonthIds,
                taskCount: taskMonthIds.length
            })
        } else {
            ElMessage.success('任务发布成功！')
            console.log('全量发布成功:', {
                year: year,
                month: month,
                projectId: projectId
            })
        }
        await handleMonthChange()
    } catch (error: any) {
        console.error('发布任务失败:', error)
        ElMessage.error('发布任务失败: ' + (error?.message || '未知错误'))
    } finally {
        // 无论成功或失败，都要重置发布状态
        isPublishing.value = false
    }
}

// 组件挂载时初始化数据
onMounted(async () => {
    await handleMonthChange()
})
</script>

<style lang="scss" scoped>
.task-month-list-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    //padding: 16px;
}

.disabled-task {
    //opacity: 0.6;
    cursor: pointer;
    //pointer-events: none;
}

.search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    gap: 20px;

    .search-left {
        display: flex;
        align-items: center;
        gap: 10px;
        flex: 1;

        .search-input {
            width: 200px;
        }
    }

    .search-right {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .task-filters {
            display: flex;
            gap: 8px;
            align-items: center;

            .filter-group {
                .filter-buttons {
                    display: flex;
                    border-radius: 16px;
                    overflow: hidden;
                    border: 1px solid rgba(226, 221, 221, 0.2);
                    background: rgba(0, 0, 0, 0.05);

                    .filter-btn {
                        padding: 4px 10px;
                        background: transparent;
                        border: none;
                        color: #c8c3c3;
                        font-size: 12px;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        white-space: nowrap;

                        &:hover {
                            background: rgba(64, 158, 255, 0.1);
                            color: #409eff;
                        }

                        &.active {
                            background: #409eff;
                            color: white;
                        }
                    }
                }
            }
        }
    }
}

.weeks-container {
    display: flex;
    flex-wrap: nowrap;
    height: 100%;
    overflow-y: auto;
    border: 0px solid #e4e7ed;
    border-radius: 8px;
}

.week-column {
    flex: 1;
    min-width: 150px;
    display: flex;
    flex-direction: column;
    border-right: 0px solid #e4e7ed;

    &:last-child {
        border-right: none;
    }
}

.week-header {
    height: 80px;
    margin: 0 5px;
    line-height: 80px;
    //padding: 10px 10px;
    font-weight: bold;
    font-size: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-bottom: 0px solid #e4e7ed;
    color: #fff;
    background-image: url('@/assets/images/<EMAIL>');
    //background-image: url('@/assets/images/<EMAIL>');
    //background-size: 80% auto; /* 宽度缩小为50%，高度自动按比例缩放 */
    background-size: cover; /* 宽度缩小为50%，高度自动按比例缩放 */
    background-position: center left;
    background-repeat: no-repeat;
    border-radius: 12px;
}

.week-tasks {
    flex: 1;
    padding: 8px 5px;
    overflow-y: auto;
    min-height: 400px;
    // 自定义滚动条样式
    &::-webkit-scrollbar {
        width: 2px; // 滚动条宽度
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f180; // 轨道背景
        border-radius: 2px; // 轨道圆角
    }

    &::-webkit-scrollbar-thumb {
        background: #ccc; // 滑块颜色
        border-radius: 2px; // 滑块圆角

        &:hover {
            background: #aaa; // 悬停时的滑块颜色
        }
    }
}

.no-task-tip {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    padding: 20px;
    margin-top: 50px;
}

.no-month-task-tip {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 18px;
    padding: 60px 20px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.task-item {
    margin-bottom: 4px;
    min-height: 50px;
    padding: 10px 10px;
    font-size: 14px;
    border-radius: 8px;
    //border: 1px solid rgba(255, 255, 255, 0.1);
    background-image: url('@/assets/images/<EMAIL>');
    background-size: 30% auto;
    background-position: center bottom;
    background-repeat: repeat-x;
    //background-color: rgba(255, 255, 255, 0.2);
    background-color: #394664a3;
    cursor: move;
    transition: all 0.2s;
    position: relative;
    color: #fff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    border-bottom: 1px solid rgba(255, 255, 255);

    &[draggable='true'] {
        cursor: move;
    }

    &:hover {
        background-color: rgba(97, 101, 95, 0.8);
        font-weight: bold;
    }

    &:last-child {
        margin-bottom: 0;
    }

    .task-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .split-icon {
            font-size: 12px;
            font-weight: bold;
            color: #fcfcfc;
            &.circle {
                background-color: white;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }
        }

        .task-name {
            //font-weight: bold;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
        }
    }

    // 任务图标区域
    .task-icons {
        margin-top: 10px;
        display: flex;
        align-items: center;
        gap: 8px;
        .status-icon {
            width: 16px;
            height: 16px;
        }
    }

    .splitable-status,
    .completed-status {
        padding: 4px 8px;
        background-color: rgba(8, 24, 61, 0.3);
        border-radius: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        font-size: 12px;
        color: #ffffffcf;

        span {
            white-space: nowrap;
        }

        div {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        img {
            width: 16px;
            height: 16px;
            filter: brightness(0.8);
        }
    }

    //巡检背景色
    &.inspect {
        background-color: rgba(5, 76, 79, 0.7);
        color: rgba(0, 255, 255, 1);
        border-bottom: 1px solid rgba(0, 255, 255, 1);
    }
    //封道背景色
    &.sealing {
        background-color: rgba(109, 70, 27, 0.28);
        color: rgba(254, 192, 83, 1);
        border-bottom: 1px solid rgba(254, 192, 83, 1);
    }
}

// 任务状态颜色样式
// .task-published {
//     color: #409eff;
// }

// .task-unpublished {
//     color: #fff;
// }

// .task-completed {
//     color: gray;
// }

.context-menu {
    position: fixed;
    z-index: 1000;
    background-color: #2a2a2a;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
    padding: 5px 0;
}

.menu-item {
    padding: 8px 16px;
    cursor: pointer;
    color: #fff;

    &:hover {
        background-color: #3a3a3a;
    }
}

.remark {
    color: #ada9a9;

    ul,
    li {
        list-style: none;
        padding: 2px;
    }
}

.btn-container {
    margin-top: 10px;
    margin-left: auto;
    .publishState {
        margin-left: auto;
        color: #fff;
        font-size: 18px;
        font-weight: bold;

        &.blue {
            color: #409eff;
        }
    }
}
</style>
