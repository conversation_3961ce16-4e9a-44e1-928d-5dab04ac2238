<template>
    <div class="p-2 health-warning-page">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px">
                        <el-form-item label="开始时间" prop="startTime">
                            <el-date-picker
                                v-model="queryParams.startTime"
                                type="datetime"
                                placeholder="请选择开始时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                clearable
                            />
                        </el-form-item>
                        <el-form-item label="结束时间" prop="endTime">
                            <el-date-picker
                                v-model="queryParams.endTime"
                                type="datetime"
                                placeholder="请选择结束时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                clearable
                            />
                        </el-form-item>
                        <el-form-item label="隧道名称" prop="tunnelName">
                            <el-input v-model="queryParams.tunnelName" placeholder="请输入隧道名称" clearable />
                        </el-form-item>
                        <el-form-item label="告警来源" prop="alarmSource" v-if="false">
                            <el-select v-model="queryParams.alarmSource" placeholder="请选择告警来源" clearable v-if="false">
                                <el-option label="系统检测" value="系统检测" />
                                <el-option label="人工上报" value="人工上报" />
                                <el-option label="定期巡检" value="定期巡检" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="设备类型" prop="alarmEquipmentType" v-if="false">
                            <el-select v-model="queryParams.alarmEquipmentType" placeholder="请选择设备类型" clearable>
                                <el-option label="监控摄像" value="监控摄像" />
                                <el-option label="电话" value="电话" />
                                <el-option label="泵房设备" value="泵房设备" />
                                <el-option label="其他设备" value="其他设备" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="维修状态" prop="status" v-if="false">
                            <el-select v-model="queryParams.status" placeholder="请选择维修状态" clearable>
                                <el-option label="未维修" value="未维修" />
                                <el-option label="维修中" value="维修中" />
                                <el-option label="已维修" value="已维修" />
                            </el-select>
                        </el-form-item>
                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">搜索</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <el-table v-loading="loading" :data="pumpWarningList" @selection-change="handleSelectionChange" stripe>
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="告警日期" align="center" prop="alarmDate" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.alarmDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="隧道名称" align="center" prop="tunnelName" />
                <el-table-column label="故障位置" align="center" prop="position" />
                <el-table-column label="告警来源" align="center" prop="alarmSource" />
                <el-table-column label="设备类型" align="center" prop="alarmEquipmentType" />
                <el-table-column label="维修状态" align="center" prop="status">
                    <template #default="scope">
                        <el-tag :type="getStatusType(scope.row.status)" effect="plain">
                            {{ scope.row.status }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-tooltip content="查看详情" placement="top">
                            <el-button link type="primary" icon="View" @click="handleViewDetails(scope.row)"></el-button>
                        </el-tooltip>
                        <el-tooltip content="标记已维修" placement="top" v-if="scope.row.status !== '已维修'">
                            <el-button link type="success" icon="Check" @click="handleMarkRepaired(scope.row)"></el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>

        <!-- 详情查看对话框 -->
        <el-dialog title="泵房故障详情" v-model="detailDialog.visible" width="600px" append-to-body>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="告警日期">
                    {{ parseTime(detailDialog.data?.alarmDate, '{y}-{m}-{d} {h}:{i}:{s}') }}
                </el-descriptions-item>
                <el-descriptions-item label="隧道名称">
                    {{ detailDialog.data?.tunnelName }}
                </el-descriptions-item>
                <el-descriptions-item label="故障位置">
                    {{ detailDialog.data?.position }}
                </el-descriptions-item>
                <el-descriptions-item label="告警来源">
                    {{ detailDialog.data?.alarmSource }}
                </el-descriptions-item>
                <el-descriptions-item label="设备类型">
                    {{ detailDialog.data?.alarmEquipmentType }}
                </el-descriptions-item>
                <el-descriptions-item label="维修状态">
                    <el-tag :type="getStatusType(detailDialog.data?.status)" effect="plain">
                        {{ detailDialog.data?.status }}
                    </el-tag>
                </el-descriptions-item>
            </el-descriptions>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="detailDialog.visible = false">关 闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { getPumpWarning } from '@/api/subProject/health/pump';
import type { PumpWarningQuery, PumpWarningVO } from '@/api/subProject/health/pump/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const pumpWarningList = ref<PumpWarningVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();

const detailDialog = reactive({
    visible: false,
    data: null as PumpWarningVO | null
});

const queryParams = ref<PumpWarningQuery>({
    pageNum: 1,
    pageSize: 10,
    startTime: undefined,
    endTime: undefined,
    tunnelName: undefined,
    alarmSource: undefined,
    alarmEquipmentType: undefined,
    status: undefined
});

/** 查询泵房故障列表 */
const getList = async () => {
    loading.value = true;
    try {
        const res = await getPumpWarning(queryParams.value);
        pumpWarningList.value = res.rows || [];
        total.value = res.total || 0;
    } catch (error) {
        console.error('获取泵房故障数据失败:', error);
        pumpWarningList.value = [];
        total.value = 0;
        proxy?.$modal.msgError('获取数据失败');
    } finally {
        loading.value = false;
    }
};

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields();
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
        startTime: undefined,
        endTime: undefined,
        tunnelName: undefined,
        alarmSource: undefined,
        alarmEquipmentType: undefined,
        status: undefined
    };
    handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PumpWarningVO[]) => {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
};

/** 查看详情 */
const handleViewDetails = (row: PumpWarningVO) => {
    detailDialog.data = row;
    detailDialog.visible = true;
};

/** 标记已维修 */
const handleMarkRepaired = async (row: PumpWarningVO) => {
    try {
        await proxy?.$modal.confirm(`确认将"${row.tunnelName} - ${row.position}"的故障标记为已维修？`);
        // 这里需要调用标记已维修的API，目前先模拟更新状态
        row.status = '已维修';
        proxy?.$modal.msgSuccess('标记成功');
        // 实际应该调用后端API更新状态，然后重新获取列表
        // await markPumpWarningRepaired(row.id);
        // await getList();
    } catch (error) {
        console.log('取消操作');
    }
};

/** 获取状态标签类型 */
const getStatusType = (status: string | undefined) => {
    switch (status) {
        case '未维修':
            return 'danger';
        case '维修中':
            return 'warning';
        case '已维修':
            return 'success';
        default:
            return 'info';
    }
};

onMounted(() => {
    getList();
});
</script>
<style scoped lang="scss">
.health-warning-page {
    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-tree-select__wrapper),
    :deep(.el-date-editor .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder),
    :deep(.el-tree-select__placeholder) {
        color: #8291A9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)),
    :deep(.el-tree-select .el-tree-select__selected-item > span:not(.el-tree-select__placeholder)) {
        color: #FFFFFF !important;
    }

    :deep(.el-select__wrapper.is-focused),
    :deep(.el-tree-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 日期选择器特殊样式 */
    :deep(.el-date-editor) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-date-editor .el-input__inner) {
        color: #FFFFFF !important;
        background: transparent !important;
    }

    :deep(.el-range-separator) {
        color: #8291A9 !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #AED7F2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #FFFFFF !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }
}
</style>
