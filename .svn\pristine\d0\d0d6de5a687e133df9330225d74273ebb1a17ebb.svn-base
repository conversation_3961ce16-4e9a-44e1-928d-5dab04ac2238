<!-- 设备巡检配置组件 -->
<template>
    <el-dialog :title="title" :model-value="visible" :draggable="true" @update:model-value="handleDialogClose" width="1400px" append-to-body>
        <div class="equipment-config-container">
            <!-- 左侧：设备查询和列表区域 -->
            <div class="left-panel">
                <div class="panel-header">
                    <h4>设备选择</h4>
                </div>
                <div class="equipment-area">
                    <!-- 查询条件区域 -->
                    <div class="search-form">
                        <el-form ref="queryFormRef" :model="queryParams" :rules="queryRules" label-width="80px">
                            <el-row :gutter="16">
                                <!-- 管理单元（必选） -->
                                <el-col :span="8">
                                    <el-form-item prop="unitId" label="管理单元">
                                        <el-select
                                            v-model="queryParams.unitId"
                                            placeholder="请选择管理单元"
                                            clearable
                                            @change="handleUnitChange"
                                            style="width: 100%"
                                        >
                                            <el-option v-for="unit in manageUnits" :key="unit.id" :label="unit.name" :value="unit.id" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>

                                <!-- 设备分类（必选，只能选择最后一级） -->
                                <el-col :span="8">
                                    <el-form-item prop="categoryPath" label="设备分类">
                                        <el-cascader
                                            v-model="queryParams.categoryPath"
                                            :options="electricCategories"
                                            :props="{
                                                value: 'id',
                                                label: 'name',
                                                children: 'children',
                                                checkStrictly: false,
                                                emitPath: true
                                            }"
                                            placeholder="请选择设备分类"
                                            clearable
                                            filterable
                                            :loading="categoriesLoading"
                                            @change="handleCategoryChange"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="设备名称">
                                        <el-input
                                            v-model="queryParams.name"
                                            placeholder="请输入设备名称"
                                            clearable
                                            @keyup.enter="handleSearch"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <!-- 房间（可选）
                                <el-col :span="8">
                                    <el-form-item label="房间">
                                        <el-select v-model="queryParams.roomId" placeholder="请选择房间" clearable style="width: 100%">
                                            <el-option v-for="room in rooms" :key="room.id" :label="room.roomNane" :value="room.id" />
                                        </el-select>
                                    </el-form-item>
                                </el-col> -->
                            </el-row>

                            <el-row :gutter="16">
                                <!-- 设备名称（可选） -->

                                <!-- 查询按钮 -->
                                <el-col :span="16">
                                    <el-form-item>
                                        <el-button type="primary" icon="Search" @click="handleSearch" :disabled="!canSearch"> 搜索设备 </el-button>
                                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>

                                        <!-- 查询状态提示 -->
                                        <el-text v-if="!canSearch" type="warning" size="small" style="margin-left: 10px">
                                            请先选择管理单元和设备分类
                                        </el-text>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>

                    <!-- 设备列表区域 -->
                    <div class="equipment-list">
                        <!-- 列表提示信息 -->
                        <div class="list-header">
                            <div v-if="!canSearch" class="search-tip">
                                <el-alert title="请先选择管理单元和设备分类，然后点击搜索" type="info" :closable="false" show-icon />
                            </div>

                            <div v-else-if="equipmentList.length > 0" class="list-info">
                                <el-text type="info"> 共找到 {{ total }} 个未配置巡检项的设备 </el-text>
                                <el-text type="warning" size="small" v-if="selectedInspectionItems.length === 0"> 请在右侧选择巡检配置项 </el-text>
                            </div>
                        </div>

                        <!-- 设备表格 -->
                        <el-table
                            v-loading="loading"
                            :data="equipmentList"
                            @selection-change="handleEquipmentSelectionChange"
                            height="400"
                            empty-text="请先选择查询条件并点击搜索"
                        >
                            <!-- <el-table-column type="selection" width="50" align="center" /> -->
                            <el-table-column label="设备名称" prop="remark" show-overflow-tooltip width="200" />
                            <el-table-column label="备注" prop="name" show-overflow-tooltip width="200" />
                            <!-- <el-table-column label="设备编码" prop="code" width="120" /> -->
                            <!-- <el-table-column label="品牌" prop="brand" width="100">
                                <template #default="scope">
                                    <dict-tag :options="device_brands_new" :value="scope.row.brand" />
                                </template>
                            </el-table-column> -->
                            <el-table-column label="型号" prop="specification" />

                            <!-- <el-table-column label="状态" width="80" align="center">
                                <template #default>
                                    <el-tag type="info" size="small">未配置</el-tag>
                                </template>
                            </el-table-column> -->
                        </el-table>

                        <!-- 分页 -->
                        <pagination
                            v-show="total > 0"
                            :total="total"
                            v-model:page="queryParams.pageNum"
                            v-model:limit="queryParams.pageSize"
                            @pagination="getEquipmentList"
                            layout="total, prev, pager, next, sizes"
                            :page-sizes="[10, 20, 50, 100]"
                        />
                    </div>
                </div>
            </div>

            <!-- 右侧：巡检配置项选择区域 -->
            <div class="right-panel">
                <div class="panel-header">
                    <h4>巡检配置项</h4>
                    <div class="header-actions">
                        <el-tag v-if="selectedInspectionItems.length > 0" type="success">
                            已选择 {{ selectedInspectionItems.length }} 个巡检项
                        </el-tag>
                    </div>
                </div>
                <div class="inspection-items-area">
                    <!-- 巡检项搜索区域 -->
                    <!-- <div class="search-area">
                        <el-input
                            v-model="inspectionQuery.name"
                            placeholder="搜索巡检项名称"
                            clearable
                            @keyup.enter="getInspectionItems"
                            style="margin-bottom: 10px"
                        >
                            <template #append>
                                <el-button icon="Search" @click="getInspectionItems" />
                            </template>
                        </el-input>


                        <el-select
                            v-model="inspectionQuery.inspectType"
                            placeholder="巡检类型"
                            clearable
                            @change="getInspectionItems"
                            style="width: 100%; margin-bottom: 10px"
                        >
                            <el-option v-for="dict in inspect_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                        </el-select>
                    </div> -->

                    <!-- 巡检项列表 -->
                    <div class="inspection-list">
                        <el-table
                            ref="inspectionTableRef"
                            v-loading="inspectionLoading"
                            :data="inspectionItemList"
                            @selection-change="handleInspectionSelectionChange"
                            height="350"
                            size="small"
                            empty-text="请先在左侧选择设备分类"
                        >
                            <el-table-column type="selection" width="40" align="center" />
                            <el-table-column label="巡检项名称" prop="name" show-overflow-tooltip width="100px" />
                            <el-table-column label="类型" prop="inspectType" width="60">
                                <template #default="scope">
                                    <dict-tag :options="inspect_type" :value="scope.row.inspectType" size="small" />
                                </template>
                            </el-table-column>
                            <el-table-column label="检查标准" prop="standard" show-overflow-tooltip />
                            <el-table-column label="单位" prop="inspectUnit" width="50">
                                <template #default="scope">
                                    <dict-tag :options="inspect_unit" :value="scope.row.inspectUnit" size="small" />
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 巡检项分页 -->
                        <pagination
                            v-show="inspectionTotal > 0"
                            :total="inspectionTotal"
                            v-model:page="inspectionQuery.pageNum"
                            v-model:limit="inspectionQuery.pageSize"
                            @pagination="getInspectionItems"
                            layout="prev, pager, next"
                            :page-sizes="[10, 20, 30, 50]"
                            small
                        />
                    </div>

                    <!-- 已选巡检项预览 -->
                    <div class="selected-preview" v-if="selectedInspectionItems.length > 0">
                        <div class="preview-header">
                            <span>已选择的巡检项 ({{ selectedInspectionItems.length }})</span>
                            <el-button type="text" size="small" @click="clearInspectionSelection" style="color: #f56c6c"> 清空 </el-button>
                        </div>
                        <div class="preview-content">
                            <el-scrollbar height="80px">
                                <el-tag
                                    v-for="item in selectedInspectionItems"
                                    :key="item.id"
                                    closable
                                    @close="removeInspectionItem(item)"
                                    style="margin: 2px"
                                    size="small"
                                >
                                    {{ item.name }}
                                </el-tag>
                            </el-scrollbar>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作按钮 -->
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitLoading" :disabled="!canSubmit"> 保存配置 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, getCurrentInstance, ComponentInternalInstance, toRefs, computed } from 'vue'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { RoomVO } from '@/api/project/room/types'
import { listRoom } from '@/api/project/room'
import { listCategory } from '@/api/common/category'
import { CategoryVO } from '@/api/common/category/types'
import { listInspectionItemCandidate } from '@/api/common/inspectionItemCandidate'
import { InspectionItemCandidateVO } from '@/api/common/inspectionItemCandidate/types'
import { listInspectionConfigableEquipments, batchConfigByConditions } from '@/api/subProject/inspection/config'
import { BatchConfigByConditionRequest, BatchConfigByConditionResponse } from '@/api/subProject/inspection/config/types'
import { EquipmentVO } from '@/api/subProject/basic/equipment/types'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import Pagination from '@/components/Pagination/index.vue'
import { ElLoading } from 'element-plus'

interface EquipmentSelectProps {
    visible: boolean
    title: string
    manageUnits: ManageUnitVO[]
    buttonLoading?: boolean
}

const props = withDefaults(defineProps<EquipmentSelectProps>(), {
    buttonLoading: false
})

const emit = defineEmits<{
    'update:visible': [value: boolean]
    'confirm': [result: { success: boolean; result: BatchConfigByConditionResponse; queryConditions: any }]
    'cancel': []
}>()

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const appStore = useAppStore()
const userStore = useUserStore()
const queryFormRef = ref<ElFormInstance>()
const inspectionTableRef = ref<any>()

// 数据字典
const { inspect_type, inspect_unit, device_brands_new } = toRefs<any>(proxy?.useDict('inspect_type', 'inspect_unit', 'device_brands_new'))

// 响应式数据定义
const rooms = ref<RoomVO[]>([])
const equipmentList = ref<EquipmentVO[]>([])
const selectedEquipments = ref<EquipmentVO[]>([])
const electricCategories = ref<CategoryVO[]>([])
const inspectionItemList = ref<InspectionItemCandidateVO[]>([])
const selectedInspectionItems = ref<InspectionItemCandidateVO[]>([])

// 加载状态
const loading = ref(false)
const categoriesLoading = ref(false)
const inspectionLoading = ref(false)
const submitLoading = ref(false)

// 分页数据
const total = ref(0)
const inspectionTotal = ref(0)

// 查询参数
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    projectId: undefined as string | number | undefined,
    unitId: undefined as string | number | undefined,
    roomId: undefined as string | number | undefined,
    categoryPath: [] as string[],
    categoryId: undefined as string | undefined,
    name: ''
})

// 巡检项查询参数
const inspectionQuery = reactive({
    pageNum: 1,
    pageSize: 50,
    name: '',
    inspectType: ''
})

// 查询规则
const queryRules = {
    unitId: [{ required: true, message: '请选择管理单元', trigger: 'change' }],
    categoryPath: [{ required: true, message: '请选择设备分类', trigger: 'change' }]
}

// 计算属性：是否可以查询
const canSearch = computed(() => {
    return queryParams.unitId && queryParams.categoryPath && queryParams.categoryPath.length > 0
})

// 计算属性：是否可以提交
const canSubmit = computed(() => {
    return selectedInspectionItems.value.length > 0 && equipmentList.value.length > 0 && !submitLoading.value
})

/** 监听visible变化 */
watch(
    () => props.visible,
    (newVal) => {
        if (newVal) {
            resetForm()
        }
    }
)

/** 组件挂载时初始化 */
onMounted(async () => {
    // 设置项目ID
    queryParams.projectId = appStore.projectContext.selectedProjectId

    // 获取设备分类
    await getElectricCategories()

    // 不自动加载设备列表，等待用户选择条件后手动查询
    // 不自动加载巡检项，等待用户选择设备分类
})

/** 重置表单 */
const resetForm = () => {
    queryParams.unitId = undefined
    queryParams.roomId = undefined
    queryParams.categoryPath = []
    queryParams.categoryId = undefined
    queryParams.name = ''

    rooms.value = []
    equipmentList.value = []
    inspectionItemList.value = []
    selectedInspectionItems.value = []
    selectedEquipments.value = []

    total.value = 0
    inspectionTotal.value = 0
}

/** 获取electric专业的设备分类 */
const getElectricCategories = async () => {
    try {
        categoriesLoading.value = true
        console.log('开始获取electric专业的设备分类')

        const response = await listCategory({
            kind: 'equipment',
            specialty: 'electric' // 筛选electric专业
        })

        // 构建树形结构供选择器使用
        electricCategories.value = buildCategoryTree(response.data || [])
        console.log(`获取到${electricCategories.value.length}个electric设备分类`)
    } catch (error) {
        console.error('获取electric设备分类失败:', error)
        proxy?.$modal.msgError('获取设备分类失败')
        electricCategories.value = []
    } finally {
        categoriesLoading.value = false
    }
}

// 构建分类树形结构
const buildCategoryTree = (categories: CategoryVO[]) => {
    const categoryMap = new Map()
    const rootCategories = []

    // 第一遍遍历，创建所有节点
    categories.forEach((category) => {
        categoryMap.set(category.id, {
            ...category,
            children: [],
            // 确保有path字段，如果没有则构建
            path: category.path || buildCategoryPath(category, categories)
        })
    })

    // 第二遍遍历，建立父子关系
    categories.forEach((category) => {
        const node = categoryMap.get(category.id)
        if (category.parentId && categoryMap.has(category.parentId)) {
            categoryMap.get(category.parentId).children.push(node)
        } else {
            rootCategories.push(node)
        }
    })

    return rootCategories
}

// 构建分类路径（如果API没有返回path字段）
const buildCategoryPath = (category: CategoryVO, allCategories: CategoryVO[]) => {
    const paths = []
    let current = category

    while (current) {
        paths.unshift(current.id)
        if (current.parentId) {
            current = allCategories.find((c) => c.id === current.parentId)
        } else {
            break
        }
    }

    return paths.join('/')
}

/** 通过管理单元获取房间列表 */
const getRoomsByUnit = async (unitId: string | number) => {
    if (!unitId) {
        rooms.value = []
        return
    }

    try {
        const projectId = appStore.projectContext.selectedProjectId
        console.log('获取房间列表参数:', { projectId, unitId })

        const queryParams = {
            projectId: projectId,
            unitId: unitId,
            pageNum: 1,
            pageSize: 1000 // 获取所有房间
        }

        const res = await listRoom(queryParams)
        console.log('API返回的房间数据:', res)

        // 兼容不同的API返回结构
        rooms.value = Array.isArray(res) ? res : res.data || res.rows || []
        console.log('解析后的房间列表:', rooms.value)

        if (rooms.value.length === 0) {
            console.warn('该管理单元下没有房间数据')
        } else {
            console.log(`成功获取 ${rooms.value.length} 个房间`)
        }
    } catch (error) {
        console.error('获取房间列表失败:', error)
        rooms.value = []
    }
}

/** 设备分类变化处理 */
const handleCategoryChange = async (selectedPath: string[]) => {
    if (selectedPath && selectedPath.length > 0) {
        // 获取最后一级分类ID
        queryParams.categoryId = selectedPath[selectedPath.length - 1]

        // 自动获取该分类下的巡检项
        getInspectionItems()

        // 如果已经选择了管理单元，自动刷新设备搜索列表
        if (queryParams.unitId) {
            console.log('设备分类已选择，管理单元已选择，自动刷新设备列表')
            queryParams.pageNum = 1
            await getEquipmentList()
        } else {
            // 如果还没选择管理单元，只清空设备列表
            equipmentList.value = []
            total.value = 0
        }
    } else {
        queryParams.categoryId = undefined
        equipmentList.value = []
        inspectionItemList.value = []
        clearInspectionSelection()
    }
}

/** 管理单元变化处理 */
const handleUnitChange = (unitId: string | number) => {
    // 清空房间选择
    queryParams.roomId = undefined
    // 获取新的房间列表
    if (unitId) {
        getRoomsByUnit(unitId)
    } else {
        rooms.value = []
    }

    // 清空设备列表
    equipmentList.value = []
    total.value = 0
}

/** 搜索设备（默认不加载，必须手动搜索） */
const handleSearch = async () => {
    // 验证必填条件
    if (!queryParams.unitId) {
        proxy?.$modal.msgWarning('请先选择管理单元')
        return
    }

    if (!queryParams.categoryPath || queryParams.categoryPath.length === 0) {
        proxy?.$modal.msgWarning('请先选择设备分类')
        return
    }

    queryParams.pageNum = 1
    await getEquipmentList()
}

/** 获取设备列表 */
const getEquipmentList = async () => {
    if (!canSearch.value) {
        return
    }

    loading.value = true
    try {
        const searchParams = {
            ...queryParams,
            categoryId: queryParams.categoryId // 使用最终选中的分类ID
        }

        // 调用返回未配置巡检项设备的接口
        const response = await listInspectionConfigableEquipments(searchParams)

        equipmentList.value = response?.rows || []
        total.value = response?.total || 0

        if (equipmentList.value.length === 0) {
            proxy?.$modal.msgWarning('未找到符合条件的未配置设备')
        }
    } catch (error) {
        console.error('获取设备列表失败:', error)
        proxy?.$modal.msgError('获取设备列表失败')
    } finally {
        loading.value = false
    }
}

/** 重置查询 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    queryParams.unitId = undefined
    queryParams.roomId = undefined
    queryParams.categoryPath = []
    queryParams.categoryId = undefined
    queryParams.name = ''

    rooms.value = []
    equipmentList.value = []
    inspectionItemList.value = []
    selectedInspectionItems.value = []

    total.value = 0
    inspectionTotal.value = 0
}

/** 获取巡检项列表 */
const getInspectionItems = async () => {
    if (!queryParams.categoryId) {
        inspectionItemList.value = []
        inspectionTotal.value = 0
        return
    }

    inspectionLoading.value = true
    try {
        console.log('EquipmentSelect - getInspectionItems执行')
        console.log('queryParams.categoryId:', queryParams.categoryId)

        // 构建查询参数，参考 InspectionItemSelect.vue 的实现
        const params = {
            pageNum: inspectionQuery.pageNum,
            pageSize: inspectionQuery.pageSize,
            name: inspectionQuery.name || undefined,
            facilityCategoryId: queryParams.categoryId, // 使用 facilityCategoryId 字段
            inspectType: inspectionQuery.inspectType || undefined
        }

        console.log('巡检项查询参数:', params)

        const response = await listInspectionItemCandidate(params)

        // 处理响应数据
        const allItems = response.rows || []
        inspectionItemList.value = allItems
        inspectionTotal.value = response.total || 0

        console.log(`获取到 ${allItems.length} 个巡检项`)
    } catch (error) {
        console.error('获取巡检项失败:', error)
        inspectionItemList.value = []
        inspectionTotal.value = 0
        proxy?.$modal.msgError('获取巡检项失败')
    } finally {
        inspectionLoading.value = false
    }
}

/** 巡检项选择变化 */
const handleInspectionSelectionChange = (selection: InspectionItemCandidateVO[]) => {
    selectedInspectionItems.value = selection
}

/** 设备选择变化 */
const handleEquipmentSelectionChange = (selection: EquipmentVO[]) => {
    selectedEquipments.value = selection
}

/** 移除单个巡检项 */
const removeInspectionItem = (item: InspectionItemCandidateVO) => {
    const index = selectedInspectionItems.value.findIndex((selected) => selected.id === item.id)
    if (index > -1) {
        selectedInspectionItems.value.splice(index, 1)

        // 同步更新表格选择状态
        const table = inspectionTableRef.value
        table?.toggleRowSelection(item, false)
    }
}

/** 清空巡检项选择 */
const clearInspectionSelection = () => {
    selectedInspectionItems.value = []
    const table = inspectionTableRef.value
    table?.clearSelection()
}

// 分页变化处理已通过 pagination 组件的 @pagination 事件处理

/** 关闭弹窗 */
const handleDialogClose = (value: boolean) => {
    emit('update:visible', value)
}

/** 数据验证 */
const validateBeforeSave = (): boolean => {
    // 1. 验证是否选择了巡检项
    if (selectedInspectionItems.value.length === 0) {
        proxy?.$modal.msgWarning('请至少选择一个巡检配置项')
        return false
    }

    // 2. 验证查询条件是否完整
    if (!queryParams.unitId || !queryParams.categoryId) {
        proxy?.$modal.msgWarning('请先完成查询条件选择')
        return false
    }

    // 3. 验证是否有符合条件的设备
    if (equipmentList.value.length === 0) {
        proxy?.$modal.msgWarning('当前查询条件下没有找到未配置的设备')
        return false
    }

    return true
}

/** 构建批量配置请求参数 */
const buildBatchConfigRequest = (): BatchConfigByConditionRequest => {
    return {
        projectId: queryParams.projectId!,
        queryConditions: {
            unitId: queryParams.unitId!,
            categoryId: queryParams.categoryId!,
            roomId: queryParams.roomId || undefined,
            name: queryParams.name?.trim() || undefined
        },
        inspectionItemIds: selectedInspectionItems.value.map((item) => String(item.id)),
        configMeta: {
            configUser: userStore.name || 'system',
            configTime: new Date().toISOString(),
            remark: `批量配置 - ${new Date().toLocaleString()}`
        }
    }
}

/** 显示保存确认对话框 */
const showSaveConfirmDialog = async (requestData: BatchConfigByConditionRequest): Promise<boolean> => {
    try {
        // 使用当前页面显示的设备数量作为预估
        const estimatedEquipmentCount = total.value // 来自分页查询的总数
        const inspectionItemCount = requestData.inspectionItemIds.length
        const estimatedTotalRecords = estimatedEquipmentCount * inspectionItemCount

        // 构建纯文本确认信息
        const confirmLines = [
            '即将执行批量配置操作',
            '',
            '查询条件：',
            `  • 管理单元：${getUnitName(requestData.queryConditions.unitId)}`,
            `  • 设备分类：${getCategoryName(requestData.queryConditions.categoryId)}`
        ]

        // 添加可选条件
        if (requestData.queryConditions.roomId) {
            confirmLines.push(`  • 房间：${getRoomName(requestData.queryConditions.roomId)}`)
        }
        if (requestData.queryConditions.name) {
            confirmLines.push(`  • 设备名称：${requestData.queryConditions.name}`)
        }

        confirmLines.push(
            '',
            '配置数量（预估）：',
            `  • 符合条件设备：约 ${estimatedEquipmentCount} 个`,
            `  • 巡检项数量：${inspectionItemCount} 个`,
            `  • 预计配置记录：约 ${estimatedTotalRecords} 条`,
            '',
            '⚠️ 实际数量以后端查询结果为准，此操作不可撤销',
            '',
            '确定要继续吗？'
        )

        const message = confirmLines.join('\n')
        await proxy?.$modal.confirm(message)

        return true
    } catch (error) {
        return false // 用户取消
    }
}

/** 辅助函数：获取名称显示 */
const getUnitName = (unitId: string | number): string => {
    const unit = props.manageUnits.find((u) => u.id === unitId)
    return unit?.name || `ID: ${unitId}`
}

const getCategoryName = (categoryId: string): string => {
    // 从设备分类树中查找名称
    const findCategory = (categories: CategoryVO[], targetId: string): CategoryVO | null => {
        for (const category of categories) {
            if (category.id === targetId) {
                return category
            }
            if (category.children && category.children.length > 0) {
                const found = findCategory(category.children, targetId)
                if (found) return found
            }
        }
        return null
    }

    const category = findCategory(electricCategories.value, categoryId)
    return category?.name || `ID: ${categoryId}`
}

const getRoomName = (roomId: string | number): string => {
    const room = rooms.value.find((r) => r.id === roomId)
    return room?.roomNane || `ID: ${roomId}`
}

/** 处理保存结果 */
const handleSaveResult = async (result: BatchConfigByConditionResponse) => {
    if (result.success) {
        // 保存成功
        const successMessage = `
            批量配置完成！

            📊 配置统计：
            • 符合条件设备：${result.totalEquipmentCount} 个
            • 实际配置设备：${result.summary.equipmentCount} 个
            • 巡检项数量：${result.summary.inspectionItemCount} 个
            • 创建配置记录：${result.totalConfigCount} 条

            ⏱️ 处理耗时：${result.processingTime}ms
            ${result.failureCount > 0 ? `\n⚠️ 失败记录：${result.failureCount} 条` : ''}
        `

        if (result.failureCount > 0) {
            proxy?.$modal.msgWarning(successMessage)
        } else {
            proxy?.$modal.msgSuccess(successMessage)
        }

        // 触发成功事件
        emit('confirm', {
            success: true,
            result: result,
            queryConditions: buildBatchConfigRequest().queryConditions
        })

        // 关闭弹窗
        emit('update:visible', false)
    } else {
        throw new Error('批量配置失败')
    }
}

/** 主提交方法 */
const handleSubmit = async () => {
    // 1. 数据验证
    if (!validateBeforeSave()) {
        return
    }

    // 2. 构建请求参数
    const requestData = buildBatchConfigRequest()

    // 3. 显示确认对话框
    const confirmResult = await showSaveConfirmDialog(requestData)
    if (!confirmResult) {
        return
    }

    // 4. 开始保存流程
    submitLoading.value = true

    // 显示耐心等待提示
    const loadingInstance = ElLoading.service({
        lock: true,
        text: '正在批量配置巡检项，此操作较为耗时，请耐心等待...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
        // 5. 调用后端批量配置接口
        const response = await batchConfigByConditions(requestData)

        // 6. 处理保存结果
        await handleSaveResult(response.data)
    } catch (error: any) {
        console.error('保存配置失败:', error)
        proxy?.$modal.msgError(`保存失败: ${error.message || '未知错误'}`)
    } finally {
        // 关闭loading提示
        loadingInstance?.close()
        submitLoading.value = false
    }
}

/** 取消按钮 */
const handleCancel = () => {
    emit('cancel')
}
</script>

<style lang="scss" scoped>
.equipment-config-container {
    display: flex;
    height: 700px;
    gap: 20px;

    .left-panel {
        flex: 1;
        min-width: 650px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
    }

    .right-panel {
        width: 600px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
    }

    .panel-header {
        padding: 15px;
        border-bottom: 1px solid #e4e7ed;
        background-color: transparent;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h4 {
            margin: 0;
            color: #ffffff;
            font-size: 16px;
        }
    }

    .equipment-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 15px;
    }

    .inspection-items-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 15px;
    }

    .search-form {
        margin-bottom: 15px;
    }

    .equipment-list {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .list-header {
        margin-bottom: 10px;

        .list-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }

    .search-area {
        margin-bottom: 15px;
    }

    .inspection-list {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .selected-preview {
        margin-top: 15px;
        padding: 10px 0;
        background-color: transparent;

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .preview-content {
            .el-tag {
                margin: 2px;
            }
        }
    }
}

.dialog-footer {
    text-align: right;
}

@media (max-width: 1200px) {
    .equipment-config-container {
        flex-direction: column;
        height: auto;

        .left-panel {
            min-width: auto;
            margin-bottom: 20px;
        }

        .right-panel {
            width: 100%;
        }
    }
}
</style>
