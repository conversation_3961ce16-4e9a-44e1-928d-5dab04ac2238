<!-- 本周计划 -->
<template>
    <div class="week-plan">
             <!-- 顶部标题 -->
             <div class="title-header">
            <span class="title-text">重点计划</span>
        </div>
        <el-card shadow="never" class="table-card">
            <el-table :data="tableData" stripe height="100%">
                <el-table-column label="计划类型" align="center" prop="type" />
                <el-table-column label="计划数量" align="center" prop="planCount" width="140" />
                <el-table-column label="闭环数量" align="center" prop="closedCount" width="140" />
                <el-table-column label="合格率" align="center" prop="passRate" width="140" />
            </el-table>
        </el-card>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

interface WeekPlanItem {
    id: number
    type: string
    planCount: number | string
    closedCount: number | string
    passRate: string
}

// 表格数据按示例图
const tableData = ref<WeekPlanItem[]>([
    { id: 1, type: '巡检计划', planCount: 54, closedCount: 54, passRate: '100%' },
    { id: 2, type: '养护计划', planCount: 84, closedCount: 84, passRate: '100%' },
    { id: 3, type: '封道计划', planCount: 1,  closedCount: 1,  passRate: '100%' }
])
</script>
<style lang="scss" scoped>
.week-plan{
    width: 40%;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-left: 2%;
    // background: red;
    .title-header {
        width: 500px;
        height: 70px;
        background: url('@/assets/images/bigscreen/短标题@2x.png') no-repeat center center;
        background-size:100%,100%;

        position: relative;

        .title-text {
            color: #fff;
            font-size: 24px;
            font-weight: bold;
            position: absolute;
            top: 15px;
            left: 50px;

        }
    }

    .table-card{
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-top: 8px;
        background: transparent !important;
        border: none !important;

    }

    :deep(.el-card__body){
        padding: 0 !important;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    :deep(.el-table){
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
        flex: 1;
    }

    :deep(.el-table__header th){
        background-color: transparent !important;
        border-bottom: none !important;
        height: 40px;
    }

    :deep(.el-table__body tr > td){
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    :deep(.el-table__body tr:hover > td){
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    :deep(.el-table__header .cell){
        color: #AED7F2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
    }
    :deep(.el-table .cell){
        color: #FFFFFF !important;
        font-size: 14px !important;
    }
    :deep(.el-table--striped .el-table__body tr.el-table__row--striped){
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

}

</style>
