<template>
    <div class="p-2">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item label="项目" prop="projectId">
                            <el-select v-model="queryParams.projectId" placeholder="请选择项目" clearable filterable @change="handleProjectChange">
                                <el-option v-for="project in projectList" :key="project.id" :label="project.name" :value="project.id" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="类型" prop="kind">
                            <el-select v-model="queryParams.kind" placeholder="请选择类型" clearable @change="handleKindChange">
                                <el-option label="设备" value="E" />
                                <el-option label="设施" value="S" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="分类" prop="category">
                            <el-tree-select
                                clearable
                                @change="handleQuery"
                                v-model="selectedQueryCategoryId"
                                :data="categoryList"
                                :props="{ value: 'id', label: 'name', children: 'children' }"
                                value-key="id"
                                placeholder="请选择分类"
                                check-strictly
                            />
                        </el-form-item>
                        <!-- <el-form-item label="是否启用" prop="enabled">
                            <el-select v-model="queryParams.enabled" placeholder="请选择是否启用" clearable>
                                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item> -->
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['common:deviceCodeRule:add']">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="success"
                            plain
                            icon="Edit"
                            :disabled="single"
                            @click="handleUpdate()"
                            v-hasPermi="['common:deviceCodeRule:edit']"
                            >修改</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="danger"
                            plain
                            icon="Delete"
                            :disabled="multiple"
                            @click="handleDelete()"
                            v-hasPermi="['common:deviceCodeRule:remove']"
                            >删除</el-button
                        >
                    </el-col>
                    <!-- <el-col :span="1.5">
                        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['common:deviceCodeRule:export']"
                            >导出</el-button
                        >
                    </el-col> -->
                    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </template>

            <el-table v-loading="loading" :data="deviceCodeRuleList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <el-table-column label="项目名称" align="center" prop="projectName" width="200px" />
                <el-table-column label="邮编" align="center" prop="zipcode" width="100px" />
                <el-table-column label="项目编码" align="center" prop="projectCode" width="100px" />
                <el-table-column label="类型" align="center" prop="kind" width="100px">
                    <template #default="scope">
                        <el-tag :type="scope.row.kind === 'E' ? 'primary' : 'success'">
                            {{ scope.row.kind === 'E' ? '设备' : '设施' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="设施设备分类" align="center" prop="categoryDisplayName" />
                <!-- <el-table-column label="一级分类编码" align="center" prop="categoryFirstCode" />
                <el-table-column label="二级分类编码" align="center" prop="categorySecondCode" />
                <el-table-column label="三级分类编码" align="center" prop="categoryThirdCode" /> -->
                <el-table-column label="编码规则" align="center" prop="prefix" show-overflow-tooltip="true">
                    <template #default="scope">
                        <el-tag type="info">
                            【{{ scope.row.zipcode }}】+【{{ scope.row.projectCode }}】+【{{ scope.row.kind === 'E' ? '设备' : '设施' }}】+【{{
                                scope.row.categoryFirstCode
                            }}】+【{{ scope.row.categorySecondCode }}】+【{{ scope.row.categoryThirdCode }}】+【流水号】
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="是否启用" align="center" prop="enabled" width="80px">
                    <template #default="scope">
                        <dict-tag :options="sys_yes_no" :value="scope.row.enabled" />
                    </template>
                </el-table-column>
                <!-- <el-table-column label="当前最大流水号" align="center" prop="currentMaxSeq" /> -->
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-tooltip :content="scope.row.enabled === 'Y' ? '禁用' : '启用'" placement="top">
                            <el-button
                                link
                                :type="scope.row.enabled === 'Y' ? 'warning' : 'success'"
                                :icon="scope.row.enabled === 'Y' ? 'CircleClose' : 'CircleCheck'"
                                @click="handleToggleStatus(scope.row)"
                                v-hasPermi="['common:deviceCodeRule:edit']"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="应用规则" placement="top">
                            <el-button
                                link
                                type="primary"
                                icon="Setting"
                                @click="handleApplyRule(scope.row)"
                                v-hasPermi="['common:deviceCodeRule:apply']"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="修改" placement="top">
                            <el-button
                                link
                                type="primary"
                                icon="Edit"
                                @click="handleUpdate(scope.row)"
                                v-hasPermi="['common:deviceCodeRule:edit']"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top">
                            <el-button
                                link
                                type="primary"
                                icon="Delete"
                                @click="handleDelete(scope.row)"
                                v-hasPermi="['common:deviceCodeRule:remove']"
                            ></el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <!-- 添加或修改设施设备编码规则对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
            <el-form ref="deviceCodeRuleFormRef" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="项目" prop="projectId">
                    <el-select v-model="form.projectId" placeholder="请选择项目" filterable @change="handleProjectCodeChange">
                        <el-option v-for="project in projectList" :key="project.id" :label="project.name" :value="project.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="邮编" prop="zipcode">
                    <el-input v-model="form.zipcode" placeholder="请输入邮编" @input="generatePrefix" />
                </el-form-item>
                <el-form-item label="项目编码" prop="projectCode">
                    <el-input v-model="form.projectCode" placeholder="请输入项目编码" @input="generatePrefix" />
                </el-form-item>
                <el-form-item label="类型" prop="kind">
                    <el-select v-model="form.kind" placeholder="请选择类型" @change="handleFormKindChange">
                        <el-option label="设备" value="E" />
                        <el-option label="设施" value="S" />
                    </el-select>
                </el-form-item>
                <el-form-item label="设施设备分类" prop="category">
                    <el-cascader
                        v-model="formCategoryIdThird"
                        :options="categoryList"
                        :props="{ value: 'id', label: 'name', children: 'children', expandTrigger: 'hover', emitPath: false }"
                        @change="handleFormCategoryChange"
                    />
                </el-form-item>
                <el-form-item label="一级分类编码" prop="categoryFirstCode">
                    <el-input v-model="form.categoryFirstCode" placeholder="请输入一级分类编码" @input="generatePrefix" />
                </el-form-item>
                <el-form-item label="二级分类编码" prop="categorySecondCode">
                    <el-input v-model="form.categorySecondCode" placeholder="请输入二级分类编码" @input="generatePrefix" />
                </el-form-item>
                <el-form-item label="三级分类编码" prop="categoryThirdCode">
                    <el-input v-model="form.categoryThirdCode" placeholder="请输入三级分类编码" @input="generatePrefix" />
                </el-form-item>
                <el-form-item label="前缀编码" prop="prefix">
                    <el-input readonly v-model="displayRule" />
                </el-form-item>
                <!-- <el-form-item label="是否启用" prop="enabled">
                    <el-select v-model="form.enabled" placeholder="请选择是否启用">
                        <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item> -->
                <!-- <el-form-item label="当前最大流水号" prop="currentMaxSeq">
                    <el-input v-model="form.currentMaxSeq" placeholder="请输入当前最大流水号" @input="generatePrefix" />
                </el-form-item> -->
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="DeviceCodeRule" lang="ts">
import {
    listDeviceCodeRule,
    getDeviceCodeRule,
    delDeviceCodeRule,
    addDeviceCodeRule,
    updateDeviceCodeRule,
    toggleDeviceCodeRuleStatus,
    applyDeviceCodeRule
} from '@/api/common/deviceCodeRule'
import { DeviceCodeRuleVO, DeviceCodeRuleQuery, DeviceCodeRuleForm } from '@/api/common/deviceCodeRule/types'
import { getAllProjects } from '@/api/project/project'
import { ProjectVO } from '@/api/project/project/types'
import { listCategory, getCategoryHierarchyNames } from '@/api/common/category'
import { CategoryVO, CategoryQuery } from '@/api/common/category/types'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'))

const deviceCodeRuleList = ref<DeviceCodeRuleVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 新增的响应式数据
const projectList = ref<ProjectVO[]>([])
const categoryList = ref<CategoryVO[]>([])
const selectedQueryCategoryId = ref('')

const queryFormRef = ref<ElFormInstance>()
const deviceCodeRuleFormRef = ref<ElFormInstance>()

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

const initFormData: DeviceCodeRuleForm = {
    id: undefined,
    projectId: undefined,
    zipcode: undefined,
    projectCode: undefined,
    kind: undefined,
    category: undefined,
    categoryPath: undefined,
    categoryFirstCode: undefined,
    categorySecondCode: undefined,
    categoryThirdCode: undefined,
    prefix: undefined,
    enabled: undefined,
    currentMaxSeq: undefined
}
const displayRule = ref('') //在表单中显示的规则

// 用于级联选择器的临时字段
const formCategoryIdThird = ref<string>('')
const data = reactive<PageData<DeviceCodeRuleForm, DeviceCodeRuleQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        kind: undefined,
        categoryPath: undefined,
        enabled: undefined,
        params: {}
    },
    rules: {
        // id: [{ required: true, message: '不能为空', trigger: 'blur' }],
        projectId: [{ required: true, message: '项目-id不能为空', trigger: 'blur' }],
        zipcode: [{ required: true, message: '邮编不能为空', trigger: 'blur' }],
        projectCode: [{ required: true, message: '项目编码不能为空', trigger: 'blur' }],
        kind: [{ required: true, message: 'E/设备,S/设施不能为空', trigger: 'blur' }],
        category: [{ required: true, message: '设施设备分类不能为空', trigger: 'blur' }],
        categoryFirstCode: [{ required: true, message: '一级分类编码不能为空', trigger: 'blur' }],
        categorySecondCode: [{ required: true, message: '二级分类编码不能为空', trigger: 'blur' }],
        categoryThirdCode: [{ required: true, message: '三级分类编码不能为空', trigger: 'blur' }]
        //prefix: [{ required: true, message: '前缀编码不能为空', trigger: 'blur' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询设施设备编码规则列表 */
const getList = async () => {
    loading.value = true
    try {
        const res = await listDeviceCodeRule(queryParams.value)

        // 提取所有第三级分类ID
        const categoryIds = res.rows.map((item) => item.category).filter(Boolean)

        let categoryNames: Record<string, any> = {}

        // 获取分类层级名称（使用现有API）
        if (categoryIds.length > 0) {
            console.log('请求分类名称，categoryIds:', categoryIds)
            categoryNames = await getCategoryNamesOneByOne(categoryIds)
        }

        // 数据增强：添加项目名称和分类显示名称
        const enhancedData = res.rows.map((item) => {
            const categoryInfo = categoryNames[item.category]
            const categoryDisplayName = categoryInfo ? `${categoryInfo.firstName}/${categoryInfo.secondName}/${categoryInfo.thirdName}` : ''

            console.log(`分类ID: ${item.category}, 分类信息:`, categoryInfo, '显示名称:', categoryDisplayName)

            return {
                ...item,
                projectName: getProjectNameById(item.projectId),
                categoryDisplayName
            }
        })

        deviceCodeRuleList.value = enhancedData
        total.value = res.total
    } catch (error) {
        console.error('获取列表失败:', error)
    } finally {
        loading.value = false
    }
}

/** 根据项目ID获取项目名称 */
const getProjectNameById = (projectId: string | number): string => {
    if (!projectId) return ''
    const project = projectList.value.find((p) => p.id === projectId)
    return project ? project.name : String(projectId)
}

/** 使用现有API逐个获取分类名称 */
const getCategoryNamesOneByOne = async (categoryIds: string[]): Promise<Record<string, any>> => {
    const result: Record<string, any> = {}

    // 并发请求以提高性能
    const promises = categoryIds.map(async (categoryId) => {
        try {
            const hierarchyNames = await getCategoryHierarchyNames(categoryId)
            console.log(`分类ID ${categoryId} 的层级名称:`, hierarchyNames.data)

            if (hierarchyNames.data && Array.isArray(hierarchyNames.data) && hierarchyNames.data.length >= 3) {
                return {
                    categoryId,
                    nameInfo: {
                        firstName: hierarchyNames.data[0] || '',
                        secondName: hierarchyNames.data[1] || '',
                        thirdName: hierarchyNames.data[2] || ''
                    }
                }
            }
        } catch (error) {
            console.error(`获取分类ID ${categoryId} 的名称失败:`, error)
        }
        return null
    })

    // 等待所有请求完成
    const results = await Promise.all(promises)

    // 构建结果对象
    results.forEach((item) => {
        if (item) {
            result[item.categoryId] = item.nameInfo
        }
    })

    console.log('获取的分类名称:', result)
    return result
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    formCategoryIdThird.value = ''
    displayRule.value = ''
    deviceCodeRuleFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    if (!selectedQueryCategoryId.value) {
        queryParams.value.categoryPath = ''
    } else {
        const selectedCategory = findCategoryById(categoryList.value, selectedQueryCategoryId.value)
        queryParams.value.categoryPath = selectedCategory?.path || ''
        console.log('selectedCategory', selectedCategory?.path)
    }
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    selectedQueryCategoryId.value = ''
    queryParams.value.categoryPath = ''
    handleQuery()
}

/** 项目变更处理 */
const handleProjectChange = () => {
    selectedQueryCategoryId.value = ''
    queryParams.value.categoryPath = ''
    handleQuery()
}

/** Kind变更处理 */
const handleKindChange = async () => {
    selectedQueryCategoryId.value = ''
    queryParams.value.categoryPath = ''
    await loadCategoryTree()
}

/** 查找分类函数 */
function findCategoryById(list: CategoryVO[], id: string): CategoryVO | null {
    for (const item of list) {
        if (item.id === id) return item
        if (item.children) {
            const found = findCategoryById(item.children, id)
            if (found) return found
        }
    }
    return null
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: DeviceCodeRuleVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    // 生成前缀编码显示
    generatePrefix()
    dialog.visible = true
    dialog.title = '添加设施设备编码规则'
}

/** 修改按钮操作 */
const handleUpdate = async (row?: DeviceCodeRuleVO) => {
    reset()
    const _id = row?.id || ids.value[0]
    const res = await getDeviceCodeRule(_id)
    Object.assign(form.value, res.data)

    // 根据kind加载对应分类树
    if (form.value.kind) {
        await loadCategoryTreeForForm()
    }

    // 设置级联选择器的值
    formCategoryIdThird.value = form.value.category || ''

    // 生成前缀编码显示
    generatePrefix()

    dialog.visible = true
    dialog.title = '修改设施设备编码规则'
}

/** 表单Kind变更处理 */
const handleFormKindChange = async () => {
    formCategoryIdThird.value = ''
    form.value.category = ''
    form.value.categoryPath = ''
    await loadCategoryTreeForForm()
    generatePrefix()
}

/** 表单项目变更处理 */
const handleProjectCodeChange = () => {
    // 项目变更时重新生成前缀编码
    generatePrefix()
}

/** 表单分类变更处理 */
const handleFormCategoryChange = (value: string) => {
    if (value) {
        const selectedCategory = findCategoryById(categoryList.value, value)
        form.value.categoryPath = selectedCategory?.path || ''
        form.value.category = value

        // 分类变更时重新生成前缀编码
        generatePrefix()
    } else {
        form.value.categoryPath = ''
        form.value.category = ''
        form.value.prefix = ''
    }
}

/** 生成前缀编码 */
const generatePrefix = () => {
    // 检查必要字段是否完整
    if (!form.value.zipcode || !form.value.projectCode) {
        form.value.prefix = ''
        return
    }

    // 获取分类编码

    // 确保所有字段都不为undefined，默认为空字符串
    const zipcode = form.value.zipcode || ''
    const projectCode = form.value.projectCode || ''
    const kind = form.value.kind || ''
    const categoryFirstCode = form.value.categoryFirstCode || ''
    const categorySecondCode = form.value.categorySecondCode || ''
    const categoryThirdCode = form.value.categoryThirdCode || ''

    displayRule.value = zipcode + projectCode + kind + categoryFirstCode + categorySecondCode + categoryThirdCode + '【流水号】'
}

/** 生成实际的前缀编码（用于提交） */
const generateActualPrefix = () => {
    // 确保所有字段都不为undefined，默认为空字符串
    const zipcode = form.value.zipcode || ''
    const projectCode = form.value.projectCode || ''
    const kind = form.value.kind || ''
    const categoryFirstCode = form.value.categoryFirstCode || ''
    const categorySecondCode = form.value.categorySecondCode || ''
    const categoryThirdCode = form.value.categoryThirdCode || ''

    // 生成流水号（6位数字，基于当前最大流水号+1）
    //const sequenceNumber = String((form.value.currentMaxSeq || 0) + 1).padStart(6, '0')

    // 组合实际的前缀编码
    form.value.prefix = zipcode + projectCode + kind + categoryFirstCode + categorySecondCode + categoryThirdCode
}

/** 为表单加载分类树 */
const loadCategoryTreeForForm = async () => {
    if (!form.value.kind) {
        categoryList.value = []
        return
    }

    try {
        const categoryQueryParams: CategoryQuery = {
            projectId: form.value.projectId as string,
            kind: form.value.kind === 'E' ? 'equipment' : 'facility',
            params: {}
        }

        const res = await listCategory(categoryQueryParams)
        const data = proxy?.handleTree<CategoryVO>(res.data, 'id', 'parentId')

        if (data) {
            categoryList.value = data
        }
    } catch (error) {
        console.error('加载表单分类树失败:', error)
        categoryList.value = []
    }
}

/** 提交按钮 */
const submitForm = () => {
    deviceCodeRuleFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true
            // 提交前生成实际的前缀编码（包含真实流水号）
            generateActualPrefix()

            if (form.value.id) {
                await updateDeviceCodeRule(form.value).finally(() => (buttonLoading.value = false))
            } else {
                form.value.enabled = 'N'
                await addDeviceCodeRule(form.value).finally(() => (buttonLoading.value = false))
            }
            proxy?.$modal.msgSuccess('操作成功')
            dialog.visible = false
            await getList()
        }
    })
}

/** 启用/禁用按钮操作 */
const handleToggleStatus = async (row: DeviceCodeRuleVO) => {
    const newStatus = row.enabled === 'Y' ? 'N' : 'Y'
    const statusText = newStatus === 'Y' ? '启用' : '禁用'

    try {
        await proxy?.$modal.confirm(
            `是否确认${statusText}该设施设备编码规则？${newStatus === 'Y' ? '\n注意：启用后，相同项目、类型、分类的其他规则将被自动禁用。' : ''}`
        )

        loading.value = true
        await toggleDeviceCodeRuleStatus(row.id, newStatus)
        proxy?.$modal.msgSuccess(`${statusText}成功`)
        await getList()
    } catch (error) {
        console.error(`${statusText}失败:`, error)
    } finally {
        loading.value = false
    }
}

/** 应用规则按钮操作 */
const handleApplyRule = async (row: DeviceCodeRuleVO) => {
    // 1. 检查规则是否启用
    if (row.enabled !== 'Y') {
        proxy?.$modal.msgWarning('请先启用该规则')
        return
    }

    // 2. 确认操作
    try {
        await proxy?.$modal.confirm(
            `确认应用该编码规则吗？\n\n` +
                `操作说明：\n` +
                `• 将按创建时间重新排序所有设备/设施\n` +
                `• 流水号从1开始重新分配（1,2,3...）\n` +
                `• 所有编码将重新生成\n` +
                `• 如果重复应用，会重新从1开始排序\n\n` +
                `项目：【${getProjectNameById(row.projectId)}】\n` +
                `类型：【${row.kind === 'E' ? '设备' : '设施'}】\n\n` +
                `注意：此操作不可撤销！`
        )

        // 3. 执行应用操作
        loading.value = true
        await applyDeviceCodeRule(row.id)
        proxy?.$modal.msgSuccess('编码规则应用成功')
        await getList() // 刷新列表
    } catch (error: any) {
        console.error('应用编码规则失败:', error)
        if (error.response?.data?.msg) {
            proxy?.$modal.msgError(error.response.data.msg)
        } else {
            proxy?.$modal.msgError('应用编码规则失败')
        }
    } finally {
        loading.value = false
    }
}

/** 删除按钮操作 */
const handleDelete = async (row?: DeviceCodeRuleVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除设施设备编码规则编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delDeviceCodeRule(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'common/deviceCodeRule/export',
        {
            ...queryParams.value
        },
        `deviceCodeRule_${new Date().getTime()}.xlsx`
    )
}

/** 加载项目列表 */
const loadProjectList = async () => {
    try {
        const res = await getAllProjects()
        projectList.value = res.data || []
    } catch (error) {
        console.error('加载项目列表失败:', error)
    }
}

/** 加载分类树 */
const loadCategoryTree = async () => {
    if (!queryParams.value.kind) {
        categoryList.value = []
        return
    }

    try {
        const categoryQueryParams: CategoryQuery = {
            projectId: queryParams.value.projectId as string,
            kind: queryParams.value.kind === 'E' ? 'equipment' : 'facility',
            params: {}
        }

        const res = await listCategory(categoryQueryParams)
        const data = proxy?.handleTree<CategoryVO>(res.data, 'id', 'parentId')

        if (data) {
            categoryList.value = data
            console.log('分类树加载完成:', data)
        }
    } catch (error) {
        console.error('加载分类树失败:', error)
        categoryList.value = []
    }
}

onMounted(() => {
    loadProjectList()
    getList()
})
</script>
