<!-- 右侧群组 -->
<template>
    <div class="right-group">
        <div class="right-group-item">
            <div title="投诉统计" class="itembox">
                <PlanChart class="pieCharts" :isBig="true" />
            </div>
            <div title="计划统计" class="itembox">
                <EventChart class="pieCharts" :isBig="true" />
            </div>
            <div title="事件统计" class="itembox">
                <ComplaintChart :isBig="true" class="pieCharts" />
            </div>
        </div>
        <div class="right-group-item">
            <div title="事件情况" class="itembox center1">
                <EventCard />
            </div>
            <div title="缺陷" class="itembox center2">
                <DefectCard />
            </div>
        </div>
        <div class="right-group-item">
            <ChartBox title="排班统计图" class="itembox flex1">
                <TeamTaskChart class="left-group-item-content" />
                <!-- <FacilityCount class="left-group-item-content" /> -->
            </ChartBox>
            <ChartBox title="车流量统计图" class="itembox flex2">
                <CarChart class="left-group-item-content" />
                <!-- <FacilityCount class="left-group-item-content" /> -->
            </ChartBox>
        </div>
    </div>
</template>

<script setup lang="ts">
import ComplaintChart from './Chart/ComplaintChart.vue'
import PlanChart from './Chart/PlanChart.vue'
import EventChart from './Chart/EventChart.vue'
import CarChart from './Chart/CarChartB.vue'
import EventCard from './Chart/EventCard.vue'
import TeamTaskChart from './Chart/TeamTaskChartB.vue'
import DefectCard from './Chart/DefectCard.vue'
</script>

<style lang="scss" scoped>
.pieCharts {
    width: 568px;
    height: 230px;
}
.right-group {
    position: absolute;
    right: 0px;
    top: 0px;
    bottom: 0px;
    width: 1920px;
    z-index: 111;
    display: flex;
    flex-direction: column;
    padding: 75px 60px 54px 60px;
    justify-content: space-between;
    background: url('@/assets/images/bigscreen/右侧渐变蒙版***********') no-repeat center center;
    background-size: 100% 100%;
    // background: red;
}
.right-group-item {
    display: flex;
    width: 100%;
    height: 30%;
    gap: 20px;
    // background: red;
    .center1 {
        flex: 1;
        height: 330px;
        // background: red;
        margin-left: 100px;
   margin-top: -50px;
    }
    .center2 {
        flex: 1;
        height: 330px;
        // margin-left: 400px;
        margin-top: -50px;
    }
    .flex1 {
        flex: 1;
        height: 302px;
    }
    .flex2 {
        flex: 2;
        height: 302px;
    }
}

.itembox {
    flex: 1;
    display: flex;
}
</style>
