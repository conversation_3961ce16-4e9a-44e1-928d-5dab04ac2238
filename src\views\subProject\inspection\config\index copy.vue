<!-- 巡检配置列表 -->
<template>
    <div class="p-2">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item>
                            <el-select placeholder="请选择管理单元" clearable />
                        </el-form-item>
                        <el-form-item>
                            <el-select placeholder="请选择房间" clearable />
                        </el-form-item>
                        <el-form-item>
                            <el-select placeholder="请选择设备系统分类" clearable />
                        </el-form-item>
                        <el-form-item prop="name">
                            <el-input v-model="queryParams.name" placeholder="设备名称" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <div class="btn-box">
                    <div class="filter">
                        <!-- <el-button type="primary" plain>待处理</el-button>
                        <el-button type="primary" plain>已处理</el-button>
                        <el-button type="primary" plain>全部</el-button> -->
                    </div>
                    <div class="export">
                        <el-button type="primary" plain @click="handleConfirm('选择设备')">选择设备</el-button>
                        <el-button plain @click="handleAssign('覆盖率')">巡检覆盖率</el-button>
                        <!-- <el-button type="primary" plain @click="handleConfirm('导出')">导出</el-button> -->
                    </div>
                </div>
            </template>

            <el-table v-loading="loading" :data="yearPlanCatalogList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="true" />
                <el-table-column label="设备名称" align="center" prop="name" />
                <el-table-column label="设备编码" align="center" prop="code" />
                <el-table-column label="机电分系统" align="center">
					<template #default="scope">

					</template>
				</el-table-column>
                <!-- <el-table-column label="养护项目" align="center" prop="name" /> -->
                <el-table-column label="机电子系统" align="center" prop="name" >
					<template #default="scope">

					</template>
				</el-table-column>
                <el-table-column label="设备类型" align="center" prop="name" >
					<template #default="scope"></template>
				</el-table-column>
                <el-table-column label="管理单元" align="center" prop="unitId" >
					<template #default="scope"></template>
				</el-table-column>
                <el-table-column label="房间" align="center" prop="roomId" >
						<template #default="scope"></template>
				</el-table-column>
                <el-table-column label="数据类型" align="center" prop="inpectionTypes" >
					<template #default="scope"></template>
				</el-table-column>
                <el-table-column label="配置巡检项" align="center" prop="configed" >
					<template #default="scope"></template>
				</el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-tooltip content="巡检项" placement="top">
                            <el-button link type="primary" @click="handleUpdate(scope.row)" v-hasPermi="['plan:yearPlanCatalog:edit']"></el-button>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top">
                            <el-button link type="primary" @click="handleUpdate(scope.row)" v-hasPermi="['plan:yearPlanCatalog:remove']"></el-button>
                        </el-tooltip>
                       
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <!-- 添加或修改年度计划目录对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="900px" append-to-body>
            <EquipmentSel />
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import {
    listYearPlanCatalog,
    getYearPlanCatalog,
    delYearPlanCatalog,
    addYearPlanCatalog,
    updateYearPlanCatalog
} from '@/api/subProject/plan/yearPlanCatalog';
import { YearPlanCatalogVO, YearPlanCatalogQuery, YearPlanCatalogForm } from '@/api/subProject/plan/yearPlanCatalog/types';
import EquipmentSel from '../../components/EquipmentSel.vue';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const yearPlanCatalogList = ref<YearPlanCatalogVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const yearPlanCatalogFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
});

const initFormData: YearPlanCatalogForm = {
    id: undefined,
    projectId: undefined,
    year: undefined,
    name: undefined
};
const data = reactive<PageData<YearPlanCatalogForm, YearPlanCatalogQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        name: undefined,
        params: {}
    },
    rules: {
        id: [{ required: true, message: '不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询年度计划目录列表 */
const getList = async () => {
    loading.value = true;
    const res = await listYearPlanCatalog(queryParams.value);
    yearPlanCatalogList.value = res.rows;
    total.value = res.total;
    loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
    reset();
    dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData };
    yearPlanCatalogFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: YearPlanCatalogVO[]) => {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = '添加年度计划目录';
};
/** 导航到查看、指派页面 */
const handleAssign = (operationName: string) => {
    // reset();
    // dialog.visible = true;
    // dialog.title = '添加年度计划目录';
    proxy?.$router.push('coverageRate');
};

/** 审批按钮操作 */
const handleConfirm = (operationName: string) => {
    //reset();
    dialog.visible = true;
    dialog.title = operationName;
    //proxy?.$router.push('assign');
};

/** 修改按钮操作 */
const handleUpdate = async (row?: YearPlanCatalogVO) => {
    reset();
    const _id = row?.id || ids.value[0];
    const res = await getYearPlanCatalog(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = '修改年度计划目录';
};

/** 提交按钮 */
const submitForm = () => {
    yearPlanCatalogFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true;
            if (form.value.id) {
                await updateYearPlanCatalog(form.value).finally(() => (buttonLoading.value = false));
            } else {
                await addYearPlanCatalog(form.value).finally(() => (buttonLoading.value = false));
            }
            proxy?.$modal.msgSuccess('操作成功');
            dialog.visible = false;
            await getList();
        }
    });
};

/** 删除按钮操作 */
const handleDelete = async (row?: YearPlanCatalogVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除年度计划目录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
    await delYearPlanCatalog(_ids);
    proxy?.$modal.msgSuccess('删除成功');
    await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'plan/yearPlanCatalog/export',
        {
            ...queryParams.value
        },
        `yearPlanCatalog_${new Date().getTime()}.xlsx`
    );
};

onMounted(() => {
    getList();
});
</script>
<style lang="scss" scoped>
.btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .filter {
        flex: 1;
    }
    .export {
        margin-left: auto;
    }
}
</style>
