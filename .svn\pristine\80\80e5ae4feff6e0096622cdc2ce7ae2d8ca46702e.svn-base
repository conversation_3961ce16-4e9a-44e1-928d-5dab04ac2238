<!-- 待办事项 -->
<template>
    <div class="service-container">
        <!-- 标题栏 -->
        <div class="header">
            <div class="filter custom-date" @click.stop="openDatePicker">
                <el-date-picker
                    ref="datePickerRef"
                    v-model="date"
                    type="date"
                    placeholder="选择日期"
                    :clearable="false"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    editable="false"
                    @change="onDateChange"
                />
            </div>
            <div class="count">总数:{{ taskCount }}条</div>
            <!-- 标签页 -->
            <div class="tabs">
                <el-tabs v-model="activeTab" @tab-change="onTabChange">
                    <el-tab-pane label="养护" name="curing" v-if="isTabVisible('curing')">
                        <div class="tab-badge">{{ getTabCount('curing') }}</div>
                    </el-tab-pane>
                    <el-tab-pane label="封道" name="sealing" v-if="isTabVisible('sealing')">
                        <div class="tab-badge">{{ getTabCount('sealing') }}</div>
                    </el-tab-pane>
                    <el-tab-pane label="巡检" name="inspect" v-if="isTabVisible('inspect')">
                        <div class="tab-badge">{{ getTabCount('inspect') }}</div>
                    </el-tab-pane>
                    <el-tab-pane label="缺陷" name="defect" v-if="isTabVisible('defect') && defectVisible">
                        <div class="tab-badge">{{ getTabCount('defect') }}</div>
                    </el-tab-pane>
                    <el-tab-pane label="维修" name="repair" v-if="isTabVisible('repair')">
                        <div class="tab-badge">3</div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <!-- 表格 -->
        <div class="table-container">
            <!-- 缺陷专用表格 -->
            <el-table
                :data="defectList"
                style="width: 100%"
                :border="false"
                :show-header="true"
                v-loading="loading"
                :height="360"
                v-if="activeTab === 'defect'"
            >
                <el-table-column type="index" label="序号" width="80" :index="indexMethod" />
                <!-- <el-table-column prop="description" label="缺陷描述">
                    <template #default="scope">
                        {{ scope.row.description || '暂无描述' }}
                    </template>
                </el-table-column> -->
                <el-table-column prop="deviceName" label="缺陷对象" width="150">
                    <template #default="scope">
                        {{ scope.row.deviceName || '未知设备' }}
                    </template>
                </el-table-column>
                <el-table-column prop="unitName" label="缺陷位置"> </el-table-column>
                <el-table-column prop="disoverTime" label="发现时间" width="180">
                    <template #default="scope">
                        <span>{{ formatDate(scope.row.disoverTime) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="缺陷来源" align="center" prop="source">
                    <template #default="scope">
                        <dict-tag :options="defect_source" :value="scope.row.source" />
                    </template>
                </el-table-column>
                <el-table-column prop="currentStatus" label="状态" width="100">
                    <template #default="scope">
                        <span class="status-assign" @click="goDefectDetail(scope.row.id)">
                            <img class="status-assign__icon" src="@/assets/images/share-icon.png" alt="assign" />
                            {{ getDefectStatusLabel(scope.row.currentStatus) }}
                        </span>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 任务表格 -->
            <el-table :data="filteredTableData" style="width: 100%" :border="false" :show-header="true" v-loading="loading" :height="360" v-else>
                <el-table-column type="index" label="序号" width="80" :index="indexMethod" />
                <el-table-column prop="name" label="作业单名称">
                    <template #default="scope">
                        {{ scope.row.name }}
                    </template>
                </el-table-column>
                <el-table-column prop="bgnDate" label="计划作业日期" width="180">
                    <template #default="scope">
                        <span>{{ formatDate(scope.row.bgnDate) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="shiftType" label="班次" width="100">
                    <template #default="scope">
                        <span>{{ work_order_white_evening.find((item) => item.value === scope.row.shiftType)?.label || scope.row.shiftType }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="currentStatus" label="状态" width="100">
                    <template #default="scope">
                        <span class="status-assign" @click="goTaskDetail(scope.row.id, scope.row.taskType)">
                            <img class="status-assign__icon" src="@/assets/images/share-icon.png" alt="assign" />
                            {{ getButtonLabel(scope.row.currentStatus) }}
                        </span>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页组件 -->
            <div class="pager-wrap">
                <!-- 缺陷分页组件 -->
                <pagination
                    v-if="activeTab === 'defect'"
                    v-show="defectTotal > 0"
                    :total="defectTotal"
                    v-model:page="defectQueryParams.pageNum"
                    v-model:limit="defectQueryParams.pageSize"
                    @pagination="handleDefectPagination"
                />

                <!-- 任务分页组件 -->
                <pagination
                    v-else
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    @pagination="handleTaskPagination"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, getCurrentInstance, toRefs, reactive, watch } from 'vue'
import { listTaskView } from '@/api/plan/taskView'
import { TaskViewVO, TaskViewQuery } from '@/api/plan/taskView/types'
import { listDiscoveredDefect } from '@/api/subProject/operation/defect'
import { DiscoveredDefectViewVO, DiscoveredDefectQuery } from '@/api/subProject/operation/defect/types'

import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'

// 定义组件属性
const props = defineProps({
    date: {
        type: String,
        default: ''
    },
    defectVisible: {
        type: Boolean,
        default: true
    },
    visibleTabs: {
        type: String,
        default: 'curing,repair,inspect,sealing,defect'
    }
})

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { task_status, work_order_white_evening, defect_status, defect_source } = toRefs<any>(
    proxy?.useDict('task_status', 'work_order_white_evening', 'defect_status', 'defect_source')
)
const userStore = useUserStore()
const appStore = useAppStore()

const datePickerRef = ref<any>(null)
const openDatePicker = () => {
    try {
        const instance = datePickerRef.value
        if (instance) {
            if (typeof instance.focus === 'function') instance.focus()
            if (typeof (instance as any).handleFocus === 'function') (instance as any).handleFocus()
            ;(instance as any).pickerVisible = true
        }
    } catch (e) {}
}
const getButtonLabel = (statusValue: string) => {
    // 从task_status数据字典中查找对应的标签
    const dictItem = task_status.value?.find((item: any) => item.value === statusValue)

    // 如果找到字典项，返回其标签；否则返回原始值
    return dictItem?.label || statusValue
}

// 内部日期状态
const date = ref(props.date)

// 日期范围状态（用于查询）
const dateRangeBgnDate = ref<[string, string]>(['', ''])

// 缺陷相关状态
const defectList = ref<DiscoveredDefectViewVO[]>([])
const defectTotal = ref(0)
const dateRangeDisoverTime = ref<[string, string]>(['', ''])

// 缺陷查询参数
const defectQueryParams = reactive<DiscoveredDefectQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: appStore.projectContext.selectedProjectId,
    todoStatus: 'ACTIVE',
    params: {}
})

// 缺陷可见性状态
const defectVisible = ref(props.defectVisible)

// 计算可见的tabs
const visibleTabsArray = computed(() => {
    return props.visibleTabs.split(',').map((tab) => tab.trim())
})

// 检查tab是否可见
const isTabVisible = (tabName: string) => {
    return visibleTabsArray.value.includes(tabName)
}

// 监听props变化
watch(
    () => props.date,
    (newValue) => {
        date.value = newValue
        loadTaskData()
    }
)

watch(
    () => props.defectVisible,
    (newValue) => {
        defectVisible.value = newValue
        loadTaskData()
    }
)

watch(
    () => props.visibleTabs,
    () => {
        // 如果当前激活的tab不在可见列表中，切换到第一个可见的tab
        if (!isTabVisible(activeTab.value) && visibleTabsArray.value.length > 0) {
            activeTab.value = visibleTabsArray.value[0]
            loadTaskData()
        }
    }
)

// 当前激活的标签页
const activeTab = ref('curing')

// 表格数据
const tableData = ref<TaskViewVO[]>([])

// 加载状态
const loading = ref(false)

// 总记录数
const total = ref(0)

// 查询参数
const queryParams = ref<TaskViewQuery>({
    pageNum: 1,
    pageSize: 5,
    taskType: 'curing'
})

// 根据当前标签页过滤数据
const filteredTableData = computed(() => {
    if (activeTab.value === 'defect') {
        // 缺陷页签返回空数组，因为使用独立的表格
        return []
    }
    return tableData.value
})

// 计算总任务数量
const taskCount = computed(() => {
    if (activeTab.value === 'defect') {
        return defectTotal.value
    }
    return total.value
})

// 获取各标签页的数量
const getTabCount = (taskType: string) => {
    if (taskType === 'defect') {
        return defectTotal.value
    }
    // 这里应该根据实际需求调整，可能需要单独的API来获取各类型的数量
    return taskType === activeTab.value ? total.value : 0
}

// 序号方法
const indexMethod = (index: number) => {
    return (queryParams.value.pageNum! - 1) * queryParams.value.pageSize! + index + 1
}

// 获取缺陷状态标签
const getDefectStatusLabel = (statusValue: string) => {
    if (!defect_status.value) return statusValue
    const dictItem = defect_status.value.find((item: any) => item.value === statusValue)
    return dictItem?.label || statusValue
}

// 跳转到缺陷详情页面
const goDefectDetail = (defectId: string) => {
    proxy?.$router.push({
        path: '/subProject/circle/maintain/defectAssign',
        query: { id: defectId }
    })
}

const goTaskDetail = (id: string, taskType: string) => {
    switch (taskType) {
        case 'curing':
            proxy?.$router.push({
                path: '/subProject/circle/maintain/maintainAssign',
                query: { taskId: id }
            })
            break
        case 'inspect':
            proxy?.$router.push({
                path: '/subProject/circle/inspection/assign',
                query: { taskId: id }
            })
            break
        case 'sealing':
            proxy?.$router.push({
                path: '/subProject/circle/maintain/sealingAssign',
                query: { taskId: id }
            })
            break
        case 'defect':
            goDefectDetail(id)
            break
    }
}

// 日期格式化函数
const formatDate = (date: string) => {
    if (!date) return ''
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
}

// 新增：查询缺陷列表方法
const getDefectList = async () => {
    try {
        loading.value = true

        // 设置查询参数
        const query: DiscoveredDefectQuery = {
            pageNum: defectQueryParams.pageNum,
            pageSize: defectQueryParams.pageSize,
            projectId: appStore.projectContext.selectedProjectId,
            todoStatus: 'ACTIVE', // 只查询待处理的缺陷
            assignee: userStore.userId.toString(), // 添加 assignee 查询条件
            params: {}
        }

        // 使用日期范围查询 disover_time
        if (date.value) {
            // 构建日期范围：从 00:00:00 到 23:59:59
            const startDateTime = `${date.value} 00:00:00`
            const endDateTime = `${date.value} 23:59:59`
            dateRangeDisoverTime.value = [startDateTime, endDateTime]

            // 使用 addDateRange 方法添加日期范围查询条件
            proxy?.addDateRange(query, dateRangeDisoverTime.value, 'DisoverTime')
        } else {
            // 清空日期范围
            dateRangeDisoverTime.value = ['', '']
        }

        const response = await listDiscoveredDefect(query)
        defectList.value = response.rows || []
        defectTotal.value = response.total || 0
    } catch (error) {
        console.error('加载缺陷数据失败:', error)
        defectList.value = []
        defectTotal.value = 0
    } finally {
        loading.value = false
    }
}

// 查询任务列表（分页版本）
const getList = async () => {
    if (activeTab.value === 'defect') {
        // 调用缺陷查询方法
        await getDefectList()
    } else {
        try {
            loading.value = true
            let taskType = activeTab.value
            let taskTypeSub = ''
            if (taskType === 'repair') {
                taskTypeSub = 'repair'
                taskType = 'curing'
            }
            // 设置查询参数
            const query: TaskViewQuery = {
                pageNum: queryParams.value.pageNum,
                pageSize: queryParams.value.pageSize,
                taskStep: 'task',
                taskType: taskType,
                taskTypeSub: taskTypeSub,
                assignee: userStore.userId.toString(),
                projectId: appStore.projectContext.selectedProjectId,
                todoStatus: 'ACTIVE' //此处必须传ACTIVE，而不是PENDING
            }

            // 使用日期范围查询，参考 index.vue 的实现
            if (date.value) {
                // 构建日期范围：从 00:00:00 到 23:59:59
                const startDateTime = `${date.value} 00:00:00`
                const endDateTime = `${date.value} 23:59:59`
                dateRangeBgnDate.value = [startDateTime, endDateTime]

                // 使用 addDateRange 方法添加日期范围查询条件
                proxy?.addDateRange(query, dateRangeBgnDate.value, 'BgnDate')
            } else {
                // 清空日期范围
                dateRangeBgnDate.value = ['', '']
            }

            const response = await listTaskView(query)
            tableData.value = response.rows || []
            total.value = response.total || 0
        } catch (error) {
            console.error('加载任务数据失败:', error)
            tableData.value = []
            total.value = 0
        } finally {
            loading.value = false
        }
    }
}

// 加载任务数据（兼容原有调用）
const loadTaskData = async () => {
    // 重置到第一页
    queryParams.value.pageNum = 1
    await getList()
}

// 日期变化处理
const onDateChange = () => {
    loadTaskData()
}

// 标签页变化处理
const onTabChange = () => {
    console.log('标签页切换到:', activeTab.value)

    if (activeTab.value === 'defect') {
        // 切换到缺陷页签时，重置缺陷分页到第一页
        defectQueryParams.pageNum = 1
        getDefectList()
    } else {
        // 切换到任务页签时，重置任务分页到第一页
        queryParams.value.pageNum = 1
        queryParams.value.taskType = activeTab.value
        loadTaskData()
    }
}

// 缺陷分页处理
const handleDefectPagination = () => {
    console.log('缺陷分页变化:', {
        pageNum: defectQueryParams.pageNum,
        pageSize: defectQueryParams.pageSize,
        total: defectTotal.value
    })
    getDefectList()
}

// 任务分页处理
const handleTaskPagination = () => {
    console.log('任务分页变化:', {
        pageNum: queryParams.value.pageNum,
        pageSize: queryParams.value.pageSize,
        total: total.value
    })
    getList()
}

// 分页变化（联动刷新）- 保持向后兼容
const onPageChange = () => {
    getList()
}

// 组件挂载时加载数据
onMounted(() => {
    // 确保初始激活的tab是可见的
    if (!isTabVisible(activeTab.value) && visibleTabsArray.value.length > 0) {
        activeTab.value = visibleTabsArray.value[0]
    }
    loadTaskData()
})
</script>

<style lang="scss" scoped>
.service-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 10px;

    .header {
        display: flex;
        align-items: center;
        padding: 5px 0px;
        margin-bottom: 8px;
        //border-bottom: 1px solid #ebeef5;

        .count {
            margin-left: 12px;
            font-size: 14px;
            color: #93b7d4;
        }

        .filter {
            margin-left: 0px;
        }

        .custom-date {
            background: #232d45;
            border-radius: 6px;
            padding: 6px 32px 6px 36px;
            position: relative;
            width: 220px;
            cursor: pointer;
        }
        .custom-date::after {
            content: '';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 8px solid #ffffff;
            pointer-events: none;
        }
        .custom-date::before {
            pointer-events: none;
        }
        .custom-date::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 18px;
            height: 18px;
            background: url('@/assets/images/date-icon.png') no-repeat center/contain;
        }
        :deep(.custom-date .el-date-editor) {
            background: transparent !important;
            width: 100%;
        }
        :deep(.custom-date .el-input__wrapper) {
            background: transparent !important;
            box-shadow: none !important;
            cursor: pointer;
        }
        :deep(.custom-date .el-input__prefix) {
            display: none !important;
        }
        :deep(.custom-date .el-input__suffix) {
            display: none !important;
        }
        :deep(.custom-date .el-input__inner) {
            color: #ffffff;
            cursor: pointer;
            caret-color: transparent;
            user-select: none;
        }

        .tabs {
            margin-left: auto;
            padding: 0 20px;

            :deep(.el-tabs__header) {
                margin-bottom: -5px;
            }

            :deep(.el-tabs__nav-wrap::after) {
                display: none;
            }

            :deep(.el-tabs__nav) {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            :deep(.el-tabs__item) {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 72px;
                height: 32px;
                line-height: 32px;
                background-image: url('@/assets/images/<EMAIL>');
                background-repeat: no-repeat;
                background-size: 72px 32px;
                background-position: center center;
                color: #7ecfff;
                cursor: pointer;
                transition: color 0.2s;
                border: 0;
                padding: 0;
            }

            :deep(.el-tabs__item.is-active) {
                background-image: url('@/assets/images/<EMAIL>');
                color: #fff;
                font-weight: bold;
            }

            :deep(.el-tabs__active-bar) {
                display: none !important;
            }

            .tab-badge {
                position: absolute;
                top: 10px;
                right: 0;
                min-width: 16px;
                height: 16px;
                line-height: 16px;
                padding: 0 4px;
                border-radius: 8px;
                background-color: #f56c6c;
                color: #fff;
                font-size: 12px;
                text-align: center;
            }
        }
    }

    .table-container {
        flex: 1;
        padding: 0;
        overflow-y: auto;

        :deep(.el-table) {
            --el-table-bg-color: transparent;
            --el-table-header-bg-color: transparent;
            --el-table-tr-bg-color: transparent;
            --el-table-row-hover-bg-color: rgba(255, 255, 255, 0.06);
            --el-table-border-color: rgba(255, 255, 255, 0.08);
            background-color: transparent;
            color: #ffffff;
        }
        :deep(.el-table__inner-wrapper::before),
        :deep(.el-table__inner-wrapper::after),
        :deep(.el-table::before),
        :deep(.el-table--border::after),
        :deep(.el-table__border-left-patch) {
            display: none !important;
            background: transparent !important;
        }
        :deep(.el-table__body-wrapper),
        :deep(.el-scrollbar__wrap) {
            border: none !important;
        }

        :deep(.el-table .el-table__cell .cell) {
            text-align: center;
        }

        :deep(.el-table th.el-table__cell),
        :deep(.el-table td.el-table__cell),
        :deep(.el-table tr) {
            background-color: transparent !important;
        }

        :deep(.el-table thead tr) {
            background: linear-gradient(to right, rgba(79, 158, 249, 0.1) 0%, rgba(79, 158, 249, 0.2) 60%, rgba(79, 158, 249, 0.3) 100%) !important;
        }

        :deep(.el-table thead .el-table__cell .cell) {
            color: #aed7f2;
            font-weight: 600;
        }

        :deep(.el-table__body tr:nth-child(odd)) {
            background: transparent !important;
        }

        :deep(.el-table__body tr:nth-child(even)) {
            background: linear-gradient(to left, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
        }

        :deep(.el-table__body tr:hover) {
            background-image: linear-gradient(rgba(255, 255, 255, 0.06), rgba(255, 255, 255, 0.06)), inherit !important;
            background-blend-mode: screen;
        }

        .task-name-link {
            color: #409eff;
            cursor: pointer;

            &:hover {
                color: #66b1ff;
                text-decoration: underline;
            }
        }

        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .status-progress {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .status-done {
            background-color: #f6ffed;
            color: #52c41a;
        }
        .status-assign {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            color: #00f5fe;
            cursor: pointer;
        }
        .status-assign__icon {
            width: 14px;
            height: 14px;
            display: inline-block;
        }
    }
    .pager-wrap {
        display: flex;
        justify-content: center;
        padding: 0;
        margin-top: 2px;
    }
}
</style>
