import { ref, onMounted, onUnmounted } from 'vue'
import { useTransition, TransitionPresets } from '@vueuse/core'
import { getStatisticItem, getDefaultStatisticValue } from '@/api/statistics/dashboard'
import type { StatisticItem, StatisticType } from '@/api/statistics/dashboard/types'

/**
 * 统计数据获取和动画效果的 composable
 * @param type 统计类型
 * @param updateInterval 更新间隔(毫秒)，默认2分钟
 * @param transitionOptions 动画配置选项
 */
export function useStatisticData(
    type: StatisticType,
    updateInterval: number = 120000,
    transitionOptions?: {
        duration?: number
        transition?: any
    }
) {
    const data = ref<StatisticItem | null>(null)
    const rawValue = ref<number>(0) // 原始数值
    const loading = ref(false)
    const error = ref<string | null>(null)

    // 默认动画配置
    const defaultOptions = {
        duration: 2000,
        transition: TransitionPresets.easeOutCubic
    }

    const options = { ...defaultOptions, ...transitionOptions }

    // 使用 VueUse 的 useTransition 实现记分牌效果
    const displayValue = useTransition(rawValue, options)

    // 安全的数值转换函数
    const convertToNumber = (value: any): number => {
        if (typeof value === 'number' && !isNaN(value)) {
            return value
        }
        if (typeof value === 'string') {
            const parsed = parseFloat(value)
            if (!isNaN(parsed)) {
                return parsed
            }
        }
        console.warn('无法转换为有效数字:', value)
        return 0 // 返回默认值
    }

    // 获取数据
    const fetchData = async (silent = false) => {
        if (!silent) loading.value = true
        error.value = null

        try {
            const response = await getStatisticItem(type)
            const newData = response.data

            // 安全的数值转换：确保 value 是有效的数字
            const processedData = {
                ...newData,
                value: convertToNumber(newData.value)
            }

            // 更新数据和触发动画
            if (data.value?.value !== processedData.value) {
                data.value = processedData
                rawValue.value = processedData.value // 这会自动触发 useTransition 动画
            }
        } catch (err: any) {
            error.value = err.message || '数据获取失败'
            console.warn(`统计数据获取失败 [${type}]:`, err.message)

            // 静默失败，使用默认值确保大屏显示不受影响
            if (!data.value) {
                const defaultData = getDefaultStatisticValue(type)
                data.value = defaultData
                rawValue.value = convertToNumber(defaultData.value)
            }
        } finally {
            if (!silent) loading.value = false
        }
    }

    // 定时更新
    let intervalId: NodeJS.Timeout | null = null

    const startAutoUpdate = () => {
        if (intervalId) clearInterval(intervalId)
        intervalId = setInterval(() => {
            fetchData(true) // 静默更新，不显示loading
        }, updateInterval)
    }

    const stopAutoUpdate = () => {
        if (intervalId) {
            clearInterval(intervalId)
            intervalId = null
        }
    }

    // 手动刷新
    const refresh = () => fetchData()

    // 组件挂载时获取数据并开始定时更新
    onMounted(() => {
        // 先设置默认值，避免初始显示空白
        const defaultData = getDefaultStatisticValue(type)
        data.value = defaultData
        rawValue.value = convertToNumber(defaultData.value)

        // 然后获取真实数据
        fetchData()
        startAutoUpdate()
    })

    // 组件卸载时清理定时器
    onUnmounted(() => {
        stopAutoUpdate()
    })

    return {
        data,
        displayValue, // 这个是带动画效果的数值
        loading,
        error,
        refresh,
        startAutoUpdate,
        stopAutoUpdate
    }
}

/**
 * 预设的动画配置
 */
export const ANIMATION_PRESETS = {
    // 快速动画 - 适用于小数值
    fast: {
        duration: 1000,
        transition: TransitionPresets.easeInOutCubic
    },
    // 标准动画 - 适用于大部分场景
    normal: {
        duration: 2000,
        transition: TransitionPresets.easeOutCubic
    },
    // 慢速动画 - 适用于大数值或重要指标
    slow: {
        duration: 3000,
        transition: TransitionPresets.easeOutQuart
    },
    // 弹性动画 - 适用于需要吸引注意的指标
    bounce: {
        duration: 2500,
        transition: TransitionPresets.easeOutBack
    }
}
