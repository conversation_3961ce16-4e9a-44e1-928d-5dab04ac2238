import request from '@/utils/request'
import { AxiosPromise } from 'axios'
import { EquipmentVO, EquipmentForm, EquipmentQuery } from '@/api/subProject/basic/equipment/types'
import { EquipmentReplacementVO, EquipmentReplacementForm, EquipmentReplacementQuery } from '@/api/subProject/basic/equipment/types'

/**
 * 查询设施即设备信息列表
 * @param query
 * @returns {*}
 */

export const listEquipment = (query?: EquipmentQuery): AxiosPromise<EquipmentVO[]> => {
    return request({
        url: '/basic/equipment/list',
        method: 'get',
        params: query
    })
}

/**
 * 查询可用于巡检配置的设备列表
 * @param query
 * @returns {*}
 */
export const listInspectionConfigableEquipments = (query?: EquipmentQuery): AxiosPromise<EquipmentVO[]> => {
    return request({
        url: '/basic/equipment/listInspectionConfigableEquipments',
        method: 'get',
        params: query
    })
}

/**
 * 查询设施即设备信息详细
 * @param id
 */
export const getEquipment = (id: string | number): AxiosPromise<EquipmentVO> => {
    return request({
        url: '/basic/equipment/' + id,
        method: 'get'
    })
}

/**
 * 新增设施即设备信息
 * @param data
 */
export const addEquipment = (data: EquipmentForm) => {
    return request({
        url: '/basic/equipment',
        method: 'post',
        data: data
    })
}

/**
 * 修改设施即设备信息
 * @param data
 */
export const updateEquipment = (data: EquipmentForm) => {
    return request({
        url: '/basic/equipment',
        method: 'put',
        data: data
    })
}

/**
 * 删除设施即设备信息
 * @param id
 */
export const delEquipment = (id: string | number | Array<string | number>) => {
    return request({
        url: '/basic/equipment/' + id,
        method: 'delete'
    })
}

/**
 * 查询设备更换记录列表
 * @param query 查询参数
 * @returns {*}
 */
export const listEquipmentReplacement = (query?: EquipmentReplacementQuery): AxiosPromise<any> => {
    return request({
        url: '/basic/equipment/replacement/list',
        method: 'get',
        params: query
    })
}

/**
 * 查询设备更换记录详细
 * @param id 记录ID
 */
export const getEquipmentReplacement = (id: string | number): AxiosPromise<EquipmentReplacementVO> => {
    return request({
        url: '/basic/equipment/replacement/' + id,
        method: 'get'
    })
}

/**
 * 新增设备更换记录
 * @param data 更换记录数据
 */
export const addEquipmentReplacement = (data: EquipmentReplacementForm) => {
    return request({
        url: '/basic/equipment/replacement',
        method: 'post',
        data: data
    })
}

/**
 * 修改设备更换记录
 * @param data 更换记录数据
 */
export const updateEquipmentReplacement = (data: EquipmentReplacementForm) => {
    return request({
        url: '/basic/equipment/replacement',
        method: 'put',
        data: data
    })
}

/**
 * 删除设备更换记录
 * @param id 记录ID
 */
export const delEquipmentReplacement = (id: string | number | Array<string | number>) => {
    return request({
        url: '/basic/equipment/replacement/' + id,
        method: 'delete'
    })
}

/**
 * 根据关键词搜索设备
 * @param keyword 搜索关键词
 * @param projectId 项目ID
 * @param categoryIdThird 设备类别（244为摄像头）
 * @param pageSize 返回数量限制
 */
export const searchEquipmentByKeyword = (params: {
    keyword: string
    projectId: string
    categoryIdThird?: string
    pageSize?: number
}): AxiosPromise<EquipmentVO[]> => {
    return request({
        url: '/basic/equipment/searchEquipmentByKeyword',
        method: 'get',
        params
    })
}

/**
 * 根据设备ID列表批量获取设备信息
 * @param ids 设备ID列表
 */
export const batchGetEquipment = (ids: string[]): AxiosPromise<EquipmentVO[]> => {
    return request({
        url: '/basic/equipment/batchGetByIds',
        method: 'post',
        data: ids
    })
}
