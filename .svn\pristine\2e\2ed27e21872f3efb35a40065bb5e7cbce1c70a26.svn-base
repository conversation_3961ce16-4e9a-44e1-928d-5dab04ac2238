<template>
    <div class="p-2">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <!-- <el-form-item label="" prop="projectId">
              <el-input v-model="queryParams.projectId" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
                        <el-form-item label="" prop="typeId">
                            <el-select v-model="form.typeId" clearable placeholder="请输入" @change="handleQuery">
                                <el-option v-for="item in resourceTypeList" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                            <!-- <el-input v-model="queryParams.typeId" placeholder="请输入" clearable @keyup.enter="handleQuery" /> -->
                        </el-form-item>
                        <el-form-item label="物资性质" prop="nature">
                            <el-select v-model="queryParams.nature" placeholder="请选择物资性质" clearable>
                                <el-option v-for="dict in tnl_resource_nature" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basic:resource:add']">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['basic:resource:edit']"
                            >修改</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="danger"
                            plain
                            icon="Delete"
                            :disabled="multiple"
                            @click="handleDelete()"
                            v-hasPermi="['basic:resource:remove']"
                            >删除</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="warning" plain icon="Download" @click="handleExport" v-if="false" v-hasPermi="['basic:resource:export']"
                            >导出</el-button
                        >
                    </el-col>
                    <!-- <el-col :span="1.5">
                        <el-button type="text" plain @click="handleStockFlow">查看出入库记录</el-button>
                    </el-col> -->
                    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </template>

            <el-table v-loading="loading" :data="resourceList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <!-- <el-table-column label="" align="center" prop="projectId" /> -->
                <el-table-column label="物资类型" align="center" prop="typeId">
                    <template #default="scope">
                        {{ getTypeName(scope.row.typeId) }}
                    </template>
                </el-table-column>

                <el-table-column label="物资性质" align="center" prop="nature">
                    <template #default="scope">
                        <dict-tag :options="tnl_resource_nature" :value="scope.row.nature" />
                    </template>
                </el-table-column>
                <el-table-column label="物资属性" align="center" prop="properties">
                    <template #default="scope">
                        <span v-if="scope.row.properties">
                            {{ getPropertiesLabels(scope.row.properties) }}
                        </span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column label="规格型号" align="center" prop="specification" />
                <el-table-column label="物资单价" align="center" prop="price" />
                <el-table-column label="库存数量" align="center" prop="balanceAmount" />
                <el-table-column label="物资单位" align="center" prop="unit">
                    <template #default="scope">
                        <dict-tag :options="tnl_resource_unit" :value="scope.row.unit" />
                    </template>
                </el-table-column>
                <!-- <el-table-column label="库存数量" align="center" prop="balanceAmount" /> -->
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <!-- <el-tooltip content="出库" placement="top">
                            <el-button link type="primary" @click="handleStock(scope.row, 'in')" v-hasPermi="['basic:resource:instock']"
                                >入库</el-button
                            >
                        </el-tooltip>
                        <el-tooltip content="出库" placement="top">
                            <el-button link type="primary" @click="handleStock(scope.row, 'out')" v-hasPermi="['basic:resource:instock']"
                                >出库</el-button
                            >
                        </el-tooltip> -->
                        <el-tooltip content="修改" placement="top">
                            <el-button
                                link
                                type="primary"
                                icon="Edit"
                                @click="handleUpdate(scope.row)"
                                v-hasPermi="['basic:resource:edit']"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top">
                            <el-button
                                link
                                type="primary"
                                icon="Delete"
                                @click="handleDelete(scope.row)"
                                v-hasPermi="['basic:resource:remove']"
                            ></el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <!-- 添加或修改物资信息对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body :draggable="true">
            <el-form ref="resourceFormRef" :model="form" :rules="rules" label-width="80px">
                <!-- <el-form-item label="" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入" />
        </el-form-item> -->
                <el-form-item label="物资类别" prop="typeId">
                    <el-select v-model="form.typeId" placeholder="请输入" @change="handleChangeResourceType">
                        <el-option v-for="item in resourceTypeList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="物资单位" prop="unit">
                    <el-select v-model="form.unit" disabled placeholder="请选择物资单位">
                        <el-option v-for="dict in tnl_resource_unit" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="物资性质" prop="nature">
                    <el-select v-model="form.nature" disabled placeholder="请选择物资性质">
                        <el-option v-for="dict in tnl_resource_nature" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="物资属性" prop="properties">
                    <el-select :multiple="true" v-model="form.properties" placeholder="请选择物资属性">
                        <el-option v-for="dict in resource_property" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="规格型号" prop="specification">
                    <el-input v-model="form.specification" placeholder="请输入规格型号" />
                </el-form-item>
                <el-form-item label="物资单价" prop="price">
                    <el-input v-model="form.price" placeholder="请输入物资单价" />
                </el-form-item>
                <!-- <el-form-item label="库存数量" prop="balanceAmount">
                    <el-input v-model="form.balanceAmount" disabled placeholder="请输入库存数量" />
                </el-form-item> -->
                <el-form-item label="储存地" prop="storage">
                    <el-input v-model="form.storage" placeholder="请输入储存地" />
                </el-form-item>
                <!-- <el-form-item label="库存数量" prop="pictures">
                    <el-input v-model="form.pictures" type="textarea" placeholder="请输入内容" />
                </el-form-item> -->
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 入库对话框 -->
        <el-dialog title="物资入库" v-model="inStockDialog.visible" width="600px" append-to-body>
            <el-form ref="inStockFormRef" :model="inStockForm" :rules="inStockRules" label-width="80px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="物资名称">
                            <el-text>{{ selectedResource?.typeName }}</el-text>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="计量单位">
                            <el-text>
                                {{
                                    selectedResource?.unit
                                        ? tnl_resource_unit.find((dict) => dict.value === selectedResource.unit)?.label || selectedResource.unit
                                        : '-'
                                }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="物资性质">
                            <el-text>
                                {{
                                    selectedResource?.nature
                                        ? tnl_resource_nature.find((dict) => dict.value === selectedResource.nature)?.label || selectedResource.nature
                                        : '-'
                                }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="规格型号">
                            <el-text>{{ selectedResource?.specification }}</el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="物资属性">
                            <el-text>
                                {{ selectedResource?.properties ? getPropertiesLabels(selectedResource.properties) : '-' }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="所属部门" prop="targetDeptId">
                            <el-tree-select
                                v-model="inStockForm.targetDeptId"
                                :data="deptTreeData"
                                :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
                                value-key="deptId"
                                placeholder="请选择所属部门"
                                style="width: 100%"
                                clearable
                                filterable
                                check-strictly
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="入库时间" prop="handleTime">
                            <el-date-picker
                                v-model="inStockForm.handleTime"
                                type="datetime"
                                placeholder="请选择入库时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="需要盘点" prop="needCheck">
                            <el-select v-model="inStockForm.needCheck" placeholder="是否需要盘点" style="width: 100%">
                                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="最新单价" prop="price">
                            <el-input v-model="inStockForm.price" placeholder="请输入最新单价" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="入库数量" prop="amount">
                            <el-input v-model="inStockForm.amount" placeholder="请输入该批数量" type="number" min="0" step="0.01" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="入库类型" prop="stockWay">
                            <el-select v-model="inStockForm.stockWay" placeholder="请选择入库类型" style="width: 100%">
                                <el-option v-for="dict in stockInTypeList" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="描述" prop="description">
                            <el-input v-model="inStockForm.description" placeholder="请输入" type="textarea" :rows="3" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitInStockForm">确 定</el-button>
                    <el-button @click="cancelInStock">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 出库对话框 -->
        <el-dialog title="物资出库" v-model="outStockDialog.visible" width="600px" append-to-body>
            <el-form ref="outStockFormRef" :model="outStockForm" :rules="outStockRules" label-width="80px">
                <!-- 物资相关详情信息 -->
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="物资名称">
                            <el-text>{{ selectedResource?.typeName }}</el-text>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="规格型号">
                            <el-text>{{ selectedResource?.specification }}</el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="计量单位">
                            <el-text>
                                {{
                                    selectedResource?.unit
                                        ? tnl_resource_unit.find((dict) => dict.value === selectedResource.unit)?.label || selectedResource.unit
                                        : '-'
                                }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="物资性质">
                            <el-text>
                                {{
                                    selectedResource?.nature
                                        ? tnl_resource_nature.find((dict) => dict.value === selectedResource.nature)?.label || selectedResource.nature
                                        : '-'
                                }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="物资属性">
                            <el-text>
                                {{ selectedResource?.properties ? getPropertiesLabels(selectedResource.properties) : '-' }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 出库相关信息 -->
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="出库部门" prop="targetDeptId">
                            <el-tree-select
                                v-model="outStockForm.targetDeptId"
                                :data="deptTreeData"
                                :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
                                value-key="deptId"
                                placeholder="请选择出库部门"
                                style="width: 100%"
                                clearable
                                filterable
                                check-strictly
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="项目名称" prop="projectId">
                            <el-select v-model="outStockForm.projectId" placeholder="请选择项目编号" @change="handleChangeProject">
                                <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="管理单元" prop="unitId">
                            <el-select v-model="outStockForm.unitId" placeholder="请选择管理单元" style="width: 100%">
                                <el-option v-for="item in manageUnitList" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <!-- 空列，保持布局平衡 -->
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="出库时间" prop="handleTime">
                            <el-date-picker
                                v-model="outStockForm.handleTime"
                                type="datetime"
                                placeholder="请选择出库时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="需要盘点" prop="needCheck">
                            <el-select v-model="outStockForm.needCheck" placeholder="是否需要盘点" style="width: 100%">
                                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="最新单价" prop="price">
                            <el-input v-model="outStockForm.price" placeholder="请输入最新单价" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="出库数量" prop="amount">
                            <el-input v-model="outStockForm.amount" placeholder="请输入出库数量" type="number" min="0" step="0.01" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="出库类型" prop="stockWay">
                            <el-select v-model="outStockForm.stockWay" placeholder="请选择出库类型">
                                <el-option v-for="dict in stockOutTypeList" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="申请人" prop="handlerName">
                            <el-input v-model="outStockForm.handlerName" placeholder="请输入领用人" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="描述" prop="description">
                            <el-input v-model="outStockForm.description" placeholder="请输入" type="textarea" :rows="3" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitOutStockForm">确 定</el-button>
                    <el-button @click="cancelOutStock">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Resource" lang="ts">
import { nextTick } from 'vue'
import { listResource, getResourceView, delResource, addResource, updateResource } from '@/api/common/resource'
import { ResourceViewVO, ResourceViewQuery, ResourceForm } from '@/api/common/resource/types'
import { listResourceType } from '@/api/common/resourceType'
import { ResourceTypeQuery, ResourceTypeVO } from '@/api/common/resourceType/types'
import { addResourceStockFlow, updateResourceStockFlow } from '@/api/common/resourceStockFlow'
import { ResourceStockFlowVO, ResourceStockFlowQuery, ResourceStockFlowForm } from '@/api/common/resourceStockFlow/types'
import { listProject } from '@/api/project/project'
import { ProjectVO } from '@/api/project/project/types'
import { listDept } from '@/api/system/dept'
import { DeptVO } from '@/api/system/dept/types'
import { getProjectResourceStock } from '@/api/common/resourceStock'
import { ResourceStockVO } from '@/api/common/resourceStock/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import router from '@/router'
import { dir } from 'console'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { tnl_resource_unit, tnl_resource_nature, sys_yes_no, resource_property } = toRefs<any>(
    proxy?.useDict('tnl_resource_unit', 'tnl_resource_nature', 'sys_yes_no', 'resource_property')
)

const resourceList = ref<ResourceViewVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const resourceTypeList = ref<ResourceTypeVO[]>([])
const queryFormRef = ref<ElFormInstance>()
const resourceFormRef = ref<ElFormInstance>()
const resourceStockFlowFormRef = ref<ElFormInstance>()
const inStockFormRef = ref<ElFormInstance>()
const outStockFormRef = ref<ElFormInstance>()
const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})
const inStockDialog = reactive<DialogOption>({
    visible: false,
    title: ''
})
const outStockDialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

const initFormData: ResourceForm = {
    id: undefined,
    //projectId: undefined,
    typeId: undefined,
    properties: [] as string[],
    unit: undefined,
    nature: undefined,
    specification: undefined,
    price: undefined,
    balanceAmount: 0,
    storage: undefined,
    pictures: undefined
}

const resourceTypeQueryParams = ref<ResourceTypeQuery>({
    pageNum: 1,
    pageSize: 1000
})
const data = reactive<PageData<ResourceForm, ResourceViewQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        typeId: undefined,
        nature: undefined,
        params: {}
    },
    rules: {
        projectId: [{ required: true, message: '不能为空', trigger: 'blur' }],
        typeId: [{ required: true, message: '不能为空', trigger: 'blur' }],
        unit: [{ required: true, message: '物资单位不能为空', trigger: 'change' }],
        nature: [{ required: true, message: '物资性质不能为空', trigger: 'change' }],
        specification: [{ required: true, message: '规格型号不能为空', trigger: 'blur' }],
        price: [{ required: true, message: '物资单价不能为空', trigger: 'blur' }]
    }
})

const { queryParams, form, rules } = toRefs(data)
const initStockFlowFormData: ResourceStockFlowForm = {
    id: undefined,
    projectId: undefined,
    resourceId: undefined,
    targetDeptId: undefined,
    unitId: undefined,
    needCheck: undefined,
    handlerName: undefined,
    handleName: undefined,
    taskId: undefined,
    taskType: undefined,
    handleTime: undefined,
    price: undefined,
    amount: undefined,
    stockWay: undefined,
    description: undefined
}

// 入库表单验证规则
const inStockRules = {
    targetDeptId: [{ required: true, message: '所属部门不能为空', trigger: 'change' }],
    handleTime: [{ required: true, message: '入库时间不能为空', trigger: 'change' }],
    needCheck: [{ required: true, message: '是否需要盘点不能为空', trigger: 'change' }],
    price: [{ required: true, message: '最新单价不能为空', trigger: 'blur' }],
    amount: [
        { required: true, message: '入库数量不能为空', trigger: 'blur' },
        {
            validator: (rule: any, value: any, callback: any) => {
                if (!value) {
                    callback()
                    return
                }
                const num = Number(value)
                if (isNaN(num)) {
                    callback(new Error('入库数量必须为数字'))
                } else if (num <= 0) {
                    callback(new Error('入库数量必须大于0'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ],
    stockWay: [{ required: true, message: '入库类型不能为空', trigger: 'change' }]
}

// 出库表单验证规则
const outStockRules = {
    projectId: [{ required: true, message: '项目名称不能为空', trigger: 'change' }],
    targetDeptId: [{ required: true, message: '出库部门不能为空', trigger: 'change' }],
    unitId: [{ required: true, message: '管理单元不能为空', trigger: 'change' }],
    handleTime: [{ required: true, message: '出库时间不能为空', trigger: 'change' }],
    needCheck: [{ required: true, message: '是否需要盘点不能为空', trigger: 'change' }],
    price: [{ required: true, message: '最新单价不能为空', trigger: 'blur' }],
    amount: [
        { required: true, message: '出库数量不能为空', trigger: 'blur' },
        {
            validator: (rule: any, value: any, callback: any) => {
                if (!value) {
                    callback()
                    return
                }
                const num = Number(value)
                if (isNaN(num)) {
                    callback(new Error('出库数量必须为数字'))
                } else if (num <= 0) {
                    callback(new Error('出库数量必须大于0'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ],
    stockWay: [{ required: true, message: '出库类型不能为空', trigger: 'change' }],
    handlerName: [{ required: true, message: '申请人不能为空', trigger: 'blur' }]
}
const selectedResource = ref<ResourceViewVO>() //
const stockFlowData = reactive<PageData<ResourceStockFlowForm, ResourceStockFlowQuery>>({
    form: { ...initStockFlowFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        resourceId: undefined,
        typeId: undefined,

        params: {}
    },
    rules: {
        id: [{ required: true, message: '不能为空', trigger: 'blur' }]
    }
})
const stockDirection = ref<string>('in')
const stockInTypeList = ref<any[]>([
    { value: 'rkdj', label: '入库登记' },
    { value: 'back', label: '归还' }
])
const stockOutTypeList = ref<any[]>([
    { value: 'use', label: '领用' },
    { value: 'borrow', label: '借用' }
])

const { queryParams: queryStockFlowParams, form: stockFlowForm, rules: stockFlowRules } = toRefs(stockFlowData)

// 入库表单数据
const inStockForm = ref<ResourceStockFlowForm>({ ...initStockFlowFormData })
// 出库表单数据
const outStockForm = ref<ResourceStockFlowForm>({ ...initStockFlowFormData })
/** 查询物资信息列表 */
const getList = async () => {
    //debugger;
    loading.value = true
    const res = await listResource(queryParams.value)
    resourceList.value = res.rows
    total.value = res.total
    loading.value = false
}
const getResourceTypreList = async () => {
    loading.value = true
    const res = await listResourceType(resourceTypeQueryParams.value)

    resourceTypeList.value = res.rows
}
const getTypeName = (typeId: string | number) => {
    const type = resourceTypeList.value.find((item) => item.id === typeId)
    return type ? type.name : '未知类型' // 如果未找到匹配项，显示"未知类型"
}

/** 获取物资属性标签，将逗号分隔的属性值转换为对应的标签 */
const getPropertiesLabels = (properties: string) => {
    if (!properties) return '-'

    const propertyValues = properties.split(',').filter((item) => item.trim() !== '')
    const labels = propertyValues.map((value) => {
        const dict = resource_property.value?.find((item) => item.value === value.trim())
        return dict ? dict.label : value.trim()
    })

    return labels.join('，')
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}
const cancelStock = () => {
    resetStockFlow()
    // 这个方法已经被 cancelInStock 和 cancelOutStock 替代
    // stockFlowDialog.visible = false;
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    resourceFormRef.value?.resetFields()
}
const resetStockFlow = () => {
    stockFlowForm.value = { ...initStockFlowFormData }
    resourceStockFlowFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ResourceViewVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    dialog.visible = true
    dialog.title = '添加物资信息'
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ResourceViewVO) => {
    reset()
    const _id = row?.id || ids.value[0]
    const res = await getResourceView(_id)
    const data = res.data

    // 创建一个副本来处理类型转换
    const formData = { ...data } as any

    // 将逗号分隔的字符串转换为数组
    if (data.properties) {
        formData.properties = data.properties.split(',').filter((item) => item.trim() !== '')
    } else {
        formData.properties = []
    }

    Object.assign(form.value, formData)
    dialog.visible = true
    dialog.title = '修改物资信息'
}

/** 提交按钮 */
const submitForm = () => {
    resourceFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true

            // 创建一个副本用于提交，避免修改原始表单数据
            const submitData = { ...form.value }

            // 将properties数组转换为逗号分隔的字符串
            if (Array.isArray(submitData.properties)) {
                submitData.properties = submitData.properties.join(',')
            }

            if (form.value.id) {
                await updateResource(submitData).finally(() => (buttonLoading.value = false))
            } else {
                await addResource(submitData).finally(() => (buttonLoading.value = false))
            }
            proxy?.$modal.msgSuccess('操作成功')
            dialog.visible = false
            await getList()
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: ResourceViewVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除物资信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delResource(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'basic/resource/export',
        {
            ...queryParams.value
        },
        `resource_${new Date().getTime()}.xlsx`
    )
}

const handleChangeResourceType = (typeId: string | number) => {
    const type = resourceTypeList.value.find((item) => item.id === typeId)
    form.value.unit = type.unit
    form.value.nature = type.nature
    // return type ? type.name : "未知类型"; // 如果未找到匹配项，显示"未知类型"
}
const handleChangeProject = async () => {
    // 当项目改变时，重新加载该项目的管理单元
    if (outStockForm.value.projectId) {
        await getProjectManageUnits(outStockForm.value.projectId)
        // 清空已选择的管理单元
        outStockForm.value.unitId = undefined
    } else {
        manageUnitList.value = []
        outStockForm.value.unitId = undefined
    }
}

const handleStock = async (row?: ResourceViewVO, direction?: string) => {
    const res = await getResourceView(row.id)
    selectedResource.value = res.data

    if (direction === 'in') {
        // 完全重置入库表单
        inStockForm.value = { ...initStockFlowFormData }
        inStockForm.value.resourceId = res.data.id
        inStockForm.value.price = res.data.price

        // 清除表单验证状态
        nextTick(() => {
            inStockFormRef.value?.clearValidate()
        })

        inStockDialog.visible = true
    } else if (direction === 'out') {
        // 完全重置出库表单
        outStockForm.value = { ...initStockFlowFormData }
        outStockForm.value.resourceId = res.data.id
        outStockForm.value.price = res.data.price

        // 清空管理单元列表，等待选择项目后重新加载
        manageUnitList.value = []

        // 清除表单验证状态
        nextTick(() => {
            outStockFormRef.value?.clearValidate()
        })

        outStockDialog.visible = true
    }
}

// 取消入库
const cancelInStock = () => {
    inStockForm.value = { ...initStockFlowFormData }
    // 清除表单验证状态
    nextTick(() => {
        inStockFormRef.value?.clearValidate()
    })
    inStockDialog.visible = false
}

// 取消出库
const cancelOutStock = () => {
    outStockForm.value = { ...initStockFlowFormData }
    // 清空管理单元列表
    manageUnitList.value = []
    // 清除表单验证状态
    nextTick(() => {
        outStockFormRef.value?.clearValidate()
    })
    outStockDialog.visible = false
}

// 提交入库表单
const submitInStockForm = () => {
    inStockFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true
            try {
                // 准备提交数据
                const submitData = { ...inStockForm.value }

                // 设置入库方向
                submitData.direction = 'in'

                // 确保 targetDeptId 和 handleTime 字段正确传递
                console.log('入库表单原始数据:', inStockForm.value)
                console.log('targetDeptId:', inStockForm.value.targetDeptId)
                console.log('handleTime:', inStockForm.value.handleTime)
                console.log('needCheck:', inStockForm.value.needCheck)

                // 确保字段值不为空
                if (!submitData.targetDeptId) {
                    proxy?.$modal.msgError('请选择所属部门')
                    return
                }

                if (!submitData.handleTime) {
                    proxy?.$modal.msgError('请选择入库时间')
                    return
                }

                if (!submitData.needCheck) {
                    proxy?.$modal.msgError('请选择是否需要盘点')
                    return
                }

                console.log('提交入库表单数据:', submitData)

                // 调用入库API
                await addResourceStockFlow(submitData)

                proxy?.$modal.msgSuccess('入库成功')
                cancelInStock()
                await getList() // 刷新物资列表
            } catch (error) {
                console.error('入库失败:', error)
                console.error('错误详情:', error)
                proxy?.$modal.msgError('入库失败: ' + ((error as any)?.message || '未知错误'))
            } finally {
                buttonLoading.value = false
            }
        } else {
            console.log('表单验证失败')
        }
    })
}

// 提交出库表单
const submitOutStockForm = () => {
    outStockFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true
            try {
                // 准备提交数据
                const submitData = { ...outStockForm.value }

                // 设置出库方向
                submitData.direction = 'out'

                // 确保 targetDeptId、handleTime 和 needCheck 字段正确传递
                console.log('出库表单原始数据:', outStockForm.value)
                console.log('targetDeptId:', outStockForm.value.targetDeptId)
                console.log('handleTime:', outStockForm.value.handleTime)
                console.log('needCheck:', outStockForm.value.needCheck)

                // 确保字段值不为空
                if (!submitData.targetDeptId) {
                    proxy?.$modal.msgError('请选择出库部门')
                    return
                }

                if (!submitData.projectId) {
                    proxy?.$modal.msgError('请选择项目名称')
                    return
                }

                if (!submitData.unitId) {
                    proxy?.$modal.msgError('请选择管理单元')
                    return
                }

                if (!submitData.handleTime) {
                    proxy?.$modal.msgError('请选择出库时间')
                    return
                }

                if (!submitData.needCheck) {
                    proxy?.$modal.msgError('请选择是否需要盘点')
                    return
                }

                console.log('提交出库表单数据:', submitData)

                // 调用出库API
                await addResourceStockFlow(submitData)

                proxy?.$modal.msgSuccess('出库成功')
                cancelOutStock()
                await getList() // 刷新物资列表
            } catch (error) {
                console.error('出库失败:', error)
                console.error('错误详情:', error)
                proxy?.$modal.msgError('出库失败: ' + ((error as any)?.message || '未知错误'))
            } finally {
                buttonLoading.value = false
            }
        } else {
            console.log('表单验证失败')
        }
    })
}
const projectList = ref<ProjectVO[]>([])
const deptList = ref<DeptVO[]>([])
const deptTreeData = ref<DeptVO[]>([])
const manageUnitList = ref<ManageUnitVO[]>([])

const getAllProjects = async () => {
    try {
        const res = await listProject({
            pageNum: 1,
            pageSize: 1000,
            params: {}
        })

        projectList.value = res.rows
    } catch (error) {
        proxy?.$modal.msgError('获取项目列表失败')
    }
}

// 使用系统提供的handleTree方法处理部门树形结构
const buildDeptTreeOptions = (depts: DeptVO[]) => {
    console.log('原始部门数据:', depts)

    if (!depts || !Array.isArray(depts)) {
        console.warn('部门数据为空或格式不正确')
        return []
    }

    // 使用系统的handleTree方法，参考系统部门管理页面的实现
    const treeData = proxy?.handleTree<DeptVO>(depts, 'deptId')
    console.log('处理后的部门树形数据:', treeData)

    return treeData || []
}

const getAllDepts = async () => {
    try {
        const res = await listDept({
            pageNum: 1,
            pageSize: 1000
        })
        console.log('部门API返回数据:', res)

        // 检查返回数据结构
        if (res.data && Array.isArray(res.data)) {
            deptList.value = res.data
            console.log('部门列表数据:', res.data)
            // 构建树形结构
            deptTreeData.value = buildDeptTreeOptions(res.data)
            console.log('部门树形数据:', deptTreeData.value)
        } else if (res.rows && Array.isArray(res.rows)) {
            // 有些API返回的是rows字段
            deptList.value = res.rows
            console.log('部门列表数据(rows):', res.rows)
            deptTreeData.value = buildDeptTreeOptions(res.rows)
            console.log('部门树形数据:', deptTreeData.value)
        } else {
            console.error('部门API返回数据格式异常:', res)
            proxy?.$modal.msgError('部门数据格式异常')

            // 添加测试数据以便调试，使用与系统部门管理一致的数据结构
            const testDepts: DeptVO[] = [
                {
                    id: 1,
                    deptName: '总公司',
                    parentId: 0,
                    parentName: '',
                    children: [],
                    deptId: 1,
                    deptCategory: 'company',
                    orderNum: 1,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0',
                    menuId: ''
                },
                {
                    id: 2,
                    deptName: '技术部',
                    parentId: 1,
                    parentName: '总公司',
                    children: [],
                    deptId: 2,
                    deptCategory: 'dept',
                    orderNum: 2,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1',
                    menuId: ''
                },
                {
                    id: 3,
                    deptName: '财务部',
                    parentId: 1,
                    parentName: '总公司',
                    children: [],
                    deptId: 3,
                    deptCategory: 'dept',
                    orderNum: 3,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1',
                    menuId: ''
                },
                {
                    id: 4,
                    deptName: '开发组',
                    parentId: 2,
                    parentName: '技术部',
                    children: [],
                    deptId: 4,
                    deptCategory: 'group',
                    orderNum: 4,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1,2',
                    menuId: ''
                },
                {
                    id: 5,
                    deptName: '前端小组',
                    parentId: 4,
                    parentName: '开发组',
                    children: [],
                    deptId: 5,
                    deptCategory: 'team',
                    orderNum: 5,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1,2,4',
                    menuId: ''
                },
                {
                    id: 6,
                    deptName: '后端小组',
                    parentId: 4,
                    parentName: '开发组',
                    children: [],
                    deptId: 6,
                    deptCategory: 'team',
                    orderNum: 6,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1,2,4',
                    menuId: ''
                },
                {
                    id: 7,
                    deptName: '测试组',
                    parentId: 2,
                    parentName: '技术部',
                    children: [],
                    deptId: 7,
                    deptCategory: 'group',
                    orderNum: 7,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1,2',
                    menuId: ''
                }
            ]
            deptList.value = testDepts
            deptTreeData.value = buildDeptTreeOptions(testDepts)
            console.log('使用测试部门数据')
        }
    } catch (error) {
        console.error('获取部门列表失败:', error)
        proxy?.$modal.msgError('获取部门列表失败')
    }
}

// 获取项目管理单元
const getProjectManageUnits = async (projectId: string | number) => {
    try {
        const res = await listProjectManageUnit(projectId.toString())
        if (res && res.data) {
            manageUnitList.value = Array.isArray(res.data) ? res.data : [res.data]
        } else {
            manageUnitList.value = []
        }
    } catch (error) {
        console.error('获取管理单元失败:', error)
        proxy?.$modal.msgError('获取管理单元失败')
        manageUnitList.value = []
    }
}
const resourceOptions = ref<ResourceViewVO[]>([]) // 定义一个数组来存储资源列表
const getAllResouces = async () => {
    try {
        const res = await listResource({
            pageNum: 1,
            pageSize: 1000,
            params: {}
        })

        resourceOptions.value = res.rows
    } catch (error) {
        proxy?.$modal.msgError('获取项目列表失败')
    }
}
const handleSelectResource = (resourceId: string | number) => {
    const selectedResource = resourceOptions.value.find((item) => item.id === resourceId)

    if (selectedResource) {
        stockFlowForm.value.resourceId = selectedResource.id

        //stockFlowForm.value.price = selectedResource.price;
    }
}
const handleStockFlow = () => {
    router.push({ path: '/standard/resouceStockFlow/index' })
}
onMounted(() => {
    getResourceTypreList()
    getAllResouces()
    getAllProjects()
    getAllDepts()
    getList()
})
</script>
