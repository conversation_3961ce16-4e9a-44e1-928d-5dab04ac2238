<template>
    <div class="p-2 h-full flex flex-col">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item label="入库单号" prop="orderSn">
                            <el-input v-model="queryParams.orderSn" placeholder="请输入入库单号" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item label="出库时间" style="width: 308px">
                            <el-date-picker
                                v-model="dateRangeOutDate"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <!-- 操作按钮区域 -->
        <el-card shadow="never" class="mb-4">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['common:resourceOutOrder:add']">新增</el-button>
                    </el-col>
                    <!-- v-hasPermi="['common:resourceOutOrder:edit']" -->
                    <el-col :span="1.5">
                        <el-button
                            type="success"
                            plain
                            icon="Edit"
                            v-hasPermi="['common:resourceOutOrder:edit']"
                            :disabled="single"
                            @click="handleUpdate()"
                            >修改</el-button
                        >
                    </el-col>
                    <!-- v-hasPermi="['common:resourceOutOrder:remove']" -->
                    <el-col :span="1.5">
                        <el-button
                            type="danger"
                            v-hasPermi="['common:resourceOutOrder:remove']"
                            plain
                            icon="Delete"
                            :disabled="multiple"
                            @click="handleDelete()"
                            >删除</el-button
                        >
                    </el-col>
                    <!-- <el-col :span="1.5">
                        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['common:resourceOutOrder:export']"
                            >导出</el-button
                        >
                    </el-col> -->
                    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </template>
        </el-card>

        <!-- 主表区域 (上半部分) -->
        <el-card shadow="never" class="flex-shrink-0" style="height: 45vh; min-height: 400px">
            <!-- <template #header>
                <div class="flex justify-between items-center">
                    <span>出库单列表</span>
                    <el-tag v-if="selectedOutOrder" type="warning"> 已选择: {{ selectedOutOrder.orderSn }} </el-tag>
                </div>
            </template> -->

            <div class="h-full overflow-hidden flex flex-col">
                <el-table
                    v-loading="loading"
                    :data="resourceOutOrderList"
                    @selection-change="handleSelectionChange"
                    @row-click="handleRowClick"
                    :row-class-name="getRowClassName"
                    highlight-current-row
                    height="100%"
                    class="flex-1"
                >
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column label="出库单号" align="center" prop="orderSn" />
                    <el-table-column label="出库类型" align="center" prop="stockOutType">
                        <template #default="scope">
                            <dict-tag :options="resource_outstock_way" :value="scope.row.stockOutType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="出库金额" align="center" prop="amount" />
                    <el-table-column label="备注" align="center" prop="remark" />
                    <el-table-column label="状态" align="center" prop="status">
                        <template #default="scope">
                            <dict-tag :options="resource_out_order_status" :value="scope.row.status" />
                        </template>
                    </el-table-column>
                    <el-table-column label="出库时间" align="center" prop="outDate" width="180">
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.outDate, '{y}-{m}-{d}') }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                            <!-- <el-button v-if="scope.row.taskId" link type="primary" @click="handleViewTask(scope.row.id)">查看关联作业</el-button> -->

                            <!-- v-hasPermi="['common:resourceOutOrder:confirm']" -->
                            <el-button
                                v-hasPermi="['common:resourceOutOrder:confirm']"
                                :disabled="scope.row.status != 'toconfirm'"
                                link
                                type="primary"
                                @click="handleConfirmOutbound(scope.row)"
                                >确认出库</el-button
                            >
                            <!-- v-hasPermi="['common:resourceOutOrder:edit']" -->
                            <el-button
                                v-hasPermi="['common:resourceOutOrder:edit']"
                                link
                                type="primary"
                                :disabled="scope.row.status == 'confirmed'"
                                @click="handleUpdate(scope.row)"
                                >修改</el-button
                            >
                            <!-- v-hasPermi="['common:resourceOutOrder:remove']" -->
                            <el-button
                                v-hasPermi="['common:resourceOutOrder:remove']"
                                link
                                :disabled="scope.row.status == 'confirmed'"
                                type="primary"
                                @click="handleDelete(scope.row)"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 主表分页 -->
                <div class="mt-4">
                    <pagination
                        v-show="total > 0"
                        :total="total"
                        v-model:page="queryParams.pageNum"
                        v-model:limit="queryParams.pageSize"
                        @pagination="getList"
                    />
                </div>
            </div>
        </el-card>

        <!-- 明细表区域 (下半部分) -->
        <el-card shadow="never" class="flex-1 mt-4 overflow-hidden">
            <template #header>
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <span>出库明细</span>
                        <!-- <el-tag v-if="selectedOutOrder" :type="getStatusTagType(selectedOutOrder.status)">
                            {{ getStatusText(selectedOutOrder.status) }}
                        </el-tag> -->
                    </div>
                    <!-- <div v-if="selectedOutOrder" class="text-sm text-gray-500">
                        出库单号: {{ selectedOutOrder.orderSn }} | 出库时间: {{ parseTime(selectedOutOrder.outDate, '{y}-{m}-{d}') }}
                    </div> -->
                </div>
            </template>

            <div v-if="selectedOutOrder" class="h-full overflow-hidden flex flex-col">
                <el-table v-loading="detailItemLoading" :data="detailItemList" border height="100%" class="flex-1">
                    <el-table-column type="index" label="序号" width="60" />
                    <el-table-column label="物资信息" prop="typeName" min-width="200">
                        <!-- <template #default="scope">
                            <div>
                                <div class="font-medium">{{ scope.row.resourceName }}</div>
                                <div class="text-sm text-gray-500">ID: {{ scope.row.resourceId }}</div>
                            </div>
                        </template> -->
                    </el-table-column>
                    <el-table-column label="物资型号" prop="specification" width="150" />
                    <el-table-column label="物资单位" prop="unit" width="100">
                        <template #default="scope">
                            <dict-tag :options="tnl_resource_unit" :value="scope.row.unit" />
                        </template>
                    </el-table-column>
                    <el-table-column label="物资性质" prop="nature" width="120">
                        <template #default="scope">
                            <dict-tag :options="tnl_resource_nature" :value="scope.row.nature" />
                        </template>
                    </el-table-column>
                    <el-table-column label="出库数量" prop="quantity" width="120" align="right">
                        <template #default="scope">
                            <el-tag type="warning">{{ scope.row.quantity }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="单价" prop="price" width="120" align="right">
                        <template #default="scope"> ¥{{ scope.row.price?.toFixed(2) || '0.00' }} </template>
                    </el-table-column>
                    <el-table-column label="金额" prop="amount" width="120" align="right">
                        <template #default="scope">
                            <span class="font-bold text-orange-500"> ¥{{ scope.row.amount?.toFixed(2) || '0.00' }} </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" prop="remark" min-width="150" show-overflow-tooltip />
                </el-table>

                <!-- 明细分页和统计 -->
                <div class="mt-4 space-y-4">
                    <!-- 统计信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-orange-600">{{ getTotalQuantity() }}</div>
                                    <div class="text-sm text-gray-500">总数量</div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-green-600">{{ detailItemList.length }}</div>
                                    <div class="text-sm text-gray-500">明细条数</div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-red-600">¥{{ getTotalAmount() }}</div>
                                    <div class="text-sm text-gray-500">总金额</div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 明细分页 -->
                    <pagination
                        v-show="detailItemTotal > 0"
                        :total="detailItemTotal"
                        v-model:page="detailItemQueryParams.pageNum"
                        v-model:limit="detailItemQueryParams.pageSize"
                        @pagination="getDetailItemList"
                    />
                </div>
            </div>
        </el-card>
        <!-- 添加或修改出库单对话框 -->
        <el-dialog
            :title="isEditMode ? '修改出库单' : '添加出库单'"
            v-model="dialog.visible"
            width="90%"
            append-to-body
            :close-on-click-modal="false"
            destroy-on-close
        >
            <el-form ref="resourceOutOrderFormRef" :model="form" :rules="rules" label-width="120px">
                <!-- 基本信息区域 -->
                <el-card shadow="never" class="mb-4">
                    <template #header>
                        <span>基本信息</span>
                    </template>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="出库单号" prop="orderSn">
                                <el-input v-model="form.orderSn" placeholder="系统自动生成" readonly style="width: 100%" :disabled="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="出库类型" prop="stockOutType">
                                <el-select v-model="form.stockOutType" placeholder="请选择出库类型" style="width: 100%">
                                    <el-option v-for="dict in resource_outstock_way" :key="dict.value" :label="dict.label" :value="dict.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="出库时间" prop="outDate">
                                <el-date-picker
                                    v-model="form.outDate"
                                    type="date"
                                    placeholder="选择出库时间"
                                    style="width: 100%"
                                    value-format="YYYY-MM-DD"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="备注" prop="remark">
                                <el-input
                                    v-model="form.remark"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入备注信息"
                                    maxlength="500"
                                    show-word-limit
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>
                <el-card shadow="never" class="mb-4">
                    <template #header>
                        <div style="display: flex; justify-content: space-between; align-items: center">
                            <span>出库明细</span>
                            <div>
                                <el-button type="primary" size="small" @click="handleAddItem">
                                    <el-icon><Plus /></el-icon>
                                    添加明细
                                </el-button>
                                <el-button type="success" size="small" @click="showOutImportDialog">
                                    <el-icon><Upload /></el-icon>
                                    导入明细
                                </el-button>
                                <!-- <el-button type="info" size="small" @click="downloadOutTemplate">
                                    <el-icon><Download /></el-icon>
                                    下载模板
                                </el-button> -->
                                <el-button type="warning" size="small" @click="clearAllOutItems" v-if="outOrderItems.length > 0" style="color: white">
                                    <el-icon><Delete /></el-icon>
                                    清空明细
                                </el-button>
                                <!-- <el-button type="danger" icon="Delete" @click="handleBatchDeleteItems"> 批量删除 </el-button> -->
                            </div>
                        </div>
                    </template>
                    <!-- 出库明细编辑区域 -->

                    <!-- 明细表格 -->
                    <el-table
                        :data="outOrderItems"
                        border
                        height="300px"
                        @selection-change="handleItemSelectionChange"
                        :key="outTableRefreshKey"
                        row-key="getOutRowKey"
                    >
                        <!-- <el-table-column type="selection" width="55" align="center" /> -->

                        <!-- 🔥 增强的序号列，显示导入标识 -->
                        <el-table-column label="序号" width="80" align="center">
                            <template #default="{ row, $index }">
                                <div>
                                    <div style="font-weight: bold">{{ $index + 1 }}</div>
                                    <div v-if="(row as any).tempId" style="font-size: 10px; color: #999">
                                        {{ (row as any).tempId ? '导入' : '手动' }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>

                        <el-table-column label="物资信息" min-width="250">
                            <template #default="scope">
                                <el-select
                                    :ref="(el) => setTableCellRef(el, scope.$index, 0)"
                                    v-model="scope.row.resourceId"
                                    placeholder="请输入物资名称搜索"
                                    filterable
                                    remote
                                    reserve-keyword
                                    :remote-method="(query) => searchResources(query, scope.$index)"
                                    :loading="resourceSearchLoading"
                                    @change="(value) => handleResourceChange(value, scope.$index)"
                                    @keydown.enter="handleCellKeyDown($event, scope.$index, 0)"
                                    style="width: 100%"
                                >
                                    <!-- 🔥 当前选中的物资信息（只显示typeName） -->
                                    <el-option
                                        v-if="(scope.row.typeName || scope.row.resourceName) && scope.row.resourceId"
                                        :key="scope.row.resourceId"
                                        :label="scope.row.typeName || scope.row.resourceName"
                                        :value="scope.row.resourceId"
                                    >
                                        <div>
                                            <div class="font-medium">{{ scope.row.typeName || scope.row.resourceName }}</div>
                                            <div class="text-sm text-gray-500">
                                                规格: {{ scope.row.specification }} | 库存: {{ scope.row.balanceAmount }} {{ scope.row.unit }}
                                                <span v-if="scope.row.price > 0"> | 单价: ¥{{ scope.row.price }}</span>
                                            </div>
                                        </div>
                                    </el-option>
                                    <!-- 搜索结果选项（下拉时显示typeName(specification)） -->
                                    <el-option v-for="resource in resourceOptions" :key="resource.id" :label="resource.typeName" :value="resource.id">
                                        <div>
                                            <div class="font-medium">{{ resource.typeName }}({{ resource.specification }})</div>
                                            <div class="text-sm text-gray-500">
                                                规格: {{ resource.specification }} | 库存: {{ resource.balanceAmount }} {{ resource.unit }}
                                            </div>
                                        </div>
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>

                        <el-table-column label="规格型号" width="150">
                            <template #default="scope">
                                <el-input v-model="scope.row.specification" placeholder="自动填充" readonly style="width: 100%" />
                            </template>
                        </el-table-column>
                        <el-table-column label="单位" width="120">
                            <template #default="scope">
                                <el-input
                                    :value="getDictLabel(tnl_resource_unit, scope.row.unit)"
                                    placeholder="自动填充"
                                    readonly
                                    style="width: 100%"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column label="物资性质" width="120">
                            <template #default="scope">
                                <el-input
                                    :value="getDictLabel(tnl_resource_nature, scope.row.nature)"
                                    placeholder="自动填充"
                                    readonly
                                    style="width: 100%"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column label="库存数量" prop="balanceAmount" width="100" align="right">
                            <template #default="scope">
                                <el-tag type="info">{{ scope.row.balanceAmount || 0 }}</el-tag>
                            </template>
                        </el-table-column>

                        <el-table-column label="出库数量" width="120">
                            <template #default="scope">
                                <el-input
                                    :ref="(el) => setTableCellRef(el, scope.$index, 1)"
                                    v-model="scope.row.quantity"
                                    placeholder="请输入数量"
                                    @input="handleQuantityInput(scope.row, scope.$index)"
                                    @keydown.enter="handleCellKeyDown($event, scope.$index, 1)"
                                    style="width: 100%"
                                />
                            </template>
                        </el-table-column>

                        <el-table-column label="单价" width="120">
                            <template #default="scope">
                                <el-input
                                    :ref="(el) => setTableCellRef(el, scope.$index, 2)"
                                    v-model="scope.row.price"
                                    placeholder="请输入单价"
                                    @input="handlePriceInput(scope.row, scope.$index)"
                                    @keydown.enter="handleCellKeyDown($event, scope.$index, 2)"
                                    style="width: 100%"
                                />
                            </template>
                        </el-table-column>

                        <el-table-column label="金额" prop="amount" width="100" align="right">
                            <template #default="scope">
                                <span class="font-bold text-orange-500"> ¥{{ (scope.row.quantity * scope.row.price || 0).toFixed(2) }} </span>
                            </template>
                        </el-table-column>

                        <el-table-column label="备注" min-width="150">
                            <template #default="scope">
                                <el-input
                                    :ref="(el) => setTableCellRef(el, scope.$index, 3)"
                                    v-model="scope.row.remark"
                                    placeholder="请输入备注"
                                    @keydown.enter="handleCellKeyDown($event, scope.$index, 3)"
                                />
                            </template>
                        </el-table-column>

                        <el-table-column label="操作" width="80" align="center">
                            <template #default="scope">
                                <el-button link type="danger" icon="Delete" @click="handleDeleteItem(scope.$index)" />
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 统计信息 -->
                    <div class="mt-4 p-4 bg-gray-50 rounded">
                        <el-row :gutter="20">
                            <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-orange-600">{{ getFormTotalQuantity() }}</div>
                                    <div class="text-sm text-gray-500">总出库数量</div>
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-blue-600">{{ outOrderItems.length }}</div>
                                    <div class="text-sm text-gray-500">明细条数</div>
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-green-600">¥{{ getFormTotalAmount() }}</div>
                                    <div class="text-sm text-gray-500">总金额</div>
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-purple-600">¥{{ getFormAvgPrice() }}</div>
                                    <div class="text-sm text-gray-500">平均单价</div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-card>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">
                        {{ isEditMode ? '更 新' : '确 定' }}
                    </el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 🔥 出库明细批量导入对话框 -->
        <el-dialog v-model="outImportDialog.visible" title="批量导入出库明细" width="900px" :close-on-click-modal="false" append-to-body>
            <!-- 文件上传区域 -->
            <el-card shadow="never" class="mb-4">
                <template #header>
                    <span>选择Excel文件</span>
                </template>

                <el-upload
                    ref="outUploadRef"
                    :limit="1"
                    accept=".xlsx,.xls"
                    :auto-upload="false"
                    :on-change="handleOutFileChange"
                    :on-remove="handleOutFileRemove"
                    drag
                    style="width: 100%"
                >
                    <el-icon class="el-icon--upload" style="font-size: 67px; color: #c0c4cc">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
                    <template #tip>
                        <div class="el-upload__tip" style="color: #999; font-size: 12px">
                            只能上传xlsx/xls文件，且不超过10MB
                            <el-link type="primary" @click="downloadOutTemplate" style="margin-left: 10px"> 下载导入模板 </el-link>
                        </div>
                    </template>
                </el-upload>
            </el-card>

            <!-- 解析结果预览 -->
            <el-card v-if="outImportPreviewData.length > 0" shadow="never">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center">
                        <span>数据预览 (共 {{ outImportPreviewData.length }} 条)</span>
                        <div>
                            <el-tag v-if="outValidImportCount > 0" type="success"> {{ outValidImportCount }} 条有效 </el-tag>
                            <el-tag v-if="outErrorImportCount > 0" type="danger" style="margin-left: 8px"> {{ outErrorImportCount }} 条错误 </el-tag>
                        </div>
                    </div>
                </template>

                <!-- 简化的预览表格 -->
                <el-table
                    :data="outImportPreviewData"
                    border
                    style="width: 100%"
                    max-height="400"
                    size="small"
                    :row-class-name="getOutImportRowClassName"
                >
                    <el-table-column prop="rowNumber" label="行号" width="60" align="center" />

                    <!-- 物资类别列 -->
                    <el-table-column label="物资类别" width="120">
                        <template #default="{ row }">
                            <div :class="getOutFieldClassName(row, 'typeName')">
                                {{ row.typeName }}
                                <el-icon v-if="row.errors.typeName" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="物资名称" prop="typeName" min-width="120">
                        <template #default="{ row }">
                            <div :class="{ 'error-field': row.errors.typeName }">
                                {{ row.typeName }}
                                <el-icon v-if="row.errors.typeName" color="#f56c6c"><Warning /></el-icon>
                            </div>
                        </template>
                    </el-table-column>
                    <!-- 规格型号列 -->
                    <el-table-column label="规格型号" width="150">
                        <template #default="{ row }">
                            <div :class="getOutFieldClassName(row, 'specification')">
                                {{ row.specification }}
                                <el-icon v-if="row.errors.specification" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 物资单位列 -->
                    <el-table-column label="物资单位" width="100">
                        <template #default="{ row }">
                            <div :class="getOutFieldClassName(row, 'unit')">
                                {{ row.unitLabel || getOutUnitLabelByCode(row.unit) }}
                                <el-icon v-if="row.errors.unit" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 数量列 -->
                    <el-table-column prop="quantity" label="数量" width="80" align="right">
                        <template #default="{ row }">
                            <div :class="getOutFieldClassName(row, 'quantity')">
                                {{ row.quantity }}
                                <el-icon v-if="row.errors.quantity" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 验证状态列 -->
                    <el-table-column label="验证状态" width="100" align="center">
                        <template #default="{ row }">
                            <div>
                                <el-tag v-if="row.isValid" type="success" size="small">通过</el-tag>
                                <el-tag v-else type="danger" size="small">失败</el-tag>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 🔥 行级错误提示区域 -->
                <div v-if="outImportPreviewData.some((row) => !row.isValid)" style="margin-top: 16px">
                    <el-card shadow="never" style="background-color: #fef0f0; border: 1px solid #fbc4c4">
                        <template #header>
                            <div style="color: #f56c6c; font-weight: bold">
                                <el-icon><Warning /></el-icon>
                                数据验证错误详情
                            </div>
                        </template>

                        <div class="error-details">
                            <div
                                v-for="row in outImportPreviewData.filter((r) => !r.isValid)"
                                :key="row.rowNumber"
                                class="error-row-detail"
                                style="margin-bottom: 12px; padding: 8px; background-color: #fff; border-radius: 4px"
                            >
                                <div style="font-weight: bold; color: #f56c6c; margin-bottom: 4px">第 {{ row.rowNumber }} 行错误：</div>

                                <!-- 基础字段错误 -->
                                <div v-if="Object.keys(row.errors).some((key) => key !== 'resourceMatch')" style="margin-bottom: 4px">
                                    <span style="color: #909399">基础字段：</span>
                                    <span v-for="(error, field) in row.errors" :key="field" style="color: #f56c6c">
                                        <span v-if="field !== 'resourceMatch'">{{ error }}；</span>
                                    </span>
                                </div>

                                <!-- 物资匹配错误 -->
                                <div v-if="row.errors.resourceMatch" style="margin-bottom: 4px">
                                    <span style="color: #909399">物资匹配：</span>
                                    <span style="color: #f56c6c">{{ row.errors.resourceMatch }}</span>
                                </div>

                                <!-- 🔥 智能建议信息 -->
                                <div v-if="row.suggestions" style="font-size: 12px; color: #606266; margin-top: 4px">
                                    <div v-if="row.suggestions.typeNames?.length > 0" style="margin-bottom: 2px">
                                        <span style="color: #909399">建议的物资类别：</span>
                                        <span style="color: #409eff">{{ row.suggestions.typeNames.join('、') }}</span>
                                    </div>
                                    <div v-if="row.suggestions.units?.length > 0" style="margin-bottom: 2px">
                                        <span style="color: #909399">建议的计量单位：</span>
                                        <span style="color: #409eff">{{ row.suggestions.units.join('、') }}</span>
                                    </div>
                                    <div v-if="row.suggestions.specifications?.length > 0" style="margin-bottom: 2px">
                                        <span style="color: #909399">建议的规格型号：</span>
                                        <span style="color: #409eff">{{ row.suggestions.specifications.join('、') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </div>
            </el-card>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelOutImport">取消</el-button>
                    <el-button
                        type="primary"
                        @click="parseOutExcelData"
                        :loading="outParsing"
                        v-if="outSelectedFile && outImportPreviewData.length === 0"
                    >
                        解析数据
                    </el-button>
                    <el-button
                        type="success"
                        @click="confirmOutImport"
                        :disabled="outErrorImportCount > 0 || outImportPreviewData.length === 0"
                        :loading="outImporting"
                    >
                        确认导入 ({{ outValidImportCount }} 条)
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ResourceOutOrder" lang="ts">
import { reactive, ref, toRefs, nextTick } from 'vue'
import {
    listResourceOutOrder,
    getResourceOutOrder,
    delResourceOutOrder,
    addResourceOutOrder,
    updateResourceOutOrder,
    confirmResourceOutOrder
} from '@/api/common/resourceOutOrder'
import { ResourceOutOrderVO, ResourceOutOrderQuery, ResourceOutOrderForm } from '@/api/common/resourceOutOrder/types'
import { getResourceOutOrderItemView } from '@/api/common/resourceOutOrderItem'
import { ResourceOutOrderItemViewVO } from '@/api/common/resourceOutOrderItem/types'
import { parseTime } from '@/utils/ruoyi'
import { listResource, getResourceView } from '@/api/common/resource'
import { ResourceViewVO, ResourceViewQuery } from '@/api/common/resource/types'
import * as XLSX from 'xlsx'
import dayjs from 'dayjs'
import { useDict } from '@/utils/dict'
import { Plus, Upload, Download, Delete, Warning, UploadFilled, Document } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance

// 🔥 出库导入行数据类型
interface OutImportRow {
    rowNumber: number
    typeName: string // 物资名称
    specification: string // 规格型号
    unit: string // 计量单位（英文代码）
    unitLabel: string // 计量单位（中文显示）
    quantity: number // 出库数量
    errors: {
        typeName?: string
        specification?: string
        unit?: string
        quantity?: string
        resourceMatch?: string
    }
    isValid: boolean
    resourceId: string | null
    resourceInfo: any
    suggestions: {
        typeNames?: string[]
        units?: string[]
        specifications?: string[]
    }
    validationDetails: {
        basicFields: boolean
        resourceMatch: boolean
    }
}

const resourceOutOrderList = ref<ResourceOutOrderVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRangeOutDate = ref<[DateModelType, DateModelType]>(['', ''])

// 🔥 出库导入相关响应式数据
const outImportDialog = reactive({
    visible: false
})

const outSelectedFile = ref<any>(null)
const outImportPreviewData = ref<OutImportRow[]>([])
const outValidImportCount = ref(0)
const outErrorImportCount = ref(0)
const outParsing = ref(false)
const outImporting = ref(false)
const outUploadRef = ref()

// 🔥 表格刷新相关
const outTableRefreshKey = ref(0)

// 生成出库明细行的唯一key
const getOutRowKey = (row: any) => {
    // 优先使用临时ID，其次使用resourceId，最后使用索引
    return row.tempId || row.resourceId || `out_row_${Date.now()}_${Math.random()}`
}
// 主从表相关数据
const selectedOutOrderId = ref<string | number | null>(null)
const selectedOutOrder = ref<ResourceOutOrderVO | null>(null)

// 明细查看表相关数据
const detailItemList = ref<ResourceOutOrderItemViewVO[]>([])
const detailItemLoading = ref(false)
const detailItemTotal = ref(0)
const detailItemQueryParams = ref({
    pageNum: 1,
    pageSize: 10
})

// 编辑状态管理
const isEditMode = ref(false) // 是否为编辑模式
const editingOrderId = ref<string>('') // 正在编辑的出库单ID

// 表单明细编辑相关数据
const outOrderItems = ref<any[]>([])
const selectedItems = ref<any[]>([])

// 物资搜索相关
const resourceOptions = ref<any[]>([])
const resourceSearchLoading = ref(false)

// 表格单元格引用，用于键盘导航
const tableCellRefs = ref<any[][]>([])

// 设置表格单元格引用
const setTableCellRef = (el: any, rowIndex: number, colIndex: number) => {
    if (!tableCellRefs.value[rowIndex]) {
        tableCellRefs.value[rowIndex] = []
    }
    tableCellRefs.value[rowIndex][colIndex] = el
}

const queryFormRef = ref<ElFormInstance>()
const resourceOutOrderFormRef = ref<ElFormInstance>()

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

const initFormData: ResourceOutOrderForm = {
    id: undefined,
    resourceId: undefined,
    orderSn: undefined,
    stockOutType: undefined,
    amount: undefined,
    remark: undefined,
    status: undefined,
    files: undefined,
    taskId: undefined,
    outDate: undefined
}
const data = reactive<PageData<ResourceOutOrderForm, ResourceOutOrderQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        resourceId: undefined,
        orderSn: undefined,
        stockOutType: undefined,
        status: undefined,
        params: {}
    },
    rules: {
        stockOutType: [{ required: true, message: '请选择出库类型', trigger: 'change' }],
        outDate: [{ required: true, message: '请选择出库时间', trigger: 'change' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

// 字典数据
const { tnl_resource_unit, tnl_resource_nature, resource_outstock_way, resource_out_order_status } = toRefs<any>(
    proxy?.useDict('tnl_resource_unit', 'tnl_resource_nature', 'resource_outstock_way', 'resource_out_order_status')
)

/** 查询出库单列表 */
const getList = async () => {
    loading.value = true
    proxy?.addDateRange(queryParams.value, dateRangeOutDate.value, 'OutDate')
    const res = await listResourceOutOrder(queryParams.value)
    resourceOutOrderList.value = res.rows
    total.value = res.total
    loading.value = false
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    outOrderItems.value = []
    selectedItems.value = []
    resourceOptions.value = []
    tableCellRefs.value = []
    // 重置编辑状态
    isEditMode.value = false
    editingOrderId.value = ''
    resourceOutOrderFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    dateRangeOutDate.value = ['', '']
    queryFormRef.value?.resetFields()
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ResourceOutOrderVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 生成出库单号 */
const generateOrderSn = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hour = String(now.getHours()).padStart(2, '0')
    const minute = String(now.getMinutes()).padStart(2, '0')
    const second = String(now.getSeconds()).padStart(2, '0')
    const millisecond = String(now.getMilliseconds()).padStart(3, '0')

    return `${year}${month}${day}-${hour}${minute}${second}-${millisecond}`
}

/** 获取编辑模式的出库明细 */
const getOutOrderItemsForEdit = async (orderId: string | number) => {
    try {
        // 使用现有的getResourceOutOrderItemView API
        const response = await getResourceOutOrderItemView(orderId)

        // 处理返回的明细数据
        const itemsData = Array.isArray(response.data) ? response.data : [response.data]

        // 清空现有明细
        outOrderItems.value = []

        // 转换明细数据为表单编辑格式
        for (const item of itemsData) {
            outOrderItems.value.push({
                resourceId: item.resourceId,
                resourceName: item.typeName, // 使用typeName
                specification: item.specification,
                unit: item.unit,
                nature: item.nature,
                balanceAmount: item.balanceAmount,
                quantity: item.quantity,
                price: item.price,
                amount: item.amount,
                remark: item.remark
            })
        }

        // 如果明细少于5行，补充空行到5行
        while (outOrderItems.value.length < 5) {
            outOrderItems.value.push({
                resourceId: '',
                resourceName: '',
                specification: '',
                unit: '',
                nature: '',
                balanceAmount: 0,
                quantity: 1,
                price: 0,
                amount: 0,
                remark: ''
            })
        }

        // 重新计算总计
        calculateTotalAmount()
    } catch (error) {
        console.error('获取出库明细失败:', error)
        proxy?.$modal.msgError('获取出库明细失败')
        // 如果获取失败，至少提供5个空行
        outOrderItems.value = []
        for (let i = 0; i < 5; i++) {
            outOrderItems.value.push({
                resourceId: '',
                resourceName: '',
                specification: '',
                unit: '',
                nature: '',
                balanceAmount: 0,
                quantity: 1,
                price: 0,
                amount: 0,
                remark: ''
            })
        }
    }
}
const handleViewTask = (id: string) => {
    proxy.$router.push('/subProject/circle/plan/baseInfo?id=' + id + '&taskType=sealing&from=task')
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    isEditMode.value = false
    editingOrderId.value = ''

    form.value.orderSn = generateOrderSn()
    form.value.outDate = parseTime(new Date(), '{y}-{m}-{d}') // 默认今天
    outOrderItems.value = []
    selectedItems.value = []

    // 默认生成五行出库明细
    for (let i = 0; i < 5; i++) {
        outOrderItems.value.push({
            resourceId: '',
            resourceName: '',
            specification: '',
            unit: '',
            nature: '',
            balanceAmount: 0,
            quantity: 1,
            price: 0,
            amount: 0,
            remark: ''
        })
    }

    dialog.visible = true
    dialog.title = '添加出库单'
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ResourceOutOrderVO) => {
    loading.value = true
    try {
        reset()
        isEditMode.value = true
        const _id = row?.id || ids.value[0]
        editingOrderId.value = _id.toString()

        // 获取出库单基本信息
        const res = await getResourceOutOrder(_id)
        Object.assign(form.value, res.data)

        // 获取出库明细列表
        await getOutOrderItemsForEdit(_id)

        dialog.visible = true
        dialog.title = '修改出库单'
    } catch (error) {
        console.error('获取出库单信息失败:', error)
        proxy?.$modal.msgError('获取出库单信息失败')
    } finally {
        loading.value = false
    }
}

/** 提交按钮 */
const submitForm = () => {
    // 验证基本信息
    resourceOutOrderFormRef.value?.validate(async (valid: boolean) => {
        if (!valid) return

        // 过滤掉未填写物资名称的行
        const validItems = outOrderItems.value.filter((item) => item.resourceId && item.resourceId !== '')

        // 验证明细数据
        if (validItems.length === 0) {
            proxy?.$modal.msgError('请至少添加一条出库明细')
            return
        }

        // 验证明细完整性
        for (let i = 0; i < validItems.length; i++) {
            const item = validItems[i]
            const originalIndex = outOrderItems.value.findIndex((originalItem) => originalItem === item)

            if (!item.quantity || item.quantity <= 0) {
                proxy?.$modal.msgError(`第${originalIndex + 1}行出库数量必须大于0`)
                return
            }
            if (item.quantity > item.balanceAmount) {
                proxy?.$modal.msgError(`第${originalIndex + 1}行出库数量不能超过库存数量`)
                return
            }
            if (item.price === undefined || item.price < 0) {
                proxy?.$modal.msgError(`第${originalIndex + 1}行请输入正确的单价`)
                return
            }
        }

        buttonLoading.value = true
        try {
            // 准备提交数据
            const submitData = {
                ...form.value,
                resourceOutOrderItems: validItems
            }

            if (isEditMode.value) {
                // 更新模式
                await updateResourceOutOrder(submitData)
                proxy?.$modal.msgSuccess('修改成功')
            } else {
                // 新增模式
                submitData.status = 'toconfirm'
                await addResourceOutOrder(submitData)
                proxy?.$modal.msgSuccess('新增成功')
            }
            dialog.visible = false
            await getList()
        } catch (error) {
            console.error('提交失败:', error)
            proxy?.$modal.msgError('操作失败')
        } finally {
            buttonLoading.value = false
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: ResourceOutOrderVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除出库单编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delResourceOutOrder(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 确认出库按钮操作 */
const handleConfirmOutbound = async (row: ResourceOutOrderVO) => {
    await proxy?.$modal.confirm(`是否确认出库单号为"${row.orderSn}"的出库操作？确认后将更新物资库存。`)
    loading.value = true
    try {
        await confirmResourceOutOrder(row.id!)
        proxy?.$modal.msgSuccess('确认出库成功')
        await getList()
    } catch (error) {
        console.error('确认出库失败:', error)
        proxy?.$modal.msgError('确认出库失败')
    } finally {
        loading.value = false
    }
}

/** 主表行点击事件 */
const handleRowClick = (row: ResourceOutOrderVO) => {
    selectedOutOrderId.value = row.id
    selectedOutOrder.value = row
    detailItemQueryParams.value.pageNum = 1 // 重置到第一页
    getDetailItemList()
}

/** 获取出库明细列表 */
const getDetailItemList = async () => {
    if (!selectedOutOrderId.value) return

    detailItemLoading.value = true
    try {
        const response = await getResourceOutOrderItemView(selectedOutOrderId.value)

        // 处理API返回数据
        if (response.data) {
            detailItemList.value = Array.isArray(response.data) ? response.data : [response.data]
            detailItemTotal.value = detailItemList.value.length
        } else {
            detailItemList.value = []
            detailItemTotal.value = 0
        }
    } catch (error) {
        console.error('获取出库明细失败:', error)
        proxy?.$modal.msgError('获取出库明细失败')
        detailItemList.value = []
        detailItemTotal.value = 0
    } finally {
        detailItemLoading.value = false
    }
}

/** 获取行样式类名 */
const getRowClassName = ({ row }: { row: ResourceOutOrderVO }) => {
    return selectedOutOrderId.value === row.id ? 'selected-row' : ''
}

/** 统计方法 */
const getTotalQuantity = () => {
    return detailItemList.value.reduce((sum, item) => sum + (item.quantity || 0), 0)
}

const getTotalAmount = () => {
    const total = detailItemList.value.reduce((sum, item) => sum + (item.amount || 0), 0)
    return total.toFixed(2)
}

/** 状态相关方法 */
const getStatusTagType = (status: string) => {
    const statusMap = {
        'waiting': 'warning',
        'confirmed': 'success',
        'cancelled': 'danger'
    }
    return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
    const statusMap = {
        'waiting': '待出库',
        'confirmed': '已确认',
        'cancelled': '已取消'
    }
    return statusMap[status] || status
}

/** 表单明细管理方法 */
/** 添加明细行 */
const handleAddItem = () => {
    outOrderItems.value.push({
        resourceId: '',
        resourceName: '',
        specification: '',
        unit: '',
        balanceAmount: 0,
        quantity: 1,
        price: 0,
        amount: 0,
        remark: ''
    })
}

/** 删除明细行 */
const handleDeleteItem = (index: number) => {
    outOrderItems.value.splice(index, 1)
    calculateTotalAmount()
}

/** 批量删除明细 */
const handleBatchDeleteItems = () => {
    const selectedIds = selectedItems.value.map((item: any) => item.id)
    outOrderItems.value = outOrderItems.value.filter((item: any) => !selectedIds.includes(item.id))
    selectedItems.value = []
    calculateTotalAmount()
}

/** 明细选择变化 */
const handleItemSelectionChange = (selection: any[]) => {
    selectedItems.value = selection
}

/** 处理单元格键盘事件 */
const handleCellKeyDown = (event: KeyboardEvent, rowIndex: number, colIndex: number) => {
    event.preventDefault()

    // 计算下一个单元格位置
    let nextRowIndex = rowIndex
    let nextColIndex = colIndex + 1

    // 如果是最后一列，跳到下一行第一列
    if (nextColIndex > 3) {
        // 0:物资信息, 1:数量, 2:单价, 3:备注
        nextColIndex = 0
        nextRowIndex = rowIndex + 1

        // 如果是最后一行最后一列，自动添加新行
        if (nextRowIndex >= outOrderItems.value.length) {
            handleAddItem()
        }
    }

    // 聚焦到下一个单元格
    nextTick(() => {
        const nextCell = tableCellRefs.value[nextRowIndex]?.[nextColIndex]
        if (nextCell) {
            // 根据组件类型进行聚焦
            if (nextCell.focus) {
                nextCell.focus()
            } else if (nextCell.$el && nextCell.$el.querySelector) {
                const input = nextCell.$el.querySelector('input')
                if (input) {
                    input.focus()
                }
            }
        }
    })
}

/** 搜索物资 */
const searchResources = async (query: string, rowIndex?: number) => {
    if (!query || query.length < 2) {
        resourceOptions.value = []
        return
    }

    resourceSearchLoading.value = true
    try {
        // 使用物资搜索API
        const queryParams: ResourceViewQuery = {
            typeName: query,
            pageNum: 1,
            pageSize: 20
        }

        const response = await listResource(queryParams)

        // 过滤掉已经添加的物资(避免重复)
        const existingResourceIds = outOrderItems.value.map((item: any) => item.resourceId)
        resourceOptions.value = (response.rows || []).filter((resource: ResourceViewVO) => !existingResourceIds.includes(resource.id))
    } catch (error) {
        console.error('搜索物资失败:', error)
        resourceOptions.value = []
    } finally {
        resourceSearchLoading.value = false
    }
}

/** 物资选择变化 */
const handleResourceChange = async (resourceId: string | number, rowIndex: number) => {
    if (!resourceId) return

    try {
        // 获取物资详细信息
        const response = await getResourceView(resourceId)
        const resource = response.data

        // 🔥 更新明细行数据，确保包含完整的显示信息
        outOrderItems.value[rowIndex] = {
            ...outOrderItems.value[rowIndex],
            resourceId: resource.id,
            resourceName: resource.typeName, // 物资名称
            typeName: resource.typeName, // 🔥 确保typeName字段存在
            specification: resource.specification,
            unit: resource.unit,
            nature: resource.nature,
            balanceAmount: resource.balanceAmount,
            price: resource.price || 0, // 🔥 设置当前单价
            currentPrice: resource.price || 0, // 备用单价字段
            quantity: outOrderItems.value[rowIndex].quantity || 1 // 保持现有数量或默认为1
        }

        // 重新计算金额
        calculateItemAmount(rowIndex)
        calculateTotalAmount()
    } catch (error) {
        console.error('获取物资信息失败:', error)
        proxy?.$modal.msgError('获取物资信息失败')
    }
}

/** 数量输入处理 - 允许数字和小数点 */
const handleQuantityInput = (item: any, rowIndex: number) => {
    // 移除非数字字符，只保留数字和小数点
    let value = String(item.quantity || '').replace(/[^\d.]/g, '')

    // 确保只有一个小数点
    const parts = value.split('.')
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('')
    }

    // 更新值
    item.quantity = value ? parseFloat(value) : 0

    // 重新计算金额
    calculateItemAmount(rowIndex)
    calculateTotalAmount()
}

/** 单价输入处理 - 允许数字和小数点 */
const handlePriceInput = (item: any, rowIndex: number) => {
    // 移除非数字字符，只保留数字和小数点
    let value = String(item.price || '').replace(/[^\d.]/g, '')

    // 确保只有一个小数点
    const parts = value.split('.')
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('')
    }

    // 更新值
    item.price = value ? parseFloat(value) : 0

    // 重新计算金额
    calculateItemAmount(rowIndex)
    calculateTotalAmount()
}

/** 数量变化处理 */
const handleQuantityChange = (value: number, rowIndex: number) => {
    outOrderItems.value[rowIndex].quantity = value
    calculateItemAmount(rowIndex)
    calculateTotalAmount()
}

/** 单价变化处理 */
const handlePriceChange = (value: number, rowIndex: number) => {
    outOrderItems.value[rowIndex].price = value
    calculateItemAmount(rowIndex)
    calculateTotalAmount()
}

/** 计算明细金额 */
const calculateItemAmount = (rowIndex: number) => {
    const item = outOrderItems.value[rowIndex]
    item.amount = (item.quantity || 0) * (item.price || 0)
}

/** 计算总金额 */
const calculateTotalAmount = () => {
    form.value.amount = outOrderItems.value.reduce((sum: number, item: any) => sum + (item.amount || 0), 0)
}

/** 表单统计方法 */
const getFormTotalQuantity = () => {
    return outOrderItems.value.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0)
}

const getFormTotalAmount = () => {
    return (form.value.amount || 0).toFixed(2)
}

const getFormAvgPrice = () => {
    if (outOrderItems.value.length === 0) return '0.00'
    const totalPrice = outOrderItems.value.reduce((sum: number, item: any) => sum + (item.price || 0), 0)
    return (totalPrice / outOrderItems.value.length).toFixed(2)
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'common/resourceOutOrder/export',
        {
            ...queryParams.value
        },
        `resourceOutOrder_${new Date().getTime()}.xlsx`
    )
}

// 🔥 ==================== 出库导入功能 ====================

// 中文单位到英文代码的映射（复用入库单逻辑）
const unitMapping: Record<string, string> = {
    '吨': 't',
    '千克': 'kg',
    '克': 'g',
    '米': 'm',
    '厘米': 'cm',
    '毫米': 'mm',
    '平方米': 'm2',
    '立方米': 'm3',
    '个': 'pcs',
    '套': 'set',
    '箱': 'box',
    '瓶': 'bottle',
    '包': 'pack',
    '袋': 'bag'
}

// 获取单位代码（中文转英文）
const getOutUnitCodeByLabel = (unitLabel: string): string | null => {
    const unitItem = tnl_resource_unit.value?.find((item: any) => item.label === unitLabel.trim())
    return unitItem?.value || unitMapping[unitLabel.trim()] || null
}

// 🔥 样式相关方法
const getOutImportRowClassName = ({ row }: { row: any }) => {
    return row.isValid ? 'success-row' : 'error-row'
}

/** 根据字典值获取中文标题 */
const getDictLabel = (dictOptions: DictDataOption[], value: string): string => {
    if (!value || !dictOptions) return ''
    const option = dictOptions.find((item) => item.value === value)
    return option ? option.label : value
}

const getOutFieldClassName = (row: any, field: string) => {
    return row.errors[field] ? 'error-field' : ''
}

const getOutUnitLabelByCode = (unitCode: string) => {
    const unitDict = tnl_resource_unit.value || []
    const unit = unitDict.find((item: any) => item.value === unitCode)
    return unit ? unit.label : unitCode
}

// 文件移除处理
const handleOutFileRemove = () => {
    outSelectedFile.value = null
    outImportPreviewData.value = []
    outValidImportCount.value = 0
    outErrorImportCount.value = 0
}

// 显示出库导入对话框
const showOutImportDialog = () => {
    outImportDialog.visible = true
    resetOutImportDialog()
}

// 下载出库模板
const downloadOutTemplate = () => {
    const templateData = [
        // 标题行
        ['物资名称*', '规格型号*', '计量单位*', '出库数量*'],
        // 示例数据
        ['建筑材料', 'HRB400 Φ12', '吨', '5'],
        ['电气材料', 'YJV 3×120+1×70', '米', '50'],
        ['建筑材料', 'P.O 42.5', '吨', '20'],
        ['五金材料', 'M16×80螺栓', '个', '100']
    ]

    // 创建工作簿
    const ws = XLSX.utils.aoa_to_sheet(templateData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '出库明细')

    // 添加填写说明
    const instructionData = [
        ['出库明细导入说明'],
        [''],
        ['1. 支持的计量单位：'],
        ['   - 米、千克、吨、个、套、箱、瓶'],
        ['   - 平方米、立方米、克等'],
        ['   - 请使用中文单位名称'],
        [''],
        ['2. 验证规则：'],
        ['   - 系统将根据"物资名称+计量单位+规格型号"三个字段组合验证'],
        ['   - 计量单位会自动转换为系统代码进行匹配'],
        ['   - 三个字段必须完全匹配系统中的物资信息'],
        [''],
        ['3. 出库数量要求：'],
        ['   - 必须为正数'],
        ['   - 建议检查库存是否充足'],
        [''],
        ['4. 注意事项：'],
        ['   - 请勿修改表头'],
        ['   - 数据从第2行开始填写'],
        ['   - 带*号的字段为必填项']
    ]

    const instructionWs = XLSX.utils.aoa_to_sheet(instructionData)
    XLSX.utils.book_append_sheet(wb, instructionWs, '填写说明')

    // 下载文件
    XLSX.writeFile(wb, `出库明细导入模板_${dayjs().format('YYYYMMDD')}.xlsx`)
}

// 处理文件选择
const handleOutFileChange = (file: any) => {
    outSelectedFile.value = file
    // 清空之前的预览数据
    outImportPreviewData.value = []
    outValidImportCount.value = 0
    outErrorImportCount.value = 0
}

// 移除文件
const removeOutFile = () => {
    outSelectedFile.value = null
    outImportPreviewData.value = []
    outValidImportCount.value = 0
    outErrorImportCount.value = 0
    outUploadRef.value?.clearFiles()
}

// 解析Excel文件（复用入库单逻辑）
const parseOutExcelFile = (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (e) => {
            try {
                const data = new Uint8Array(e.target?.result as ArrayBuffer)
                const workbook = XLSX.read(data, { type: 'array' })
                const sheetName = workbook.SheetNames[0]
                const worksheet = workbook.Sheets[sheetName]
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

                // 跳过标题行，转换数据格式
                const rows = jsonData.slice(1).filter((row: any) => {
                    return row && row.length > 0 && row.some((cell: any) => cell !== undefined && cell !== '')
                })

                const result = rows.map((row: any) => ({
                    typeName: String(row[0] || '').trim(), // 物资名称
                    specification: String(row[1] || '').trim(), // 规格型号
                    unit: String(row[2] || '').trim(), // 计量单位
                    quantity: Number(row[3]) || 0 // 出库数量
                }))

                resolve(result)
            } catch (error) {
                reject(new Error('Excel文件格式错误，请检查文件格式'))
            }
        }
        reader.onerror = () => reject(new Error('文件读取失败'))
        reader.readAsArrayBuffer(file)
    })
}

// 清空所有出库明细
const clearAllOutItems = () => {
    proxy?.$modal.confirm('确认清空所有出库明细吗？').then(() => {
        outOrderItems.value = []
        calculateTotalAmount()
        proxy?.$modal.msgSuccess('已清空所有明细')
    })
}

// 解析Excel数据
const parseOutExcelData = async () => {
    if (!outSelectedFile.value) return

    outParsing.value = true
    try {
        const rawData = await parseOutExcelFile(outSelectedFile.value.raw!)

        // 转换为出库预览数据格式
        outImportPreviewData.value = rawData.map((row: any, index: number) => {
            const unitCode = getOutUnitCodeByLabel(row.unit || '')

            return {
                rowNumber: index + 2,
                typeName: row.typeName || '',
                specification: row.specification || '',
                unit: unitCode || row.unit || '',
                unitLabel: row.unit || '',
                quantity: Number(row.quantity) || 0,
                errors: {},
                isValid: false,
                resourceId: null,
                resourceInfo: null,
                suggestions: {},
                validationDetails: {
                    basicFields: false,
                    resourceMatch: false
                }
            }
        })

        // 执行数据验证
        await validateOutImportData()
    } catch (error: any) {
        proxy?.$modal.msgError('Excel文件解析失败: ' + error.message)
    } finally {
        outParsing.value = false
    }
}

// 基础字段验证（出库单定制）
const validateOutBasicFields = (row: OutImportRow) => {
    row.errors = {} // 重置错误
    row.isValid = true // 默认有效

    // 物资名称验证
    if (!row.typeName || !row.typeName.trim()) {
        row.errors.typeName = '物资名称不能为空'
        row.isValid = false
    }

    // 规格型号验证
    if (!row.specification || !row.specification.trim()) {
        row.errors.specification = '规格型号不能为空'
        row.isValid = false
    }

    // 计量单位验证
    if (!row.unitLabel || !row.unitLabel.trim()) {
        row.errors.unit = '计量单位不能为空'
        row.isValid = false
    } else if (!row.unit) {
        row.errors.unit = `无法识别的单位"${row.unitLabel}"，请检查是否为系统支持的单位`
        row.isValid = false
    }

    // 出库数量验证
    if (!row.quantity || isNaN(row.quantity) || Number(row.quantity) <= 0) {
        row.errors.quantity = '出库数量必须为正数'
        row.isValid = false
    }

    row.validationDetails.basicFields = row.isValid
}

// 出库物资匹配验证（复用入库单逻辑）
const validateOutResourceExists = async (matchData: {
    typeName: string // 物资名称
    unit: string // 计量单位（英文代码）
    specification: string // 规格型号
}) => {
    try {
        // 使用相同的listResource接口进行查询
        const response = await listResource({
            typeName: matchData.typeName,
            unit: matchData.unit,
            specification: matchData.specification,
            pageNum: 1,
            pageSize: 100
        })

        const resources = response.rows || []

        // 🔥 三字段精确匹配
        const exactMatch = resources.find(
            (resource) =>
                resource.typeName === matchData.typeName && resource.unit === matchData.unit && resource.specification === matchData.specification
        )

        if (exactMatch) {
            return {
                data: {
                    exists: true,
                    resourceId: exactMatch.id,
                    resourceInfo: exactMatch,
                    message: '验证通过'
                }
            }
        } else {
            // 智能错误分析
            return await analyzeOutMatchFailure(matchData, resources)
        }
    } catch (error) {
        throw new Error('验证物资信息时发生错误')
    }
}

// 智能错误分析（出库版本）
const analyzeOutMatchFailure = async (matchData: any, resources: any[]) => {
    const suggestions = {
        typeNames: [] as string[],
        units: [] as string[],
        specifications: [] as string[]
    }

    // 分析部分匹配情况
    const typeNameMatches = resources.filter((r) => r.typeName === matchData.typeName)
    const unitMatches = resources.filter((r) => r.unit === matchData.unit)
    const specMatches = resources.filter((r) => r.specification === matchData.specification)

    // 收集建议
    if (typeNameMatches.length > 0) {
        suggestions.units = [...new Set(typeNameMatches.map((r) => r.unit))]
        suggestions.specifications = [...new Set(typeNameMatches.map((r) => r.specification))]
    }

    if (unitMatches.length > 0) {
        suggestions.typeNames = [...new Set(unitMatches.map((r) => r.typeName))]
        suggestions.specifications = [...new Set(unitMatches.map((r) => r.specification))]
    }

    if (specMatches.length > 0) {
        suggestions.typeNames = [...new Set(specMatches.map((r) => r.typeName))]
        suggestions.units = [...new Set(specMatches.map((r) => r.unit))]
    }

    // 生成错误信息
    let errorMessage = '未找到匹配的物资信息'
    if (typeNameMatches.length > 0 && unitMatches.length === 0 && specMatches.length === 0) {
        errorMessage = '物资名称正确，但计量单位和规格型号不匹配'
    } else if (typeNameMatches.length > 0 && unitMatches.length > 0 && specMatches.length === 0) {
        errorMessage = '物资名称和计量单位正确，但规格型号不匹配'
    } else if (typeNameMatches.length === 0 && unitMatches.length > 0 && specMatches.length > 0) {
        errorMessage = '计量单位和规格型号正确，但物资名称不匹配'
    }

    return {
        data: {
            exists: false,
            resourceId: null,
            resourceInfo: null,
            message: errorMessage,
            suggestions
        }
    }
}

// 验证导入数据
const validateOutImportData = async () => {
    console.log('🔍 开始验证出库导入数据...')

    for (const row of outImportPreviewData.value) {
        // 1. 基础字段验证
        validateOutBasicFields(row)

        // 2. 如果基础字段验证通过，进行物资匹配验证
        if (row.validationDetails.basicFields) {
            try {
                const result = await validateOutResourceExists({
                    typeName: row.typeName,
                    unit: row.unit,
                    specification: row.specification
                })

                if (result.data.exists) {
                    row.resourceId = result.data.resourceId
                    row.resourceInfo = result.data.resourceInfo
                    row.validationDetails.resourceMatch = true
                    row.isValid = true
                } else {
                    row.errors.resourceMatch = result.data.message
                    row.suggestions = (result.data as any).suggestions || {}
                    row.validationDetails.resourceMatch = false
                    row.isValid = false
                }
            } catch (error: any) {
                row.errors.resourceMatch = error.message
                row.validationDetails.resourceMatch = false
                row.isValid = false
            }
        }
    }

    // 更新统计
    outValidImportCount.value = outImportPreviewData.value.filter((row) => row.isValid).length
    outErrorImportCount.value = outImportPreviewData.value.filter((row) => !row.isValid).length

    console.log('✅ 出库数据验证完成:', {
        总数: outImportPreviewData.value.length,
        有效: outValidImportCount.value,
        错误: outErrorImportCount.value
    })
}

// 出库数据完整性检查
const validateOutDataIntegrity = () => {
    console.log('🔍 开始出库数据完整性检查...')

    const originalLength = outOrderItems.value.length

    // 检查并清理空行或无效数据
    outOrderItems.value = outOrderItems.value.filter((item) => {
        const isValid = item.resourceId && item.quantity && item.quantity > 0

        if (!isValid) {
            console.log('🗑️ 清理无效出库数据:', {
                resourceId: item.resourceId,
                quantity: item.quantity,
                specification: item.specification
            })
        }

        return isValid
    })

    const cleanedLength = outOrderItems.value.length
    if (originalLength !== cleanedLength) {
        console.log(`✅ 出库数据完整性检查完成，清理了 ${originalLength - cleanedLength} 条无效数据`)
    }
}

// 确认出库导入
const confirmOutImport = () => {
    if (outErrorImportCount.value > 0) {
        proxy?.$modal.msgError(`存在 ${outErrorImportCount.value} 条验证失败的数据，请修正后重新导入`)
        return
    }

    if (outValidImportCount.value === 0) {
        proxy?.$modal.msgError('没有有效的数据可以导入')
        return
    }

    proxy?.$modal
        .confirm(`确认导入 ${outValidImportCount.value} 条有效数据？`)
        .then(() => {
            outImporting.value = true

            try {
                const validItems = outImportPreviewData.value.filter((item) => item.isValid)
                const sortedValidItems = validItems.sort((a, b) => a.rowNumber - b.rowNumber)

                console.log('📊 出库导入前状态:', {
                    现有明细数量: outOrderItems.value.length,
                    准备导入数量: sortedValidItems.length,
                    总计: outOrderItems.value.length + sortedValidItems.length
                })

                // 转换为出库明细数据格式
                const newOutItems = sortedValidItems.map((item, index) => ({
                    tempId: `out_import_${Date.now()}_${index}`,
                    resourceId: item.resourceId,

                    // 🔥 完善物资显示信息
                    resourceName: item.resourceInfo?.typeName || item.typeName, // 物资名称
                    typeName: item.resourceInfo?.typeName || item.typeName, // 确保typeName字段存在

                    specification: item.specification,
                    unit: item.unit,
                    nature: item.resourceInfo?.nature || '',
                    balanceAmount: item.resourceInfo?.balanceAmount || 0,

                    // 🔥 添加单价信息
                    price: item.resourceInfo?.price || 0, // 使用物资库中的当前单价
                    currentPrice: item.resourceInfo?.price || 0, // 当前单价（备用字段）

                    quantity: item.quantity,
                    amount: item.quantity * (item.resourceInfo?.price || 0), // 自动计算金额
                    remark: '',

                    // 导入标识
                    isImported: true,
                    importTime: new Date().toISOString()
                }))

                console.log(
                    '🔄 转换后的出库数据:',
                    newOutItems.map((item, index) => ({
                        索引: index,
                        临时ID: item.tempId,
                        物资ID: item.resourceId,
                        物资名称: item.typeName || item.resourceName,
                        规格型号: item.specification,
                        数量: item.quantity,
                        单价: item.price,
                        金额: item.amount
                    }))
                )

                // 添加到出库明细列表
                outOrderItems.value.push(...newOutItems)

                console.log('📈 出库导入后状态:', {
                    总明细数量: outOrderItems.value.length,
                    最后几条数据: outOrderItems.value.slice(-3).map((item, index) => ({
                        相对索引: index,
                        临时ID: (item as any).tempId,
                        规格: item.specification,
                        是否导入: (item as any).isImported
                    }))
                })

                // 数据完整性检查
                validateOutDataIntegrity()

                // 重新计算总计
                calculateTotalAmount()

                // 强制刷新表格
                nextTick(() => {
                    outTableRefreshKey.value++
                    console.log('🔄 出库表格已刷新，key值:', outTableRefreshKey.value)
                })

                // 关闭导入对话框
                outImportDialog.visible = false
                proxy?.$modal.msgSuccess(`成功导入 ${newOutItems.length} 条明细`)

                resetOutImportDialog()

                console.log('✅ 出库导入完成，最终数据状态:', {
                    总数量: outOrderItems.value.length,
                    总金额: (form.value as any).amount || '计算中...'
                })
            } catch (error: any) {
                console.error('❌ 出库导入失败:', error)
                proxy?.$modal.msgError('导入失败: ' + error.message)
            } finally {
                outImporting.value = false
            }
        })
        .catch(() => {
            console.log('🚫 用户取消出库导入')
        })
}

// 取消出库导入
const cancelOutImport = () => {
    outImportDialog.visible = false
    resetOutImportDialog()
}

// 重置出库导入对话框
const resetOutImportDialog = () => {
    outSelectedFile.value = null
    outImportPreviewData.value = []
    outValidImportCount.value = 0
    outErrorImportCount.value = 0
    outParsing.value = false
    outImporting.value = false
    outUploadRef.value?.clearFiles()
}

onMounted(() => {
    getList()
})
</script>

<style scoped lang="scss">
/* 页面整体布局 */
.h-full {
    height: 100%;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.flex-1 {
    flex: 1;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

/* 选中行高亮 */
:deep(.selected-row) {
    background-color: #fef3c7 !important;
}

:deep(.selected-row:hover) {
    background-color: #fde68a !important;
}

/* 去掉表格行hover时的白色背景 */
:deep(.el-table__row:hover) {
    background-color: transparent !important;
}

:deep(.el-table__row:hover > td) {
    background-color: transparent !important;
}

/* 去掉表格行的默认背景 */
:deep(.el-table__row) {
    background-color: transparent !important;
}

:deep(.el-table__row > td) {
    background-color: transparent !important;
}

/* 去掉表格体的背景 */
:deep(.el-table__body-wrapper) {
    background-color: transparent !important;
}

/* 空状态样式 */
.text-gray-400 {
    color: #9ca3af;
}

.text-gray-500 {
    color: #6b7280;
}

/* 统计信息样式 */
.bg-gray-50 {
    background-color: transparent !important;
}

/* 出库特色样式 */
.text-orange-500 {
    color: #f97316;
}

.text-orange-600 {
    color: #ea580c;
}

.text-green-600 {
    color: #16a34a;
}

.text-red-600 {
    color: #dc2626;
}

.mb-4 {
    margin-bottom: 16px;
}

/* 出库数量标签样式 */
:deep(.el-tag--warning) {
    background-color: #fef3c7;
    border-color: #fbbf24;
    color: #92400e;
}

/* 确认出库按钮样式 */
:deep(.el-button--warning) {
    --el-button-text-color: #b45309;
    --el-button-hover-text-color: #92400e;
}

/* 去掉统计区域的背景 */
:deep(.p-4.rounded-lg) {
    background-color: transparent !important;
}

/* 响应式调整 */
@media (max-width: 1024px) {
    .el-card {
        margin-bottom: 16px;
    }

    /* 小屏幕时调整高度比例 */
    .el-card:first-of-type {
        height: 50vh !important;
        min-height: 300px !important;
    }
}

@media (max-width: 768px) {
    .el-col {
        margin-bottom: 10px;
    }

    /* 移动端时统计信息垂直排列 */
    .el-row .el-col {
        width: 100% !important;
        max-width: 100% !important;
    }
}

/* 🔥 出库导入相关样式 */
.import-container {
    .upload-section {
        text-align: center;
        padding: 20px;

        .file-info {
            margin-top: 15px;
            padding: 10px;
            background-color: #f5f7fa;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
    }

    .preview-section {
        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;

            h4 {
                margin: 0;
                color: #303133;
            }

            .preview-stats {
                display: flex;
                gap: 8px;
            }
        }

        .preview-table {
            margin-bottom: 20px;

            .error-field {
                color: #f56c6c;
                display: flex;
                align-items: center;
                gap: 4px;
            }
        }

        .error-summary {
            background-color: #fef0f0;
            border: 1px solid #fbc4c4;
            border-radius: 4px;
            padding: 15px;

            h5 {
                margin: 0 0 10px 0;
                color: #f56c6c;
            }

            .error-item {
                margin-bottom: 15px;

                &:last-child {
                    margin-bottom: 0;
                }

                .error-row-header {
                    font-weight: bold;
                    color: #f56c6c;
                    margin-bottom: 5px;
                }

                .error-details {
                    margin-left: 15px;

                    .error-detail {
                        display: flex;
                        align-items: center;
                        gap: 5px;
                        margin-bottom: 3px;
                        color: #606266;
                    }
                }

                .suggestions {
                    margin-left: 15px;
                    margin-top: 8px;
                    padding: 8px;
                    background-color: #f0f9ff;
                    border-left: 3px solid #409eff;
                    border-radius: 0 4px 4px 0;
                }
            }
        }
    }
}

/* 上传组件样式优化 */
:deep(.el-upload-dragger) {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    width: 100%;
    height: 180px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color 0.3s;

    &:hover {
        border-color: #409eff;
    }
}

:deep(.el-upload-dragger .el-icon--upload) {
    font-size: 67px;
    color: #c0c4cc;
    margin: 20px 0 16px; /* 🔥 上边距从40px改为20px，上移20像素 */
    line-height: 50px;
}

:deep(.el-upload__text) {
    color: #606266;
    font-size: 14px;
    text-align: center;
    margin-top: -20px; /* 🔥 添加负边距，上移20像素 */

    em {
        color: #409eff;
        font-style: normal;
    }
}

/* 🔥 导入预览表格样式 */
:deep(.success-row) {
    background-color: #f0f9ff;
}

:deep(.error-row) {
    background-color: #fef0f0;
}

.error-field {
    color: #f56c6c;
    font-weight: bold;
}

.error-icon {
    margin-left: 4px;
}

/* 导入对话框样式优化 */
.mb-4 {
    margin-bottom: 16px;
}
</style>
