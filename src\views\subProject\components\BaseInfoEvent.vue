<!-- 事件基本信息详情 -->
<template>
    <div class="base-info-event" :class="{ 'bigscreen-theme': props.theme === 'bigscreen' }">
        <!-- 加载状态 -->
        <div v-if="loading" style="text-align: center; padding: 50px">
            <el-icon class="is-loading" style="font-size: 24px; color: #409eff">
                <Loading />
            </el-icon>
            <div style="margin-top: 10px; color: #909399">数据加载中...</div>
        </div>

        <!-- 内容区域 -->
        <div v-else>
            <el-card shadow="never">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>基本信息</span>
                        <el-divider direction="horizontal"></el-divider>
                    </div>
                </template>
                <el-form label-width="150px">
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="发生时间">
                                <el-text>{{ eventDetail?.happenTime || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="发现时间">
                                <el-text>{{ eventDetail?.discoverTime || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="预计恢复时间">
                                <el-text>{{ eventDetail?.predicateRecoverTime || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="事件等级">
                                <el-text>{{ emergency_level.find((item) => item.value === eventDetail?.emergencyLevel)?.label || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="事件发生瓶颈类型">
                                <el-text>{{ event_bottleneck.find((item) => item.value === eventDetail?.eventBottleneck)?.label || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8"> </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="事件分类">
                                <el-text>{{ event_class.find((item) => item.value === eventDetail?.eventClass)?.label || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="事件类型">
                                <el-text>{{ event_type.find((item) => item.value === eventDetail?.eventType)?.label || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="事件子类型">
                                <el-text>{{ event_sub_type.find((item) => item.value === eventDetail?.eventSubtype)?.label || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="天气情况">
                                <el-text>{{ weather.find((item) => item.value === eventDetail?.fstrWeather)?.label || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8"> </el-col>
                        <el-col :span="8"> </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="涉及行业车辆">
                                <el-text>{{ yes_no.find((item) => item.value === eventDetail?.involveCar)?.label || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="涉及车辆号码">
                                <el-text>{{ eventDetail?.carPlateNumber || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="车辆离开情况">
                                <el-text>{{ road_leave.find((item) => item.value === eventDetail?.roadLeave)?.label || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="起点位置">
                                <el-text>{{ eventDetail?.bgnAddress || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="终点位置">
                                <el-text>{{ eventDetail?.endAddress || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8"> </el-col>
                    </el-row>
                </el-form>
            </el-card>
            <el-card shadow="never" style="margin-top: 10px">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>交通通行情况</span>
                        <!-- <el-divider direction="horizontal"></el-divider> -->
                    </div>
                </template>
                <el-form label-width="150px">
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="车道总数">
                                <el-text>{{ eventDetail?.roadNum || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="占用总数">
                                <el-text>{{ eventDetail?.roadUnavailable || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="拥堵长度">
                                <el-text>{{ eventDetail?.congestionLength || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="可用车道数">
                                <el-text>{{ eventDetail?.roadAvailable || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="事件所在车道">
                                <el-text>{{ eventDetail?.roadPosition || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8"> </el-col>
                    </el-row>
                </el-form>
            </el-card>
            <el-card style="margin-top: 10px" v-if="false">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>封道记录 ({{ (eventDetail as any)?.sealings?.length || 0 }}条)</span>
                        <!-- <el-divider direction="horizontal"></el-divider> -->
                    </div>
                </template>
                <el-table :data="(eventDetail as any)?.sealings || []" style="width: 100%" border>
                    <el-table-column label="开始时间" align="center" prop="bgnTime" width="180">
                        <template #default="scope">
                            <el-text>{{ scope.row.bgnTime || '暂无' }}</el-text>
                        </template>
                    </el-table-column>
                    <el-table-column label="结束时间" align="center" prop="endTime" width="180">
                        <template #default="scope">
                            <el-text>{{ scope.row.endTime || '暂无' }}</el-text>
                        </template>
                    </el-table-column>
                    <el-table-column label="封道车辆" align="center" prop="sealingCar">
                        <template #default="scope">
                            <el-text>{{ getCarPlateNumber(scope.row.sealingCar) || '暂无' }}</el-text>
                        </template>
                    </el-table-column>
                    <el-table-column label="封道车道号" align="center" prop="roadNumbers">
                        <template #default="scope">
                            <el-text>{{ getRoadPositionLabel(scope.row.roadNumbers) || '暂无' }}</el-text>
                        </template>
                    </el-table-column>
                    <el-table-column label="封道负责人" align="center" prop="sealingUser">
                        <template #default="scope">
                            <el-text>{{ getUserName(scope.row.sealingUser) || '暂无' }}</el-text>
                        </template>
                    </el-table-column>
                </el-table>
                <div
                    v-if="!(eventDetail as any)?.sealings || (eventDetail as any).sealings.length === 0"
                    style="text-align: center; padding: 20px; color: #909399"
                >
                    暂无封道记录
                </div>
            </el-card>
            <el-card style="margin-top: 10px">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>路产损失 ({{ lossedList.length }}条)</span>
                        <!-- <el-divider direction="horizontal"></el-divider> -->
                    </div>
                </template>
                <el-table :data="lossedList" style="width: 100%" border>
                    <el-table-column label="设备名称" align="center" prop="deviceName">
                        <template #default="scope">
                            <el-text>{{ scope.row.deviceName || '暂无' }}</el-text>
                        </template>
                    </el-table-column>
                    <el-table-column label="管理单元" align="center" prop="adminUnitName">
                        <template #default="scope">
                            <el-text>{{ getManageUnitName(scope.row.adminUnitId) || scope.row.adminUnitName || '暂无' }}</el-text>
                        </template>
                    </el-table-column>
                    <el-table-column label="管理单元编号" align="center" prop="adminUnitNo">
                        <template #default="scope">
                            <el-text>{{ scope.row.adminUnitNo || '暂无' }}</el-text>
                        </template>
                    </el-table-column>
                    <el-table-column label="专业类型" align="center" prop="specialty">
                        <template #default="scope">
                            <el-text>{{ tnl_specialty.find((item) => item.value === scope.row.specialty)?.label || '暂无' }}</el-text>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- <div v-if="lossedList.length === 0" style="text-align: center; padding: 20px; color: #909399">暂无路产损失记录</div> -->
            </el-card>
            <el-card style="margin-top: 10px">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>事件情况</span>
                        <!-- <el-divider direction="horizontal"></el-divider> -->
                    </div>
                </template>
                <el-form label-width="150px">
                    <el-row :gutter="gutter">
                        <el-col :span="24">
                            <el-form-item label="现场描述">
                                <el-text>{{ eventDetail?.liveDescription || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="24">
                            <el-form-item label="现场处置">
                                <el-text>{{ eventDetail?.liveDealDescription || '暂无' }}</el-text>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-card>
            <el-card style="margin-top: 10px">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>事件结束</span>
                        <!-- <el-divider direction="horizontal"></el-divider> -->
                    </div>
                </template>
                <el-form label-width="150px">
                    <el-row :gutter="gutter">
                        <el-col :span="24">
                            <el-form-item label="结束时间">
                                <el-text>{{ eventDetail?.endTime || '未销项' }}</el-text>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="24">
                            <el-form-item label="附件">
                                <FileUpload v-model="eventDetail.files" disabled="true" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-card>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, watch, getCurrentInstance, toRefs, onMounted } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { getEvent } from '@/api/subProject/operation/event'
import { EventVO, EventLossVO } from '@/api/subProject/operation/event/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { useAppStore } from '@/store/modules/app'
import FileUpload from '@/components/FileUpload/index.vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const appStore = useAppStore()
const { emergency_level, event_bottleneck, event_class, event_type, event_sub_type, weather, yes_no, road_leave, road_position, tnl_specialty } =
    toRefs<any>(
        proxy?.useDict(
            'emergency_level',
            'event_bottleneck',
            'event_class',
            'event_type',
            'event_sub_type',
            'weather',
            'yes_no',
            'road_leave',
            'road_position',
            'tnl_specialty'
        )
    )

const props = defineProps({
    id: {
        type: String,
        required: true
    },
    theme: {
        type: String,
        default: 'default',
        validator: (value: string) => ['default', 'bigscreen'].includes(value)
    }
})

const gutter = ref(50) // 设置项目表单两列的距离
const eventDetail = ref<EventVO | null>(null)
const loading = ref(false)
const lossedList = ref<EventLossVO[]>([]) // 路产损失列表
const manageUnits = ref<ManageUnitVO[]>([]) // 管理单元列表

// 辅助方法：获取车辆车牌号
const getCarPlateNumber = (carId: string | number | undefined): string => {
    if (!carId) return '暂无'
    // 这里可以根据实际需求从车辆列表中查找对应的车牌号
    // 由于是只读展示，暂时直接返回ID，实际项目中可能需要调用API获取车辆详情
    return carId.toString()
}

// 辅助方法：获取车道位置标签
const getRoadPositionLabel = (value: string | number | undefined): string => {
    if (!value) return '暂无'
    const roadPos = road_position.value?.find((item: any) => item.value === value)
    return roadPos?.label || value.toString()
}

// 辅助方法：获取用户名称
const getUserName = (userId: string | number | undefined): string => {
    if (!userId) return '暂无'
    // 这里可以根据实际需求从用户列表中查找对应的用户名
    // 由于是只读展示，暂时直接返回ID，实际项目中可能需要调用API获取用户详情
    return userId.toString()
}

// 辅助方法：根据管理单元ID获取名称
const getManageUnitName = (unitId: string | undefined): string => {
    if (!unitId || !manageUnits.value.length) {
        return unitId || '暂无'
    }
    const unit = manageUnits.value.find((unit) => unit.id === unitId)
    return unit?.name || unitId
}

// 获取当前项目的所有管理单元
const loadManageUnits = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取管理单元')
            return
        }

        console.log('开始获取项目管理单元，projectId:', projectId)
        const response = await listProjectManageUnit(projectId)

        if (response && response.data) {
            manageUnits.value = Array.isArray(response.data) ? response.data : [response.data]
            console.log('获取管理单元成功:', manageUnits.value)
        } else if (response && Array.isArray(response)) {
            manageUnits.value = response
            console.log('获取管理单元成功:', manageUnits.value)
        } else {
            console.warn('获取管理单元返回数据格式异常:', response)
            manageUnits.value = []
        }
    } catch (error) {
        console.error('获取管理单元失败:', error)
        manageUnits.value = []
    }
}

// 加载事件详情
const loadEventInfo = async (id: string) => {
    loading.value = true
    try {
        const res = await getEvent(id)
        eventDetail.value = res.data

        // 反序列化路产损失数据

        if (eventDetail.value && (eventDetail.value as any).lossJson) {
            try {
                const lossedStr = (eventDetail.value as any).lossJson
                if (typeof lossedStr === 'string') {
                    lossedList.value = JSON.parse(lossedStr) as EventLossVO[]
                } else if (Array.isArray(lossedStr)) {
                    lossedList.value = lossedStr as EventLossVO[]
                } else {
                    lossedList.value = []
                }
            } catch (parseError) {
                console.error('路产损失数据解析失败:', parseError)
                lossedList.value = []
            }
        } else {
            lossedList.value = []
        }
    } catch (error) {
        console.error('加载事件详情失败:', error)
        lossedList.value = []
    } finally {
        loading.value = false
    }
}

// 监听 id 变化，重新加载数据
watch(
    () => props.id,
    async (newId) => {
        if (newId) {
            await loadEventInfo(newId)
        }
    },
    { immediate: true }
)

// 页面加载时获取管理单元列表
onMounted(async () => {
    await loadManageUnits()
})
</script>

<style lang="scss" scoped>
// 大屏主题样式
.base-info-event.bigscreen-theme {
    // 卡片样式
    :deep(.el-card) {
        background-color: rgba(0, 20, 50, 0.8) !important;
        border: 1px solid rgba(0, 150, 255, 0.3) !important;

        .el-card__header {
            background-color: rgba(17, 39, 75, 0.8) !important;
            border-bottom: 1px solid rgba(0, 150, 255, 0.2) !important;
            color: #ffffff !important;

            .clearfix span {
                color: #ffffff !important;
                font-weight: bold;
            }
        }

        .el-card__body {
            background-color: transparent !important;
            color: #ffffff !important;
        }
    }

    // 表单样式
    :deep(.el-form) {
        .el-form-item__label {
            color: #ffffff !important;
        }

        .el-text {
            color: #ffffff !important;
        }

        .el-input {
            .el-input__inner {
                background-color: rgba(0, 20, 50, 0.6) !important;
                border-color: rgba(0, 150, 255, 0.3) !important;
                color: #ffffff !important;
            }
        }

        .el-textarea {
            .el-textarea__inner {
                background-color: rgba(0, 20, 50, 0.6) !important;
                border-color: rgba(0, 150, 255, 0.3) !important;
                color: #ffffff !important;
            }
        }
    }

    // 表格样式
    :deep(.el-table) {
        background-color: transparent !important;
        color: #ffffff !important;

        .el-table__header {
            background-color: rgba(17, 39, 75, 0.8) !important;

            th {
                background-color: rgba(17, 39, 75, 0.8) !important;
                color: #ffffff !important;
                border-color: rgba(0, 150, 255, 0.2) !important;
            }
        }

        .el-table__body {
            tr {
                background-color: rgba(0, 20, 50, 0.6) !important;

                &:nth-child(even) {
                    background-color: rgba(17, 39, 75, 0.4) !important;
                }

                td {
                    background-color: inherit !important;
                    color: #ffffff !important;
                    border-color: rgba(0, 150, 255, 0.1) !important;
                }
            }
        }
    }

    // 加载状态样式
    .loading-container {
        background-color: rgba(0, 20, 50, 0.8) !important;
        color: #ffffff !important;

        .el-icon {
            color: #00d4ff !important;
        }

        div {
            color: #ffffff !important;
        }
    }

    // 分割线样式
    :deep(.el-divider) {
        border-color: rgba(0, 150, 255, 0.3) !important;
    }
}
</style>
