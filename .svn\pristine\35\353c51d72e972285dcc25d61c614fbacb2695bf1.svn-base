export interface EventVO {
    /**
     *
     */
    id: string | number

    /**
     * 所属项目
     */
    projectId: string | number

    /**
     * 发生时间
     */
    happenTime: string

    /**
     * 发现时间
     */
    discoverTime: string
    tunnelName: string

    /**
     * 预计回复时间
     */
    predicateRecoverTime: string

    /**
     * 事件等级
     */
    emergencyLevel: string

    /**
     * 事件发生瓶颈类型
     */
    eventBottleneck: string

    /**
     * 事件分类
     */
    eventClass: string

    /**
     * 事件类型
     */
    eventType: string

    /**
     * 事件子类型
     */
    eventSubtype: string

    /**
     * 天气情况
     */
    fstrWeather: string

    /**
     * 涉及行业车辆
     */
    involveCar: string

    /**
     * 涉及车辆号码
     */
    involveCarBrand: string

    /**
     * 车辆离开情况
     */
    roadLeave: string

    /**
     * 信息位置
     */
    addressType: string

    /**
     * 起点位置
     */
    bgnAddress: string
    bgnAddressNumber?: number
    bgnKilometer?: string
    endKilometer?: string

    /**
     * 终点位置
     */
    endAddress: string
    endAddressNumber?: number
    roadwayId?: string
    /**
     * 车道总数
     */
    roadNum: string

    /**
     * 占用总数
     */
    roadUnavailable: string

    /**
     * 拥堵长度
     */
    congestionLength: string

    /**
     * 可用车道数
     */
    roadAvailable: string

    /**
     * 事件所在车道
     */
    roadPosition: string

    /**
     * 封道记录
     */
    sealJson: string

    /**
     * 路产损失
     */
    lossJson: string

    /**
     * 车牌号码
     */
    carPlateNumber: string

    /**
     * 车主电话
     */
    carOwnerPhone: string

    /**
     * 损失金额
     */
    lossAmount: string

    /**
     * 报警时间
     */
    policeTime: string

    /**
     * 消防时间
     */
    fireTime: string

    /**
     * 医疗时间
     */
    medicalTime: string

    /**
     * 应急时间
     */
    emergencyTime: string

    /**
     * 牵引记录
     */
    pullJson: string

    /**
     * 死亡人数
     */
    dies: number

    /**
     * 受伤人数
     */
    induries: number

    /**
     * 现场描述
     */
    liveDescription: string

    /**
     * 现场处置
     */
    liveDealDescription: string
    endTime: Date
    files?: string
}
export interface MapEventVO {
    projectId: string | number
    tunnelName: string
}

export interface EventForm extends BaseEntity {
    /**
     *
     */
    id?: string | number

    /**
     * 所属项目
     */
    projectId?: string | number

    /**
     * 发生时间
     */
    happenTime?: string

    /**
     * 发现时间
     */
    discoverTime?: string
    tunnelName?: string

    /**
     * 预计回复时间
     */
    predicateRecoverTime?: string

    /**
     * 事件等级
     */
    emergencyLevel?: string

    /**
     * 事件发生瓶颈类型
     */
    eventBottleneck?: string

    /**
     * 事件分类
     */
    eventClass?: string

    /**
     * 事件类型
     */
    eventType?: string

    /**
     * 事件子类型
     */
    eventSubtype?: string

    /**
     * 天气情况
     */
    fstrWeather?: string

    /**
     * 涉及行业车辆
     */
    involveCar?: string

    /**
     * 涉及车辆品牌
     */
    involveCarBrand?: string

    /**
     * 车辆离开情况
     */
    roadLeave?: string

    /**
     * 信息位置
     */
    addressType?: string
    bgnAddress?: string

    /**
     * 起点里程数字（范围查询 >=）
     */
    bgnAddressNumber?: number
    endAddress?: string

    /**
     * 终点里程数字（范围查询 <=）
     */
    endAddressNumber?: number
    bgnKilometer?: string
    endKilometer?: string

    roadwayId?: string
    /**
     * 车道总数
     */
    roadNum?: string

    /**
     * 占用总数
     */
    roadUnavailable?: string

    /**
     * 拥堵长度
     */
    congestionLength?: string

    /**
     * 可用车道数
     */
    roadAvailable?: string

    /**
     * 事件所在车道
     */
    roadPosition?: string

    /**
     * 封道记录
     */
    sealJson?: string
    sealings?: EventSealingVO[]

    /**
     * 路产损失
     */
    lossJson?: string
    losses?: EventLossVO[]

    /**
     *
     */
    pullJson?: string
    pulls?: EventPullVO[]

    /**
     * 死亡人数
     */
    dies?: number

    /**
     * 受伤人数
     */
    induries?: number

    /**
     * 现场描述
     */
    liveDescription?: string

    /**
     * 现场处置
     */
    liveDealDescription?: string

    /**
     * 车牌号码
     */
    carPlateNumber?: string

    /**
     * 车主电话
     */
    carOwnerPhone?: string

    /**
     * 损失金额
     */
    lossAmount?: string

    /**
     * 报警时间
     */
    policeTime?: string

    /**
     * 消防时间
     */
    fireTime?: string

    /**
     * 医疗时间
     */
    medicalTime?: string

    /**
     * 应急时间
     */
    emergencyTime?: string

    /**
     * 事件结束时间
     */
    endTime?: string

    files?: string
}

export interface EventQuery extends PageQuery {
    /**
     * 所属项目
     */
    projectId?: string | number

    /**
     * 发生时间
     */
    happenTime?: string

    /**
     * 发现时间
     */
    discoverTime?: string
    tunnelName?: string

    /**
     * 预计回复时间
     */
    predicateRecoverTime?: string

    /**
     * 事件等级
     */
    emergencyLevel?: string

    /**
     * 事件发生瓶颈类型
     */
    eventBottleneck?: string

    /**
     * 事件分类
     */
    eventClass?: string

    /**
     * 事件类型
     */
    eventType?: string

    /**
     * 事件子类型
     */
    eventSubtype?: string

    /**
     * 天气情况
     */
    fstrWeather?: string

    /**
     * 涉及行业车辆
     */
    involveCar?: string

    /**
     * 涉及车辆号码
     */
    involveCarPlatenumber?: string

    /**
     * 车辆离开情况
     */
    roadLeave?: string

    /**
     * 信息位置
     */
    addressType?: string

    /**
     * 起点位置
     */
    bgnAddress?: string
    bgnAddressNumber?: number
    bgnKilometer?: string
    endKilometer?: string

    /**
     * 终点位置
     */
    endAddress?: string
    endAddressNumber?: number
    roadwayId?: string

    /**
     * 车道总数
     */
    roadNum?: string

    /**
     * 占用总数
     */
    roadUnavailable?: string

    /**
     * 拥堵长度
     */
    congestionLength?: string

    /**
     * 可用车道数
     */
    roadAvailable?: string

    /**
     * 事件所在车道
     */
    roadPosition?: string

    /**
     * 封道记录
     */
    sealJson?: string

    /**
     * 路产损失
     */
    lossJson?: string

    /**
     *
     */
    pullJson?: string

    /**
     * 死亡人数
     */
    dies?: number

    /**
     * 受伤人数
     */
    induries?: number

    /**
     * 现场描述
     */
    liveDescription?: string

    /**
     * 现场处置
     */
    liveDealDescription?: string

    /**
     * 车牌号码
     */
    carPlateNumber?: string

    /**
     * 车主电话
     */
    carOwnerPhone?: string

    /**
     * 损失金额
     */
    lossAmount?: string

    /**
     * 报警时间
     */
    policeTime?: string

    /**
     * 消防时间
     */
    fireTime?: string

    /**
     * 医疗时间
     */
    medicalTime?: string

    /**
     * 应急时间
     */
    emergencyTime?: string

    /**
     * 日期范围参数
     */
    params?: any
}

export interface EventSealingVO {
    bgnTime: string
    endTime: string
    sealingCar: string // 封道车辆
    road_numbers: string // 封道车道号
    sealingUser: string // 封道负责人
}

// 路产损失
export interface EventLossVO {
    deviceId: string
    deviceName: string
    adminUnitId: string
    adminUnitName: string
    adminUnitNo: string // 管理单元编号
    specialty: string
    categoryId?: string | number
}
export interface EventPullVO {
    carId: string //车牌号
    outPullCar: string //外部牵引车辆
    carBrand: string //被牵引车品牌
    carSpecification: string //被牵引车型号
    carType: string //被牵引车车辆类型
    callTime: Date //呼叫时间
    outTime: Date //出车时间
    dutyTime: Date //牵引到位时间
    leaveTime: Date //离开时间
}
