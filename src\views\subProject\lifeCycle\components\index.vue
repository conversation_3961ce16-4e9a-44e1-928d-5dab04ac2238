<template>
    <div class="archive-update-container">
        <el-card shadow="never" class="archive-card" style="position: relative;">
            <!-- 滚动提示 -->
            <!-- <div class="scroll-indicator" :class="{ 'paused': isScrollPaused }">
                <div class="scroll-hint">
                    <i class="scroll-arrow">↓</i>
                    <span>自动滚动中...</span>
                </div>
            </div> -->
            <!-- 表格头部 -->
            <div class="custom-table-header">
                <div class="header-cell" style="width: 80px;">序号</div>
                <div class="header-cell" style="width: 200px;">实际完工日期</div>
                <div class="header-cell" style="width: 200px;">设施/设备名称</div>
                <div class="header-cell" style="width: 200px;">管理单元</div>
                <div class="header-cell" style="width: 240px;">作业单名称</div>
                <div class="header-cell" style="width: 200px;">专业类型</div>
                <div class="header-cell" style="width: 300px;">物资记录</div>
                <div class="header-cell" style="width: 120px;">操作</div>
            </div>

            <!-- 滚动表格主体 -->
            <Vue3SeamlessScroll
                ref="scrollRef"
                :list="(archiveList as any)"
                :options="scrollOptions"
                class="scroll-container"
                @mouseenter="pauseScroll"
                @mouseleave="resumeScroll"
            >
                <div v-for="(item, index) in archiveList" :key="item.id" class="custom-table-row">
                    <div class="table-cell" style="width: 80px;">{{ index + 1 }}</div>
                    <div class="table-cell" style="width: 200px;">{{ item.completionDate }}</div>
                    <div class="table-cell" style="width: 200px;">{{ item.equipmentName }}</div>
                    <div class="table-cell" style="width: 200px;">{{ item.managementUnit }}</div>
                    <div class="table-cell" style="width: 240px;">{{ item.workOrderName }}</div>
                    <div class="table-cell" style="width: 200px;">{{ item.specialtyType }}</div>
                    <div class="table-cell" style="width: 300px;">{{ item.materialRecord }}</div>
                    <div class="table-cell" style="width: 120px;">
                        <div class="op-actions">
                            <el-button link type="primary" class="op-link op-info" @click="handleView(item)">
                                <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="查看" />
                                查看
                            </el-button>
                        </div>
                    </div>
                </div>
            </Vue3SeamlessScroll>
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, toRefs } from 'vue'
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'

interface ArchiveUpdateItem {
    id: string
    completionDate: string
    equipmentName: string
    managementUnit: string
    workOrderName: string
    specialtyType: string
    materialRecord: string
}

const loading = ref(false)
const total = ref(0)
const scrollRef = ref()
const isScrollPaused = ref(false)


const scrollOptions = {
    step: 0.5, // 滚动步长，越小滚动越慢
    limitMoveNum: 6, // 开始无缝滚动的数据量 (需要超过显示行数)
    hoverStop: true, // 是否开启鼠标悬停stop
    direction: 1, // 0向下 1向上 2向左 3向右
    openWatch: true, // 开启数据实时监控刷新dom
    singleHeight: 0, // 连续滚动，不停顿
    singleWidth: 0, // 单步运动停止的宽度
    waitTime: 2000 // 循环间隔时间
}

// 示例数据
const archiveList = ref<ArchiveUpdateItem[]>([
    {
        id: '1',
        completionDate: '2025年9月15日',
        equipmentName: '行车道0001',
        managementUnit: '路段1',
        workOrderName: '南线靖市湖西路隧道3车道',
        specialtyType: '主体结构',
        materialRecord: '路基结构'
    },
    {
        id: '2',
        completionDate: '2025年9月15日',
        equipmentName: '行车道0001',
        managementUnit: '路段1',
        workOrderName: '南线靖市湖西路隧道3车道',
        specialtyType: '主体结构',
        materialRecord: '路基结构'
    },
    {
        id: '3',
        completionDate: '2025年9月15日',
        equipmentName: '行车道0001',
        managementUnit: '路段1',
        workOrderName: '南线靖市湖西路隧道3车道',
        specialtyType: '主体结构',
        materialRecord: '路基结构'
    },
    {
        id: '4',
        completionDate: '2025年9月15日',
        equipmentName: '行车道0001',
        managementUnit: '路段1',
        workOrderName: '南线靖市湖西路隧道3车道',
        specialtyType: '主体结构',
        materialRecord: '路基结构'
    },

    {
        id: '5',
        completionDate: '2025年9月15日',
        equipmentName: '行车道0001',
        managementUnit: '路段1',
        workOrderName: '南线靖市湖西路隧道3车道',
        specialtyType: '主体结构',
        materialRecord: '路基结构'
    },
    {
        id: '6',
        completionDate: '2025年9月15日',
        equipmentName: '行车道0001',
        managementUnit: '路段1',
        workOrderName: '南线靖市湖西路隧道3车道',
        specialtyType: '主体结构',
        materialRecord: '路基结构'
    },
    {
        id: '7',
        completionDate: '2025年9月15日',
        equipmentName: '行车道0001',
        managementUnit: '路段1',
        workOrderName: '南线靖市湖西路隧道3车道',
        specialtyType: '主体结构',
        materialRecord: '路基结构'
    },
    {
        id: '8',
        completionDate: '2025年9月15日',
        equipmentName: '行车道0001',
        managementUnit: '路段1',
        workOrderName: '南线靖市湖西路隧道3车道',
        specialtyType: '主体结构',
        materialRecord: '路基结构'
    },   {
        id: '9',
        completionDate: '2025年9月15日',
        equipmentName: '行车道0001',
        managementUnit: '路段1',
        workOrderName: '南线靖市湖西路隧道3车道',
        specialtyType: '主体结构',
        materialRecord: '路基结构'
    },
    {
        id: '10',
        completionDate: '2025年9月15日',
        equipmentName: '行车道0001',
        managementUnit: '路段1',
        workOrderName: '南线靖市湖西路隧道3车道',
        specialtyType: '主体结构',
        materialRecord: '路基结构'
    }
])
/** 查看操作 */
const handleView = (row: ArchiveUpdateItem) => {
    console.log('查看档案:', row)
}

/** 暂停滚动 */
const pauseScroll = () => {
    isScrollPaused.value = true
}

/** 恢复滚动 */
const resumeScroll = () => {
    isScrollPaused.value = false
}

onMounted(() => {
    // getList()
})
</script>

<style lang="scss" scoped>
.archive-update-container {
    padding: 8px;
}

/* 滚动提示器 */
.scroll-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    background: rgba(66, 134, 243, 0.8);
    border-radius: 20px;
    padding: 6px 12px;
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;
}

.scroll-indicator.paused {
    background: rgba(255, 193, 7, 0.8);
}

.scroll-hint {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #ffffff;
    font-size: 12px;
    font-weight: 500;
}

.scroll-arrow {
    font-size: 14px;
    animation: bounceDown 1.5s ease-in-out infinite;
}

.scroll-indicator.paused .scroll-arrow {
    animation-play-state: paused;
}

.scroll-indicator.paused span::after {
    content: ' (已暂停)';
}

@keyframes bounceDown {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(4px);
    }
    60% {
        transform: translateY(2px);
    }
}

/* 自定义表格样式 */
.custom-table-header {
    display: flex;
    align-items: center;
    height: 44px;
    background: rgb(31, 43, 78);

}

.header-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 12px;
    color: #ffffff;
    font-weight: bold;
    font-size: 16px;
    border-right: 1px solid rgba(255, 255, 255, 0.08);
}

.header-cell:last-child {
    border-right: none;
}

.scroll-container {
    height: 360px; /* 显示6行数据 (60px * 6) */
    overflow: hidden;
}

.custom-table-row {
    display: flex;
    align-items: center;
    min-height: 60px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
    transition: background-color 0.2s;
    // background: red;
    // margin-top: 50px;
}

.custom-table-row:nth-child(even) {
    background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%);
}

.custom-table-row:hover {
    background-color: rgba(66, 134, 243, 0.08);
}

.table-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px 12px;
    color: #ffffff;
    font-size: 14px;
    border-right: 1px solid rgba(255, 255, 255, 0.08);
}

.table-cell:last-child {
    border-right: none;
}

/* 自定义标题样式 */
.custom-tab-title {
    font-size: 26px;
    font-weight: bold;
    font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
    letter-spacing: 1px;
    color: #e9f2ff; /* 回退色 */
    background: linear-gradient(to top, #497ef1 0%, #ffffff 60%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    line-height: 26px;
    display: inline-block;
}

/* 档案卡片与表格去除灰白背景与边框 */
:deep(.archive-card.el-card) {
    background-color: transparent;
    border: none;
}
:deep(.archive-card .el-card__header) {
    background-color: transparent;
    border-bottom: none;
}
/* 表格主体区域背景渐变 */
:deep(.archive-card .el-card__body) {
    background: linear-gradient(to top, rgba(13, 24, 58, 0.01) 0%, rgba(15, 24, 57, 0.42) 60%, rgba(22, 33, 69, 1) 100%);
}

/* 保持原有样式兼容性 */

/* 操作列样式 */
.op-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    justify-content: center;
}
.op-link {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 0 6px;
}
.op-icon {
    width: 14px;
    height: 14px;
    display: inline-block;
    margin-right: 4px;
}
.op-info {
    color: #4286F3 !important;
}
.op-edit {
    color: #42F3E9 !important;
}
.op-delete {
    color: #D62121 !important;
}
</style>
