<!-- 管养总里程 -->
<template>
    <div class="total-mileage">
        <div class="total-mileage-content">
            <div class="imageBack"></div>
            <div class="total-mileage-content-right">
                <div class="total-mileage-number">
                    {{ displayValue.toFixed(1) || data?.value }}
                </div>
                <div class="total-mileage-unit">{{ data?.unit || 'KM' }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useStatisticData, ANIMATION_PRESETS } from '@/composables/useStatisticData'
import { StatisticType } from '@/api/statistics/dashboard/types'

const { data, displayValue } = useStatisticData(
    StatisticType.TOTAL_MILEAGE,
    120000, // 2分钟更新一次
    ANIMATION_PRESETS.slow // 使用慢速动画突出重要性
)
</script>

<style lang="scss" scoped>
.total-mileage {
    width: 450px;
    height: 155px;
    margin-top: 49px;
    // background: url('@/assets/images/bigscreen/设备数量@2x.png') no-repeat center center;
    // background-size: 100% 100%;
    display: flex;
    align-items: center;
    position: relative;

    .total-mileage-content {
        display: flex;
        align-items: center;
        width: 100%;
        justify-content: space-between;
        .imageBack {
            width: 140px;
            height: 100px;
            background: url('@/assets/images/bigscreen/设备数量@2x.png') no-repeat center center;
            background-size: 100% 100%;
        }
        .total-mileage-content-right {
            display: flex;
            align-items: flex-end;
            box-sizing: content-box;
            margin-right: 13px;
        }
        .total-mileage-number {
            font-family: YouSheBiaoTiHei-Regular;
            font-weight: 400;
            font-size: 64px;
            color: #00f0ff;
            line-height: 57px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }

        .total-mileage-unit {
            font-size: 24px;
            color: #ffffff;
            opacity: 0.8;
        }
    }
}
</style>
