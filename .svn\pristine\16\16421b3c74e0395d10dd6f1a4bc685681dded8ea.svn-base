<template>
    <div class="event-form" v-loading="pageLoading" :element-loading-text="loadingText">
        <el-form ref="eventFormRef" :model="form" :rules="rules" label-width="150px">
            <el-card shadow="never">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>基本信息</span>

                        <el-divider direction="horizontal"></el-divider>
                    </div>
                </template>
                <el-row :gutter="gutter">
                    <el-col :span="8">
                        <el-form-item label="发生时间" prop="happenTime">
                            <el-date-picker
                                clearable
                                v-model="form.happenTime"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                placeholder="请选择发生时间"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="8">
                        <el-form-item label="发现时间" prop="discoverTime">
                            <el-date-picker
                                clearable
                                v-model="form.discoverTime"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                placeholder="请选择发现时间"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="预计恢复时间" prop="predicateRecoverTime">
                            <el-date-picker
                                clearable
                                v-model="form.predicateRecoverTime"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                placeholder="请选择预计恢复时间"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>-->
                    <el-col :span="8">
                        <el-form-item label="详细位置" prop="addressType">
                            <el-radio-group v-model="form.addressType" @change="(value: string) => handleAddressTypeChange(value)">
                                <el-radio v-for="item in code_type" :key="item.value" :value="item.value">
                                    {{ item.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="gutter" v-if="form.addressType == 'camera_number'">
                    <el-col :span="8">
                        <el-form-item label="起点位置" prop="bgnAddress">
                            <el-select
                                v-model="form.bgnAddress"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入摄像头名称搜索"
                                :remote-method="searchStartCameras"
                                :loading="startCameraLoading"
                                @change="handleStartCameraChange"
                                clearable
                            >
                                <el-option
                                    v-for="camera in startCameraOptions"
                                    :key="camera.id"
                                    :label="`${camera.remark}${camera.bgnKilometer ? ' (' + camera.bgnKilometer + ')' : ''}`"
                                    :value="camera.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="终点位置" prop="endAddress">
                            <el-select
                                v-model="form.endAddress"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入摄像头名称搜索"
                                :remote-method="searchEndCameras"
                                :loading="endCameraLoading"
                                @change="handleEndCameraChange"
                                clearable
                            >
                                <el-option
                                    v-for="camera in endCameraOptions"
                                    :key="camera.id"
                                    :label="`${camera.remark}${camera.endKilometer ? ' (' + camera.endKilometer + ')' : ''}`"
                                    :value="camera.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8"> </el-col>
                </el-row>
                <el-row :gutter="gutter" v-else>
                    <el-col :span="8">
                        <el-form-item label="起点位置" prop="bgnAddress">
                            <el-input type="text" v-model="form.bgnAddress" placeholder="请输入起点位置" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="终点位置" prop="endAddress">
                            <el-input type="text" v-model="form.endAddress" placeholder="请输入终点位置" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- <el-row :gutter="gutter" v-if="false">
                    <el-col :span="8">
                        <el-form-item label="事件等级" prop="emergencyLevel">
                            <el-select v-model="form.emergencyLevel" placeholder="请选择事件等级">
                                <el-option v-for="dict in emergency_level" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="事件发生瓶颈类型" prop="eventBottleneck">
                            <el-select v-model="form.eventBottleneck" placeholder="请选择事件发生瓶颈类型">
                                <el-option v-for="dict in event_bottleneck" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8"> </el-col>
                </el-row> -->
                <el-row :gutter="gutter">
                    <el-col :span="8">
                        <el-form-item label="所属区域" prop="tunnelName">
                            <el-select v-model="form.tunnelName" clearable filterable placeholder="请选择所属区域">
                                <el-option v-for="tunnel in tunnelList" :key="tunnel" :label="tunnel" :value="tunnel" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="gutter">
                    <el-col :span="8">
                        <el-form-item label="事件分类" prop="eventClass">
                            <el-select v-model="form.eventClass" placeholder="请选择事件分类" @change="handleEventClassChange" clearable>
                                <el-option v-for="category in eventCategories" :key="category.id" :label="category.name" :value="category.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="事件类型" prop="eventType">
                            <el-select
                                v-model="form.eventType"
                                placeholder="请选择事件类型"
                                @change="handleEventTypeChange"
                                :disabled="!form.eventClass"
                                clearable
                            >
                                <el-option v-for="type in eventTypeOptions" :key="type.id" :label="type.name" :value="type.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="gutter">
                    <el-col :span="8">
                        <el-form-item label="事件子类型" prop="eventSubtype">
                            <el-select v-model="form.eventSubtype" placeholder="请选择事件子类型" :disabled="!form.eventType" clearable>
                                <el-option v-for="subtype in eventSubtypeOptions" :key="subtype.id" :label="subtype.name" :value="subtype.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="天气情况" prop="fstrWeather">
                            <el-select v-model="form.fstrWeather" placeholder="请选择天气情况">
                                <el-option v-for="dict in fstr_weather" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8"> </el-col>
                </el-row>
                <el-row :gutter="gutter">
                    <el-col :span="8">
                        <el-form-item label="事故车辆品牌" prop="involveCarBrand">
                            <el-select v-model="form.involveCarBrand" placeholder="请选择涉及行业车辆">
                                <el-option v-for="dict in clppxh" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="8">
                        <el-form-item label="涉及行业车辆" prop="involveCar">
                            <el-select v-model="form.involveCar" placeholder="请选择涉及行业车辆">
                                <el-option v-for="dict in involve_car" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col> -->
                    <!-- <el-col :span="8">
                        <el-form-item label="涉及车辆号码" prop="involveCarPlatenumber">
                            <el-input v-model="form.involveCarPlatenumber" placeholder="请输入涉及车辆号码" />
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="8">
                        <el-form-item label="车辆离开情况" prop="roadLeave">
                            <el-select v-model="form.roadLeave" placeholder="请选择车辆离开情况">
                                <el-option v-for="dict in road_leave" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="never" style="margin-top: 10px">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>交通通行情况</span>
                        <el-divider direction="horizontal"></el-divider>
                    </div>
                </template>
                <el-row :gutter="gutter">
                    <el-col :span="8">
                        <el-form-item label="车道总数" prop="roadNum">
                            <el-select v-model="form.roadNum" placeholder="请选择车道总数">
                                <el-option v-for="dict in road_num" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="占用车道数" prop="roadUnavailable">
                            <el-select v-model="form.roadUnavailable" placeholder="请选择占用车道数">
                                <el-option v-for="dict in road_unavailable" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="拥堵长度" prop="congestionLength">
                            <el-select v-model="form.congestionLength" placeholder="请选择拥堵长度">
                                <el-option v-for="dict in congestion_length" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="gutter">
                    <el-col :span="8">
                        <el-form-item label="可用车道数" prop="roadAvailable">
                            <el-select v-model="form.roadAvailable" placeholder="请选择可用车道数">
                                <el-option v-for="dict in road_available" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="事件所在车道" prop="roadPosition">
                            <el-select v-model="form.roadPosition" placeholder="请选择事件所在车道">
                                <el-option v-for="dict in road_position" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8"> </el-col>
                </el-row>
            </el-card>

            <!-- 封道记录组件 -->
            <!--  <SealingComponent :sealings="form.sealings" :team-member-tree="teamMemberTree" @update:sealings="handleSealingsUpdate" />-->

            <!-- 路产损失组件 -->
            <LossComponent
                ref="lossComponentRef"
                :losses="form.losses"
                :carPlateNumber="form.carPlateNumber"
                :carOwnerPhone="form.carOwnerPhone"
                :lossAmount="form.lossAmount"
                @update:losses="handleLossesUpdate"
                @update:carPlateNumber="handleCarPlateNumberUpdate"
                @update:carOwnerPhone="handleCarOwnerPhoneUpdate"
                @update:lossAmount="handleLossAmountUpdate"
                @validate-car-info="handleCarInfoValidation"
            />

            <!-- 牵引记录组件 -->
            <PullComponent ref="pullComponentRef" v-if="form.roadLeave === '0'" :pulls="form.pulls" @update:pulls="handlePullsUpdate" />

            <!-- 到达时间组件 -->
            <ArriveTimeComponent
                :policeTime="form.policeTime"
                :fireTime="form.fireTime"
                :medicalTime="form.medicalTime"
                :emergencyTime="form.emergencyTime"
                @update:policeTime="handlePoliceTimeUpdate"
                @update:fireTime="handleFireTimeUpdate"
                @update:medicalTime="handleMedicalTimeUpdate"
                @update:emergencyTime="handleEmergencyTimeUpdate"
            />

            <el-card style="margin-top: 10px">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>事件情况</span>
                        <el-divider direction="horizontal"></el-divider>
                    </div>
                </template>

                <el-row :gutter="gutter">
                    <el-col :span="20">
                        <el-form-item label="现场描述" prop="liveDescription">
                            <el-input v-model="form.liveDescription" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="4" style="display: flex; align-items: flex-start; padding-top: 5px">
                        <el-button type="primary" size="small" @click="generateSceneDescription">自动生成</el-button>
                    </el-col>
                </el-row>
                <el-row :gutter="gutter">
                    <el-col :span="20">
                        <el-form-item label="现场处置" prop="liveDealDescription">
                            <el-input v-model="form.liveDealDescription" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="4" style="display: flex; align-items: flex-start; padding-top: 5px">
                        <el-button type="primary" size="small" @click="generateSceneHandling">自动生成</el-button>
                    </el-col>
                </el-row>
            </el-card>

            <el-card style="margin-top: 10px">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>事件结束</span>
                        <el-divider direction="horizontal"></el-divider>
                    </div>
                </template>

                <el-row :gutter="gutter">
                    <el-col :span="20">
                        <el-form-item label="事件结束时间" prop="endTime">
                            <el-date-picker
                                clearable
                                v-model="form.endTime"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                placeholder="请选择事件结束时间"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <!-- <el-button type="primary" @click="handleAutoGenerateDesc">自动生成</el-button> -->
                    </el-col>
                </el-row>
                <el-row :gutter="gutter">
                    <el-col :span="20">
                        <el-form-item label="上传附件" prop="files">
                            <FileUpload v-model="form.files" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <!-- <el-button type="primary" @click="handleAutoGenerateDeal">自动生成</el-button> -->
                    </el-col>
                </el-row>
            </el-card>
        </el-form>
        <div class="form-footer">
            <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
        <!-- 设备选择对话框现在由LossComponent内部管理 -->
    </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { addEvent, updateEvent, getEvent } from '@/api/subProject/operation/event'
import { EventForm, EventSealingVO, EventLossVO, EventPullVO } from '@/api/subProject/operation/event/types'
// 车辆相关API现在由SealingComponent内部管理
// 注意：设备、分类、管理单元相关API现在由LossComponent内部管理
import { listCode } from '@/api/subProject/basic/code'
// 添加Category相关API
import { listCategory } from '@/api/common/category'
import { getProjectTunnels } from '@/api/project/project'
import { CategoryVO } from '@/api/common/category/types'
import { useAppStore } from '@/store/modules/app'
import LossComponent from './LossComponent.vue'
import PullComponent from './PullComponent.vue'
import ArriveTimeComponent from './ArriveTimeComponent.vue'
import FileUpload from '@/components/FileUpload/index.vue'
import { listEquipment, searchEquipmentByKeyword, getEquipment } from '@/api/subProject/basic/equipment'
import { EquipmentQuery, EquipmentVO } from '@/api/subProject/basic/equipment/types'
const route = useRoute()
const router = useRouter()
interface Props {
    form: EventForm
    rules: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
    'update:form': [value: EventForm]
    'refresh': []
    'reset': []
}>()

const { proxy } = getCurrentInstance() as ComponentInternalInstance

const appStore = useAppStore()
const {
    fstr_weather,
    road_position,
    involve_car,
    road_num,
    road_leave,
    emergency_level,
    road_available,
    road_unavailable,
    event_bottleneck,
    congestion_length,
    tnl_specialty,
    clppxh
} = toRefs<any>(
    proxy?.useDict(
        'fstr_weather',
        'road_position',
        'involve_car',
        'road_num',
        'road_leave',
        'emergency_level',
        'road_available',
        'road_unavailable',
        'event_bottleneck',
        'congestion_length',
        'tnl_specialty',
        'clppxh'
    )
)
// 事件分类数据 - 从Category表获取
const eventCategories = ref<CategoryVO[]>([])
const eventTypeOptions = ref<CategoryVO[]>([])
const eventSubtypeOptions = ref<CategoryVO[]>([])
const eventFormRef = ref<ElFormInstance>()
const buttonLoading = ref(false)
const pageLoading = ref(false)
const loadingText = ref('正在加载数据...')
const locationLoading = ref(false)
let searchTimeout: NodeJS.Timeout | null = null
const gutter = ref(10)

// 注意：车辆列表现在由SealingComponent内部管理

// 班组和人员的树形数据
const teamMemberTree = ref<any[]>([])

// 隧道列表
const tunnelList = ref<string[]>([])

// 组件引用
const lossComponentRef = ref()
const pullComponentRef = ref()

// 事件类型联动相关 - 已在上方重新声明

// 位置选择相关
const cameraList = ref<any[]>([])
const ringCodeList = ref<any[]>([])
const locationOptions = ref<any[]>([])

// 🔥 新增：摄像头搜索相关
const startCameraOptions = ref<EquipmentVO[]>([])
const endCameraOptions = ref<EquipmentVO[]>([])
const startCameraLoading = ref(false)
const endCameraLoading = ref(false)

// 存储选中的摄像头完整信息
const selectedStartCamera = ref<EquipmentVO | null>(null)
const selectedEndCamera = ref<EquipmentVO | null>(null)

// 注意：项目管理单元列表现在由LossComponent内部管理

// 注意：设备选择相关功能现在由LossComponent内部管理

// 响应式的form
const form = ref<EventForm>({
    id: undefined,
    projectId: undefined,
    happenTime: undefined,
    discoverTime: undefined,
    predicateRecoverTime: undefined,
    tunnelName: undefined,
    emergencyLevel: undefined,
    eventBottleneck: undefined,
    eventClass: undefined,
    eventType: undefined,
    eventSubtype: undefined,
    fstrWeather: undefined,
    involveCar: undefined,
    involveCarBrand: undefined,
    roadLeave: undefined,
    addressType: undefined,
    bgnAddress: undefined,
    endAddress: undefined,
    bgnAddressNumber: undefined,
    endAddressNumber: undefined,
    bgnKilometer: undefined,
    endKilometer: undefined,
    roadNum: undefined,
    roadUnavailable: undefined,
    congestionLength: undefined,
    roadAvailable: undefined,
    roadPosition: undefined,
    sealJson: undefined,
    sealings: [],
    lossJson: undefined,
    losses: [],
    pullJson: undefined,
    pulls: [],
    dies: undefined,
    induries: undefined,
    liveDescription: undefined,
    liveDealDescription: undefined,
    carPlateNumber: undefined,
    carOwnerPhone: undefined,
    lossAmount: undefined,
    policeTime: undefined,
    fireTime: undefined,
    medicalTime: undefined,
    emergencyTime: undefined,
    endTime: undefined
})

const rules = ref({
    happenTime: [{ required: true, message: '发生时间不能为空', trigger: 'blur' }],
    discoverTime: [{ required: true, message: '发现时间不能为空', trigger: 'blur' }],
    emergencyLevel: [{ required: true, message: '事件等级不能为空', trigger: 'change' }],
    eventBottleneck: [{ required: true, message: '事件发生瓶颈类型不能为空', trigger: 'change' }],
    eventClass: [{ required: true, message: '事件分类不能为空', trigger: 'change' }],
    eventType: [{ required: true, message: '事件类型不能为空', trigger: 'change' }],
    // eventSubtype: [{ required: true, message: '事件子类型不能为空', trigger: 'change' }], // 事件子类型可选
    fstrWeather: [{ required: true, message: '天气情况不能为空', trigger: 'change' }],
    involveCar: [{ required: true, message: '涉及行业车辆不能为空', trigger: 'change' }],
    roadLeave: [{ required: true, message: '车辆离开情况不能为空', trigger: 'change' }],
    //bgnAddress: [{ required: true, message: '起点位置不能为空', trigger: 'change' }],
    //endAddress: [{ required: true, message: '终点位置不能为空', trigger: 'blur' }],
    roadNum: [{ required: true, message: '车道总数不能为空', trigger: 'change' }],
    roadUnavailable: [{ required: true, message: '占用车道数不能为空', trigger: 'change' }],
    roadAvailable: [{ required: true, message: '可用车道数不能为空', trigger: 'change' }],
    roadPosition: [{ required: true, message: '事件所在车道不能为空', trigger: 'change' }],
    liveDescription: [{ required: true, message: '现场描述不能为空', trigger: 'blur' }],
    liveDealDescription: [{ required: true, message: '现场处置不能为空', trigger: 'blur' }]
})

// 车辆信息验证状态
const carInfoValidation = ref<{ isValid: boolean; message?: string }>({ isValid: true })

/** 取消按钮 */
const cancel = () => {
    emit('reset')
}

/** 获取项目名称 */
const getProjectName = () => {
    // 从 appStore 获取当前项目名称，这里暂时使用固定值，实际应该从项目信息中获取
    return '苏州隧道项目'
}

/** 获取选中的设施设备名称 */
const getSelectedEquipmentNames = () => {
    try {
        // 通过 ref 获取 LossComponent 中选中的设备
        const lossComponent = lossComponentRef.value
        if (lossComponent && lossComponent.getSelectedEquipments) {
            const equipments = lossComponent.getSelectedEquipments()
            if (equipments && equipments.length > 0) {
                return equipments
                    .map((equipment: any) => equipment.deviceName)
                    .filter(Boolean)
                    .join('，')
            }
        }
        return ''
    } catch (error) {
        console.error('获取选中设备名称失败:', error)
        return ''
    }
}

/** 获取选中的事件类型名称 */
const getSelectedEventTypeName = () => {
    try {
        if (!form.value.eventType) {
            return null
        }

        // 从 eventTypeOptions 中查找对应的事件类型名称
        const selectedEventType = eventTypeOptions.value.find((type) => type.id === form.value.eventType)

        if (selectedEventType) {
            console.log('从 eventTypeOptions 中找到事件类型:', selectedEventType.name)
            return selectedEventType.name
        }

        // 如果在 eventTypeOptions 中没找到，可能是编辑模式下数据还未加载完成
        // 尝试从 eventCategories 中查找
        for (const category of eventCategories.value) {
            if (category.children) {
                const foundType = category.children.find((type) => type.id === form.value.eventType)
                if (foundType) {
                    console.log('从 eventCategories 中找到事件类型:', foundType.name)
                    return foundType.name
                }
            }
        }

        // 降级方案：如果无法获取具体的事件类型名称，返回默认值
        if (form.value.eventType) {
            console.warn('无法获取事件类型名称，使用默认值')
            return '事件'
        }

        return null
    } catch (error) {
        console.error('获取事件类型名称失败:', error)
        return null
    }
}

/** 获取牵引信息第一行数据 */
const getFirstPullInfo = () => {
    try {
        const pullComponent = pullComponentRef.value
        if (pullComponent && pullComponent.getFirstRowData) {
            return pullComponent.getFirstRowData()
        }
        return null
    } catch (error) {
        console.error('获取牵引信息失败:', error)
        return null
    }
}

/** 自动生成现场描述 */
const generateSceneDescription = () => {
    try {
        const projectName = getProjectName()
        const discoveryTime = form.value.happenTime // 使用发生时间作为发现时间
        const equipmentNames = getSelectedEquipmentNames()
        const eventTypeName = getSelectedEventTypeName() // 获取事件类型名称

        // 验证必要字段
        if (!discoveryTime) {
            proxy?.$modal.msgWarning('请先填写发生时间')
            return
        }

        if (!eventTypeName) {
            proxy?.$modal.msgWarning('请先选择事件类型')
            return
        }

        // 格式化时间
        const formattedTime = dayjs(discoveryTime).format('YYYY年MM月DD日HH时mm分')

        // 生成描述 - 调整规则：将"发现事故"改为"发现【事件类型】"
        let description = ''
        if (equipmentNames) {
            // 有设备损坏
            description = `${projectName}${formattedTime}发现${eventTypeName}，${equipmentNames}损坏`
        } else {
            // 无设备损坏
            description = `${projectName}${formattedTime}发现${eventTypeName}，无设施设备损坏`
        }

        form.value.liveDescription = description

        proxy?.$modal.msgSuccess('现场描述已自动生成')
    } catch (error) {
        console.error('生成现场描述失败:', error)

        // 根据错误类型提供更具体的提示
        if (error instanceof Error && error.message.includes('事件类型')) {
            proxy?.$modal.msgError('获取事件类型信息失败，请重新选择事件类型')
        } else {
            proxy?.$modal.msgError('生成失败，请检查相关信息是否完整')
        }
    }
}

/** 自动生成现场处理 */
const generateSceneHandling = () => {
    try {
        const vehicleLeaveStatus = form.value.roadLeave
        const eventEndTime = form.value.endTime

        let handling = ''

        if (vehicleLeaveStatus === '1') {
            // 无需牵引
            handling = '无需牵引'
            if (eventEndTime) {
                const formattedEndTime = dayjs(eventEndTime).format('HH时mm分')
                handling += `，${formattedEndTime}现场打扫完毕，恢复通行`
            }
        } else if (vehicleLeaveStatus === '0') {
            // 牵引车施救
            const pullInfo = getFirstPullInfo()

            console.log('牵引信息:', pullInfo)

            if (!pullInfo) {
                proxy?.$modal.msgWarning('请先填写牵引信息')
                return
            }

            const arrivalTime = pullInfo.dutyTime ? dayjs(pullInfo.dutyTime).format('HH时mm分') : ''
            const departureTime = pullInfo.leaveTime ? dayjs(pullInfo.leaveTime).format('HH时mm分') : ''

            console.log('到位时间:', pullInfo.dutyTime, '格式化后:', arrivalTime)
            console.log('离开时间:', pullInfo.leaveTime, '格式化后:', departureTime)

            if (!arrivalTime || !departureTime) {
                proxy?.$modal.msgWarning('请完善牵引车到位时间和离开时间')
                return
            }

            handling = `${arrivalTime}牵引车到位，${departureTime}施救完成`

            if (eventEndTime) {
                const formattedEndTime = dayjs(eventEndTime).format('HH时mm分')
                handling += `，${formattedEndTime}现场打扫完毕，恢复通行`
            }
        } else {
            proxy?.$modal.msgWarning('请先选择车辆离开情况')
            return
        }

        form.value.liveDealDescription = handling

        proxy?.$modal.msgSuccess('现场处理已自动生成')
    } catch (error) {
        console.error('生成现场处理失败:', error)
        proxy?.$modal.msgError('生成失败，请检查相关信息是否完整')
    }
}

/** 处理封道记录更新 */
const handleSealingsUpdate = (sealings: EventSealingVO[]) => {
    form.value.sealings = sealings
    console.log('Sealings updated:', sealings)
}

/** 处理路产损失更新 */
const handleLossesUpdate = (losses: EventLossVO[]) => {
    form.value.losses = losses
    console.log('Losses updated:', losses)
}

/** 处理牵引记录更新 */
const handlePullsUpdate = (pulls: EventPullVO[]) => {
    form.value.pulls = pulls
    console.log('Pulls updated:', pulls)
}

/** 处理交警到达时间更新 */
const handlePoliceTimeUpdate = (policeTime: string | undefined) => {
    form.value.policeTime = policeTime
    console.log('Police time updated:', policeTime)
}

/** 处理119到达时间更新 */
const handleFireTimeUpdate = (fireTime: string | undefined) => {
    form.value.fireTime = fireTime
    console.log('Fire time updated:', fireTime)
}

/** 处理120到达时间更新 */
const handleMedicalTimeUpdate = (medicalTime: string | undefined) => {
    form.value.medicalTime = medicalTime
    console.log('Medical time updated:', medicalTime)
}

/** 处理应急人员到达时间更新 */
const handleEmergencyTimeUpdate = (emergencyTime: string | undefined) => {
    form.value.emergencyTime = emergencyTime
    console.log('Emergency time updated:', emergencyTime)
}

/** 处理车牌号更新 */
const handleCarPlateNumberUpdate = (carPlateNumber: string | undefined) => {
    form.value.carPlateNumber = carPlateNumber
    console.log('Car plate number updated:', carPlateNumber)
}

/** 处理车主联系方式更新 */
const handleCarOwnerPhoneUpdate = (carOwnerPhone: string | undefined) => {
    form.value.carOwnerPhone = carOwnerPhone
    console.log('Car owner phone updated:', carOwnerPhone)
}
const handleLossAmountUpdate = (lossAmount: string | undefined) => {
    form.value.lossAmount = lossAmount
    console.log('Loss amount updated:', lossAmount)
}

/** 处理事件分类变化 */
const handleEventClassChange = (value: string) => {
    console.log('事件分类变化:', value)

    // 清空事件类型和事件子类型
    form.value.eventType = undefined
    form.value.eventSubtype = undefined
    eventTypeOptions.value = []
    eventSubtypeOptions.value = []

    if (value) {
        // 根据选择的事件分类获取对应的事件类型
        const selectedCategory = eventCategories.value.find((category) => category.id === value)
        console.log('找到的事件分类:', selectedCategory)

        if (selectedCategory && selectedCategory.children) {
            eventTypeOptions.value = selectedCategory.children
            console.log('事件类型选项:', eventTypeOptions.value)
        } else {
            console.log('该分类下没有子分类')
        }
    }
}

/** 处理事件类型变化 */
const handleEventTypeChange = (value: string) => {
    console.log('事件类型变化:', value)

    // 清空事件子类型
    form.value.eventSubtype = undefined
    eventSubtypeOptions.value = []

    if (value) {
        // 根据选择的事件类型获取对应的事件子类型
        const selectedType = eventTypeOptions.value.find((type) => type.id === value)
        console.log('找到的事件类型:', selectedType)

        if (selectedType && selectedType.children) {
            eventSubtypeOptions.value = selectedType.children
            console.log('事件子类型选项:', eventSubtypeOptions.value)
        } else {
            console.log('该类型下没有子类型')
        }
    }
}

/** 初始化事件类型联动选择器 */
const initEventTypeOptions = () => {
    console.log('初始化事件类型联动选择器')
    console.log('当前事件分类数据:', eventCategories.value)
    console.log('表单中的事件分类:', form.value.eventClass)

    // 根据已选择的事件分类初始化事件类型选项
    if (form.value.eventClass) {
        const selectedCategory = eventCategories.value.find((category) => category.id === form.value.eventClass)
        console.log('找到的已选事件分类:', selectedCategory)

        if (selectedCategory && selectedCategory.children) {
            eventTypeOptions.value = selectedCategory.children
            console.log('初始化事件类型选项:', eventTypeOptions.value)

            // 根据已选择的事件类型初始化事件子类型选项
            if (form.value.eventType) {
                const selectedType = eventTypeOptions.value.find((type) => type.id === form.value.eventType)
                console.log('找到的已选事件类型:', selectedType)

                if (selectedType && selectedType.children) {
                    eventSubtypeOptions.value = selectedType.children
                    console.log('初始化事件子类型选项:', eventSubtypeOptions.value)
                }
            }
        }
    }
}

/** 处理详细位置类型变化 */
const handleAddressTypeChange = async (value: string, preserveValues: boolean = false) => {
    try {
        // 在新增模式下显示位置加载状态
        if (!route.query.id && !preserveValues) {
            locationLoading.value = true
        }

        // 如果不是保留值模式，清空起点位置选择
        if (!preserveValues) {
            form.value.bgnAddress = undefined
            form.value.endAddress = undefined
        }
        locationOptions.value = []

        // 动态加载对应的位置数据
        await loadLocationDataByType(value)

        // 在编辑模式下且需要保留值时，初始化选项以便正确显示已选择的值
        if (preserveValues && (form.value.bgnAddress || form.value.endAddress)) {
            locationOptions.value = getAllLocationOptions()
        } else {
            // 其他情况下清空选项，等待用户搜索
            locationOptions.value = []
        }

        console.log('位置选择器选项更新:', locationOptions.value)
        console.log('当前起点位置:', form.value.bgnAddress)
        console.log('当前终点位置:', form.value.endAddress)
    } finally {
        locationLoading.value = false
    }
}

/** 获取起点位置占位符文本 */
const getLocationPlaceholder = () => {
    if (!form.value.addressType) {
        return '请先选择详细位置类型'
    }
    return form.value.addressType === 'camera_number' ? '请输入摄像头号搜索' : '请输入桩号搜索'
}

/** 处理车辆信息验证 */
const handleCarInfoValidation = (isValid: boolean, message?: string) => {
    carInfoValidation.value = { isValid, message }
    console.log('车辆信息验证结果:', { isValid, message })
}

/** 提交按钮 */
const submitForm = () => {
    eventFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            // 检查车辆信息验证
            if (!carInfoValidation.value.isValid) {
                ElMessage.error(carInfoValidation.value.message || '车辆信息验证失败')
                return
            }

            // 🔥 新增：摄像头选择校验（仅在选择摄像头模式时）
            if (form.value.addressType === 'camera_number') {
                if (!validateRouteConsistency()) {
                    return // 校验失败，不继续提交
                }

                // 🔥 保存 roadwayId 字段
                if (selectedStartCamera.value) {
                    form.value.roadwayId = String(selectedStartCamera.value.roadwayId)
                    console.log('保存起点摄像头的 roadwayId:', selectedStartCamera.value.roadwayId)
                }
            }

            buttonLoading.value = true
            try {
                // 从appStore获取项目ID并赋值
                const projectId = appStore.projectContext.selectedProjectId
                if (projectId) {
                    form.value.projectId = projectId as string
                }

                // 序列化封道记录列表为JSON字符串
                form.value.sealJson = JSON.stringify(form.value.sealings)
                form.value.lossJson = JSON.stringify(form.value.losses)
                if (form.value.roadLeave === '1') {
                    form.value.pulls = []
                }
                form.value.pullJson = JSON.stringify(form.value.pulls)
                form.value.roadwayId = selectedStartCamera.value.roadwayId
                if (form.value.id) {
                    await updateEvent(form.value)
                } else {
                    await addEvent(form.value)
                }
                proxy?.$modal.msgSuccess('操作成功')
                emit('refresh')
            } catch (error) {
                console.error('提交失败:', error)
                ElMessage.error('提交失败，请重试')
            } finally {
                buttonLoading.value = false
            }
            router.push('event')
        } else {
            ElMessage.error('请填写必填字段后再提交')
            console.log('表单验证失败')
        }
    })
}
const code_type = ref([
    { value: 'camera_number', label: '按摄像头号' },
    { value: 'ring_number', label: '按桩号' }
])

// 注意：路产损失相关方法现在由LossComponent内部管理

// 注意：所有设备查询相关方法现在由LossComponent内部管理

// 注意：管理单元和设备分类相关方法现在由LossComponent内部管理

// 注意：设备表格相关方法现在由LossComponent内部管理

/** 当封道车辆下拉框展开时检查并获取车辆列表 */
// const handleSealingCarFocus = () => {
//     if (availableCars.value.length === 0) {
//         getAvailableCars();
//     }
// };

/** 根据ID获取事件详情 */
const getEventDetail = async (id: string | number) => {
    try {
        console.log('开始获取事件详情:', id)
        const response = await getEvent(id)
        const eventData = response.data

        if (eventData) {
            // 基本信息直接赋值
            Object.assign(form.value, eventData)

            // 反序列化JSON字段
            if (eventData.sealJson) {
                try {
                    form.value.sealings = JSON.parse(eventData.sealJson)
                } catch (e) {
                    console.warn('封道记录JSON解析失败:', e)
                    form.value.sealings = []
                }
            } else {
                form.value.sealings = []
            }

            if (eventData.lossJson) {
                try {
                    form.value.losses = JSON.parse(eventData.lossJson)
                } catch (e) {
                    console.warn('路产损失JSON解析失败:', e)
                    form.value.losses = []
                }
            } else {
                form.value.losses = []
            }

            if (eventData.pullJson) {
                try {
                    form.value.pulls = JSON.parse(eventData.pullJson)
                } catch (e) {
                    console.warn('拖拽记录JSON解析失败:', e)
                    form.value.pulls = []
                }
            } else {
                form.value.pulls = []
            }

            // 初始化事件类型联动选择器（需要在事件分类数据加载完成后执行）
            // 这里先不调用，在组件挂载完成、数据加载完毕后再调用

            // 初始化位置选择器（编辑模式下保留已有值）
            if (form.value.addressType) {
                handleAddressTypeChange(form.value.addressType, true)
            }

            console.log('事件详情加载成功:', form.value)
            console.log('交通通行情况 - 车道总数:', form.value.roadNum)
            console.log('交通通行情况 - 占用车道数:', form.value.roadUnavailable)
            console.log('交通通行情况 - 可用车道数:', form.value.roadAvailable)
            console.log('交通通行情况 - 事件所在车道:', form.value.roadPosition)
            console.log('到达时间 - 交警时间:', form.value.policeTime)
            console.log('到达时间 - 消防时间:', form.value.fireTime)
            console.log('到达时间 - 医疗时间:', form.value.medicalTime)
            console.log('到达时间 - 应急时间:', form.value.emergencyTime)
            console.log('封道记录:', form.value.sealings)
            console.log('路产损失:', form.value.losses)
        }
    } catch (error) {
        console.error('获取事件详情失败:', error)
        ElMessage.error('获取事件详情失败')
    }
}

/** 获取所有摄像头号的方法 */
const getCameraList = async () => {
    try {
        if (cameraList.value.length > 0) {
            return
        }
        // 从appStore获取项目ID
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未获取到项目ID，无法加载摄像头号列表')
            return
        }

        // 调用listCode API获取摄像头号列表
        // const response = await listCode({
        //     projectId: projectId as string,
        //     codeType: 'camera_number',
        //     pageNum: 1,
        //     pageSize: 10000, // 获取足够多的数据
        // });
        const query: EquipmentQuery = {
            projectId: appStore.projectContext.selectedProjectId,
            categoryIdThird: '111',
            pageNum: 1,
            pageSize: 10000,
            configedInspection: '',
            kind: ''
        }
        const response = await listEquipment(query)

        const codes = response.rows || []
        cameraList.value = codes
        console.log('摄像头号列表:', cameraList.value)
    } catch (error) {
        console.error('获取摄像头号列表失败:', error)
        ElMessage.error('获取摄像头号列表失败')
    }
}

// 🔥 新增：搜索起点摄像头
const searchStartCameras = async (keyword: string) => {
    if (!keyword || keyword.length < 2) {
        startCameraOptions.value = []
        return
    }

    startCameraLoading.value = true
    try {
        const response = await searchEquipmentByKeyword({
            keyword,
            projectId: appStore.projectContext.selectedProjectId,
            categoryIdThird: '111', // 摄像头类别
            pageSize: 30
        })

        startCameraOptions.value = response.data || []
        console.log('起点摄像头搜索结果:', startCameraOptions.value)

        // 🔥 调试：检查第一个设备的字段
        if (startCameraOptions.value.length > 0) {
            const firstCamera = startCameraOptions.value[0]
            console.log('第一个摄像头字段详情:', {
                id: firstCamera.id,
                name: firstCamera.name,
                remark: firstCamera.remark,
                bgnKilometer: firstCamera.bgnKilometer,
                endKilometer: firstCamera.endKilometer,
                displayLabel: `${firstCamera.name || firstCamera.remark}${firstCamera.bgnKilometer ? ' (' + firstCamera.bgnKilometer + ')' : ''}`
            })
        }
    } catch (error) {
        console.error('搜索起点摄像头失败:', error)
        ElMessage.error('搜索摄像头失败')
    } finally {
        startCameraLoading.value = false
    }
}

// 🔥 新增：搜索终点摄像头
const searchEndCameras = async (keyword: string) => {
    if (!keyword || keyword.length < 2) {
        endCameraOptions.value = []
        return
    }

    endCameraLoading.value = true
    try {
        const response = await searchEquipmentByKeyword({
            keyword,
            projectId: appStore.projectContext.selectedProjectId,
            categoryIdThird: '111', // 摄像头类别
            pageSize: 30
        })

        endCameraOptions.value = response.data || []
        console.log('终点摄像头搜索结果:', endCameraOptions.value)
    } catch (error) {
        console.error('搜索终点摄像头失败:', error)
        ElMessage.error('搜索摄像头失败')
    } finally {
        endCameraLoading.value = false
    }
}

// 🔥 新增：根据设备ID获取设备信息
const getEquipmentById = async (equipmentId: string): Promise<EquipmentVO | null> => {
    try {
        console.log('根据ID获取设备信息:', equipmentId)
        const response = await getEquipment(equipmentId)

        if (response.data) {
            console.log('成功获取设备信息:', {
                id: response.data.id,
                name: response.data.name,
                remark: response.data.remark,
                bgnKilometer: response.data.bgnKilometer,
                endKilometer: response.data.endKilometer
            })
            return response.data
        } else {
            console.warn('设备信息为空:', equipmentId)
            return null
        }
    } catch (error) {
        console.error('获取设备信息失败:', error)
        return null
    }
}

// 🔥 新增：编辑模式下预加载摄像头信息
const preloadCameraInfoForEdit = async () => {
    if (form.value.addressType !== 'camera_number') return

    console.log('编辑模式：预加载摄像头信息')

    // 预加载起点摄像头信息
    if (form.value.bgnAddress) {
        console.log('预加载起点摄像头ID:', form.value.bgnAddress)
        const startCamera = await getEquipmentById(form.value.bgnAddress)
        if (startCamera) {
            startCameraOptions.value = [startCamera]
            selectedStartCamera.value = startCamera
            console.log('预加载起点摄像头成功:', startCamera.name || startCamera.remark)

            // 🔥 新增：提取里程数字并更新表单字段
            const extractedNumber = extractKilometerNumber(startCamera.bgnKilometer)
            if (extractedNumber !== undefined) {
                form.value.bgnAddressNumber = extractedNumber
            }

            // 🔥 新增：预加载时也赋值里程字段
            form.value.bgnKilometer = startCamera.bgnKilometer

            console.log('预加载起点里程信息:', {
                原始里程: startCamera.bgnKilometer,
                提取数字: extractedNumber,
                // 🔥 新增日志
                赋值里程: form.value.bgnKilometer
            })

            if (extractedNumber === undefined) {
                console.warn('无法从起点里程中提取数字:', startCamera.bgnKilometer)
            }
        } else {
            console.warn('未找到起点摄像头信息，将显示设备ID:', form.value.bgnAddress)
            // 🔥 备用方案：创建一个虚拟的设备对象，显示ID
            const fallbackCamera: EquipmentVO = {
                id: form.value.bgnAddress,
                name: `设备ID: ${form.value.bgnAddress}`,
                code: '',
                remark: '',
                bgnKilometer: '',
                endKilometer: '',
                roadwayId: '',
                lineId: '',
                categoryIdThird: '',
                projectId: '',
                unitId: '',
                roomId: '',
                seq: 0,
                codeType: '',
                timeArrangeBgn: '',
                timeArrangeEnd: '',
                arrangeBgn: '',
                arrangeEnd: '',
                equipmentOwner: '',
                modelId: '',
                owner: '',
                installDate: '',
                brand: '',
                categoryIdFirst: '',
                categoryIdSecond: '',
                specification: '',
                images: '',
                files: '',
                configedInspection: ''
            }
            startCameraOptions.value = [fallbackCamera]
            selectedStartCamera.value = fallbackCamera
        }
    }

    // 预加载终点摄像头信息
    if (form.value.endAddress) {
        console.log('预加载终点摄像头ID:', form.value.endAddress)
        const endCamera = await getEquipmentById(form.value.endAddress)
        if (endCamera) {
            endCameraOptions.value = [endCamera]
            selectedEndCamera.value = endCamera
            console.log('预加载终点摄像头成功:', endCamera.name || endCamera.remark)

            // 🔥 新增：提取里程数字并更新表单字段
            const extractedNumber = extractKilometerNumber(endCamera.endKilometer)
            if (extractedNumber !== undefined) {
                form.value.endAddressNumber = extractedNumber
            }

            // 🔥 新增：预加载时也赋值里程字段（注意：使用 bgnKilometer）
            form.value.endKilometer = endCamera.bgnKilometer

            console.log('预加载终点里程信息:', {
                原始里程: endCamera.bgnKilometer, // 注意：显示 bgnKilometer
                提取数字: extractedNumber,
                // 🔥 新增日志
                赋值里程: form.value.endKilometer
            })

            if (extractedNumber === undefined) {
                console.warn('无法从终点里程中提取数字:', endCamera.endKilometer)
            }
        } else {
            console.warn('未找到终点摄像头信息，将显示设备ID:', form.value.endAddress)
            // 🔥 备用方案：创建一个虚拟的设备对象，显示ID
            const fallbackCamera: EquipmentVO = {
                id: form.value.endAddress,
                name: `设备ID: ${form.value.endAddress}`,
                code: '',
                remark: '',
                bgnKilometer: '',
                endKilometer: '',
                roadwayId: '',
                lineId: '',
                categoryIdThird: '',
                projectId: '',
                unitId: '',
                roomId: '',
                seq: 0,
                codeType: '',
                timeArrangeBgn: '',
                timeArrangeEnd: '',
                arrangeBgn: '',
                arrangeEnd: '',
                equipmentOwner: '',
                modelId: '',
                owner: '',
                installDate: '',
                brand: '',
                categoryIdFirst: '',
                categoryIdSecond: '',
                specification: '',
                images: '',
                files: '',
                configedInspection: ''
            }
            endCameraOptions.value = [fallbackCamera]
            selectedEndCamera.value = fallbackCamera
        }
    }
}

// 🔥 新增：处理起点摄像头选择
const handleStartCameraChange = (cameraId: string) => {
    if (!cameraId) {
        selectedStartCamera.value = null
        form.value.bgnAddressNumber = undefined
        // 🔥 新增：清除里程字段
        form.value.bgnKilometer = undefined
        return
    }

    const camera = startCameraOptions.value.find((c) => c.id === cameraId)
    if (camera) {
        selectedStartCamera.value = camera

        // 提取里程数字
        const extractedNumber = extractKilometerNumber(camera.bgnKilometer)
        form.value.bgnAddressNumber = extractedNumber

        // 🔥 新增：赋值里程字段
        form.value.bgnKilometer = camera.bgnKilometer

        console.log('用户选择起点摄像头:', {
            id: camera.id,
            name: camera.name || camera.remark,
            displayName: `${camera.name || camera.remark}${camera.bgnKilometer ? ' (' + camera.bgnKilometer + ')' : ''}`,
            roadwayId: camera.roadwayId,
            lineId: camera.lineId,
            原始里程: camera.bgnKilometer,
            提取数字: extractedNumber,
            // 🔥 新增日志
            赋值里程: form.value.bgnKilometer
        })

        // 🔥 移除选择时的校验，改为提交时校验
        // validateRouteConsistency()
    }
}

// 🔥 新增：处理终点摄像头选择
const handleEndCameraChange = (cameraId: string) => {
    if (!cameraId) {
        selectedEndCamera.value = null
        form.value.endAddressNumber = undefined
        // 🔥 新增：清除里程字段
        form.value.endKilometer = undefined
        return
    }

    const camera = endCameraOptions.value.find((c) => c.id === cameraId)
    if (camera) {
        selectedEndCamera.value = camera

        // 提取里程数字
        const extractedNumber = extractKilometerNumber(camera.endKilometer)
        form.value.endAddressNumber = extractedNumber

        // 🔥 新增：赋值里程字段（注意：使用 bgnKilometer）
        form.value.endKilometer = camera.bgnKilometer

        console.log('用户选择终点摄像头:', {
            id: camera.id,
            name: camera.name || camera.remark,
            displayName: `${camera.name || camera.remark}${camera.endKilometer ? ' (' + camera.endKilometer + ')' : ''}`,
            roadwayId: camera.roadwayId,
            lineId: camera.lineId,
            原始里程: camera.bgnKilometer, // 注意：显示 bgnKilometer
            提取数字: extractedNumber,
            // 🔥 新增日志
            赋值里程: form.value.endKilometer
        })

        // 🔥 移除选择时的校验，改为提交时校验
        // validateRouteConsistency()
    }
}

// 🔥 新增：提取里程数字部分
const extractKilometerNumber = (kilometer: string): number | undefined => {
    if (!kilometer) return undefined

    console.log('提取里程数字，输入:', kilometer)

    // 匹配格式：NK5+857.5、NK11+860，只提取+号后面的数字
    const match = kilometer.match(/\+(\d+(?:\.\d+)?)/)
    if (match) {
        const result = parseFloat(match[1])
        console.log('里程格式匹配成功，提取+号后数字:', result)
        return result
    }

    // 如果格式不匹配，尝试提取最后一个数字
    const numberMatch = kilometer.match(/(\d+(?:\.\d+)?)/g)
    if (numberMatch && numberMatch.length > 0) {
        const result = parseFloat(numberMatch[numberMatch.length - 1])
        console.log('提取最后一个数字:', result)
        return result
    }

    console.log('无法提取里程数字')
    return undefined
}

// 🔥 新增：校验线路车道一致性（提交时调用）
const validateRouteConsistency = () => {
    if (!selectedStartCamera.value || !selectedEndCamera.value) {
        console.log('跳过校验：起点或终点摄像头未选择')
        return true
    }

    const start = selectedStartCamera.value
    const end = selectedEndCamera.value

    console.log('提交时校验线路车道一致性:', {
        start: {
            id: start.id,
            name: start.name || start.remark,
            roadwayId: start.roadwayId,
            lineId: start.lineId
        },
        end: {
            id: end.id,
            name: end.name || end.remark,
            roadwayId: end.roadwayId,
            lineId: end.lineId
        }
    })
    debugger
    // 检查线路ID
    if (start.lineId !== end.lineId) {
        ElMessage.error('提交失败：起点和终点摄像头的线路必须相同')
        console.error('线路不一致:', { startLineId: start.lineId, endLineId: end.lineId })
        return false
    }

    // 检查车道ID
    if (start.roadwayId !== end.roadwayId) {
        ElMessage.error('提交失败：起点和终点摄像头的车道必须相同')
        console.error('车道不一致:', { startRoadwayId: start.roadwayId, endRoadwayId: end.roadwayId })
        return false
    }

    console.log('校验通过：起点和终点摄像头的线路和车道一致')
    return true
}

/** 获取事件分类数据 */
const getEventCategories = async () => {
    try {
        // 从appStore获取项目ID
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未获取到项目ID，无法加载事件分类')
            return
        }

        // 调用listCategory API获取事件分类列表
        const response = await listCategory({
            projectId: projectId as string,
            kind: 'event'
        })

        const categories = response.data || []
        // 使用handleTree方法构建树形结构
        const treeData = proxy?.handleTree<CategoryVO>(categories, 'id', 'parentId')
        eventCategories.value = treeData || []
        console.log('事件分类树形列表:', eventCategories.value)
    } catch (error) {
        console.error('获取事件分类失败:', error)
        ElMessage.error('获取事件分类失败')
    }
}

/** 获取当前项目下所有桩号的方法 */
const getRingCodeList = async () => {
    try {
        // 从appStore获取项目ID
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未获取到项目ID，无法加载桩号列表')
            return
        }

        // 调用listCode API获取桩号列表
        const response = await listCode({
            projectId: projectId as string,
            codeType: 'ring_number',
            pageNum: 1,
            pageSize: 10000 // 获取足够多的数据
        })

        const codes = response.rows || []
        ringCodeList.value = codes
        console.log('桩号列表:', ringCodeList.value)
    } catch (error) {
        console.error('获取桩号列表失败:', error)
        ElMessage.error('获取桩号列表失败')
    }
}

/** 获取当前项目下所有隧道的方法 */
const getTunnelList = async () => {
    try {
        // 从appStore获取项目ID
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未获取到项目ID，无法加载隧道列表')
            return
        }
        // 调用getProjectTunnels API获取隧道列表
        const response = await getProjectTunnels(projectId as string)
        tunnelList.value = response.data || []
        tunnelList.value.push('其他')
        console.log('隧道列表加载成功:', tunnelList.value.length)
    } catch (error) {
        console.error('获取隧道列表失败:', error)
        ElMessage.error('获取隧道列表失败')
    }
}

/** 根据位置类型动态加载对应的数据 */
const loadLocationDataByType = async (addressType: string) => {
    try {
        console.log('根据位置类型加载数据:', addressType)

        if (addressType === 'camera_number') {
            // 如果摄像头号列表为空，则加载
            if (cameraList.value.length === 0) {
                console.log('加载摄像头号列表')
                await getCameraList()
            }
        } else if (addressType === 'ring_number') {
            // 如果桩号列表为空，则加载
            if (ringCodeList.value.length === 0) {
                console.log('加载桩号列表')
                await getRingCodeList()
            }
        }
    } catch (error) {
        console.error('根据位置类型加载数据失败:', error)
        ElMessage.error('加载位置数据失败')
    }
}

/** 远程搜索位置选项 */
const searchLocationOptions = (query: string) => {
    if (!query || !form.value.addressType) {
        locationOptions.value = []
        return
    }

    // 清除之前的搜索定时器
    if (searchTimeout) {
        clearTimeout(searchTimeout)
    }

    // 设置防抖，300ms后执行搜索
    searchTimeout = setTimeout(() => {
        locationLoading.value = true

        try {
            const allOptions = getAllLocationOptions()

            // 根据查询关键字过滤选项
            locationOptions.value = allOptions.filter(
                (option) => option.label.toLowerCase().includes(query.toLowerCase()) || option.value.toLowerCase().includes(query.toLowerCase())
            )

            console.log(`搜索关键字: "${query}", 搜索结果:`, locationOptions.value)
        } catch (error) {
            console.error('搜索位置选项失败:', error)
            locationOptions.value = []
        } finally {
            locationLoading.value = false
        }
    }, 300)
}

/** 获取所有位置选项 */
const getAllLocationOptions = () => {
    if (form.value.addressType === 'camera_number') {
        return cameraList.value.map((camera) => ({
            value: camera.code,
            label: `${camera.code}${camera.mileageNumber ? ` (${camera.mileageNumber})` : ''}`
        }))
    } else if (form.value.addressType === 'ring_number') {
        return ringCodeList.value.map((code) => ({
            value: code.code,
            label: `${code.code}${code.mileageNumber ? ` (${code.mileageNumber})` : ''}`
        }))
    }
    return []
}

/** 处理位置选择器获得焦点 */
const handleLocationFocus = () => {
    if (!form.value.addressType) {
        return
    }

    // 如果选项为空，显示所有可用选项
    if (locationOptions.value.length === 0) {
        locationOptions.value = getAllLocationOptions()
    }
}

/** 初始化数据 - 检查URL参数并获取事件详情 */
const initData = async () => {
    const eventId = route.query.id

    if (eventId) {
        console.log('编辑模式 - 加载事件详情')
        await getEventDetail(eventId as string)
    } else {
        console.log('新增模式 - 初始化空表单')
        // 新增模式 - 初始化空数据
        form.value = {
            id: undefined,
            projectId: undefined,
            happenTime: undefined,
            discoverTime: undefined,
            predicateRecoverTime: undefined,
            emergencyLevel: undefined,
            eventBottleneck: undefined,
            eventClass: undefined,
            eventType: undefined,
            eventSubtype: undefined,
            fstrWeather: undefined,
            involveCar: undefined,
            involveCarBrand: undefined,
            roadLeave: undefined,
            addressType: undefined,
            bgnAddress: undefined,
            endAddress: undefined,
            bgnAddressNumber: undefined,
            endAddressNumber: undefined,
            // 🔥 新增：里程字段初始化
            bgnKilometer: undefined,
            endKilometer: undefined,
            roadwayId: undefined,
            roadNum: undefined,
            roadUnavailable: undefined,
            congestionLength: undefined,
            roadAvailable: undefined,
            roadPosition: undefined,
            sealJson: undefined,
            sealings: [],
            lossJson: undefined,
            losses: [],
            pullJson: undefined,
            pulls: [],
            dies: undefined,
            induries: undefined,
            liveDescription: undefined,
            liveDealDescription: undefined,
            carPlateNumber: undefined,
            carOwnerPhone: undefined,
            lossAmount: undefined,
            policeTime: undefined,
            fireTime: undefined,
            medicalTime: undefined,
            emergencyTime: undefined,
            endTime: undefined
        }
    }
}

// 🔥 新增：表单重置时清理摄像头数据
const resetCameraData = () => {
    startCameraOptions.value = []
    endCameraOptions.value = []
    selectedStartCamera.value = null
    selectedEndCamera.value = null
    form.value.bgnAddress = ''
    form.value.endAddress = ''
    form.value.bgnAddressNumber = undefined
    form.value.endAddressNumber = undefined
    // 🔥 新增：重置里程字段
    form.value.bgnKilometer = undefined
    form.value.endKilometer = undefined
}

// 🔥 新增：监听详细位置变化
watch(
    () => form.value.addressType,
    async (newValue) => {
        if (newValue !== 'camera_number') {
            resetCameraData()
        } else {
            // 如果切换到摄像头模式且已有数据，预加载信息
            await preloadCameraInfoForEdit()
        }
    }
)

// 组件挂载时初始化数据
onMounted(async () => {
    try {
        // 开始加载
        pageLoading.value = true

        const isEditMode = !!route.query.id

        if (isEditMode) {
            // 编辑模式：先加载基础数据
            loadingText.value = '正在加载基础数据...'
            await Promise.all([getCameraList(), getRingCodeList(), getEventCategories(), getTunnelList()])

            // 加载事件详情
            loadingText.value = '正在加载事件详情...'
            await initData()

            // 根据详细位置类型加载对应的位置列表
            if (form.value.addressType) {
                loadingText.value = '正在初始化位置选择器...'
                await loadLocationDataByType(form.value.addressType)
                handleAddressTypeChange(form.value.addressType, true)

                // 🔥 如果是摄像头模式，预加载摄像头信息
                if (form.value.addressType === 'camera_number') {
                    loadingText.value = '正在加载摄像头信息...'
                    await preloadCameraInfoForEdit()
                }
            }
        } else {
            // 新增模式：只加载摄像头号、事件分类数据和隧道列表
            loadingText.value = '正在加载基础数据...'
            await Promise.all([getCameraList(), getEventCategories(), getTunnelList()])

            // 初始化空表单
            await initData()
        }

        loadingText.value = '正在初始化联动选择器...'
        // 最后初始化事件类型联动选择器（在事件分类数据已加载的情况下）
        initEventTypeOptions()

        console.log('页面初始化完成')
    } catch (error) {
        console.error('页面初始化失败:', error)
        ElMessage.error('页面初始化失败，请刷新重试')
    } finally {
        // 结束加载
        pageLoading.value = false
    }
})

// 组件卸载时清理定时器
onUnmounted(() => {
    if (searchTimeout) {
        clearTimeout(searchTimeout)
    }
})
</script>

<style lang="scss" scoped>
.event-form {
    padding: 20px;
    min-height: 500px; // 确保有足够的高度显示加载动画

    .form-footer {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e4e7ed;
    }
}

// 加载动画样式优化
:deep(.el-loading-mask) {
    background-color: rgba(0, 0, 0, 0.1); // 改为淡灰色或透明
}

:deep(.el-loading-text) {
    color: #409eff;
    font-size: 14px;
}

:deep(.el-loading-spinner) {
    .path {
        stroke: #409eff;
    }
}
</style>
