<!-- 封道线路选择器组件 - 只读版本 -->
<template>
    <div class="sealing-road-readonly-selector">
        <!-- 封道类型显示 -->

        <el-form-item v-if="props.showSealingTypeRadio" label="封道类型" label-width="160px" class="sealing-type-label">
            <el-text type="primary" size="large">
                {{ getSealingTypeLabel(props.sealingType) }}
            </el-text>
        </el-form-item>

        <!-- 封闭线路和位置列表 - 根据封道类型动态显示 -->
        <template v-if="props.sealingType === 'full' || props.sealingType === 'part'">
            <div v-for="(item, index) in props.taskResourceItems" :key="index" style="margin-bottom: 16px">
                <el-form-item label-width="0" class="align-label-right">
                    <div style="display: flex; align-items: center; gap: 8px; width: 100%">
                        <span v-if="index === 0" style="width: 80px; text-align: right; flex-shrink: 0; margin-left: 68px">线路</span>
                        <span v-else style="width: 80px; flex-shrink: 0; margin-left: 68px"></span>

                        <!-- 线路/车道名称显示 -->
                        <el-text
                            v-if="item.roadId"
                            type="primary"
                            size="large"
                            style="width: 200px; text-align: left; min-height: 32px; display: flex; align-items: center"
                        >
                            {{ getRoadName(item.roadId) }}
                        </el-text>
                        <el-text
                            v-else
                            type="info"
                            size="large"
                            style="width: 200px; text-align: left; min-height: 32px; display: flex; align-items: center"
                        >
                            未选择
                        </el-text>

                        <!-- 部分封闭时显示桩号 -->
                        <template v-if="props.sealingType === 'part'">
                            <span style="width: 80px; text-align: right; flex-shrink: 0">位置桩号</span>

                            <!-- 起点桩号 -->
                            <el-text
                                v-if="item.startStake"
                                type="success"
                                size="large"
                                style="
                                    width: 150px;
                                    text-align: center;
                                    min-height: 32px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                "
                            >
                                {{ item.startStake }}
                            </el-text>
                            <el-text
                                v-else
                                type="info"
                                size="large"
                                style="
                                    width: 150px;
                                    text-align: center;
                                    min-height: 32px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                "
                            >
                                未设置
                            </el-text>

                            <span style="color: #909399; font-size: 14px; white-space: nowrap">至</span>

                            <!-- 终点桩号 -->
                            <el-text
                                v-if="item.endStake"
                                type="success"
                                size="large"
                                style="
                                    width: 150px;
                                    text-align: center;
                                    min-height: 32px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                "
                            >
                                {{ item.endStake }}
                            </el-text>
                            <el-text
                                v-else
                                type="info"
                                size="large"
                                style="
                                    width: 150px;
                                    text-align: center;
                                    min-height: 32px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                "
                            >
                                未设置
                            </el-text>
                        </template>

                        <!-- 关联作业显示 -->
                        <div v-if="props.showJobButton" style="min-width: 240px; display: flex; align-items: center; gap: 6px; flex-wrap: wrap">
                            <template v-if="jobsByIndex[index] && jobsByIndex[index].length > 0">
                                <el-tag v-for="job in jobsByIndex[index]" :key="job.id" size="small" type="warning" effect="light">
                                    {{ job.name }}
                                    <!-- <a :href="buildJobLink(job.id)" target="_blank" style="color: inherit; text-decoration: none;">
                                        {{ job.name }}
                                    </a> -->
                                </el-tag>
                            </template>
                            <template v-else>
                                <el-text type="info" size="small">未关联作业</el-text>
                            </template>
                        </div>
                    </div>
                </el-form-item>
            </div>
        </template>

        <!-- 无数据时显示提示 -->
        <div v-if="!props.taskResourceItems || props.taskResourceItems.length === 0" style="text-align: center; padding: 20px; color: #909399">
            <el-text type="info">暂无封道数据</el-text>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { LineTreeVO } from '@/api/project/project/types'
import { getProjectLineTree } from '@/api/project/project'
import { useAppStore } from '@/store/modules/app'
import { TaskResourceItemVO } from '@/api/plan/taskResourceItem/types'
import { listByTaskIds } from '@/api/plan/task/index'
import type { TaskVO } from '@/api/plan/task/types'

// 定义组件的props - 与原组件保持一致
interface Props {
    sealingType?: string
    taskResourceItems: TaskResourceItemVO[]
    showJobButton?: boolean
    showSealingTypeRadio?: boolean
    jobDetailUrlPrefix?: string // 作业详情路由前缀，例如 '/subProject/circle/plan/task/detail?id='
}

const props = withDefaults(defineProps<Props>(), {
    sealingType: 'part',
    taskResourceItems: () => [],
    showJobButton: true,
    showSealingTypeRadio: true,
    jobDetailUrlPrefix: '/subProject/circle/plan/task/detail?id='
})

const appStore = useAppStore()

// 响应式数据
const lineTreeVos = ref<LineTreeVO[]>([])
const jobsByIndex = ref<Record<number, TaskVO[]>>({})

// 获取封道类型标签
const getSealingTypeLabel = (type: string) => {
    const typeMap: Record<string, string> = {
        'full': '全封闭',
        'part': '部分封闭'
    }
    return typeMap[type] || type
}

// 根据roadId获取线路/车道名称
const getRoadName = (roadId: string) => {
    if (!roadId || !lineTreeVos.value) return ''

    // 递归查找节点名称
    const findNodeName = (nodes: LineTreeVO[], id: string): string => {
        for (const node of nodes) {
            if (node.id === id) {
                return node.name || ''
            }
            if (node.children && node.children.length > 0) {
                const found = findNodeName(node.children, id)
                if (found) return found
                // 检查父节点名称
                if (node.id === id) {
                    return node.name || ''
                }
            }
        }
        return ''
    }

    return findNodeName(lineTreeVos.value, roadId)
}

// 获取作业名称列表
const getJobNames = (jobIdList: string) => {
    if (!jobIdList) return '未关联作业'

    const jobIds = jobIdList.split(',').filter((id) => id.trim() !== '')
    if (jobIds.length === 0) return '未关联作业'

    const names = jobIds.map((id) => jobNamesMap.value.get(id) || id)
    return names.join('、')
}

// 构建作业详情链接
const buildJobLink = (taskId: string | number) => {
    return `${props.jobDetailUrlPrefix}${taskId}`
}

// 拉取每一行的作业明细
const fetchJobsForItem = async (item: TaskResourceItemVO, index: number) => {
    try {
        const raw = item?.jobIdList || ''
        const ids = raw
            .split(',')
            .map((s) => s.trim())
            .filter((s) => s)
        if (ids.length === 0) {
            jobsByIndex.value[index] = []
            return
        }
        const res = await listByTaskIds(ids)
        jobsByIndex.value[index] = res.data || []
    } catch (e) {
        jobsByIndex.value[index] = []
        // 静默失败，避免打扰展示
    }
}

// 刷新所有行的作业明细
const refreshAllJobs = async () => {
    const items = props.taskResourceItems || []
    const tasks: Promise<void>[] = []
    items.forEach((it, idx) => {
        tasks.push(fetchJobsForItem(it, idx))
    })
    await Promise.allSettled(tasks)
}

// 刷新线路树数据
const refreshLineTree = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        const res = await getProjectLineTree(projectId)
        lineTreeVos.value = []
        if (res.data && res.data.children && res.data.children.length > 0) {
            lineTreeVos.value = res.data.children
        } else {
            lineTreeVos.value = [res.data]
        }
    } catch (error) {
        lineTreeVos.value = []
    }
}

// 初始化
const init = async () => {
    await Promise.all([refreshLineTree(), refreshAllJobs()])
}

// 监听数据变化，自动刷新
watch(
    () => props.taskResourceItems,
    () => {
        refreshAllJobs()
    },
    { deep: true }
)

// 组件挂载时初始化
onMounted(() => {
    init()
})
</script>

<style lang="scss" scoped>
.sealing-road-readonly-selector {
    /* 强制所有表单label宽度一致并右对齐 */
    :deep(.el-form-item__label) {
        width: 160px !important;
        min-width: 160px !important;
        text-align: right;
        font-size: 14px;
        font-weight: 500;
        color: #606266;
        line-height: 32px;
    }

    /* 封道类型四个字设置为白色 */
    .sealing-type-label :deep(.el-form-item__label) {
        color: #fff;
    }

    .align-label-right :deep(.el-form-item__label) {
        justify-content: flex-end;
        text-align: right;
        display: flex;
        align-items: center;
    }

    /* 只读文本样式 */
    .el-text {
        border: none;
        border-radius: 4px;
        padding: 0 8px;
        background-color: transparent;
        font-size: 14px;
        font-weight: 400;
        color: #ffffff;
        line-height: 32px;
        min-height: 32px;
        display: flex;
        align-items: center;
    }

    /* 标题样式 */
    .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 16px 0 12px 0;
        padding-left: 8px;
        border-left: 3px solid #409eff;
    }

    /* 无数据提示样式 */
    .no-data-tip {
        font-size: 14px;
        color: #909399;
        text-align: center;
        padding: 20px;
        background-color: #fafafa;
        border: 1px dashed #d9d9d9;
        border-radius: 4px;
    }
}
</style>
