<!-- 班组统计图 -->
<template>
    <div class="chart-section">
        <div class="header-row">
            <div class="title">排班统计图</div>
            <div class="current-date">排班日期：{{ currentDate }}</div>
        </div>
        <div class="header-underline"></div>
        <div class="plan-chart-row">
            <div v-if="teamData.length > 0" ref="teamChartRef" class="chart"></div>
            <div v-else class="no-data">
                <div class="no-data-icon">📊</div>
                <div class="no-data-text">当前日期暂无排班数据</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import * as echarts from 'echarts'
import { getTeamScheduleStatistics, type TeamScheduleStatisticsVO } from '@/api/statistics/schedule'
import { ElMessage } from 'element-plus'
import { useAppStore } from '@/store/modules/app'

// 图表DOM引用
const teamChartRef = ref(null)
const appStore = useAppStore()

// 班组统计数据
const teamData = ref<{ name: string; value: number; color: string }[]>([])

// 当前日期
const currentDate = computed(() => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
})

// 默认颜色配置
const defaultColors = ['#2DE1FC', '#FFB347', '#FF8C42', '#FF6B6B', '#A855F7', '#00D4FF']

// 获取班组排班统计数据
const fetchTeamStatistics = async () => {
    try {
        const today = new Date().toISOString().split('T')[0] // 格式：yyyy-MM-dd
        const { data } = await getTeamScheduleStatistics(today, appStore.projectContext.selectedProjectId)

        if (data && data.length > 0) {
            // 转换数据格式
            teamData.value = data.map((item, index) => ({
                name: item.teamName,
                value: item.memberCount,
                color: defaultColors[index % defaultColors.length]
            }))
            // 使用 nextTick 确保 DOM 更新后再初始化图表
            await nextTick()
            console.log('准备初始化图表，数据长度:', teamData.value.length)
            initTeamChart()
        } else {
            // API 返回空数据时，清空数据不显示图表
            teamData.value = []
            console.log('当前日期没有排班数据')
        }
    } catch (error) {
        console.error('获取班组排班统计数据失败:', error)
        ElMessage.warning('获取班组排班统计数据失败，使用默认数据')
        teamData.value = getDefaultTeamData()
        // 使用 nextTick 确保 DOM 更新后再初始化图表
        await nextTick()
        initTeamChart()
    }
}

// 默认数据
const getDefaultTeamData = () => [
    { name: '机电A班', value: 145, color: '#2DE1FC' },
    { name: '机电B班', value: 166, color: '#FFB347' },
    { name: '土建班组', value: 206, color: '#FF8C42' },
    { name: '监控中心', value: 252, color: '#FF6B6B' },
    { name: '清扫班组', value: 252, color: '#A855F7' },
    { name: '春申湖管廊', value: 45, color: '#00D4FF' }
]

// 去除左侧额外 padding，按容器宽度自然定位
const LEFT_OVERFLOW_PAD = 0
const getLegendGap = (): number => {
    const el = teamChartRef.value as unknown as HTMLElement | null
    if (!el) return 8
    const containerHeight = el.clientHeight || 0
    const itemCount = teamData.value.length || 0
    if (itemCount <= 1) return 8
    const legendItemLineHeight = 16
    const verticalPadding = 10
    const available = containerHeight - verticalPadding * 2 - legendItemLineHeight * itemCount
    return Math.max(8, Math.floor(available / (itemCount - 1)))
}

// 初始化饼图
const initTeamChart = () => {
    console.log('开始初始化图表...')
    console.log('teamChartRef.value:', teamChartRef.value)
    console.log('teamData.value:', teamData.value)

    if (!teamChartRef.value) {
        console.warn('图表容器未找到，无法初始化图表')
        return
    }
    if (teamData.value.length === 0) {
        console.warn('没有数据，无法初始化图表')
        return
    }

    console.log('正在初始化 ECharts 实例...')
    const chartInstance = echarts.init(teamChartRef.value)
    const chartEl = teamChartRef.value as unknown as HTMLElement
    const containerWidth = chartEl.parentElement?.clientWidth || chartEl.clientWidth || 0
    const centerX = containerWidth * 0.3 + LEFT_OVERFLOW_PAD

    const option = {
        tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderWidth: 1,
            formatter: (p: any) => {
                const color = typeof p.color === 'string' ? p.color : '#fff'
                return `<span style="color:${color}">${p.name}：${p.value}</span>`
            }
        },
        legend: {
            orient: 'vertical',
            right: '3%',
            top: 0,
            bottom: 0,
            itemWidth: 8,
            itemHeight: 5,
            itemGap: Math.max(12, getLegendGap()),
            data: teamData.value.map((it) => ({ name: it.name, textStyle: { color: it.color } })),
            textStyle: {
                color: '#7ECFFF',
                fontSize: 16,
                rich: {
                    name: {
                        fontSize: 16
                        // 颜色由 legend.data[].textStyle 控制
                    },
                    value: {
                        fontSize: 16,
                        fontWeight: 'bold',
                        // 颜色由 legend.data[].textStyle 控制
                        padding: [0, 0, 0, 8]
                    }
                }
            },
            formatter: (name: string) => {
                const item = teamData.value.find((d) => d.name === name)
                return `{name|${name}:}{value|${item?.value || 0}}`
            }
        },
        series: [
            {
                name: '班组统计',
                type: 'pie',
                radius: ['42%', '58%'],
                center: ['30%', '52%'],
                avoidLabelOverlap: false,
                label: {
                    show: true,
                    position: 'outside',
                    formatter: '{a|{b}：{d}%}\n{hr|}',
                    fontSize: 13,
                    lineHeight: 18,
                    rich: {
                        hr: {
                            backgroundColor: 't',
                            borderRadius: 3,
                            width: 3,
                            height: 3,
                            padding: [3, 3, 0, -12]
                        },
                        a: { padding: [-24, 12, -16, 12], fontSize: 13 }
                    }
                },
                labelLayout: (params: any) => {
                    const minX = 6
                    if (params.labelRect && params.labelRect.x < minX) {
                        return { x: minX, align: 'left' }
                    }
                    return {}
                },
                labelLine: {
                    show: true,
                    length: 12,
                    length2: 40,
                    lineStyle: { width: 1, type: 'solid' }
                },
                data: teamData.value.map((item) => ({
                    value: item.value,
                    name: item.name,
                    itemStyle: {
                        color: item.color,
                        shadowColor: `${item.color}40`,
                        shadowBlur: 6,
                        shadowOffsetY: 1
                    },
                    // 让文本与引导线颜色随扇区一致
                    label: { color: item.color },
                    labelLine: { lineStyle: { color: item.color } }
                })),
                animationType: 'scale',
                animationEasing: 'elasticOut',
                animationDelay: (idx: number) => idx * 100
            }
        ],
        graphic: []
    }

    console.log('设置图表配置:', option)
    chartInstance.setOption(option)
    console.log('图表初始化完成')

    // 响应式处理：同时更新图例的 itemGap 以保持纵向均匀
    const onResize = () => {
        const el = teamChartRef.value as unknown as HTMLElement
        const w = el.parentElement?.clientWidth || el.clientWidth || 0
        const newCenterX = w * 0.3 + LEFT_OVERFLOW_PAD
        chartInstance.resize()
        chartInstance.setOption({
            legend: { itemGap: getLegendGap(), top: 0, bottom: 0 },
            series: [{ center: [newCenterX, '52%'] }]
        })
    }
    window.addEventListener('resize', onResize)
}

onMounted(() => {
    fetchTeamStatistics()
})
</script>

<style lang="scss" scoped>
.chart-section {
    width: 100%;
// background: red;
    .header-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        position: relative;
        padding-left: 20px;
        padding-right: 20px;
    }

    .title {
        font-size: 22px;
        font-weight: bold;
        letter-spacing: 1px;
        color: #e9f2ff;
        background: linear-gradient(to top, #497ef1 0%, #ffffff 60%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;
        margin-left: 70px;
        flex: 0 0 auto;
        display: flex;
        align-items: center;
    }

    .current-date {
        font-size: 16px;
        font-weight: 500;
        color: #7ecfff;
        background: linear-gradient(to top, #497ef1 0%, #7ecfff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        letter-spacing: 0.5px;
    }

    .header-underline {
        width: 100%;
        height: 10px;
        background: url('@/assets/images/SubTitleBar.png') no-repeat 10px center;
        background-size: calc(100% - 20px) 100%;
        margin-bottom: 6px;
        border-radius: 0;
        opacity: 1;
    }

    .plan-chart-row {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 4px;
        padding: 0 6px;
    }

    .chart {
        width: 100%; /* 宽度由外层 chart-slot 控制为更宽 */
        height: 280px;
        min-height: 280px;
        overflow: visible;
    }

    .no-data {
        width: 100%;
        height: 280px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 16px;

        .no-data-icon {
            font-size: 48px;
            opacity: 0.6;
        }

        .no-data-text {
            font-size: 16px;
            color: #7ecfff;
            opacity: 0.8;
            letter-spacing: 0.5px;
        }
    }
}
</style>
