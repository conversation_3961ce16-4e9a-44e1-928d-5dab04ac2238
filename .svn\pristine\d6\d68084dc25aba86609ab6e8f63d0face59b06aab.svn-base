import request from '@/utils/request'
import { AxiosPromise } from 'axios'
import {
    TrafficResponse,
    TrafficQuery,
    TrafficAggregateResponse,
    TotalFlowStatisticsResponse,
    DailyFlowStatisticsResponse,
    SaturationStatisticsResponse
} from './types'

/**
 * 获取24小时车流统计数据
 * @param query 查询参数
 * @returns 车流数据
 */
export const getTraffic24Hour = (query?: TrafficQuery): AxiosPromise<TrafficResponse> => {
    return request({
        url: '/health/traffic/24hour',
        method: 'get',
        params: query
    })
}
export const refreshTraffic24Hour = (query?: TrafficQuery): AxiosPromise<TrafficResponse> => {
    return request({
        url: '/health/traffic/save24hour',
        method: 'get',
        params: query
    })
}

/**
 * 获取车流量聚合统计数据
 * @param type 统计类型：year/month/day/custom
 * @param projectId 项目ID
 * @param startDate 开始日期（可选）
 * @param endDate 结束日期（可选）
 * @returns 聚合统计数据
 */
export const getTrafficAggregate = (params: {
    type: string
    projectId: string | number
    startDate?: string
    endDate?: string
}): AxiosPromise<TrafficAggregateResponse> => {
    return request({
        url: '/subProject/statistics/car/trafficAggregate',
        method: 'get',
        params
    })
}

/**
 * 获取总流量统计
 */
export const getTotalFlowStatistics = (projectId: string | number): AxiosPromise<TotalFlowStatisticsResponse> => {
    return request({
        url: '/subProject/statistics/car/totalFlowStatistics',
        method: 'get',
        params: { projectId }
    })
}

/**
 * 获取当日车流量统计
 */
export const getDailyFlowStatistics = (projectId: string | number): AxiosPromise<DailyFlowStatisticsResponse> => {
    return request({
        url: '/subProject/statistics/car/dailyFlowStatistics',
        method: 'get',
        params: { projectId }
    })
}

/**
 * 获取饱和度统计
 */
export const getSaturationStatistics = (projectId: string | number): AxiosPromise<SaturationStatisticsResponse> => {
    return request({
        url: '/subProject/statistics/car/saturationStatistics',
        method: 'get',
        params: { projectId }
    })
}
