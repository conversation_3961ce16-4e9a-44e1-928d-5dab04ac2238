<!-- 项目级-MAP (百度地图版本) -->
<template>
    <div class="main-box">
        <div class="project-tabs" v-if="props.showProjectList">
            <div v-if="loading" class="loading-text">加载项目数据中...</div>
            <div
                v-else
                v-for="project in projects"
                :key="project.id"
                class="tab"
                :class="{ active: selectedProjectId === project.id }"
                @click="selectProject(project.id)"
            >
                {{ project.name }}
            </div>
        </div>
        <div class="map-container">
            <div id="baidu-map-container" class="map"></div>
            <!-- <img class="map-mask" src="@/assets/images/<EMAIL>" /> -->
        </div>

        <!-- 事件对话框 -->
        <EventDialog :visible="showEventDialog" :project-id="selectedProjectId" :tunnel-name="selectedTunnelName" @close="closeEventDialog" />

        <!-- 积水监测对话框 -->
        <WaterDialog :visible="showWaterDialog" :water-data="selectedWaterData" @close="closeWaterDialog" />

        <!-- 台风信息对话框 -->
        <TyphoonDialog :visible="showTyphoonDialog" :typhoon-data="selectedTyphoonData" @close="closeTyphoonDialog" />
        <div class="check-filter">
            <div class="filter-item">
                <input type="checkbox" v-model="showEventAnchors" @change="toggleEventAnchors" />
                <img src="@/assets/images/bigscreen/icons/events.png" />
                <span>事件点位</span>
            </div>
            <div class="filter-item">
                <input type="checkbox" v-model="showWaterAnchors" @change="toggleWaterAnchors" />
                <img src="@/assets/images/bigscreen/icons/<EMAIL>" />
                <span>积水点位</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { getAllProjects } from '@/api/project/project/index'
import { ProjectVO, ProjectMapItem } from '@/api/project/project/types'
import { getWaterMonitorData } from '@/api/subProject/health/waterDetecter/index'
import { WaterMonitorDataVO } from '@/api/subProject/health/waterDetecter/types'
import { listEventLocations } from '@/api/subProject/operation/event/index'
import { MapEventVO } from '@/api/subProject/operation/event/types'
import tunnelIcon from '@/assets/images/<EMAIL>'
import eventPosIcon from '@/assets/images/bigscreen/icons/events.png'
import sjIcon from '@/assets/images/bigscreen/icons/sj.png'
import waterPosIcon from '@/assets/images/bigscreen/icons/<EMAIL>'
import EventDialog from '@/views/bigscreen/components/Chart/EventDialog.vue'
import WaterDialog from '@/views/bigscreen/components/Chart/WaterDialog.vue'
import TyphoonDialog from '@/views/bigscreen/components/Chart/TyphoonDialog.vue'
import { getTfList, getTfInfo } from '@/api/weather/hfIndex'
import dayjs from 'dayjs'
import { el } from 'element-plus/es/locale/index.mjs'

var tfdw = []
var tflines = []
// 获取台风列表
const getTfListF = async () => {
    const response = await getTfList({
        key: '74a093bad58e43568446a246fe6d3cbd',
        basin: 'NP',
        year: new Date().getFullYear()
    })
    console.log('获取台风列表成功:', response.data)
}

const getTfInfoF = async (name) => {
    const response = await getTfInfo({
        key: '74a093bad58e43568446a246fe6d3cbd',
        stormid: name
    })
    let lines = []
    let windCircles30 = [] // 30节风圈数据
    let windCircles50 = [] // 50节风圈数据

    response.track.forEach((element) => {
        tfdw.push({
            geometry: {
                type: 'Point',
                coordinates: [element.lon, element.lat]
            },
            properties: {
                count: element.type,
                ...element
            }
        })
        lines.push([element.lon, element.lat])

        // 创建风圈数据
        if (element.windRadius30 && element.windRadius30 > 0) {
            windCircles30.push({
                geometry: {
                    type: 'Point',
                    coordinates: [element.lon, element.lat]
                },
                properties: {
                    radius: element.windRadius30, // 30节风圈半径（公里）
                    windSpeed: 30,
                    typhoonType: element.type,
                    ...element
                }
            })
        }

        if (element.windRadius50 && element.windRadius50 > 0) {
            windCircles50.push({
                geometry: {
                    type: 'Point',
                    coordinates: [element.lon, element.lat]
                },
                properties: {
                    radius: element.windRadius50, // 50节风圈半径（公里）
                    windSpeed: 50,
                    typhoonType: element.type,
                    ...element
                }
            })
        }
    })
    console.log(tfdw, 'tfdw')
    console.log('30节风圈数据:', windCircles30)
    console.log('50节风圈数据:', windCircles50)

    tflines.push({
        'geometry': {
            'type': 'LineString',
            'coordinates': lines
        }
    })

    // 台风路径线
    var lineLayer = new mapvgl.LineLayer({
        width: 3,
        // dashArray: [20, 30],
        // dashOffset: 20,
        color: 'rgba(149, 204, 219, 1)'
    })
    mapViewer.addLayer(lineLayer)
    lineLayer.setData(tflines)

    // 50节风圈图层（更大的圈，先绘制）
    if (windCircles50.length > 0) {
        var windCircle50Layer = new window.mapvgl.PointLayer({
            blend: 'normal',
            size: function (item) {
                // 将半径从公里转换为地图像素大小
                // 基于地图缩放级别调整风圈大小，使其更符合实际地理尺寸
                const zoom = mapInstance ? mapInstance.getZoom() : 14
                const baseScale = Math.pow(2, zoom - 10) // 基础缩放因子
                return Math.max((item.properties.radius || 50) * baseScale * 0.8, 20)
            },
            borderWidth: 2,
            borderColor: 'rgba(255, 215, 0, 0.9)', // 金黄色边框，50节风圈
            color: 'rgba(255, 215, 0, 0.12)', // 半透明金黄色填充
            enablePicked: false
        })
        mapViewer.addLayer(windCircle50Layer)
        windCircle50Layer.setData(windCircles50)
        console.log('50节风圈图层已创建，数据点数:', windCircles50.length)
    }

    // 30节风圈图层（较小的圈，后绘制，显示在上层）
    if (windCircles30.length > 0) {
        var windCircle30Layer = new window.mapvgl.PointLayer({
            blend: 'normal',
            size: function (item) {
                // 将半径从公里转换为地图像素大小
                const zoom = mapInstance ? mapInstance.getZoom() : 14
                const baseScale = Math.pow(2, zoom - 10)
                return Math.max((item.properties.radius || 30) * baseScale * 0.8, 15)
            },
            borderWidth: 2,
            borderColor: 'rgba(255, 140, 0, 1.0)', // 橙红色边框，30节风圈
            color: 'rgba(255, 140, 0, 0.18)', // 半透明橙红色填充
            enablePicked: false
        })
        mapViewer.addLayer(windCircle30Layer)
        windCircle30Layer.setData(windCircles30)
        console.log('30节风圈图层已创建，数据点数:', windCircles30.length)
    }

    // 台风点位图层（最上层）
    var pointLayer = new mapvgl.PointLayer({
        blend: 'lighter',
        size: 10,
        borderWidth: 1,
        enablePicked: true, // 是否可以拾取
        selectedIndex: -1, // 选中数据项索引
        borderColor: 'rgba(255, 255, 255, 1)',
        color: function (item) {
            if (item.properties.count === 'TD') {
                return 'rgb(79, 255, 219, 0.8)'
            } else if (item.properties.count === 'TS') {
                return 'rgb(254, 242, 0, 0.8)'
            } else if (item.properties.count === 'STS') {
                return 'rgb(246, 122, 25, 0.8)'
            } else if (item.properties.count === 'TY') {
                return 'rgb(243, 0, 2, 0.8)'
            } else if (item.properties.count === 'STY') {
                return 'rgb(243, 26, 148, 0.8)'
            } else if (item.properties.count === 'SuperTY') {
                return 'rgb(196, 114, 230, 0.8)'
            }
        },
        onClick: (e) => {
            if (e.dataIndex == -1) {
                return
            } else {
                // 点击事件，显示台风弹框
                handleTyphoonClick(e)
            }
        }
    })

    mapViewer.addLayer(pointLayer)
    pointLayer.setData(tfdw)

    console.log('获取台风信息成功:', response.data)
}

// Props定义
interface Props {
    showProjectList?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    showProjectList: true
})

// 声明全局类型
declare global {
    interface Window {
        BMapGL: any
        mapvgl: any
        initBaiduMapCallback: () => void
    }
    const mapvgl: any
}

// 响应式数据
const appStore = useAppStore()
const projects = ref<ProjectVO[]>([])
// 默认选中项：中环北路
const selectedProjectId = ref<string>('')
const loading = ref(true)
var mapInstance: any = null
var mapViewer: any = null
var lineLayer: any = null
const mapMarkers = ref<any[]>([])
const mapPolylines = ref<any[]>([])

// 连线开关变量 - 暂时关闭坐标间的连线
const showConnectingLines = ref(true)

// 锚点图标显示开关变量
const showAnchorIcons = ref(false)

// 事件和积水点位显示开关
const showEventAnchors = ref(true) // 默认显示事件点位
const showWaterAnchors = ref(true) // 默认不显示积水点位

// 锚点配置
const anchorConfigs = {
    event: {
        icon: eventPosIcon,
        size: { width: 37, height: 70 },
        count: { min: 1, max: 1 }, // 每条线路固定1个事件点位
        offsetRange: 0,
        dialogTitle: '事件监测'
    },
    water: {
        icon: waterPosIcon,
        size: { width: 37, height: 50 },
        count: { min: 1, max: 1 }, // 单个锚点配置，但全地图总共5个
        offsetRange: 0, // 增大偏移范围，使积水点位更分散
        dialogTitle: '积水监测'
    }
}

// 监测对话框相关状态
const showEventDialog = ref(false)
const showWaterDialog = ref(false)
const showTyphoonDialog = ref(false)
const selectedWaterData = ref<any>({})
const selectedTunnelName = ref<string>('')
const selectedTyphoonData = ref<any>({})

// 锚点存储
const eventAnchors = ref<any[]>([])
const waterAnchors = ref<any[]>([])
// 事件底座存储
const eventBases = ref<any[]>([])

// 积水监测数据存储
const waterMonitorData = ref<WaterMonitorDataVO[]>([])

// 事件位置数据存储
const eventLocations = ref<MapEventVO[]>([])

// 定时器存储
let refreshTimer: NodeJS.Timeout | null = null

// 自定义动态Canvas底座类变量（将在百度地图API加载完成后定义）
let CustomSymbol: any = null

// zoom级别对应的字体大小和锚点图标大小配置
const zoomStyleConfig = ref({
    '>15': { fontSize: 16, iconSize: 40 }, // zoom > 15时，字体大小16px，图标大小40px
    '15': { fontSize: 14, iconSize: 35 }, // zoom = 15时，字体大小14px，图标大小35px
    '14': { fontSize: 12, iconSize: 30 }, // zoom = 14时，字体大小12px，图标大小30px
    '13': { fontSize: 10, iconSize: 25 }, // zoom = 13时，字体大小10px，图标大小25px
    '12': { fontSize: 8, iconSize: 20 }, // zoom = 12时，字体大小8px，图标大小20px
    '<12': { fontSize: 6, iconSize: 15 } // zoom < 12时，字体大小6px，图标大小15px
})

// 根据zoom级别获取对应的样式配置
const getStyleConfigByZoom = (zoom: number) => {
    if (zoom > 15) return zoomStyleConfig.value['>15']
    if (zoom === 15) return zoomStyleConfig.value['15']
    if (zoom === 14) return zoomStyleConfig.value['14']
    if (zoom === 13) return zoomStyleConfig.value['13']
    if (zoom === 12) return zoomStyleConfig.value['12']
    return zoomStyleConfig.value['<12'] // zoom < 12
}

// 更新所有标签和图标样式的方法
const updateAllElementsStyle = (styleConfig: { fontSize: number; iconSize: number }) => {
    mapMarkers.value.forEach((marker) => {
        if (marker instanceof window.BMapGL.Label) {
            // 更新标签字体大小
            marker.setStyle({
                fontSize: `${styleConfig.fontSize}px`
            })
        } else if (marker instanceof window.BMapGL.Marker) {
            // 更新图标大小
            const icon = marker.getIcon()
            if (icon && icon.imageUrl) {
                // 判断是否为监测锚点（通过图标URL判断）
                const isEventAnchor = icon.imageUrl.includes('event-pos')
                const isWaterAnchor = icon.imageUrl.includes('water-pos')

                if (isEventAnchor || isWaterAnchor) {
                    // 监测锚点保持固定大小
                    const config = isEventAnchor ? anchorConfigs.event : anchorConfigs.water
                    const newIcon = createIcon(icon.imageUrl, config.size)
                    marker.setIcon(newIcon)
                } else {
                    // 其他图标使用动态大小
                    const newIcon = new window.BMapGL.Icon(icon.imageUrl, new window.BMapGL.Size(styleConfig.iconSize, styleConfig.iconSize), {
                        imageSize: new window.BMapGL.Size(styleConfig.iconSize * 0.75, styleConfig.iconSize * 0.75),
                        anchor: new window.BMapGL.Size((styleConfig.iconSize * 0.75) / 2, styleConfig.iconSize * 0.75)
                    })
                    marker.setIcon(newIcon)
                }
            }
        }
    })
}

// 获取项目数据
const fetchProjects = async () => {
    try {
        loading.value = true
        const response = await getAllProjects()
        projects.value = response.data || []

        // 默认选中包含"中环北线"字样的项目，如果没有则选择第一个项目
        if (projects.value.length > 0) {
            const zhonghuanProject = projects.value.find((project) => project.name && project.name.includes('中环北线'))
            selectedProjectId.value = zhonghuanProject ? zhonghuanProject.id : projects.value[0].id
        }
    } catch (error) {
        console.error('获取项目数据失败:', error)
    } finally {
        loading.value = false
    }
}

// 获取积水监测数据
const fetchWaterMonitorData = async () => {
    try {
        const response = await getWaterMonitorData()
        waterMonitorData.value = response.data || []
        console.log('获取积水监测数据成功:', waterMonitorData.value.length, '个点位')
    } catch (error) {
        console.error('获取积水监测数据失败:', error)
        waterMonitorData.value = []
    }
}

// 获取事件位置数据
const fetchEventLocations = async () => {
    try {
        const response = await listEventLocations()
        eventLocations.value = response.data || []
        console.log('获取事件位置数据成功:', eventLocations.value.length, '个事件位置')
    } catch (error) {
        console.error('获取事件位置数据失败:', error)
        eventLocations.value = []
    }
}

// 通用数据刷新方法
const refreshAllMapData = async () => {
    console.log('开始刷新所有地图数据...')
    try {
        await Promise.all([
            fetchWaterMonitorData(),
            fetchEventLocations()
            // 未来可以添加其他数据获取方法
            // fetchOtherMonitorData()
        ])

        // 只有在地图已初始化的情况下才重新渲染地图数据
        if (mapInstance) {
            console.log('地图已初始化，重新渲染地图数据')
            displayAllProjectsOnMap()
        } else {
            console.log('地图未初始化，跳过地图渲染')
        }
        console.log('地图数据刷新完成')
    } catch (error) {
        console.error('刷新地图数据失败:', error)
    }
}

// 解析项目地图数据
const parseProjectMapData = (mapJsonString: string): ProjectMapItem[] => {
    try {
        if (!mapJsonString) return []

        // 处理可能的多种JSON格式
        const mapData = JSON.parse(mapJsonString)

        // 如果是数组，直接返回
        if (Array.isArray(mapData)) {
            return mapData
        }

        // 如果是单个对象，转换为数组
        if (typeof mapData === 'object') {
            return [mapData]
        }

        return []
    } catch (error) {
        console.error('解析地图数据失败:', error)
        return []
    }
}

// 清除地图标记和线条（优化性能）
const clearMapElements = () => {
    if (mapInstance) {
        // 清除所有覆盖物
        mapInstance.clearOverlays()
        mapMarkers.value = []
        mapPolylines.value = []
        eventAnchors.value = []
        waterAnchors.value = []
        eventBases.value = []
    }
}

// 创建标记点击处理函数
const createMarkerClickHandler = (project: ProjectVO) => {
    return () => {
        // 设置当前项目到store
        appStore.projectContext.selectedProjectId = project.id
        appStore.projectContext.isPlatform = false

        // 跳转到navigation页面
        window.open('/navigation', 'navigation')
    }
}

// 通用函数：生成模拟监测数据
const generateMockData = (segmentName: string, dataType: 'event' | 'water') => {
    if (dataType === 'event') {
        return [
            {
                time: '2025年8月26日 14:24:14',
                type: '设备故障',
                detail: `${segmentName}段设备异常报警`
            },
            {
                time: '2025年8月26日 13:15:32',
                type: '环境监测',
                detail: `${segmentName}段温度超标`
            },
            {
                time: '2025年8月26日 12:08:45',
                type: '安全巡检',
                detail: `${segmentName}段例行巡检完成`
            },
            {
                time: '2025年8月26日 11:30:21',
                type: '维护作业',
                detail: `${segmentName}段设备维护中`
            },
            {
                time: '2025年8月26日 10:45:18',
                type: '系统监控',
                detail: `${segmentName}段监控系统正常`
            }
        ]
    } else {
        return [
            {
                time: '2025年8月26日 14:20:33',
                type: '积水深度',
                detail: `${segmentName}段积水深度15cm`
            },
            {
                time: '2025年8月26日 13:45:12',
                type: '排水状态',
                detail: `${segmentName}段排水泵运行正常`
            },
            {
                time: '2025年8月26日 12:30:45',
                type: '水位监测',
                detail: `${segmentName}段水位传感器正常`
            },
            {
                time: '2025年8月26日 11:15:28',
                type: '预警信息',
                detail: `${segmentName}段降雨量预警`
            }
        ]
    }
}

// 通用函数：创建图标
const createIcon = (iconUrl: string, size: { width: number; height: number }) => {
    return new window.BMapGL.Icon(iconUrl, new window.BMapGL.Size(size.width, size.height), {
        imageSize: new window.BMapGL.Size(size.width, size.height),
        anchor: new window.BMapGL.Size(size.width / 2, size.width)
    })
}

// 通用函数：生成随机偏移
const generateRandomOffset = (offsetRange: number) => {
    return {
        lng: (Math.random() - 0.5) * offsetRange,
        lat: (Math.random() - 0.5) * offsetRange
    }
}

// 事件锚点点击处理函数
const handleEventAnchorClick = (projectId: string, segmentName: string) => {
    // 设置选中的项目ID和线段名称，EventDialog将自动获取数据
    selectedTunnelName.value = segmentName
    showEventDialog.value = true
    console.log('点击事件锚点:', { projectId, segmentName })
}

// 积水锚点点击处理函数
const handleWaterAnchorClick = (waterData: WaterMonitorDataVO) => {
    selectedWaterData.value = {
        id: waterData.id,
        name: waterData.name,
        location: waterData.name, // 使用点位名称作为位置
        status: waterData.level, // 使用API返回的预警级别
        time: waterData.lastUpdateTime,
        depth: waterData.value,
        warningLimit: waterData.warningLimit,
        lat: waterData.lat,
        lng: waterData.lng
    }
    showWaterDialog.value = true
}

// 台风锚点点击处理函数
const handleTyphoonClick = (typhoonInfo: any) => {
    selectedTyphoonData.value = typhoonInfo.dataItem.properties
    showTyphoonDialog.value = true
    console.log('点击台风:', typhoonInfo.properties)
}

// 关闭对话框
const closeEventDialog = () => {
    showEventDialog.value = false
    selectedTunnelName.value = ''
}

const closeWaterDialog = () => {
    showWaterDialog.value = false
    selectedWaterData.value = {}
}

const closeTyphoonDialog = () => {
    showTyphoonDialog.value = false
    selectedTyphoonData.value = {}
}

// 切换事件点位显示/隐藏
const toggleEventAnchors = () => {
    // 同时控制事件锚点和底座的显示/隐藏
    eventAnchors.value.forEach((anchor) => {
        if (showEventAnchors.value) {
            // mapInstance.addOverlay(anchor)
            anchor._visible = true
        } else {
            anchor._visible = false
            // mapInstance.removeOverlay(anchor)
        }
    })

    eventBases.value.forEach((base) => {
        if (base) {
            if (showEventAnchors.value) {
                base._visible = true
            } else {
                base._visible = false
            }
        }
    })
}

// 切换积水点位显示/隐藏
const toggleWaterAnchors = () => {
    waterAnchors.value.forEach((anchor) => {
        if (showWaterAnchors.value) {
            mapInstance.addOverlay(anchor)
        } else {
            mapInstance.removeOverlay(anchor)
        }
    })
}

// 创建事件锚点（基于真实的事件位置数据）
const createEventAnchors = (segmentCoordinates: any[], segmentName: string, projectId: string) => {
    const config = anchorConfigs.event

    // 随机选择线段上的一个点
    const randomIndex = Math.floor(segmentCoordinates.length / 2)
    const basePoint = segmentCoordinates[randomIndex]

    // 生成随机偏移
    const offset = generateRandomOffset(config.offsetRange)
    const anchorPoint = new window.BMapGL.Point(basePoint.lng + offset.lng, basePoint.lat + offset.lat)

    // 创建动态canvas底座（只有在CustomSymbol类已定义时才创建）
    let baseMarker = null
    let customSymbol = null
    if (CustomSymbol) {
        const baseSize = 100
        customSymbol = new CustomSymbol(new window.BMapGL.Size(baseSize, baseSize), new window.BMapGL.Size(baseSize / 2, baseSize / 2))

        baseMarker = new window.BMapGL.Marker(anchorPoint, {
            icon: customSymbol,
            enableDragging: false
        })
    }

    // 创建事件锚点图标
    const anchor = new window.BMapGL.Marker(anchorPoint, {
        icon: createIcon(config.icon, config.size),
        offset: new window.BMapGL.Size(0, -10)
    })
    // 添加点击事件，传递项目ID和线段名称
    anchor.addEventListener('click', () => handleEventAnchorClick(projectId, segmentName))

    // 根据开关状态决定是否添加到地图
    if (showEventAnchors.value) {
        if (baseMarker) {
            mapInstance.addOverlay(baseMarker)
        }
        mapInstance.addOverlay(anchor)
        // 延迟启动动画，等待地图完全加载
        if (customSymbol) {
            setTimeout(() => {
                customSymbol.isReDraw = true
            }, 500)
        }
    }

    mapMarkers.value.push(anchor)
    if (baseMarker) {
        mapMarkers.value.push(baseMarker)
        eventBases.value.push(baseMarker)
    }
    eventAnchors.value.push(anchor)
    window.eventBases = eventBases.value
    console.log(`事件锚点已创建: 项目${projectId}, 线段${segmentName}`)
}

// 创建积水锚点（基于真实的积水监测数据）
const createWaterAnchors = () => {
    const config = anchorConfigs.water
    console.log('开始创建积水锚点，数据数量:', waterMonitorData.value.length)

    // 检查地图实例是否存在
    if (!mapInstance) {
        console.error('地图实例不存在，无法创建积水锚点')
        return
    }

    // 使用真实的积水监测数据创建锚点
    waterMonitorData.value.forEach((waterData) => {
        // 详细的坐标调试信息
        // console.log(`积水点位 ${index + 1}:`, {
        //     name: waterData.name,
        //     lat: waterData.lat,
        //     lng: waterData.lng,
        //     level: waterData.level,
        //     value: waterData.value,
        //     坐标精度: `lat精度:${waterData.lat.toString().split('.')[1]?.length || 0}位, lng精度:${waterData.lng.toString().split('.')[1]?.length || 0}位`
        // })

        // 使用真实的经纬度坐标
        const anchorPoint = new window.BMapGL.Point(waterData.lng, waterData.lat)

        // 创建锚点
        const anchor = new window.BMapGL.Marker(anchorPoint, {
            icon: createIcon(config.icon, config.size)
        })

        // 添加点击事件，传递完整的积水监测数据
        anchor.addEventListener('click', () => handleWaterAnchorClick(waterData))

        // 根据开关状态决定是否添加到地图
        if (showWaterAnchors.value) {
            mapInstance.addOverlay(anchor)
            //console.log(`积水点位 ${waterData.name} 已添加到地图`)
        } else {
            //console.log(`积水点位 ${waterData.name} 未添加到地图（开关关闭）`)
        }
        mapMarkers.value.push(anchor)
        waterAnchors.value.push(anchor)
    })

    console.log(`积水锚点创建完成，共创建 ${waterMonitorData.value.length} 个锚点`)
}

// 预定义的项目颜色数组 - 适合暗黑模式的冷色系
const projectColors = [
    //{ line: '#00ffff', start: '#00ffff', end: '#00ffff', name: 'rgba(0, 255, 255, 0.8)' }, // 亮青色
    { line: '#33c9f1', start: '#00ff88', end: '#00ff88', name: '#33c9f1' } // 翠绿色
    //{ line: '#4da6ff', start: '#4da6ff', end: '#4da6ff', name: 'rgba(77, 166, 255, 0.8)' }, // 亮蓝色
    //{ line: '#FF9326', start: '#00bfff', end: '#00bfff', name: 'rgba(0, 191, 255, 0.8)' }, // 深天蓝
    //{ line: '#FF73FF', start: '#7fffd4', end: '#7fffd4', name: 'rgba(127, 255, 212, 0.8)' } // 碧绿色
]

const getGeometryCoordinates = (segmentCoordinates: any[]) => {
    return segmentCoordinates.map((item) => {
        return [item.lng, item.lat]
    })
}

// 在地图上显示项目数据（百度地图版本）
const displayProjectOnMap = (project: ProjectVO, colorIndex: number = 0, clearMap: boolean = true) => {
    if (!mapInstance || !project.map) return

    // 根据参数决定是否清除现有标记和线条
    if (clearMap) {
        clearMapElements()
    }

    const mapItems = parseProjectMapData(project.map)
    if (mapItems.length === 0) return

    // 获取项目对应的颜色
    const colors = projectColors[colorIndex % projectColors.length]

    const allCoordinates: any[] = []

    // 按线段名称分组
    const segmentGroups = new Map<string, ProjectMapItem[]>()
    mapItems.forEach((item) => {
        if (item.name) {
            if (!segmentGroups.has(item.name)) {
                segmentGroups.set(item.name, [])
            }
            segmentGroups.get(item.name)!.push(item)
        }
    })
    var allLines = []
    // 为每个线段组创建连线和标记
    segmentGroups.forEach((segments, segmentName) => {
        if (segments.length === 0) return

        // 收集该线段组的所有坐标点（按顺序，去重）
        const segmentCoordinates: any[] = []
        const coordinateMap = new Map<string, any>() // 用于去重

        // 按顺序收集所有坐标点，避免重复
        segments.forEach((item) => {
            const beginLng = parseFloat(item.beginLongitude?.toString() || '0')
            const beginLat = parseFloat(item.beginLatitude?.toString() || '0')
            const endLng = parseFloat(item.endLongitude?.toString() || '0')
            const endLat = parseFloat(item.endLatitude?.toString() || '0')

            // 处理起点
            if (beginLng && beginLat) {
                const beginKey = `${beginLng},${beginLat}`
                if (!coordinateMap.has(beginKey)) {
                    const beginPos = new window.BMapGL.Point(beginLng, beginLat)
                    coordinateMap.set(beginKey, beginPos)
                    segmentCoordinates.push(beginPos)
                    allCoordinates.push(beginPos)
                }
            }

            // 处理终点（确保不与起点重复）
            if (endLng && endLat && (endLng !== beginLng || endLat !== beginLat)) {
                const endKey = `${endLng},${endLat}`
                if (!coordinateMap.has(endKey)) {
                    const endPos = new window.BMapGL.Point(endLng, endLat)
                    coordinateMap.set(endKey, endPos)
                    segmentCoordinates.push(endPos)
                    allCoordinates.push(endPos)
                }
            }
        })

        // 创建单一连续线段（如果有足够的坐标点，且开关开启）
        if (showConnectingLines.value && segmentCoordinates.length >= 2) {
            allLines.push({
                'geometry': {
                    'type': 'LineString',
                    'coordinates': getGeometryCoordinates(segmentCoordinates)
                }
            })

            // const polyline = new window.BMapGL.Polyline(segmentCoordinates, {
            //     strokeColor: colors.line,
            //     strokeWeight: 6,
            //     strokeStyle: 'solid',
            //     strokeOpacity: 0.8,
            //     strokeLineCap: 'round', // 设置线段端点为圆形，避免尖锐连接
            //     strokeLineJoin: 'round' // 设置线段连接处为圆形，避免尖锐角
            // })
            // mapInstance.addOverlay(polyline)
            // mapPolylines.value.push(polyline)
        }

        // 为该线段组创建首尾标记
        if (segmentCoordinates.length >= 2) {
            // 起点标记（第一个坐标）- 根据开关控制显示
            if (showAnchorIcons.value) {
                const startPoint = segmentCoordinates[0]
                const currentZoom = mapInstance.getZoom()
                const styleConfig = getStyleConfigByZoom(currentZoom)
                const startMarker = new window.BMapGL.Marker(startPoint, {
                    icon: new window.BMapGL.Icon(tunnelIcon, new window.BMapGL.Size(styleConfig.iconSize, styleConfig.iconSize), {
                        imageSize: new window.BMapGL.Size(styleConfig.iconSize * 0.75, styleConfig.iconSize * 0.75),
                        anchor: new window.BMapGL.Size((styleConfig.iconSize * 0.75) / 2, styleConfig.iconSize * 0.75)
                    })
                })
                startMarker.addEventListener('click', createMarkerClickHandler(project))
                mapInstance.addOverlay(startMarker)
                //起始点标记
                mapMarkers.value.push(startMarker)
            }

            // 计算标签显示位置：如果只有2个坐标显示在第1个，否则显示在中间节点
            let labelPointIndex = 0
            if (segmentCoordinates.length > 2) {
                labelPointIndex = Math.floor((segmentCoordinates.length - 1) / 2)
            }
            const labelPoint = segmentCoordinates[labelPointIndex]

            // 线段名称标签（显示在中间节点）
            const currentZoom = mapInstance.getZoom()
            const styleConfig = getStyleConfigByZoom(currentZoom)

            const nameLabel = new window.BMapGL.Label(segmentName, {
                position: labelPoint
                // offset: new window.BMapGL.Size(25, -10)
            })
            nameLabel.setStyle({
                background: '#0B2B37',
                border: '0px solid #4a9bb0',
                borderRadius: '8px',
                color: '#ffffff',
                fontSize: `${styleConfig.fontSize}px`,
                fontWeight: 'bold',
                padding: '6px 12px',
                whiteSpace: 'nowrap',
                boxShadow: 'inset 0px 0px 8px 0px rgba(0,217,255,0.75)',
                maxWidth: '200px',
                cursor: 'pointer',
                transform: 'translate(-50%, -120%)'
            })
            // 为线段名称标签添加点击事件
            nameLabel.addEventListener('click', createMarkerClickHandler(project))
            mapInstance.addOverlay(nameLabel)
            mapMarkers.value.push(nameLabel)

            // 检查该项目+线段是否有事件，只为有事件的线段创建锚点
            if (segmentCoordinates.length >= 2) {
                const hasEvent = eventLocations.value.some((event) => event.projectId === project.id && event.tunnelName === segmentName)
                if (hasEvent) {
                    createEventAnchors(segmentCoordinates, segmentName, project.id)
                    console.log(`线段 ${segmentName} 有事件，已创建事件锚点`)
                } else {
                    console.log(`线段 ${segmentName} 无事件，跳过事件锚点创建`)
                }
            }

            // 终点标记（最后一个坐标）- 根据开关控制显示
            const endPoint = segmentCoordinates[segmentCoordinates.length - 1]
            // 避免起点和终点是同一个坐标的情况
            if (showAnchorIcons.value && (segmentCoordinates[0].lng !== endPoint.lng || segmentCoordinates[0].lat !== endPoint.lat)) {
                const currentZoom = mapInstance.getZoom()
                const styleConfig = getStyleConfigByZoom(currentZoom)
                const endMarker = new window.BMapGL.Marker(endPoint, {
                    icon: new window.BMapGL.Icon(tunnelIcon, new window.BMapGL.Size(styleConfig.iconSize, styleConfig.iconSize), {
                        imageSize: new window.BMapGL.Size(styleConfig.iconSize * 0.75, styleConfig.iconSize * 0.75),
                        anchor: new window.BMapGL.Size((styleConfig.iconSize * 0.75) / 2, styleConfig.iconSize * 0.75)
                    })
                })
                endMarker.addEventListener('click', createMarkerClickHandler(project))
                mapInstance.addOverlay(endMarker)
                mapMarkers.value.push(endMarker)
            }
        }
    })

    if (allLines.length > 0) {
        lineLayer = new window.mapvgl.LineLayer(
            window.appConfig.lineConfig || {
                color: 'rgba(47,153,195, 0.9)',
                width: 4,
                animation: true,
                duration: 4, // 循环时间2s
                trailLength: 1.8, // 拖尾长度占间隔的0.4
                interval: 0.2 // 粒子长度占线整体长度的0.2
            }
        )
        mapViewer.addLayer(lineLayer)
        lineLayer.setData(allLines)
        // lineLayer.setData(allLines)
        // mapViewer.addLayer(lineLayer)
    }

    // 注意：不同线段组之间不需要连线，每个线段组都是独立的路径

    // 调整地图视野 - 项目切换时固定zoom为13
    if (allCoordinates.length > 0) {
        //根据项目坐标调整视野
        //mapInstance.setViewport(allCoordinates, { margins: [50, 50, 50, 50] })
        // 计算项目坐标的中心点
        const lngs = allCoordinates.map((point) => point.lng)
        const lats = allCoordinates.map((point) => point.lat)
        const centerLng = (Math.min(...lngs) + Math.max(...lngs)) / 2
        const centerLat = (Math.min(...lats) + Math.max(...lats)) / 2
        const centerPoint = new window.BMapGL.Point(centerLng, centerLat)

        // 设置固定zoom为13
        mapInstance.centerAndZoom(centerPoint, 13)
        console.log('项目切换 - 设置zoom级别为:', 13)
    }
}

// 显示所有项目的线路
const displayAllProjectsOnMap = () => {
    if (!mapInstance || projects.value.length === 0) {
        console.log('地图未初始化或项目数据为空', { mapInstance: !!mapInstance, projectsLength: projects.value.length })
        return
    }

    // console.log('开始显示所有项目线路，项目数量:', projects.value.length)

    // 清除现有标记和线条
    clearMapElements()

    // 收集所有项目的线段坐标，用于创建积水锚点
    const allSegmentCoordinates: any[] = []

    // 遍历所有项目，为每个项目分配不同颜色
    projects.value.forEach((project, index) => {
        // console.log(`处理项目 ${index}:`, project.name, '是否有地图数据:', !!project.map)
        if (project.map) {
            // 收集该项目的坐标用于积水锚点
            const mapItems = parseProjectMapData(project.map)
            mapItems.forEach((item) => {
                const beginLng = parseFloat(item.beginLongitude?.toString() || '0')
                const beginLat = parseFloat(item.beginLatitude?.toString() || '0')
                const endLng = parseFloat(item.endLongitude?.toString() || '0')
                const endLat = parseFloat(item.endLatitude?.toString() || '0')

                const segmentCoords: any[] = []
                if (beginLng && beginLat) {
                    segmentCoords.push(new window.BMapGL.Point(beginLng, beginLat))
                }
                if (endLng && endLat && (endLng !== beginLng || endLat !== beginLat)) {
                    segmentCoords.push(new window.BMapGL.Point(endLng, endLat))
                }
                if (segmentCoords.length > 0) {
                    allSegmentCoordinates.push(segmentCoords)
                }
            })

            // 第一个项目清除地图，后续项目不清除
            displayProjectOnMap(project, index, false)
        }
    })

    // 创建积水锚点（基于真实的积水监测数据）
    if (waterMonitorData.value.length > 0) {
        //console.log('开始创建积水锚点，数据数量:', waterMonitorData.value)
        createWaterAnchors()
    }

    // getTfInfoF('NP_2501')

    // 获取选中项目的坐标，用于调整视野
    const selectedProject = projects.value.find((p) => p.id === selectedProjectId.value)
    if (selectedProject && selectedProject.map) {
        const mapItems = parseProjectMapData(selectedProject.map)
        if (mapItems.length > 0) {
            const selectedCoordinates: any[] = []

            mapItems.forEach((item) => {
                const beginLng = parseFloat(item.beginLongitude?.toString() || '0')
                const beginLat = parseFloat(item.beginLatitude?.toString() || '0')
                const endLng = parseFloat(item.endLongitude?.toString() || '0')
                const endLat = parseFloat(item.endLatitude?.toString() || '0')

                if (beginLng && beginLat) {
                    selectedCoordinates.push(new window.BMapGL.Point(beginLng, beginLat))
                }
                if (endLng && endLat) {
                    selectedCoordinates.push(new window.BMapGL.Point(endLng, endLat))
                }
            })

            // 调整地图视野到选中项目的具体坐标范围 - 项目切换时固定zoom为13
            if (selectedCoordinates.length > 0) {
                // 计算选中项目坐标的中心点
                const lngs = selectedCoordinates.map((point) => point.lng)
                const lats = selectedCoordinates.map((point) => point.lat)
                const centerLng = (Math.min(...lngs) + Math.max(...lngs)) / 2
                const centerLat = (Math.min(...lats) + Math.max(...lats)) / 2
                const centerPoint = new window.BMapGL.Point(centerLng, centerLat)

                // 设置固定zoom为14
                mapInstance.centerAndZoom(centerPoint, 14)
                console.log('显示所有项目 - 设置zoom级别为:', 14)
            }
        }
    }
}

// 选择项目
const selectProject = (projectId: string) => {
    selectedProjectId.value = projectId
}

// 监听选中项目变化
watch(selectedProjectId, () => {
    // 重新显示所有项目，但视角会聚焦到选中项目
    displayAllProjectsOnMap()
})

// 初始化百度地图
const initBaiduMap = () => {
    if (window.BMapGL) {
        // 定义CustomSymbol类（现在BMapGL已经加载完成）
        CustomSymbol = class extends window.BMapGL.Symbol {
            width: number
            height: number
            context: CanvasRenderingContext2D | null
            isReDraw: boolean

            constructor(_size: any, _anchor: any) {
                super(_size, _anchor)
                this.width = _size.width
                this.height = _size.height
                this.isReDraw = true
                this.context = null
            }

            add() {
                const canvas = document.createElement('canvas')
                canvas.width = this.width * 2
                canvas.height = this.height * 2
                this.context = canvas.getContext('2d')
                // 保持isReDraw状态不变，让外部控制动画
            }

            render(map: any): boolean {
                if (!this.context) return false

                // 只有在isReDraw为true时才执行动画渲染
                if (!this.isReDraw) return false

                const duration = 1500
                const t = (performance.now() % duration) / duration

                // 椭圆扩散效果
                const base = this.width / 2
                const innerRx = base * 0.1,
                    innerRy = base * 0.06 // 椭圆内圈半径
                const outerRx = base * 0.5 * t + innerRx
                const outerRy = base * 0.3 * t + innerRy

                const context = this.context
                context.save()
                context.scale(2, 2) // 使用 2x 像素
                context.clearRect(0, 0, this.width, this.height)

                // 扩散椭圆
                context.beginPath()
                context.ellipse(this.width / 2, this.height / 2, outerRx, outerRy, 0, 0, Math.PI * 2)
                context.fillStyle = 'rgba(70, 140, 215,' + (1 - t) + ')'
                context.fill()

                // 中间椭圆
                context.beginPath()
                context.ellipse(this.width / 2, this.height / 2, innerRx, innerRy, 0, 0, Math.PI * 2)
                context.fillStyle = 'rgba(205, 218, 254, 1)'
                context.strokeStyle = 'rgba(205, 218, 254, .1)'
                context.lineWidth = 2 + 4 * (1 - t)
                context.fill()
                context.stroke()

                context.restore()

                this.data = context.getImageData(0, 0, this.context.canvas.width, this.context.canvas.height)
                return this.isReDraw // 返回isReDraw状态，告诉百度地图是否继续动画
            }
        }

        mapInstance = new window.BMapGL.Map('baidu-map-container')
        // window.mapInstance = mapInstance

        // 设置地图中心点和缩放级别
        const point = new window.BMapGL.Point(120.6, 31.38) // 苏州地区坐标
        mapInstance.centerAndZoom(point, 14)
        // mapInstance.enableTilt(false)
        mapInstance.setOptions({ enableTilt: false })

        // 启用滚轮缩放
        mapInstance.enableScrollWheelZoom(true)

        // 输出初始zoom级别
        console.log('地图初始化 - 当前zoom级别:', mapInstance.getZoom())

        // 监听地图缩放事件
        mapInstance.addEventListener('zoomend', () => {
            const currentZoom = mapInstance.getZoom()
            const styleConfig = getStyleConfigByZoom(currentZoom)

            console.log(
                '地图缩放变化 - 当前zoom级别:',
                currentZoom,
                '字体大小:',
                styleConfig.fontSize + 'px',
                '图标大小:',
                styleConfig.iconSize + 'px'
            )

            // 更新所有标签和图标的样式
            updateAllElementsStyle(styleConfig)
        })

        // 设置地图样式（使用提供的样式ID）
        mapInstance.setMapStyleV2({
            styleId: 'a532aef0efd1ed8b7b0dfc38a67ea9bf'
        })

        // 地图初始化完成后，显示所有项目的线路和积水点位
        console.log('地图初始化完成，开始显示项目线路和积水点位')
        mapViewer = new window.mapvgl.View({
            map: mapInstance
        })
        displayAllProjectsOnMap()
    }
}

onMounted(async () => {
    // 先获取项目数据
    await fetchProjects()

    // 获取积水监测数据（但不立即渲染到地图）
    await fetchWaterMonitorData()
    // 获取事件发生地点数据
    await fetchEventLocations()
    console.log('数据获取完成，等待地图初始化...')

    // 动态加载百度地图API
    const script = document.createElement('script')
    script.src = 'https://api.map.baidu.com/api?type=webgl&v=1.0&ak=KRTlSNEe8zfVZSsr88CUle86l1o0bTJI&callback=initBaiduMapCallback'
    script.async = true
    const script1 = document.createElement('script')
    script1.src = 'https://mapv.baidu.com/build/mapv.min.js'
    script1.async = true
    const script2 = document.createElement('script')
    script2.src = 'https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.189/dist/mapvgl.min.js'
    script2.async = true
    window.initBaiduMapCallback = () => {
        // 地图API加载完成后初始化地图，此时会自动调用displayAllProjectsOnMap()
        initBaiduMap()
    }
    document.head.appendChild(script)
    document.head.appendChild(script1)
    document.head.appendChild(script2)

    // 设置30分钟定时器，定期刷新所有地图数据
    refreshTimer = setInterval(
        () => {
            console.log('定时器触发，刷新地图数据...')
            refreshAllMapData()
        },
        30 * 60 * 1000
    )
    console.log('已设置30分钟定时器，定期刷新地图数据')
})

// 组件卸载时清理定时器
onUnmounted(() => {
    if (refreshTimer) {
        clearInterval(refreshTimer)
        refreshTimer = null
        console.log('已清理定时器')
    }
})
</script>

<style lang="scss" scoped>
::v-deep {
    .BMapLabel {
        &::after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: -30%;
            width: 12px;
            height: 9px;
            transform: translateX(-50%);
            background: url('@/assets/images/bigscreen/icons/sj.png');
            background-size: 100% 100%;
        }
    }
}
.main-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px;
    position: relative;

    .project-tabs {
        display: flex;
        margin-bottom: 10px;

        .loading-text {
            padding: 8px 15px;
            color: #0ff;
            font-size: 14px;
        }

        .tab {
            padding: 8px 15px;
            background-color: rgba(0, 100, 200, 0.3);
            border-radius: 4px;
            margin-right: 5px;
            cursor: pointer;
            font-size: 14px;

            &.active {
                background-color: rgba(0, 150, 255, 0.8);
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    right: 0;
                    border-width: 0 8px 8px 0;
                    border-style: solid;
                    border-color: transparent #ffcc00 transparent transparent;
                }
            }

            &:hover {
                background-color: rgba(0, 120, 220, 0.5);
            }
        }
    }

    .map-container {
        position: relative;
        margin-bottom: 10px;

        .map {
            width: 100%;
            height: calc(100vh - 180px);
            background-color: #0a1a2a;
            border-radius: 4px;
            overflow: hidden;
        }
        .map-mask {
            position: absolute;
            background-color: #011124;
            bottom: 0;
            left: 0;
            width: 83px;
            height: 54px;
            z-index: 999;
        }
    }

    .check-filter {
        position: absolute;
        top: 30px;
        right: 1920px;
        width: 260px;
        height: 134px;
        background: rgba(3, 16, 37, 0.9);
        border-radius: 0px 0px 0px 0px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 25px;
        z-index: 1000;

        .filter-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }

            input[type='checkbox'] {
                width: 20px;
                height: 20px;
                margin-right: 20px;
                cursor: pointer;
                accent-color: #00d9ff;
                background-color: transparent;
                border: 2px solid #ffffff;
                border-radius: 2px;
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;

                &:checked {
                    background-color: #00d9ff;
                    border-color: #00d9ff;
                    position: relative;

                    &::after {
                        content: '✓';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        color: #ffffff;
                        font-size: 12px;
                        font-weight: bold;
                    }
                }
            }

            img {
                width: 24px;
                height: 24px;
                margin-right: 8px;
            }

            span {
                color: #ffffff;
                font-size: 22px;
                font-weight: 500;
                white-space: nowrap;
            }
        }
    }
}
</style>
