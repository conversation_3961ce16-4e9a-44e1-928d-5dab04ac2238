// 基础类型导入
interface BaseEntity {
    createBy?: string
    createTime?: string
    updateBy?: string
    updateTime?: string
    remark?: string
}

interface PageQuery {
    pageNum?: number
    pageSize?: number
    orderByColumn?: string
    isAsc?: string
}

export interface EquipmentVO {
    /**
     *
     */
    id: string | number

    /**
     * 管理单元-id
     */
    unitId: string | number
    roomId: string | number
    /**
     *
     */
    name: string

    /**
     *
     */
    code: string

    /**
     * 自动生成的序列号
     */
    seq: number

    /**
     * 线路-id
     */
    roadwayId: string
    lineId: string

    /**
     * 编码类型，字典项：code_type
     */
    codeType: string

    /**
     * 时钟范围-bgn，字典项：时钟范围
     */
    timeArrangeBgn: string

    /**
     * 时钟范围-end，字典项：时钟范围
     */
    timeArrangeEnd: string
    arrangeBgn: string
    arrangeEnd: string

    /**
     * 起始里程（km）
     */
    bgnKilometer: string

    /**
     * 终止里程（km）
     */
    equipmentOwner: string

    /**
     * 终止里程
     */
    endKilometer: string

    /**
     *
     */
    modelId: string | number

    /**
     *
     */
    remark: string

    /**
     * 设备归属单位（设备专属字段），字典项：device_owner
     */
    owner: string

    /**
     * 安装时间（设备专属字段）
     */
    installDate: string

    /**
     * 品牌（设备专有字段），字典项：device_brands_new
     */
    brand: string

    /**
     *
     */
    categoryIdFirst: string | number

    /**
     *
     */
    categoryIdSecond: string | number

    /**
     *
     */
    categoryIdThird: string | number

    /**
     * 型号
     */
    specification: string

    /**
     *
     */
    images: string

    /**
     *
     */
    files: string

    /**
     *
     */
    projectId: string | number
    configedInspection: string
}

export interface EquipmentForm extends BaseEntity {
    /**
     *
     */
    id?: string | number

    /**
     * 管理单元-id
     */
    unitId?: string | number

    roomId?: string | number
    /**
     *
     */
    name?: string

    /**
     *
     */
    code?: string

    /**
     * 自动生成的序列号
     */
    seq?: number

    /**
     * 线路-id
     */
    roadwayId?: string | number

    /**
     * 编码类型，字典项：code_type
     */
    codeType?: string

    /**
     * 时钟范围-bgn，字典项：时钟范围
     */
    timeArrangeBgn?: string

    /**
     * 时钟范围-end，字典项：时钟范围
     */
    timeArrangeEnd?: string
    arrangeBgn?: string
    arrangeEnd?: string

    /**
     * 起始里程（km）
     */
    bgnKilometer?: string

    /**
     * 终止里程（km）
     */
    equipmentOwner?: string

    /**
     * 终止里程
     */
    endKilometer?: string

    /**
     *
     */
    modelId?: string | number

    /**
     *
     */
    remark?: string

    /**
     * 设备归属单位（设备专属字段），字典项：device_owner
     */
    owner?: string

    /**
     * 安装时间（设备专属字段）
     */
    installDate?: string

    /**
     * 品牌（设备专有字段），字典项：device_brands_new
     */
    brand?: string

    /**
     *
     */
    categoryIdFirst?: string | number

    /**
     *
     */
    categoryIdSecond?: string | number

    /**
     *
     */
    categoryIdThird?: string | number

    /**
     * 型号
     */
    specification?: string

    /**
     *
     */
    images?: string

    /**
     *
     */
    files?: string

    /**
     *
     */
    projectId?: string | number
}

export interface EquipmentQuery extends PageQuery {
    projectId: string
    /**
     * 管理单元-id
     */
    unitId?: string | number

    /**
     *
     */
    name?: string

    /**
     *
     */
    code?: string

    /**
     * 备注名称
     */
    remark?: string

    /**
     *
     */
    categoryIdFirst?: string | number

    /**
     *
     */
    categoryIdSecond?: string | number

    /**
     *
     */
    categoryIdThird?: string | number
    configedInspection: string
    kind: string
    categoryPath?: string

    /**
     * 专业类型
     */
    specialty?: string

    /**
     * 日期范围参数
     */
    params?: any
}

/**
 * 设备更换记录查询参数
 */
export interface EquipmentReplacementQuery extends PageQuery {
    /**
     * 项目ID
     */
    projectId: string

    /**
     * 设备ID
     */
    deviceId?: string

    /**
     * 品牌
     */
    brand?: string

    /**
     * 设备型号
     */
    specification?: string

    /**
     * 供应商
     */
    supplier?: string

    /**
     * 日期范围参数
     */
    params?: any
}

/**
 * 设备更换记录VO
 */
export interface EquipmentReplacementVO {
    /**
     * 主键ID
     */
    id: string

    /**
     * 项目ID
     */
    projectId: string

    /**
     * 设备ID
     */
    deviceId: string

    /**
     * 设备名称
     */
    deviceName: string

    /**
     * 品牌
     */
    brand: string

    /**
     * 设备型号
     */
    specification: string

    /**
     * 供应商
     */
    supplier: string

    /**
     * 已投入使用寿命（小时）
     */
    serviceLife: number

    /**
     * 更换时间
     */
    replacementDate: string

    /**
     * 更新人
     */
    updateBy: string

    /**
     * 更新时间
     */
    updateTime: string

    /**
     * 备注
     */
    remark?: string
}

/**
 * 设备更换记录表单
 */
export interface EquipmentReplacementForm extends BaseEntity {
    /**
     * 主键ID
     */
    id?: string

    /**
     * 项目ID
     */
    projectId?: string

    /**
     * 设备ID
     */
    deviceId?: string

    /**
     * 设备名称
     */
    deviceName?: string

    /**
     * 品牌
     */
    brand?: string

    /**
     * 设备型号
     */
    specification?: string

    /**
     * 供应商
     */
    supplier?: string

    /**
     * 已投入使用寿命（小时）
     */
    serviceLife?: number

    /**
     * 更换时间
     */
    replacementDate?: string

    /**
     * 备注
     */
    remark?: string
}
