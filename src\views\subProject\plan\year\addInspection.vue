<!-- 新增巡检计划
workloadComparison 工作量合计数比较
-->
<template>
    <div class="p-2">
        <!-- :model="form" :rules="rules" -->
        <el-form ref="taskDefineFormRef" label-width="160px" :rules="rules" :model="form">
            <el-card class="box-card">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>巡检计划</span>
                    </div>
                </template>
                <div class="text item">
                    <el-row :gutter="gutter" v-if="isTemp != '1'">
                        <el-col :span="24">
                            <el-form-item label="年份" prop="code">
                                <el-text>{{ form.year }}</el-text>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter" v-if="isTemp == '1'">
                        <el-col :span="8">
                            <el-form-item label="计划作业日期段" prop="dateRange">
                                <el-date-picker
                                    v-model="form.dateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD"
                                    :clearable="true"
                                    :picker-options="datePickerOptions"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="gutter">
                        <el-col :span="12">
                            <el-form-item label="专业类型" prop="speciality">
                                <el-select placeholder="请选择专业类型" v-model="form.speciality" :clearable="true">
                                    <el-option v-for="dict in tnl_specialty" :key="dict.value" :label="dict.label" :value="dict.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="巡检作业类型" prop="maintenanceContent">
                                <el-select
                                    placeholder="请选择巡检作业类型"
                                    v-model="form.maintenanceContent"
                                    :clearable="true"
                                    collapse-tags
                                    collapse-tags-tooltip
                                >
                                    <el-option v-for="dict in inspection_work_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!--临时巡检计划才会选择巡检形式-->
                    <el-row :gutter="gutter" v-if="isTemp == '1'">
                        <el-col :span="12">
                            <el-form-item label="巡检形式" prop="inspectionMode">
                                <el-select placeholder="请选择巡检形式" v-model="form.inspectionMode" :clearable="true">
                                    <el-option v-for="dict in inspection_mode" :key="dict.value" :label="dict.label" :value="dict.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 巡检路线选择 - 根据巡检形式控制显示 -->
                    <el-row :gutter="gutter" v-if="shouldShowInspectionLines">
                        <el-col :span="12">
                            <el-form-item label="巡检路线" prop="inspectionIds">
                                <el-tree-select
                                    v-model="selectedInspectionLines"
                                    :props="{ label: 'label', children: 'children' }"
                                    node-key="id"
                                    multiple
                                    :data="inspectionLineOptions"
                                    show-checkbox
                                    :filter-node-method="filterNode"
                                    placeholder="请选择巡检路线"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <!-- 设备选择 - 根据巡检形式控制显示 -->
                    <el-row :gutter="gutter" v-if="shouldShowDeviceSelection">
                        <el-col :span="12">
                            <el-form-item label="设备">
                                <div class="device-selection-area">
                                    <el-button @click="openDeviceSelectDialog" type="primary">选择设备</el-button>
                                    <div class="selected-devices" v-if="selectedDeviceList.length > 0">
                                        <el-tag
                                            v-for="device in selectedDeviceList"
                                            :key="device.id"
                                            closable
                                            @close="removeDevice(device)"
                                            style="margin: 4px 4px 0 0"
                                        >
                                            {{ device.name }}
                                        </el-tag>
                                    </div>
                                    <div class="device-count" v-if="selectedDeviceList.length > 0">已选择 {{ selectedDeviceList.length }} 个设备</div>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="gutter">
                        <el-col :span="12">
                            <el-form-item label="计划名称" prop="name">
                                <el-input v-model="form.name" placeholder="请输入年度计划名称" maxlength="50">
                                    <template #append>
                                        <el-button @click="handleGenerateName" type="primary" size="small"> 自动生成 </el-button>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 此处用频次控件 -->

                    <FrequencySelect
                        ref="frequencyRef"
                        :filterUnits="isTemp === '1' ? 'day' : 'month,week,day'"
                        :max-unit-value="isTemp == '1' ? 1 : undefined"
                        :frequency-selectable="isTemp == '1' ? false : true"
                        :unit-selectable="isTemp == '1' ? false : true"
                        v-model:unit="form.frequencyType"
                        v-model:unitValue="form.frequency"
                        plan-type="inspect"
                    />
                </div>
            </el-card>

            <el-card class="box-card">
                <div class="text item">
                    <el-row :gutter="gutter" justify="center">
                        <el-col :span="24" style="text-align: center">
                            <el-button type="primary" :loading="buttonLoading" :disabled="buttonLoading" @click="submitForm" class="submit-btn">
                                <template v-if="!buttonLoading">
                                    <el-icon class="submit-icon"><Check /></el-icon>
                                    保存
                                </template>
                                <template v-else>
                                    <el-icon class="loading-icon"><Loading /></el-icon>
                                    正在保存...
                                </template>
                            </el-button>
                        </el-col>
                    </el-row>
                </div>
            </el-card>
        </el-form>

        <!-- 设备选择对话框 -->
        <EquipmentSelectForInspectionDefine
            v-model:visible="deviceSelectDialogVisible"
            :selected-equipments="selectedDeviceList"
            :available-inspection-lines="getAvailableInspectionLinesForDevice()"
            :specialty-type="form.speciality"
            @confirm="handleDeviceSelectConfirm"
        />
    </div>
</template>

<script setup lang="ts">
import FrequencySelect from '../../components/Frequency/FrequencySelect.vue'
import EquipmentSelectForInspectionDefine from '@/views/subProject/components/DeviceSelector/EquipmentSelectForInspectionDefine.vue'
import { addTaskDefine, updateTaskDefine, getTaskDefine, addTempTaskDefine } from '@/api/plan/taskDefine'
import { getYearOfTaskYear } from '@/api/plan/taskYear'
import { TaskDefineQuery, TaskDefineForm } from '@/api/plan/taskDefine/types'
import { listInspectionLine } from '@/api/subProject/inspection/inspectionLine'
import { InspectionLineVO } from '@/api/subProject/inspection/inspectionLine/types'
import { ComponentInternalInstance, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { Check, Loading } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const { tnl_specialty, inspection_work_type, inspection_mode } = toRefs<any>(
    proxy?.useDict('tnl_specialty', 'inspection_work_type', 'inspection_mode')
)

const buttonLoading = ref(false)
const gutter = ref(50) //设置项目表单两列的距离
const inspectionLineOptions = ref<InspectionLineVO[]>([])
// 添加一个变量保存原始的巡检路线数据
const originalInspectionLineOptions = ref<InspectionLineVO[]>([])
const taskDefineFormRef = ref<ElFormInstance>()
const frequencyRef = ref() // 添加对组件的引用
const yearTaskId = ref<string>('')
const isTemp = ref('0') //是否是临时任务

// 日期选择器配置 - 限制只能选择从今天开始到一个月后的日期范围
const datePickerOptions = {
    disabledDate(time: Date) {
        const today = new Date()
        today.setHours(0, 0, 0, 0) // 设置为今天的开始时间

        const oneMonthLater = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate())
        oneMonthLater.setHours(23, 59, 59, 999) // 设置为一个月后的结束时间

        return time.getTime() < today.getTime() || time.getTime() > oneMonthLater.getTime()
    }
}
// 添加过滤函数
const filterNode = (node: InspectionLineVO) => {
    if (!form.value.speciality) return true
    return node.speciality === form.value.speciality
}

const selectedInspectionLineIdArray = ref<string[]>([])

/** 创建表单验证规则 */
const createValidationRules = () => ({
    name: [
        { required: true, message: '请输入计划名称', trigger: 'blur' },
        { max: 50, message: '计划名称不能超过50个字符', trigger: 'blur' }
    ],
    speciality: [{ required: true, message: '请选择专业类型', trigger: 'change' }],
    maintenanceContent: [{ required: true, message: '请选择巡检作业类型', trigger: 'change' }],
    frequencyType: [{ required: true, message: '请选择频次类型', trigger: 'change' }],
    frequency: [{ required: true, message: '请选择频次数值', trigger: 'change' }],
    dateRange: [{ required: true, message: '计划作业日期段不能为空', trigger: 'change' }],
    inspectionMode: [{ required: true, message: '巡检形式不能为空', trigger: 'change' }],
    inspectionIds: [
        {
            required: shouldShowInspectionLines.value,
            validator: (_: any, __: any, callback: any) => {
                if (shouldShowInspectionLines.value && (!selectedInspectionLines.value || selectedInspectionLines.value.length === 0)) {
                    callback(new Error('巡检路线不能为空'))
                } else {
                    callback()
                }
            },
            trigger: 'change'
        }
    ],
    deviceSelection: [
        {
            required: shouldShowDeviceSelection.value,
            validator: (_: any, __: any, callback: any) => {
                if (shouldShowDeviceSelection.value && (!selectedDeviceList.value || selectedDeviceList.value.length === 0)) {
                    callback(new Error('设备选择不能为空'))
                } else {
                    callback()
                }
            },
            trigger: 'change'
        }
    ]
})

// 响应式数组：巡检路线选择
const selectedInspectionLines = ref<string[]>([])

// 设备选择相关
const selectedDeviceList = ref<DeviceInfo[]>([])
const deviceSelectDialogVisible = ref(false)

// 设备信息接口
interface DeviceInfo {
    id: string
    name: string
    roomCode: string
    roomName: string
    unitId: string
    unitName: string
    deviceCategoryId: string
    inspectionLineId: string
    inspectionLineName: string
}

// 添加用户手动修改标识
const userModifiedName = ref(false)

// 计算属性：是否显示巡检路线选择
const shouldShowInspectionLines = computed(() => {
    if (isTemp.value !== '1') return true // 非临时计划始终显示
    const mode = form.value.inspectionMode
    return mode === 'byline' || mode === 'hybrid'
})

// 计算属性：是否显示设备选择
const shouldShowDeviceSelection = computed(() => {
    if (isTemp.value !== '1') return false // 非临时计划不显示
    const mode = form.value.inspectionMode
    return mode === 'bydevice' || mode === 'hybrid'
})

// 获取选中路线名称的辅助函数
const getInspectionLineNames = (lineIds: string[]): string[] => {
    const names: string[] = []

    // 递归查找路线名称的函数
    const findLineNames = (nodes: any[], targetIds: string[]) => {
        for (const node of nodes) {
            if (targetIds.includes(node.id)) {
                names.push(node.label)
            }
            if (node.children && node.children.length > 0) {
                findLineNames(node.children, targetIds)
            }
        }
    }

    findLineNames(inspectionLineOptions.value, lineIds)
    return names
}

// 获取作业类型名称的辅助函数
const getMaintenanceContentLabel = (value: string | string[]): string => {
    // 如果是数组，取第一个值
    const actualValue = Array.isArray(value) ? value[0] : value
    if (!actualValue) return ''

    const option = inspection_work_type.value.find((item: any) => item.value === actualValue)
    return option ? option.label : ''
}

// 自动生成计划名称的函数
const generatePlanName = () => {
    try {
        console.log('开始生成计划名称')

        // 只有在用户没有手动修改过名称时才自动生成
        if (userModifiedName.value) {
            console.log('用户已手动修改过名称，跳过自动生成')
            return
        }

        // 1. 获取选中的路线名称
        const lineNames = getInspectionLineNames(selectedInspectionLines.value)
        console.log('获取到的路线名称:', lineNames)

        // 2. 获取作业类型名称
        let contentLabel = '-'
        if (form.value.maintenanceContent) {
            contentLabel = getMaintenanceContentLabel(form.value.maintenanceContent)
        }
        console.log('获取到的作业类型名称:', contentLabel)

        // 3. 生成计划名称
        if (lineNames.length > 0 && contentLabel) {
            // 路线名称 + 作业类型
            const linesText = lineNames.join('/')
            form.value.name = `${linesText} ${contentLabel}`
        } else if (lineNames.length > 0) {
            // 只有路线名称
            form.value.name = lineNames.join('/')
        } else {
            // 清空计划名称
            form.value.name = ''
        }

        console.log('生成的计划名称:', form.value.name)
    } catch (error) {
        console.error('生成计划名称失败:', error)
    }
}

// 手动触发生成计划名称
const handleGenerateName = () => {
    console.log('手动触发生成计划名称')
    userModifiedName.value = false // 重置用户修改标识
    generatePlanName()
}

// 设备选择相关方法
const openDeviceSelectDialog = () => {
    deviceSelectDialogVisible.value = true
}

const removeDevice = (device: DeviceInfo) => {
    const index = selectedDeviceList.value.findIndex((item) => item.id === device.id)
    if (index > -1) {
        selectedDeviceList.value.splice(index, 1)
    }
}

const handleDeviceSelectConfirm = (devices: DeviceInfo[]) => {
    selectedDeviceList.value = devices
    deviceSelectDialogVisible.value = false
}

const getAvailableInspectionLinesForDevice = () => {
    // 如果是hybrid模式且已选择路线，则只返回已选择的路线
    if (form.value.inspectionMode === 'hybrid' && selectedInspectionLines.value.length > 0) {
        return originalInspectionLineOptions.value.filter((line) => selectedInspectionLines.value.includes(line.id.toString()))
    }
    // 否则返回所有可用路线
    return originalInspectionLineOptions.value
}

const initFormData: TaskDefineForm = {
    id: undefined,
    projectId: undefined,
    yearTaskId: undefined,
    name: undefined,
    taskType: undefined,
    speciality: undefined,
    year: undefined,
    frequencyType: undefined,
    frequency: undefined,
    frequencyData: undefined,
    bgnDate: undefined,
    endDate: undefined,
    tempTask: undefined,
    maintenanceContent: undefined,
    dateRange: undefined, // 添加日期段字段
    status: undefined,
    publishState: 'temp',
    taskResourceItems: [] // 🔥 添加taskResourceItems字段
}

const data = reactive<PageData<TaskDefineForm, TaskDefineQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        yearTaskId: undefined,
        name: undefined,
        taskType: undefined,
        speciality: undefined,
        year: undefined,
        frequencyType: undefined,
        frequency: undefined,
        maintenanceContent: undefined,
        status: undefined,
        params: {}
    },
    rules: createValidationRules()
})
const { queryParams, form, rules } = toRefs(data)

/**
 * 对taskResourceItems进行去重处理
 * 解决编辑时巡检路线重复显示的问题
 */
const deduplicateTaskResourceItems = (items: any[]) => {
    if (!items || items.length === 0) {
        return []
    }

    const uniqueItems: any[] = []
    const processedLineIds = new Set<string>()

    console.log('开始去重处理，原始数据数量:', items.length)

    items.forEach((item, index) => {
        console.log(`处理第${index + 1}项:`, {
            inspectionLineId: item.inspectionLineId,
            deviceId: item.deviceId,
            dataType: item.dataType
        })

        if (item.inspectionLineId) {
            // 巡检路线记录：去重，只保留第一次出现的
            if (!processedLineIds.has(item.inspectionLineId)) {
                uniqueItems.push({
                    inspectionLineId: item.inspectionLineId,
                    dataType: 'line',
                    projectId: item.projectId,
                    defineId: item.defineId,
                    // 保留其他可能需要的字段
                    unitId: item.unitId,
                    deviceCategoryId: item.deviceCategoryId
                })
                processedLineIds.add(item.inspectionLineId)
                console.log(`添加巡检路线: ${item.inspectionLineId}`)
            } else {
                console.log(`跳过重复的巡检路线: ${item.inspectionLineId}`)
            }
        } else if (item.deviceId) {
            // 单独设备记录：直接保留
            uniqueItems.push({
                deviceId: item.deviceId,
                dataType: 'device',
                projectId: item.projectId,
                defineId: item.defineId,
                unitId: item.unitId,
                deviceCategoryId: item.deviceCategoryId,
                inspectionLineId: item.inspectionLineId,
                // 保留设备相关的其他字段
                deviceName: item.deviceName
            })
            console.log(`添加单独设备: ${item.deviceId}`)
        }
    })

    console.log('去重完成，结果数量:', uniqueItems.length)
    return uniqueItems
}

/** 根据ID获取数据 */
const getInfo = async (id: string) => {
    try {
        const res = await getTaskDefine(id)

        // 🔥 对taskResourceItems进行去重处理
        if (res.data.taskResourceItems) {
            console.log('原始taskResourceItems数据:', res.data.taskResourceItems)
            res.data.taskResourceItems = deduplicateTaskResourceItems(res.data.taskResourceItems)
            console.log('taskResourceItems去重后:', res.data.taskResourceItems)
        } else {
            console.log('未找到taskResourceItems数据')
        }

        // 设置基础表单数据
        setFormData(res.data)

        // 初始化频次组件
        await initFrequencyComponent(res.data)

        // 处理巡检路线回填
        handleInspectionLines(res.data)

        // 处理设备选择回填
        handleDeviceSelection(res.data)

        console.log('编辑模式数据回填完成')
    } catch (error) {
        handleError(error, '获取详情失败')
    }
}

/** 设置表单基础数据 */
const setFormData = (data: any) => {
    form.value = {
        ...initFormData,
        id: data.id,
        projectId: data.projectId,
        yearTaskId: data.yearTaskId,
        name: data.name,
        taskType: data.taskType,
        speciality: data.speciality,
        year: data.year,
        frequencyType: data.frequencyType,
        frequency: data.frequency,
        frequencyData: data.frequencyData,
        maintenanceContent: data.maintenanceContent,
        status: data.status,
        // 🔥 将去重后的taskResourceItems赋值给表单
        taskResourceItems: data.taskResourceItems || []
    }

    console.log('表单数据设置完成，taskResourceItems数量:', form.value.taskResourceItems?.length || 0)
}

/** 初始化频次组件 */
const initFrequencyComponent = async (data: any) => {
    if (!frequencyRef.value || !data.frequencyData) return

    try {
        frequencyRef.value.setLoadingState(true)
        const frequencyData = JSON.parse(data.frequencyData)

        // 设置表格数据
        frequencyRef.value.tableData = frequencyData.tableData || []

        // 根据频次类型设置对应的调度数据
        setScheduleDataByType(data.frequencyType, frequencyData.scheduleData)

        // 设置频次单位和数值
        frequencyRef.value.unit = data.frequencyType
        frequencyRef.value.value = data.frequency

        await nextTick()
        frequencyRef.value.setLoadingState(false)
    } catch (error) {
        console.error('解析频次数据失败:', error)
        frequencyRef.value?.setLoadingState(false)
    }
}

/** 根据频次类型设置调度数据 */
const setScheduleDataByType = (frequencyType: string, scheduleData: any[]) => {
    if (!frequencyRef.value) return

    switch (frequencyType) {
        case 'day':
            frequencyRef.value.dayScheduleData = scheduleData || []
            break
        case 'week':
            frequencyRef.value.weekScheduleData = scheduleData || []
            break
        case 'month':
            frequencyRef.value.monthScheduleData = scheduleData || []
            break
    }
}

/** 处理巡检路线相关逻辑 */
const handleInspectionLines = (data: any) => {
    let inspectionLineIds: string[] = []

    // 使用taskResourceItems数据
    if (data.taskResourceItems && data.taskResourceItems.length > 0) {
        // 从taskResourceItems中提取巡检路线ID
        inspectionLineIds = data.taskResourceItems
            .filter((item: any) => item.dataType === 'line' && item.inspectionLineId)
            .map((item: any) => item.inspectionLineId.toString())
            .filter((id: string) => id.trim() !== '')

        console.log('从taskResourceItems获取巡检路线:', inspectionLineIds)
    }

    // 设置选中的巡检路线
    if (inspectionLineIds.length > 0) {
        selectedInspectionLineIdArray.value = inspectionLineIds
        selectedInspectionLines.value = inspectionLineIds

        console.log('成功回填巡检路线选择:', inspectionLineIds)
    } else {
        // 清空选择
        selectedInspectionLineIdArray.value = []
        selectedInspectionLines.value = []
        console.log('未找到巡检路线数据，清空选择')
    }

    // 根据专业类型过滤巡检路线
    filterInspectionLines(data.speciality)
}

/** 处理设备选择回填 */
const handleDeviceSelection = (data: any) => {
    if (data.taskResourceItems && data.taskResourceItems.length > 0) {
        // 从taskResourceItems中提取设备信息
        const deviceItems = data.taskResourceItems.filter((item: any) => item.dataType === 'device' && item.deviceId)

        if (deviceItems.length > 0) {
            console.log('从taskResourceItems获取设备信息:', deviceItems)

            // 构建设备选择数据结构
            const devices: DeviceInfo[] = deviceItems.map((item: any) => ({
                id: item.deviceId.toString(),
                name: item.deviceName || `设备${item.deviceId}`, // 使用设备名称或默认名称
                unitId: item.unitId?.toString() || '',
                deviceCategoryId: item.deviceCategoryId?.toString() || '',
                inspectionLineId: item.inspectionLineId?.toString() || ''
            }))

            selectedDeviceList.value = devices
            console.log('成功回填设备选择:', devices)
        } else {
            // 清空设备选择
            selectedDeviceList.value = []
            console.log('未找到设备数据，清空设备选择')
        }
    } else {
        // 清空设备选择
        selectedDeviceList.value = []
        console.log('taskResourceItems为空，清空设备选择')
    }
}

/** 统一错误处理 */
const handleError = (error: any, message: string) => {
    // 关闭加载提示
    proxy?.$modal.closeLoading()

    console.error(message, error)
    proxy?.$modal.msgError(message)
}

// 修改获取巡检路线列表的方法
const getInspectionLineList = async () => {
    try {
        const response = await listInspectionLine({
            prorjectId: appStore.projectContext.selectedProjectId
        })

        // 构建树形结构
        const treeData = buildTreeData(response.data || [])
        originalInspectionLineOptions.value = treeData

        console.log('加载巡检线路树形数据:', treeData)
        // 根据当前专业类型过滤
        filterInspectionLines(form.value.speciality)
    } catch (error) {
        console.error('获取巡检路线失败:', error)
        proxy?.$modal.msgError('获取巡检路线失败')
    }
}

// 构建树形数据结构
const buildTreeData = (data: any[]) => {
    const map = new Map()
    const roots: any[] = []

    // 先将所有节点放入map
    data.forEach((item) => {
        map.set(item.id, {
            id: item.id,
            label: item.name,
            level: item.level,
            speciality: item.speciality,
            parentId: item.parentId,
            children: [],
            isLeaf: item.level > 1 // level > 1 的为叶子节点（具体线路）
        })
    })

    // 构建父子关系
    data.forEach((item) => {
        const node = map.get(item.id)
        if (item.parentId && map.has(item.parentId)) {
            // 有父节点，添加到父节点的children中
            const parent = map.get(item.parentId)
            parent.children.push(node)
        } else {
            // 没有父节点，是根节点
            roots.push(node)
        }
    })

    return roots
}

// 添加专门的过滤方法
const filterInspectionLines = (speciality?: string) => {
    if (!speciality) {
        // 如果没有选择专业类型，显示所有巡检路线
        inspectionLineOptions.value = originalInspectionLineOptions.value
        return
    }

    // 根据专业类型过滤
    const filteredData = originalInspectionLineOptions.value
        .map((parentNode) => {
            // 检查该父节点下是否有符合专业类型的子节点
            const matchingChildren = parentNode.children?.filter((child) => child.speciality === speciality) || []

            if (matchingChildren.length > 0) {
                // 如果存在匹配的子节点，返回父节点和匹配的子节点
                return {
                    ...parentNode,
                    children: matchingChildren
                }
            } else {
                // 如果没有匹配的子节点，返回null（将被过滤掉）
                return null
            }
        })
        .filter((node) => node !== null) // 过滤掉null值

    inspectionLineOptions.value = filteredData
}

/** 准备taskResourceItems数据 */
const prepareTaskResourceItems = () => {
    const resourceItems: any[] = []

    // 处理巡检路线
    if (shouldShowInspectionLines.value && selectedInspectionLines.value.length > 0) {
        selectedInspectionLines.value.forEach((lineId) => {
            resourceItems.push({
                inspectionLineId: lineId,
                dataType: 'line',
                projectId: appStore.projectContext.selectedProjectId,
                defineId: form.value.id || undefined,
                taskId: undefined,
                unitId: undefined,
                deviceCategoryId: undefined,
                deviceId: undefined,
                roadId: undefined,
                roadName: undefined,
                startStake: undefined,
                endStake: undefined,
                jobIdList: undefined
            })
        })
    }

    // 处理设备
    if (shouldShowDeviceSelection.value && selectedDeviceList.value.length > 0) {
        selectedDeviceList.value.forEach((device) => {
            resourceItems.push({
                inspectionLineId: device.inspectionLineId,
                deviceId: device.id,
                deviceCategoryId: device.deviceCategoryId,
                unitId: device.unitId,
                dataType: 'device',
                projectId: appStore.projectContext.selectedProjectId,
                defineId: form.value.id || undefined,
                taskId: undefined,
                roadId: undefined,
                roadName: undefined,
                startStake: undefined,
                endStake: undefined,
                jobIdList: undefined
            })
        })
    }

    form.value.taskResourceItems = resourceItems
}

/** 提交按钮 */
const submitForm = () => {
    taskDefineFormRef.value?.validate(async (valid: boolean) => {
        if (!valid) return
        if (isTemp.value == '1') {
            form.value.bgnDate = form.value.dateRange[0]
            form.value.endDate = form.value.dateRange[1]
            form.value.tempTask = 'YES'
        } else {
            form.value.bgnDate = null
            form.value.endDate = null
            form.value.tempTask = 'NO'
        }
        try {
            buttonLoading.value = true

            // 显示开始提交的提示
            proxy?.$modal.loading('正在保存巡检计划，请稍候...')

            // 准备taskResourceItems数据
            prepareTaskResourceItems()
            // 准备提交数据
            prepareSubmitData()

            // 执行提交
            await executeSubmit()

            // 提交成功处理
            handleSubmitSuccess()
        } catch (error) {
            handleError(error, '操作失败')
        } finally {
            buttonLoading.value = false
        }
    })
}

/** 准备提交数据 */
const prepareSubmitData = () => {
    // 设置基础信息
    setBasicFormData()

    // 设置频次数据
    setFrequencyData()
}

/** 设置基础表单数据 */
const setBasicFormData = () => {
    form.value.taskType = 'inspect'
    form.value.frequencyType = frequencyRef.value?.unit
    form.value.frequency = frequencyRef.value?.value
    form.value.year = getYearOfTaskYear()
    form.value.projectId = appStore.projectContext.selectedProjectId
}

/** 设置频次数据 */
const setFrequencyData = () => {
    const frequencyType = frequencyRef.value?.unit
    if (!frequencyType) return

    const frequencyDataMap = {
        'day': () => ({
            tableData: frequencyRef.value?.tableData,
            scheduleData: frequencyRef.value?.dayScheduleData
        }),
        'week': () => ({
            tableData: frequencyRef.value?.tableData,
            scheduleData: frequencyRef.value?.weekScheduleData
        }),
        'month': () => ({
            tableData: frequencyRef.value?.tableData,
            scheduleData: frequencyRef.value?.monthScheduleData
        })
    }

    const dataGenerator = frequencyDataMap[frequencyType]
    if (dataGenerator) {
        form.value.frequencyData = JSON.stringify(dataGenerator())
    }
}

/** 执行提交操作 */
const executeSubmit = async () => {
    if (form.value.id) {
        await updateTaskDefine(form.value)
    } else {
        if (isTemp.value == '1') {
            //form.value.tempTask = '1';
            //提交临时巡检计划
            await addTempTaskDefine(form.value)
        } else {
            await addTaskDefine(form.value)
        }
    }
}

/** 提交成功处理 */
const handleSubmitSuccess = () => {
    // 关闭加载提示
    proxy?.$modal.closeLoading()

    proxy?.$modal.msgSuccess('操作成功')
    router.push({
        path: '/subProject/circle/plan/defineList',
        query: { yearTaskId: yearTaskId.value }
    })
}

onMounted(async () => {
    try {
        // 先加载所有基础数据
        await getInspectionLineList()

        form.value.year = getYearOfTaskYear()
        // // 从路由参数中获取 yearTaskId 和 id
        const queryId = route.query.yearTaskId
        const id = route.query.id
        yearTaskId.value = Array.isArray(queryId) ? queryId[0] : queryId || ''
        isTemp.value = Array.isArray(route.query.isTemp) ? route.query.isTemp[0] : route.query.isTemp || '0'
        if (yearTaskId.value) {
            form.value.yearTaskId = yearTaskId.value
        }

        // 如果有ID，则获取数据
        if (id) {
            await getInfo(id as string)
        }
    } catch (error) {
        console.error('初始化失败:', error)
        proxy?.$modal.msgError('加载数据失败')
    }
})

// 监听用户手动修改计划名称
watch(
    () => form.value.name,
    (newVal, oldVal) => {
        // 如果是用户手动修改（不是自动生成），设置标识
        // 需要确保不是初始化时的变化，且当前焦点在输入框上
        if (newVal !== oldVal && oldVal !== undefined && document.activeElement?.tagName === 'INPUT') {
            userModifiedName.value = true
            console.log('用户手动修改了计划名称:', newVal)
        }
    }
)

// 监听巡检路线选择变化
watch(
    () => selectedInspectionLines.value,
    (newVal) => {
        console.log('巡检路线选择变化:', newVal)

        // 自动生成计划名称
        generatePlanName()
    },
    { deep: true, immediate: false }
)

// 监听巡检作业类型变化
watch(
    () => form.value.maintenanceContent,
    (newVal) => {
        console.log('巡检作业类型变化:', newVal)
        // 自动生成计划名称
        generatePlanName()
    }
)

// 监听专业类型变化，重置用户修改标识
watch(
    () => form.value.speciality,
    (newVal, oldVal) => {
        if (newVal !== oldVal) {
            // 专业类型改变时，重置用户修改标识，允许重新自动生成名称
            userModifiedName.value = false
            // 清空巡检路线选择
            selectedInspectionLines.value = []
            // 清空设备选择
            selectedDeviceList.value = []
        }
        // 过滤巡检路线
        filterInspectionLines(newVal)
    }
)

// 监听巡检形式变化
watch(
    () => form.value.inspectionMode,
    (newMode) => {
        // 清空相关选择
        if (newMode === 'byline') {
            selectedDeviceList.value = []
        } else if (newMode === 'bydevice') {
            selectedInspectionLines.value = []
        }
        // hybrid模式保持现有选择
    }
)
</script>

<style scoped>
.device-selection-area {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.selected-devices {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
}

.device-count {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
}
</style>

<style lang="scss" scoped>
.box-card {
    margin-top: 10px;
}

.submit-btn {
    min-width: 120px;
    height: 40px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
        opacity: 0.8;
        cursor: not-allowed;
        transform: scale(0.98);
    }

    .submit-icon {
        margin-right: 6px;
        font-size: 16px;
        transition: transform 0.2s ease;
    }

    .loading-icon {
        margin-right: 6px;
        font-size: 16px;
        animation: spin 1s linear infinite;
    }

    &:hover .submit-icon {
        transform: scale(1.1);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

// 添加脉冲动画效果
.submit-btn.is-loading {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
    }
}
</style>
