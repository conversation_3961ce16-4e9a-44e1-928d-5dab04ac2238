<template>
    <div v-if="visible" class="dialog-overlay" @click.self="$emit('close')">
        <div class="typhoon-dialog">
            <div class="dialog-header">
                <h3>台风信息</h3>
                <button class="close-btn" @click="$emit('close')">✕</button>
            </div>

            <div class="dialog-content">
                <div class="info-grid">
                    <div class="info-item">
                        <label>时间</label>
                        <span>{{ formatTime(typhoonData.time) }}</span>
                    </div>

                    <div class="info-item">
                        <label>纬度</label>
                        <span>{{ typhoonData.lat }}°</span>
                    </div>

                    <div class="info-item">
                        <label>经度</label>
                        <span>{{ typhoonData.lon }}°</span>
                    </div>

                    <div class="info-item">
                        <label>强度等级</label>
                        <span class="type-badge" :class="getTypeClass(typhoonData.type)">{{ getTypeName(typhoonData.type) }}</span>
                    </div>

                    <div class="info-item">
                        <label>中心气压</label>
                        <span>{{ typhoonData.pressure }} hPa</span>
                    </div>

                    <div class="info-item">
                        <label>最大风速</label>
                        <span>{{ typhoonData.windSpeed }} m/s</span>
                    </div>

                    <div class="info-item">
                        <label>移动速度</label>
                        <span>{{ typhoonData.moveSpeed }} km/h</span>
                    </div>

                    <div class="info-item">
                        <label>移动方向</label>
                        <span>{{ getMoveDirection(typhoonData.moveDir) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import dayjs from 'dayjs'

// Props
interface Props {
    visible: boolean
    typhoonData: any
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    typhoonData: () => ({})
})

// Emits
const emit = defineEmits<{
    close: []
}>()

// 格式化时间
const formatTime = (time: string) => {
    if (!time) return '--'
    return dayjs(time).format('YYYY年MM月DD日 HH:mm')
}

// 获取台风类型名称
const getTypeName = (type: string) => {
    const typeMap: Record<string, string> = {
        TD: '热带低压',
        TS: '热带风暴',
        STS: '强热带风暴',
        TY: '台风',
        STY: '强台风',
        SuperTY: '超强台风'
    }
    return typeMap[type] || type
}

// 获取台风类型样式类
const getTypeClass = (type: string) => {
    return `type-${type?.toLowerCase()}`
}

// 获取移动方向
const getMoveDirection = (dir: string) => {
    const dirMap: Record<string, string> = {
        N: '北',
        NE: '东北',
        E: '东',
        SE: '东南',
        S: '南',
        SW: '西南',
        W: '西',
        NW: '西北'
    }
    return dirMap[dir] || dir
}
</script>

<style lang="scss" scoped>
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.typhoon-dialog {
    background: linear-gradient(135deg, rgba(11, 43, 55, 0.95) 0%, rgba(3, 16, 37, 0.95) 100%);
    border: 1px solid rgba(0, 217, 255, 0.3);
    border-radius: 12px;
    padding: 0;
    min-width: 480px;
    max-width: 90vw;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);

    .dialog-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 25px;
        border-bottom: 1px solid rgba(0, 217, 255, 0.2);
        background: rgba(0, 217, 255, 0.05);
        border-radius: 12px 12px 0 0;

        h3 {
            margin: 0;
            color: #00d9ff;
            font-size: 18px;
            font-weight: 600;
            text-shadow: 0 0 10px rgba(0, 217, 255, 0.3);
        }

        .close-btn {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &:hover {
                background: rgba(255, 255, 255, 0.1);
                color: #ffffff;
                transform: scale(1.1);
            }
        }
    }

    .dialog-content {
        padding: 25px;

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;

            .info-item {
                display: flex;
                flex-direction: column;
                gap: 8px;

                label {
                    color: rgba(255, 255, 255, 0.7);
                    font-size: 13px;
                    font-weight: 500;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                span {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: 600;

                    &.type-badge {
                        display: inline-block;
                        padding: 4px 12px;
                        border-radius: 20px;
                        font-size: 14px;
                        font-weight: 700;
                        text-align: center;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

                        &.type-td {
                            background: linear-gradient(45deg, rgba(79, 255, 219, 0.2), rgba(79, 255, 219, 0.4));
                            border: 1px solid rgba(79, 255, 219, 0.6);
                            color: rgb(79, 255, 219);
                        }

                        &.type-ts {
                            background: linear-gradient(45deg, rgba(254, 242, 0, 0.2), rgba(254, 242, 0, 0.4));
                            border: 1px solid rgba(254, 242, 0, 0.6);
                            color: rgb(254, 242, 0);
                        }

                        &.type-sts {
                            background: linear-gradient(45deg, rgba(246, 122, 25, 0.2), rgba(246, 122, 25, 0.4));
                            border: 1px solid rgba(246, 122, 25, 0.6);
                            color: rgb(246, 122, 25);
                        }

                        &.type-ty {
                            background: linear-gradient(45deg, rgba(243, 0, 2, 0.2), rgba(243, 0, 2, 0.4));
                            border: 1px solid rgba(243, 0, 2, 0.6);
                            color: rgb(243, 0, 2);
                        }

                        &.type-sty {
                            background: linear-gradient(45deg, rgba(243, 26, 148, 0.2), rgba(243, 26, 148, 0.4));
                            border: 1px solid rgba(243, 26, 148, 0.6);
                            color: rgb(243, 26, 148);
                        }

                        &.type-superty {
                            background: linear-gradient(45deg, rgba(196, 114, 230, 0.2), rgba(196, 114, 230, 0.4));
                            border: 1px solid rgba(196, 114, 230, 0.6);
                            color: rgb(196, 114, 230);
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 600px) {
    .typhoon-dialog {
        min-width: 90vw;
        margin: 20px;

        .dialog-content .info-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }
    }
}
</style>
