import request from '@/utils/request'
import { AxiosPromise } from 'axios'
import { ResourceViewVO, ResourceForm, ResourceViewQuery, CheckResourceExistsRequest, BatchImportResourceRequest } from '@/api/common/resource/types'

/**
 * 查询物资信息列表
 * @param query
 * @returns {*}
 */

export const listResource = (query?: ResourceViewQuery): AxiosPromise<ResourceViewVO[]> => {
    return request({
        url: '/common/resource/listview',
        method: 'get',
        params: query
    })
}

/**
 * 查询物资信息详细
 * @param id
 */
export const getResourceView = (id: string | number): AxiosPromise<ResourceViewVO> => {
    return request({
        url: '/common/resource/v/' + id,
        method: 'get'
    })
}

/**
 * 新增物资信息
 * @param data
 */
export const addResource = (data: ResourceForm) => {
    return request({
        url: '/common/resource',
        method: 'post',
        data: data
    })
}

/**
 * 修改物资信息
 * @param data
 */
export const updateResource = (data: ResourceForm) => {
    return request({
        url: '/common/resource',
        method: 'put',
        data: data
    })
}

/**
 * 删除物资信息
 * @param id
 */
export const delResource = (id: string | number | Array<string | number>) => {
    return request({
        url: '/common/resource/' + id,
        method: 'delete'
    })
}

/**
 * 检查物资重复
 * @param data
 */
export const checkResourceExists = (data: CheckResourceExistsRequest) => {
    return request({
        url: '/common/resource/checkExists',
        method: 'post',
        data: data
    })
}

/**
 * 批量导入物资
 * @param data
 */
export const batchImportResource = (data: BatchImportResourceRequest) => {
    return request({
        url: '/common/resource/batchImport',
        method: 'post',
        data: data
    })
}
