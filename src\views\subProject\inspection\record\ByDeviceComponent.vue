<!-- 按巡检设备查询组件 -->
<template>
    <div class="p-2 inspection-device-page">
        <!-- 原有的设备列表 -->
        <div v-show="!showDeviceConfig">
            <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
                <div v-show="showSearch" class="mb-[10px]">
                    <el-card shadow="hover">
                        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                            <el-form-item label="管理单元" prop="unitId">
                                <el-select v-model="queryParams.unitId" placeholder="请选择管理单元" clearable @change="handleUnitChange">
                                    <el-option v-for="unit in unitOptions" :key="unit.id" :label="unit.name" :value="unit.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="房间" prop="roomId">
                                <el-select v-model="queryParams.roomId" placeholder="请选择房间" clearable>
                                    <el-option v-for="room in roomOptions" :key="room.id" :label="room.roomNane" :value="room.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="设备分类" prop="categoryPath">
                                <el-tree-select
                                    v-model="selectedQueryCategoryId"
                                    :data="categoryOptions"
                                    :props="{ value: 'id', label: 'name', children: 'children' }"
                                    value-key="id"
                                    placeholder="请选择设备系统分类"
                                    clearable
                                    check-strictly
                                    @change="handleCategoryChange"
                                    style="width: 300px"
                                />
                            </el-form-item>
                            <el-form-item class="filter-actions">
                                <el-button type="primary" @click="handleQuery">搜索</el-button>
                                <el-button @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>
                </div>
            </transition>
            <el-card shadow="never">
                <template #header>
                    <div class="card-header">
                        <span>巡检设备列表</span>
                        <div>
                            <el-button link type="primary" icon="Search" @click="showSearch = !showSearch">搜索</el-button>
                        </div>
                    </div>
                </template>
                <el-table v-loading="loading" stripe :data="deviceList" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column label="设备名称" align="center" prop="remark" min-width="120" />
                    <el-table-column label="设备编码" align="center" prop="code" min-width="120" />
                    <el-table-column label="机电分系统" align="center" prop="firstLevelCategory" min-width="120" />
                    <el-table-column label="机电子系统" align="center" prop="secondLevelCategory" min-width="120" />
                    <el-table-column label="设备类型" align="center" prop="thirdLevelCategory" min-width="120" />
                    <el-table-column label="管理单元名称" align="center" prop="unitName" min-width="120" />
                    <el-table-column label="房间名称" align="center" prop="roomName" min-width="120" />
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
                        <template #default="scope">
                            <div class="op-actions">
                                <el-button
                                    link
                                    type="primary"
                                    class="op-link op-info"
                                    @click="handleView(scope.row)"
                                >
                                    <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="巡检项" />
                                    巡检项
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination
                    v-show="total > 0"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    :total="total"
                    @pagination="getList"
                />
            </el-card>
        </div>

        <!-- 设备巡检配置组件 -->
        <DeviceConfigedInspectionItemsComponent
            v-show="showDeviceConfig"
            :device-info="selectedDevice"
            @back="handleBackToDeviceList"
            @view-records="handleViewInspectionRecords"
        />
    </div>
</template>

<script setup name="ByDeviceComponent" lang="ts">
import { ref, onMounted, getCurrentInstance, ComponentInternalInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { listDevice } from '@/api/subProject/inspection/inspectionByDeviceView'
import { InspectionByDeviceViewVO, InspectionByDeviceViewVOQuery } from '@/api/subProject/inspection/inspectionByDeviceView/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { listRoom } from '@/api/project/room'
import { RoomVO } from '@/api/project/room/types'
import { listCategory, getCategoryHierarchyNames } from '@/api/common/category'
import { CategoryVO } from '@/api/common/category/types'
import { useAppStore } from '@/store/modules/app'
import DeviceConfigedInspectionItemsComponent from './DeviceConfigedInspectionItemsComponent.vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const appStore = useAppStore()

const deviceList = ref<InspectionByDeviceViewVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 设备分类选择
const selectedQueryCategoryId = ref('')

// 设备配置组件状态
const showDeviceConfig = ref(false)
const selectedDevice = ref<InspectionByDeviceViewVO | null>(null)

// 查询参数
const queryParams = ref<InspectionByDeviceViewVOQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: '',
    unitId: undefined,
    roomId: undefined,
    categoryPath: undefined
})

// 选项数据
const unitOptions = ref<ManageUnitVO[]>([])
const roomOptions = ref<RoomVO[]>([])
const categoryOptions = ref<CategoryVO[]>([])

// 查找分类的辅助函数
const findCategoryById = (list: any[], id: string | number): any => {
    for (const item of list) {
        if (item.id === id) return item
        if (item.children) {
            const found = findCategoryById(item.children, id)
            if (found) return found
        }
    }
    return null
}

const queryFormRef = ref()

/** 查询设备列表 */
const getList = async () => {
    loading.value = true
    try {
        // 设置项目ID
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
        // queryParams.value.tempTask = ''  // 该属性在当前类型中不存在
        const response = await listDevice(queryParams.value)

        // 处理返回数据
        if (response) {
            let rawDeviceList: InspectionByDeviceViewVO[] = []

            if (response.rows) {
                // 分页数据格式
                rawDeviceList = response.rows
                total.value = response.total
            } else if (Array.isArray(response.rows)) {
                // 数组格式
                rawDeviceList = response.rows
                total.value = response.rows.length
            }

            // 异步处理设备数据，解析分类信息
            if (rawDeviceList.length > 0) {
                const processedDevices = await Promise.all(rawDeviceList.map((device) => processDeviceData(device)))
                deviceList.value = processedDevices
            } else {
                deviceList.value = []
            }
        } else {
            deviceList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('获取设备列表失败:', error)
        ElMessage.error('获取设备列表失败')
        deviceList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

/** 处理设备数据，解析分类信息 */
const processDeviceData = async (device: InspectionByDeviceViewVO) => {
    let firstLevelCategory = ''
    let secondLevelCategory = ''
    let thirdLevelCategory = ''

    // 如果有三级分类ID，通过API获取层级名称
    if (device.categoryIdThird) {
        try {
            const response = await getCategoryHierarchyNames(device.categoryIdThird)
            if (response && response.data && Array.isArray(response.data)) {
                const hierarchyNames = response.data
                // 层级名称数组：[祖父节点名称, 父节点名称, 当前节点名称]
                if (hierarchyNames.length >= 3) {
                    firstLevelCategory = hierarchyNames[0] || '' // 机电分系统（一级）
                    secondLevelCategory = hierarchyNames[1] || '' // 机电子系统（二级）
                    thirdLevelCategory = hierarchyNames[2] || '' // 设备类型（三级）
                } else if (hierarchyNames.length === 2) {
                    secondLevelCategory = hierarchyNames[0] || ''
                    thirdLevelCategory = hierarchyNames[1] || ''
                } else if (hierarchyNames.length === 1) {
                    thirdLevelCategory = hierarchyNames[0] || ''
                }
            }
        } catch (error) {
            console.warn('获取分类层级名称失败:', error)
            // 降级处理：解析 categoryPath
            if (device.categoryPath) {
                const pathArray = device.categoryPath.split('/')
                if (pathArray.length >= 1) firstLevelCategory = pathArray[0]
                if (pathArray.length >= 2) secondLevelCategory = pathArray[1]
                if (pathArray.length >= 3) thirdLevelCategory = pathArray[2]
            }
        }
    } else if (device.categoryPath) {
        // 降级处理：解析 categoryPath
        const pathArray = device.categoryPath.split('/')
        if (pathArray.length >= 1) firstLevelCategory = pathArray[0]
        if (pathArray.length >= 2) secondLevelCategory = pathArray[1]
        if (pathArray.length >= 3) thirdLevelCategory = pathArray[2]
    }

    return {
        ...device,
        firstLevelCategory,
        secondLevelCategory,
        thirdLevelCategory
    }
}

/** 获取管理单元列表 */
const getUnitList = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) return

        const response = await listProjectManageUnit(projectId)
        unitOptions.value = Array.isArray(response.data) ? response.data : []
    } catch (error) {
        console.error('获取管理单元列表失败:', error)
        unitOptions.value = []
    }
}

/** 管理单元变化时获取房间列表 */
const handleUnitChange = async (unitId: string | number) => {
    roomOptions.value = []
    queryParams.value.roomId = undefined

    if (!unitId) return

    try {
        const projectId = appStore.projectContext.selectedProjectId
        const response = await listRoom({
            projectId: projectId,
            unitId: unitId,
            pageNum: 1,
            pageSize: 1000
        })

        // 处理返回数据 - 修复类型错误
        if (response && response.rows) {
            roomOptions.value = Array.isArray(response.rows) ? response.rows : []
        }
    } catch (error) {
        console.error('获取房间列表失败:', error)
        roomOptions.value = []
    }
}

/** 设备分类变化时处理 categoryPath */
const handleCategoryChange = () => {
    if (!selectedQueryCategoryId.value) {
        queryParams.value.categoryPath = undefined
    } else {
        const selectedCategory = findCategoryById(categoryOptions.value, selectedQueryCategoryId.value)
        if (selectedCategory && selectedCategory.path) {
            queryParams.value.categoryPath = selectedCategory.path
        }
    }
}

/** 获取设备分类树 */
const getCategoryTree = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) return

        // 获取机电设备分类列表
        const response = await listCategory({
            projectId: projectId,
            kind: 'equipment'
        })

        if (response && response.data) {
            // 使用 handleTree 构建树形结构
            const treeData = proxy?.handleTree<CategoryVO>(response.data, 'id', 'parentId')
            categoryOptions.value = treeData || []
        } else {
            categoryOptions.value = []
        }
    } catch (error) {
        console.error('获取设备分类树失败:', error)
        categoryOptions.value = []
    }
}

/** 搜索按钮操作 */
const handleQuery = () => {
    // 处理分类路径
    if (!selectedQueryCategoryId.value) {
        queryParams.value.categoryPath = undefined
    } else {
        const selectedCategory = findCategoryById(categoryOptions.value, selectedQueryCategoryId.value)
        if (selectedCategory && selectedCategory.path) {
            queryParams.value.categoryPath = selectedCategory.path
        }
    }

    queryParams.value.pageNum = 1

    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    selectedQueryCategoryId.value = ''
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
        projectId: appStore.projectContext.selectedProjectId,
        unitId: undefined,
        roomId: undefined,
        categoryPath: undefined
    }
    roomOptions.value = []
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: InspectionByDeviceViewVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length !== 1
    multiple.value = !selection.length
}

/** 查看设备巡检配置 */
const handleView = (row: InspectionByDeviceViewVO) => {
    console.log('选中的设备信息:', row)

    // 确保设备信息完整
    if (!row.id) {
        ElMessage.error('设备信息不完整，无法查看配置项')
        return
    }

    selectedDevice.value = row
    showDeviceConfig.value = true
}

/** 返回设备列表 */
const handleBackToDeviceList = () => {
    showDeviceConfig.value = false
    selectedDevice.value = null
}

/** 查看巡检记录 */
const handleViewInspectionRecords = (configItem: any) => {
    // 跳转到巡检记录详情页面或打开弹窗
    ElMessage.info(`查看巡检记录: ${configItem.configName}`)
    console.log('查看巡检记录:', configItem)
}

/** 初始化 */
onMounted(() => {
    getUnitList()
    getCategoryTree()
    getList()
})
</script>

<style lang="scss" scoped>
.inspection-device-page {

    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-tree-select__wrapper),
    :deep(.el-date-editor .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder),
    :deep(.el-tree-select__placeholder) {
        color: #8291A9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)),
    :deep(.el-tree-select .el-tree-select__selected-item > span:not(.el-tree-select__placeholder)) {
        color: #FFFFFF !important;
    }

    :deep(.el-select__wrapper.is-focused),
    :deep(.el-tree-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 树形选择器特殊样式 */
    :deep(.el-tree-select) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-tree-select__inner) {
        color: #FFFFFF !important;
        background: transparent !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #AED7F2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #FFFFFF !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 操作列图标按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286F3 !important;
    }

    .op-edit {
        color: #42F3E9 !important;
    }

    .op-delete {
        color: #D62121 !important;
    }

    /* 头部卡片标题样式 */
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}
</style>
