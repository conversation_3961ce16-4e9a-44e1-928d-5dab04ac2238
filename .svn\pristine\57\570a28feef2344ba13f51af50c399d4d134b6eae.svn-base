<!-- 封道作业流程表单 -->
<template>
    <div class="p-2" style="margin-top: -10px">
        <el-form ref="projectFormRef" :model="form" :rules="rules" label-width="160px">
            <el-card class="box-card">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>封道信息</span>
                    </div>
                </template>
                <!-- 作业单任务基本信息 -->

                <BaseInfoSealing :id="taskId" from="task" />
            </el-card>
            <TaskDelayHistory :taskId="taskId" />
            <el-card class="box-card">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>作业进度</span>
                    </div>
                </template>
                <div class="text item">
                    <el-form ref="projectFormRef" :model="form" :rules="rules" label-width="160px">
                        <div v-if="form.task.currentStatus == 'START'">
                            <el-form-item label="作业开始时间" prop="task.bgnDate" label-width="160px">
                                <el-date-picker
                                    v-model="form.task.bgnDate"
                                    type="datetime"
                                    style="width: 200px"
                                    placeholder="请选择作业开始时间"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                />
                            </el-form-item>
                            <el-form-item label="作业结束时间" prop="task.endDate" label-width="160px">
                                <el-date-picker
                                    v-model="form.task.endDate"
                                    type="datetime"
                                    style="width: 200px"
                                    placeholder="请选择作业结束时间"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                    :disabled-date="disabledEndDate"
                                    :disabled="!form.task.bgnDate"
                                />
                            </el-form-item>
                            <el-form-item label="封道类型" prop="assignSealing.roadType" label-width="160px">
                                <el-radio-group v-model="form.assignSealing.roadType">
                                    <el-radio v-for="dict in sealing_type" :key="dict.value" :value="dict.value">
                                        {{ dict.label }}
                                    </el-radio>
                                </el-radio-group>
                            </el-form-item>

                            <!-- 封闭线路和位置列表 - 根据封道类型动态显示 -->
                            <template v-if="form.assignSealing.roadType === 'full' || form.assignSealing.roadType === 'part'">
                                <div v-for="(item, index) in form.assignSealing.sealingItems" :key="index" style="margin-bottom: 16px">
                                    <el-form-item label-width="0" class="align-label-right">
                                        <div style="display: flex; align-items: center; gap: 8px; width: 100%">
                                            <span v-if="index === 0" style="width: 80px; text-align: right; flex-shrink: 0; margin-left: 68px"
                                                >封闭线路</span
                                            >
                                            <span v-else style="width: 80px; flex-shrink: 0; margin-left: 68px"></span>
                                            <el-tree-select
                                                v-model="item.roadId"
                                                :data="getFilteredLineTreeForIndex(index)"
                                                placeholder="请选择线路/车道"
                                                check-strictly
                                                :render-after-expand="false"
                                                node-key="id"
                                                :props="{ value: 'id', label: 'name', children: 'children', disabled: 'disabled' }"
                                                @change="(value) => handleLineChange(value, item, index)"
                                                style="width: 200px"
                                            />
                                            <template v-if="form.assignSealing.roadType === 'part'">
                                                <span style="width: 80px; text-align: right; flex-shrink: 0">封闭位置</span>
                                                <el-select
                                                    v-model="item.startStake"
                                                    placeholder="起点桩号（里程号）"
                                                    style="width: 120px"
                                                    filterable
                                                    remote
                                                    :remote-method="(query) => queryStakeOptions(query, item)"
                                                    :loading="stakeLoading"
                                                >
                                                    <el-option
                                                        v-for="option in stakeOptions"
                                                        :key="option.value"
                                                        :label="option.label"
                                                        :value="option.value"
                                                    />
                                                </el-select>
                                                <span style="color: #909399; font-size: 14px; white-space: nowrap">至</span>
                                                <el-select
                                                    v-model="item.endStake"
                                                    placeholder="终点桩号（里程号）"
                                                    style="width: 120px"
                                                    filterable
                                                    remote
                                                    :remote-method="(query) => queryStakeOptions(query, item)"
                                                    :loading="stakeLoading"
                                                >
                                                    <el-option
                                                        v-for="option in stakeOptions"
                                                        :key="option.value"
                                                        :label="option.label"
                                                        :value="option.value"
                                                    />
                                                </el-select>
                                            </template>
                                            <el-tooltip :content="item.roadId ? '点击关联作业' : '请先选择线路/车道'" placement="top">
                                                <el-button type="primary" plain size="small" :disabled="!item.roadId" @click="openJobDrawer(index)"
                                                    >关联作业</el-button
                                                >
                                            </el-tooltip>
                                            <el-button v-if="index === 0" type="success" icon="plus" size="small" @click="addSealingPosition"
                                                >封闭线路/车道</el-button
                                            >
                                            <el-button
                                                v-if="index > 0"
                                                type="danger"
                                                icon="delete"
                                                size="small"
                                                @click="removeSealingPosition(index)"
                                                plain
                                                >删除</el-button
                                            >
                                        </div>
                                    </el-form-item>
                                </div>
                            </template>

                            <el-form-item label="安全员" prop="assignSealing.safeManId" label-width="160px">
                                <ManagersSelector
                                    v-model="form.assignSealing.safeManId"
                                    post-code="aqy"
                                    placeholder="请选择安全员"
                                    style="width: 200px"
                                />
                            </el-form-item>
                            <el-form-item label="带班负责人" prop="nextAssignee.nextAssignees" label-width="160px">
                                <ManagersSelector v-model="form.nextAssignee.nextAssignees" placeholder="请选择带班负责人" style="width: 200px" />
                            </el-form-item>
                            <el-form-item label="班组" prop="assignSealing.teams" label-width="160px">
                                <TeamSelector v-model="form.assignSealing.teams" multiple placeholder="请选择班组" style="width: 200px" />
                            </el-form-item>
                            <el-form-item label="封道物资" label-width="160px">
                                <div class="resource-section">
                                    <div v-for="(resource, index) in form.assignSealing.resourceList" :key="index" class="resource-item">
                                        <div class="resource-row">
                                            <!-- 物资选择 -->
                                            <el-col :span="8">
                                                <el-select v-model="resource.resourceId" placeholder="选择物资" style="width: 100%">
                                                    <el-option
                                                        v-for="type in resourceTypes"
                                                        :key="type.value"
                                                        :label="type.label"
                                                        :value="type.value"
                                                    />
                                                </el-select>
                                            </el-col>

                                            <!-- 数量输入 -->
                                            <el-col :span="4" style="margin-left: 10px">
                                                <el-input-number v-model="resource.quantity" :min="1" :max="9999" style="width: 100%" />
                                            </el-col>

                                            <!-- 单位显示 -->
                                            <el-col :span="4" style="margin-left: 10px; text-align: center">
                                                <span class="unit-text">{{ getResourceUnit(resource.resourceId) }}</span>
                                            </el-col>

                                            <!-- 删除按钮 -->
                                            <el-col :span="4" style="margin-left: 10px">
                                                <el-button v-if="index > 0" type="danger" size="small" @click="removeResourceItem(index)" plain>
                                                    删除
                                                </el-button>
                                            </el-col>
                                        </div>
                                    </div>

                                    <!-- 添加物资按钮 -->
                                    <div class="add-resource" style="margin-top: 10px">
                                        <el-button type="success" size="small" @click="addResourceItem"> + 添加物资 </el-button>
                                    </div>
                                </div>
                            </el-form-item>
                        </div>
                        <!-- 确认环节 -->
                        <div v-if="form.task.currentStatus == 'Confirm'">
                            <el-row :gutter="gutter">
                                <el-col :span="7">
                                    <el-form-item label="防撞车驾驶人" prop="assignSealing.driverId" label-width="160px">
                                        <el-select v-model="form.assignSealing.driverId" placeholder="请输入防撞车驾驶人">
                                            <el-option v-for="user in safetyMembers" :key="user.userId" :label="user.nickName" :value="user.userId" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                        <!-- 安全交底 -->
                        <div v-if="form.task.currentStatus == 'Safety_Briefing'">
                            <el-row :gutter="gutter">
                                <el-col :span="24">
                                    <el-form-item label="安全交底图片" prop="safeBriefImages">
                                        <imageUpload v-model="form.assignSealing.safeBriefImages" @update:modelValue="handleSafeUploadChange" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                        <div v-if="form.task.currentStatus == 'Execute' || form.task.currentStatus == 'accept_unsealing'">
                            <el-row :gutter="gutter">
                                <el-row :gutter="gutter">
                                    <el-col :span="24">
                                        <!-- <el-form-item label="安全交底图片" prop="safeImages">
                                            <image-upload />
                                        </el-form-item> -->
                                        <div style="display: flex; justify-content: center; align-items: center">
                                            <el-alert type="info" show-icon :closable="false" style="width: 200px">
                                                <template #icon>
                                                    <el-icon><Iphone /></el-icon>
                                                </template>
                                                请在移动设备操作
                                            </el-alert>
                                        </div>
                                    </el-col>
                                </el-row>
                            </el-row>
                        </div>

                        <!-- 执行拆封 -->
                        <div v-if="false">
                            <el-row :gutter="gutter">
                                <el-col :span="7">
                                    <el-form-item label="防撞车" prop="assignSealing.cars" label-width="160px">
                                        <CarSelector v-model="form.assignSealing.cars" multiple placeholder="请选择车辆" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                        <div class="auditOption" v-show="form.task.currentStatus == 'Second_Acceptance'">
                            <!-- @todo 某些环节不需要审批意见，用v-show控制-->
                            <el-row :gutter="gutter">
                                <el-col :span="12">
                                    <el-form-item label="是否确认" prop="nextAssignee.wfOperation" label-width="160px">
                                        <el-radio-group v-model="form.nextAssignee.wfOperation">
                                            <el-radio v-for="option in approvalOptions" :key="option.value" :value="option.value">
                                                {{ option.label }}
                                            </el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="gutter">
                                <el-col :span="12">
                                    <el-form-item label="意见" label-width="160px">
                                        <el-input v-model="form.nextAssignee.opinion" type="textarea" placeholder="请输入内容" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                        <!-- 当form.nextAssignee.businessData.CANCEL_OPTION == 'CANCEL'时，后端会启动取消流程
                    @todo 这里的表单要做控制
                    -->
                        <!-- <div>
                            <el-form-item label="取消原因">
                                <el-input v-model="form.nextAssignee.businessData.CANCEL_OPTION" type="textarea"
                                    placeholder="请输入内容" />
                            </el-form-item>
                        </div> -->
                        <div class="text item">
                            <el-row justify="center">
                                <el-col style="text-align: center; margin-top: 20px">
                                    <!-- START状态：显示提交和变更计划日期按钮 -->
                                    <template v-if="form.task.currentStatus == 'START'">
                                        <el-button type="primary" :loading="submitLoading" @click="handleSubmit" style="margin-right: 12px"
                                            >提交</el-button
                                        >
                                    </template>
                                    <!-- 其他状态：仅显示提交按钮 -->
                                    <template v-else>
                                        <el-button
                                            v-if="form.task.currentStatus != 'Execute' && form.task.currentStatus != 'accept_unsealing'"
                                            type="primary"
                                            :loading="submitLoading"
                                            @click="handleSubmit"
                                            >提交</el-button
                                        >
                                    </template>
                                    <el-button
                                        v-if="form.task.currentStatus == 'Execute'"
                                        type="primary"
                                        :loading="submitLoading"
                                        @click="handleChangePlanDate"
                                        >变更计划日期</el-button
                                    >
                                </el-col>
                            </el-row>
                        </div>
                    </el-form>
                </div>
            </el-card>
            <el-card class="box-card">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>审批进度</span>
                    </div>
                </template>
                <WorkflowInfo :business-id="taskId" exclude-activity-codes="START,Confirm" />
            </el-card>
        </el-form>

        <!-- 关联作业选择组件 -->
        <TaskSelect
            v-model="jobDrawerVisible"
            :selected-job-ids="getCurrentSelectedJobIds()"
            :current-index="currentSealingIndex"
            @confirm="handleJobConfirm"
        />

        <!-- 变更计划日期组件 -->
        <ChangeTaskDate
            v-model="changePlanDateVisible"
            :project-id="appStore.projectContext.selectedProjectId"
            :task-id="taskId"
            :current-date="form.task.bgnDate"
            taskType="sealing"
            @success="handleChangePlanDateSuccess"
            @navigate-to-list="handleNavigateToList"
        />
    </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/store/modules/app'
import { ref, reactive, getCurrentInstance, toRefs, onMounted, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { AssignSealingFlowForm, AssignSealingFlowVO, AssignSealingRoadVO } from '@/api/plan/assignSealing/types'
import { sealingAssign } from '@/api/plan/task'
import { getTaskAssignmentByTaskId } from '@/api/plan/assignSealing'
import { useRoute, useRouter } from 'vue-router'
import BaseInfoSealing from '../../components/BaseInfoSealing.vue'
import WorkflowInfo from '../../components/Workflow/WorkflowInfo.vue'
import TaskSelect from './TaskSelect.vue'
import ChangeTaskDate from '../../components/ChangeTaskDate.vue'
import TeamSelector from '../../components/TeamSelector/index.vue'
import ManagersSelector from '../../components/ManagersSelector/index.vue'
import CarSelector from '../../components/CarSelector/index.vue'
import TaskDelayHistory from '../../components/TaskDelayHistory.vue'
import { LineTreeVO } from '@/api/project/project/types'

import { getProjectLineTree } from '@/api/project/project'

import { listResourceType } from '@/api/common/resourceType'
import { ResourceTypeVO, ResourceTypeQuery } from '@/api/common/resourceType/types'
import { Iphone } from '@element-plus/icons-vue'
import { listCode } from '@/api/subProject/basic/code'

const route = useRoute()
const router = useRouter()
const gutter = ref(50)
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const appStore = useAppStore()

const { sealing_type, tnl_resource_unit } = toRefs<any>(proxy?.useDict('sealing_type', 'tnl_resource_unit'))

// 获取路由参数
const taskId = ref((route.query.taskId as string) || '')

// 审核结果选项
const approvalOptions = ref([
    { label: '通过', value: 'APPROVE' },
    { label: '退回', value: 'ROLLBACK' } //,
    // @todo 处理延期
    // { label: '延期', value: 'SUSPENDED' }
])

// 表单数据
// 表单数据 - 提供完整的初始化
const form = reactive<AssignSealingFlowForm>({
    task: {
        tempTask: '',
        tempResourceId: ''
    },
    assignSealing: {
        taskId: taskId.value,
        roadContent: '',
        cars: [] as string[],
        resourceList: [
            // 物资列表
            {
                resourceId: '',
                quantity: 1
            }
        ],
        // 封闭线路/位置列表（初始为空，根据封道类型动态添加）
        sealingItems: [] as AssignSealingRoadVO[]
    } as any,
    nextAssignee: {
        nextAssignees: undefined, // 单选模式，初始化为undefined
        wfOperation: 'APPROVE', // 添加审批状态字段
        businessData: {} // 初始化businessData对象
    }
})

// 表单验证规则
const rules = reactive({
    'task.bgnDate': [{ required: true, message: '请选择作业开始时间', trigger: 'change' }],
    'task.endDate': [{ required: true, message: '请选择作业结束时间', trigger: 'change' }],
    'assignSealing.roadType': [{ required: true, message: '请选择封道类型', trigger: 'change' }],
    'assignSealing.safeManId': [{ required: true, message: '请选择安全员', trigger: 'change' }],
    'assignSealing.managerId': [{ required: true, message: '请选择带班负责人', trigger: 'change' }],
    'assignSealing.teams': [{ required: true, message: '请选择班组', trigger: 'change' }],
    'assignSealing.driverId': [{ required: true, message: '请输入防撞车驾驶人', trigger: 'change' }],
    'nextAssignee.nextAssignees': [{ required: true, message: '请选择带班负责人', trigger: 'change' }],
    'nextAssignee.wfOperation': [{ required: true, message: '请选择意见', trigger: 'change' }]
})

// 数据列表
const stakeOptions = ref<any[]>([])
const stakeLoading = ref(false)
const lineTreeVos = ref<LineTreeVO[]>([])
const resourceTypes = ref<Array<{ value: string; label: string; unit?: string }>>([])
const loading = ref(false)
const projectFormRef = ref()
const submitLoading = ref(false)

// 关联作业抽屉相关数据
const jobDrawerVisible = ref(false)
const currentSealingIndex = ref(-1) // 当前操作的封闭项索引

// 变更计划日期相关数据
const changePlanDateVisible = ref(false)

// 根据封堵类型动态过滤线路树数据
const filteredLineTreeVos = computed(() => {
    if (!form.assignSealing.roadType) {
        return lineTreeVos.value
    }

    if (form.assignSealing.roadType === 'full') {
        // 全封闭：仅显示第一级节点，移除所有子节点
        return lineTreeVos.value.map((node) => ({
            ...node,
            children: undefined // 移除子节点，使其不可展开
        }))
    } else if (form.assignSealing.roadType === 'part') {
        // 部分封闭：显示第一级和第二级节点
        return lineTreeVos.value
    }

    return lineTreeVos.value
})

// 获取已选择的线路/车道ID列表
const getSelectedRoadIds = () => {
    const assignSealing = form.assignSealing as any
    if (!assignSealing.sealingItems || !Array.isArray(assignSealing.sealingItems)) {
        return []
    }

    return assignSealing.sealingItems.map((item: AssignSealingRoadVO) => item.roadId).filter((id: string) => id && id.trim() !== '')
}

// 为特定索引的选择框获取过滤后的线路树数据（排除已选择的选项）
const getFilteredLineTreeForIndex = (currentIndex: number) => {
    const selectedIds = getSelectedRoadIds()
    const assignSealing = form.assignSealing as any
    const currentItem = assignSealing.sealingItems?.[currentIndex]
    const currentSelectedId = currentItem?.roadId

    // 需要禁用的ID列表（已被其他选择框选中的ID）
    const idsToDisable = selectedIds.filter((id: string, index: number) => {
        // 排除当前选择框自己选中的ID
        const itemIndex = assignSealing.sealingItems.findIndex((item: AssignSealingRoadVO, idx: number) => item.roadId === id && idx !== currentIndex)
        return itemIndex !== -1
    })

    // 递归函数：禁用已选择的节点
    const disableSelectedNodes = (nodes: LineTreeVO[]): any[] => {
        return nodes.map((node) => {
            const isDisabled = idsToDisable.includes(node.id)
            const newNode: any = {
                ...node,
                disabled: isDisabled
            }

            // 如果有子节点，递归处理
            if (node.children && Array.isArray(node.children)) {
                newNode.children = disableSelectedNodes(node.children)
            }

            return newNode
        })
    }

    // 应用封堵类型过滤
    const baseFilteredData = filteredLineTreeVos.value

    // 应用已选择项目的禁用逻辑
    return disableSelectedNodes(baseFilteredData)
}

const refreshLineTree = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        console.log('获取项目线路树，项目ID:', projectId)

        const res = await getProjectLineTree(projectId)
        console.log('API返回的线路树数据:', res)

        // 清空原有数据
        lineTreeVos.value = []

        // 如果返回的数据有children（第二级节点），则以第二级节点作为顶级节点
        if (res.data && res.data.children && res.data.children.length > 0) {
            lineTreeVos.value = res.data.children
            console.log('使用第二级节点作为顶级节点:', lineTreeVos.value)
        } else {
            // 如果没有子节点，则使用原数据
            lineTreeVos.value = [res.data]
            console.log('没有子节点，使用原始数据:', lineTreeVos.value)
        }

        console.log('最终的线路树数据:', lineTreeVos.value)
    } catch (error) {
        console.error('获取项目线路树失败:', error)
        ElMessage.warning('获取线路数据失败')
        lineTreeVos.value = []
    }
}

// 提交封道任务分配
const handleSubmit = async () => {
    loading.value = true
    try {
        // 表单验证
        const valid = await projectFormRef.value?.validate()
        if (!valid) {
            return
        }
        // 处理sealingItems
        if (form.assignSealing.sealingItems && Array.isArray(form.assignSealing.sealingItems)) {
            const sealingItems = form.assignSealing.sealingItems
            if (sealingItems.length > 0) {
                // 清理sealingItems中的无用字段
                const cleanedSealingItems = sealingItems.map((item) => ({
                    roadId: item.roadId,
                    roadName: item.roadName,
                    startStake: item.startStake,
                    endStake: item.endStake,
                    jobIdList: item.jobIdList,
                    sealingImages: item.sealingImages,
                    unsealingImages: item.unsealingImages
                }))
                // 序列化为roadContent
                form.assignSealing.roadContent = JSON.stringify(cleanedSealingItems)
            } else {
                form.assignSealing.roadContent = ''
            }
            // 删除sealingItems字段，避免发送到后端
            delete form.assignSealing.sealingItems
        }

        // 准备提交数据
        //@todo 指定分配环节的人员
        submitLoading.value = true

        const processedForm = {
            ...form
        }

        // 将teams数组转换为字符串
        if (processedForm.assignSealing.teams && Array.isArray(processedForm.assignSealing.teams)) {
            processedForm.assignSealing.teams = processedForm.assignSealing.teams.join(',')
            console.log('将teams数组转换为字符串:', processedForm.assignSealing.teams)
        }

        // 将cars数组转换为字符串
        if (processedForm.assignSealing.cars && Array.isArray(processedForm.assignSealing.cars)) {
            processedForm.assignSealing.cars = processedForm.assignSealing.cars.join(',')
            console.log('将cars数组转换为字符串:', processedForm.assignSealing.cars)
        }

        // 将resourceList数组转换为字符串
        if (processedForm.assignSealing.resourceList && Array.isArray(processedForm.assignSealing.resourceList)) {
            processedForm.assignSealing.resourceList = processedForm.assignSealing.resourceList.join(',')
            console.log('将resourceList数组转换为字符串:', processedForm.assignSealing.resourceList)
        }

        switch (form.task.currentStatus) {
            // 指派环节需要指定处理人
            case 'START':
                processedForm.nextAssignee = {
                    ...form.nextAssignee,
                    nextAssignees: form.nextAssignee?.nextAssignees
                        ? Array.isArray(form.nextAssignee.nextAssignees)
                            ? form.nextAssignee.nextAssignees
                            : [form.nextAssignee.nextAssignees]
                        : []
                }

                console.log('processedForm:', processedForm)
                break
            default:
                // 其他环节不需要指定处理人
                processedForm.nextAssignee = {
                    ...form.nextAssignee,
                    nextAssignees: []
                }
                break
        }
        // 调用API提交
        await sealingAssign(processedForm)

        proxy?.$modal.msgSuccess('封道任务分配成功')

        // 返回列表页或关闭页面

        router.push('sealing')
    } catch (error) {
        console.error('封道任务分配失败:', error)
        proxy?.$modal.msgError('封道任务分配失败，请重试')
    } finally {
        loading.value = false
        submitLoading.value = false
    }
}
/** 处理安全交底图片上传变化 */
const handleSafeUploadChange = (ossIds: string) => {
    form.assignSealing.safeBriefImages = ossIds
}
// 获取物资类型数据
const getResourceTypes = async () => {
    try {
        const queryParams: ResourceTypeQuery = {
            pageNum: 1,
            pageSize: 1000 // 获取足够多的数据
        }
        const res = await listResourceType(queryParams)
        const resourceData = Array.isArray(res) ? res : res.rows || res.data || []

        resourceTypes.value = resourceData.map((item: ResourceTypeVO) => ({
            value: item.id,
            label: item.name,
            unit: item.unit
        }))
    } catch (error) {
        resourceTypes.value = []
        ElMessage.warning('获取物资类型数据失败')
    }
}

// 获取资源单位的辅助方法
const getResourceUnit = (resourceId: string) => {
    const resource = resourceTypes.value.find((resource) => resource.value === resourceId)
    const originalUnit = resource?.unit || ''

    // 如果原单位为空，返回空字符串
    if (!originalUnit) {
        return ''
    }

    // 从数据字典中查找对应的中文单位名称
    const unitItem = tnl_resource_unit.value?.find((item: any) => item.value === originalUnit)

    // 如果数据字典中有对应的中文名称，返回中文名称；否则返回原单位
    return unitItem?.label || originalUnit
}

// 物资管理方法
const addResourceItem = () => {
    // 防御性检查，确保resourceList存在且为数组
    if (!form.assignSealing.resourceList || !Array.isArray(form.assignSealing.resourceList)) {
        form.assignSealing.resourceList = []
    }

    form.assignSealing.resourceList.push({
        resourceId: '',
        quantity: 1
    })
}

const removeResourceItem = (index: number) => {
    if (form.assignSealing.resourceList && form.assignSealing.resourceList.length > 1) {
        form.assignSealing.resourceList.splice(index, 1)
    }
}

// 获取任务分配数据
const getTaskAssignmentData = async () => {
    try {
        // 设置加载标志，避免触发时间验证
        console.log('开始加载任务数据，设置isLoadingData = true')
        isLoadingData.value = true

        const response = await getTaskAssignmentByTaskId(taskId.value)
        if (response.data) {
            const assignmentData: AssignSealingFlowVO = response.data

            // 初始化form数据
            if (assignmentData.task) {
                console.log('准备赋值任务数据:', {
                    bgnDate: assignmentData.task.bgnDate,
                    endDate: assignmentData.task.endDate,
                    isLoadingData: isLoadingData.value
                })
                Object.assign(form.task, assignmentData.task)
                console.log('任务信息赋值完成:', {
                    id: assignmentData.task.id,
                    name: assignmentData.task.name,
                    taskType: assignmentData.task.taskType,
                    bgnDate: assignmentData.task.bgnDate,
                    endDate: assignmentData.task.endDate
                })
            }

            if (assignmentData.assignSealing) {
                Object.assign(form.assignSealing, assignmentData.assignSealing)

                // 如果roadContent包含JSON数据，解析为sealingItems数组
                if (assignmentData.assignSealing.roadContent) {
                    try {
                        const parsedItems = JSON.parse(assignmentData.assignSealing.roadContent)
                        if (Array.isArray(parsedItems)) {
                            // 为每个项目添加线路名称（如果还没有的话）
                            const itemsWithNames = parsedItems.map((item: any) => {
                                if (!item.roadName && item.roadId) {
                                    item.roadName = findRoadNameById(item.roadId, lineTreeVos.value)
                                }
                                return item
                            })
                            ;(form.assignSealing as any).sealingItems = itemsWithNames
                            console.log('从roadContent解析得到sealingItems:', itemsWithNames)
                        }
                    } catch (error) {
                        console.warn('解析roadContent失败:', error)
                        ;(form.assignSealing as any).sealingItems = []
                    }
                } else {
                    // 如果没有roadContent数据，初始化空数组
                    ;(form.assignSealing as any).sealingItems = []
                }

                // 如果teams是字符串，解析为数组
                if (assignmentData.assignSealing.teams && typeof assignmentData.assignSealing.teams === 'string') {
                    ;(form.assignSealing as any).teams = assignmentData.assignSealing.teams.split(',').filter((team) => team.trim() !== '')
                    console.log('从teams字符串解析得到数组:', (form.assignSealing as any).teams)
                } else if (!assignmentData.assignSealing.teams) {
                    // 如果没有teams数据，初始化空数组
                    ;(form.assignSealing as any).teams = []
                }

                // 如果cars是字符串，解析为数组
                if (assignmentData.assignSealing.cars && typeof assignmentData.assignSealing.cars === 'string') {
                    ;(form.assignSealing as any).cars = assignmentData.assignSealing.cars.split(',').filter((car) => car.trim() !== '')
                    console.log('从cars字符串解析得到数组:', (form.assignSealing as any).cars)
                } else if (!assignmentData.assignSealing.cars) {
                    // 如果没有cars数据，初始化空数组
                    ;(form.assignSealing as any).cars = []
                }

                // 如果resourceList是字符串，解析为数组
                if (assignmentData.assignSealing.resourceList && typeof assignmentData.assignSealing.resourceList === 'string') {
                    ;(form.assignSealing as any).resourceList = assignmentData.assignSealing.resourceList
                        .split(',')
                        .filter((resource) => resource.trim() !== '')
                    console.log('从resourceList字符串解析得到数组:', (form.assignSealing as any).resourceList)
                } else if (!assignmentData.assignSealing.resourceList) {
                    // 如果没有resourceList数据，初始化默认数组
                    ;(form.assignSealing as any).resourceList = [
                        {
                            resourceId: '',
                            quantity: 1
                        }
                    ]
                }

                console.log('封道分配信息:', {
                    id: assignmentData.assignSealing.id,
                    taskId: assignmentData.assignSealing.taskId,
                    roadType: assignmentData.assignSealing.roadType,
                    safeManId: assignmentData.assignSealing.safeManId,
                    managerId: assignmentData.assignSealing.managerId,
                    teams: (form.assignSealing as any).teams,
                    roadContent: assignmentData.assignSealing.roadContent,
                    sealingItems: (form.assignSealing as any).sealingItems
                })
            }

            console.log('获取任务分配数据成功:', assignmentData)
        }
    } catch (error) {
        console.error('获取任务分配数据失败:', error)
        ElMessage.warning('获取任务数据失败，将使用默认数据')
    } finally {
        // 延迟重置加载标志，确保所有异步操作完成后再恢复时间验证
        console.log('数据加载完成，延迟设置isLoadingData = false')
        setTimeout(() => {
            isLoadingData.value = false
            console.log('延迟重置完成，isLoadingData = false')
        }, 100)
    }
}

// 根据roadId递归查找线路名称
const findRoadNameById = (roadId: string | number, treeNodes: LineTreeVO[]): string => {
    if (!roadId || !treeNodes || treeNodes.length === 0) {
        return ''
    }

    for (const node of treeNodes) {
        // 检查当前节点
        if (node.id === roadId || node.id === String(roadId)) {
            return node.name
        }

        // 递归检查子节点
        if (node.children && node.children.length > 0) {
            const foundName = findRoadNameById(roadId, node.children)
            if (foundName) {
                return foundName
            }
        }
    }

    return ''
}

// 新增封闭线路/位置行
const addSealingPosition = () => {
    const assignSealing = form.assignSealing as any
    if (!assignSealing.sealingItems) {
        assignSealing.sealingItems = [] as AssignSealingRoadVO[]
    }

    // 根据封道类型决定新增的数据结构
    const newItem: AssignSealingRoadVO = {
        roadId: '',
        startStake: '',
        endStake: '',
        jobIdList: []
    }

    assignSealing.sealingItems.push(newItem)
}

// 删除封闭线路/位置行
const removeSealingPosition = (index: number) => {
    const assignSealing = form.assignSealing as any
    if (assignSealing.sealingItems && assignSealing.sealingItems.length > 1) {
        assignSealing.sealingItems.splice(index, 1)
    }
}

// 关联作业相关方法
// 打开作业选择抽屉
const openJobDrawer = (index: number) => {
    const assignSealing = form.assignSealing as any
    const currentItem = assignSealing.sealingItems?.[index]

    // 验证是否已选择线路/车道
    if (!currentItem || !currentItem.roadId) {
        ElMessage.warning('请先选择封闭线路/车道')
        return
    }

    currentSealingIndex.value = index
    jobDrawerVisible.value = true
    console.log(`打开作业选择抽屉，封闭项索引: ${index}, 线路ID: ${currentItem.roadId}`)
}

// 获取当前选中的作业ID列表
const getCurrentSelectedJobIds = (): string[] => {
    if (currentSealingIndex.value >= 0) {
        const assignSealing = form.assignSealing as any
        const currentItem = assignSealing.sealingItems?.[currentSealingIndex.value]
        return currentItem?.jobIdList || []
    }
    return []
}

// 处理作业确认选择
const handleJobConfirm = (selectedJobs: any[], index: number) => {
    console.log('处理作业确认选择:', { selectedJobs, index })

    if (index >= 0) {
        const assignSealing = form.assignSealing as any
        const currentItem = assignSealing.sealingItems?.[index]

        if (currentItem) {
            // 将选中的作业ID和详细信息保存到jobIdList
            currentItem.jobIdList = selectedJobs.map((job) => job.id)
            // 可选：保存作业的详细信息用于显示
            currentItem.linkedJobs = selectedJobs.map((job) => ({
                id: job.id,
                name: job.jobName,
                type: job.jobType,
                status: job.jobStatus
            }))

            console.log(`封闭项 ${index} 关联的作业:`, {
                jobIdList: currentItem.jobIdList,
                linkedJobs: currentItem.linkedJobs
            })

            ElMessage.success(`成功为封闭项 ${index + 1} 关联 ${selectedJobs.length} 个作业`)
        }
    }

    // 重置当前索引
    currentSealingIndex.value = -1
}

// 变更计划日期相关方法
// 打开变更计划日期对话框
const handleChangePlanDate = () => {
    changePlanDateVisible.value = true
}

// 处理变更计划日期成功
const handleChangePlanDateSuccess = (newDate: string) => {
    // 更新本地数据
    form.task.bgnDate = newDate
    console.log('计划日期已更新为:', newDate)
}

// 处理跳转到列表页面
const handleNavigateToList = () => {
    router.push('/subProject/circle/maintain/sealing')
}

// 监听封道类型变化，调整数据结构
watch(
    () => form.assignSealing.roadType,
    (newType, oldType) => {
        const assignSealing = form.assignSealing as any
        if (!assignSealing.sealingItems) {
            assignSealing.sealingItems = [] as AssignSealingRoadVO[]
        }

        // 如果没有数据，初始化一行
        if (assignSealing.sealingItems.length === 0) {
            const newItem: AssignSealingRoadVO = {
                roadId: '',
                startStake: '',
                endStake: '',
                jobIdList: []
            }

            assignSealing.sealingItems.push(newItem)
        } else {
            // 如果已有数据，更新现有项的结构
            assignSealing.sealingItems.forEach((item: any) => {
                // 当封堵类型发生变化时，清除已选择的线路ID，因为可选层级发生了变化
                if (oldType && oldType !== newType) {
                    item.roadId = ''
                    console.log('封堵类型变化，清除已选择的线路')
                }

                if (newType === 'part') {
                    // 切换到部分封闭，添加位置字段
                    if (!item.hasOwnProperty('startStake')) {
                        item.startStake = ''
                    }
                    if (!item.hasOwnProperty('endStake')) {
                        item.endStake = ''
                    }
                }
                // 注意：不删除已存在的字段，以免数据丢失
            })
        }

        console.log(`封堵类型从 ${oldType} 切换到 ${newType}，线路树数据已更新`)
    }
)

// 在 script setup 部分添加 disabledEndDate 函数
const disabledEndDate = (time: Date) => {
    if (!form.task.bgnDate) {
        return true
    }

    const startDate = new Date(form.task.bgnDate)
    const startDay = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate())
    const nextDay = new Date(startDay)
    nextDay.setDate(nextDay.getDate() + 1)
    const dayAfterNext = new Date(startDay)
    dayAfterNext.setDate(dayAfterNext.getDate() + 2)

    return time < startDate || time >= dayAfterNext
}

// 标记是否正在加载数据，避免在数据加载时触发时间验证
const isLoadingData = ref(false)

// 监听开始时间变化，如果结束时间不在允许范围内，则清空结束时间
watch(
    () => form.task.bgnDate,
    (newStartDate, oldStartDate) => {
        console.log('开始时间变化监听器触发:', {
            newStartDate,
            oldStartDate,
            isLoadingData: isLoadingData.value,
            endDate: form.task.endDate
        })

        // 如果正在加载数据，跳过验证
        if (isLoadingData.value) {
            console.log('正在加载数据，跳过时间验证')
            return
        }

        // 如果是初始化时的数据加载（oldStartDate为undefined），且时间数据看起来是从后端加载的，跳过验证
        // if (!oldStartDate && newStartDate && form.task.endDate) {
        //     const endDate = new Date(form.task.endDate)
        //     const startDate = new Date(newStartDate)
        //     // 如果结束时间早于开始时间，很可能是后端数据问题，不应该清空
        //     if (endDate < startDate) {
        //         console.log('检测到后端数据时间顺序问题，跳过验证以保持原始数据')
        //         return
        //     }
        // }

        if (!newStartDate) {
            form.task.endDate = ''
            return
        }

        if (form.task.endDate) {
            const endDate = new Date(form.task.endDate)
            const startDate = new Date(newStartDate)
            const startDay = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate())
            const dayAfterNext = new Date(startDay)
            dayAfterNext.setDate(dayAfterNext.getDate() + 2)

            console.log('时间验证:', {
                endDate: endDate.toISOString(),
                startDate: startDate.toISOString(),
                dayAfterNext: dayAfterNext.toISOString(),
                isEndDateValid: endDate >= startDate && endDate < dayAfterNext
            })

            if (endDate < startDate || endDate >= dayAfterNext) {
                console.log('时间验证失败，清空结束时间')
                form.task.endDate = ''
                ElMessage.warning('由于开始时间变更，结束时间已被清空，请重新选择')
            }
        }
    }
)

// 保存每个item的选中节点信息
function findNodeAndParent(nodes, id, parent = null) {
    for (const node of nodes) {
        if (node.id === id) return { node, parent }
        if (node.children) {
            const found = findNodeAndParent(node.children, id, node)
            if (found) return found
        }
    }
    return null
}

function handleLineChange(value, item, index) {
    // 递归查找当前节点和父节点
    const result = findNodeAndParent(lineTreeVos.value, value)
    if (result) {
        item._selectedRoadNode = result
    } else {
        item._selectedRoadNode = null
    }
}

const queryStakeOptions = async (query, item) => {
    if (!query) {
        stakeOptions.value = []
        return
    }
    stakeLoading.value = true
    try {
        // 获取当前选中节点和父节点
        const nodeInfo = item._selectedRoadNode
        let roadId = ''
        if (nodeInfo) {
            if (!nodeInfo.parent) {
                // 一级节点
                roadId = nodeInfo.node.id
            } else {
                // 二级节点
                roadId = nodeInfo.parent.id
            }
        }
        const res = await listCode({
            mileageNumber: query,
            lineId: roadId,
            codeType: 'management_number',
            pageNum: 1,
            pageSize: 100
        })
        stakeOptions.value = (res.rows || []).map((item) => ({
            value: item.mileageNumber,
            label: item.mileageNumber
        }))
    } finally {
        stakeLoading.value = false
    }
}

onMounted(async () => {
    await getResourceTypes()
    await refreshLineTree()
    await getTaskAssignmentData()
})
</script>
<style lang="scss" scoped>
.box-card {
    margin-top: 10px;
}

.resource-section {
    /* 移除背景色和边框，保持与其他字段一致 */
}

.resource-item {
    margin-bottom: 16px;
    /* 移除背景色和边框，保持与其他字段一致 */
}

.resource-row {
    display: flex;
    align-items: center;
    gap: 12px;
}

.unit-text {
    color: #ffffff;
    font-size: 14px;
    line-height: 32px;
    display: flex;
    align-items: center;
    min-height: 32px;
}

.add-resource {
    text-align: left;
    margin-top: 0;
    margin-left: 12px;
}

/* 按钮样式 */
.text.item {
    .el-row {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .el-button {
        min-width: 100px;
        margin: 0 6px;
    }

    .el-col {
        display: flex;
        justify-content: center;
        gap: 12px;
    }
}

/* 强制所有表单label宽度一致并右对齐 */
.el-form-item__label {
    width: 160px !important;
    min-width: 160px !important;
    text-align: right;
}

.align-label-right .el-form-item__label {
    justify-content: flex-end;
    text-align: right;
    display: flex;
    align-items: center;
}
</style>
