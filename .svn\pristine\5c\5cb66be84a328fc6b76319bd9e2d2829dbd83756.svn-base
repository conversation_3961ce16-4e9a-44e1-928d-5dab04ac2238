<template>
    <div class="p-2 event-management-page">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <!-- <el-form-item label="所属项目" prop="projectId">
                            <el-input v-model="queryParams.projectId" placeholder="请输入所属项目" clearable
                                @keyup.enter="handleQuery" />
                        </el-form-item> -->
                        <el-form-item label="发生时间" style="width: 308px">
                            <el-date-picker
                                v-model="dateRangeHappenTime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                            />
                        </el-form-item>
                        <el-form-item label="事件类型" prop="emergencyLevel">
                            <el-tree-select
                                clearable
                                @change="handleQuery"
                                v-model="queryParams.eventSubtype"
                                :data="categoryList"
                                :props="{ value: 'id', label: 'name', children: 'children' }"
                                value-key="id"
                                placeholder="请选择事件类型"
                                check-strictly
                            />
                        </el-form-item>
                        <!-- <el-form-item label="事件等级" prop="emergencyLevel">
                            <el-select v-model="queryParams.emergencyLevel" placeholder="请选择事件等级" clearable>
                                <el-option v-for="dict in emergency_level" :key="dict.value" :label="dict.label"
                                    :value="dict.value" />
                            </el-select>
                        </el-form-item> -->
                        <!-- <el-form-item label="天气情况" prop="fstrWeather">
                            <el-select v-model="queryParams.fstrWeather" placeholder="请选择天气情况" clearable>
                                <el-option v-for="dict in fstr_weather" :key="dict.value" :label="dict.label"
                                    :value="dict.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="占用总数" prop="roadUnavailable">
                            <el-select v-model="queryParams.roadUnavailable" placeholder="请选择占用总数" clearable>
                                <el-option v-for="dict in road_unavailable" :key="dict.value" :label="dict.label"
                                    :value="dict.value" />
                            </el-select>
                        </el-form-item> -->
                        <!-- <el-form-item label="死亡人数" prop="dies">
                            <el-input v-model="queryParams.dies" placeholder="请输入死亡人数" clearable
                                @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item label="受伤人数" prop="induries">
                            <el-input v-model="queryParams.induries" placeholder="请输入受伤人数" clearable
                                @keyup.enter="handleQuery" />
                        </el-form-item> -->
                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">搜索</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <div class="btn-box">
                    <div class="filter">
                        <el-radio-group v-model="filterStatus" @change="handleFilterStatus">
                            <el-radio-button value="" v-if="false">全部</el-radio-button>
                            <el-radio-button value="Assign">待处理</el-radio-button>
                            <el-radio-button value="Accepted">已处理</el-radio-button>
                        </el-radio-group>
                        <!-- v-hasPermi="['operation:event:edit']" -->
                        <el-button type="success" v-if="false" plain icon="Edit" :disabled="single" @click="() => handleUpdate()">修改</el-button>
                        <!-- v-hasPermi="['operation:event:remove']" -->
                        <el-button type="danger" v-if="false" plain icon="Delete" :disabled="multiple" @click="handleDelete()">删除</el-button>
                    </div>
                    <div class="export">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
                        <el-button type="warning" v-if="false" plain icon="Download" @click="handleExport" v-hasPermi="['operation:event:export']"
                            >导出</el-button
                        >
                    </div>
                </div>
            </template>

            <el-table v-loading="loading" stripe :data="eventList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <!-- <el-table-column label="所属项目" align="center" prop="projectId" /> -->
                <el-table-column label="发生时间" align="center" prop="happenTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.happenTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="发现时间" align="center" prop="discoverTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.discoverTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column> -->
                <el-table-column label="事件分类" align="center" prop="eventClass">
                    <template #default="scope">
                        <span>{{ getCategoryName(scope.row.eventClass) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="事件类型" align="center" prop="eventType">
                    <template #default="scope">
                        <span>{{ getCategoryName(scope.row.eventType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="事件子类型" align="center" prop="eventSubtype">
                    <template #default="scope">
                        <span>{{ getCategoryName(scope.row.eventSubtype) }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="占用车道数" align="center" prop="roadUnavailable">
                    <template #default="scope">
                        <dict-tag :options="road_unavailable" :value="scope.row.roadUnavailable" />
                    </template>
                </el-table-column>
                <el-table-column label="摄像机号" align="center" />
                <el-table-column label="里程号" align="center" />

                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
                    <template #default="scope">
                        <div class="op-actions">
                            <el-button link type="primary" class="op-link op-info" @click="handleView(scope.row)">
                                <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="查看" />
                                查看
                            </el-button>
                            <el-button link type="primary" class="op-link op-edit" @click="handleUpdate(scope.row)">
                                <img class="op-icon" src="@/assets/images/edit-icon.png" alt="修改" />
                                修改
                            </el-button>
                            <el-button link type="danger" class="op-link op-delete" @click="handleDelete(scope.row)">
                                <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
    </div>
</template>

<script setup name="Event" lang="ts">
import { listEvent, listNoPageEvents, getEvent, delEvent, listEventLocations } from '@/api/subProject/operation/event'
import { EventVO, EventQuery } from '@/api/subProject/operation/event/types'
import { listCategory } from '@/api/common/category'
import { CategoryVO } from '@/api/common/category/types'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const router = useRouter()
const {
    fstr_weather,
    road_position,
    involve_car,
    road_num,
    road_leave,
    emergency_level,
    road_available,
    road_unavailable,
    event_bottleneck,
    congestion_length
} = toRefs<any>(
    proxy?.useDict(
        'fstr_weather',
        'road_position',
        'involve_car',
        'road_num',
        'road_leave',
        'emergency_level',
        'road_available',
        'road_unavailable',
        'event_bottleneck',
        'congestion_length'
    )
)
const categoryList = ref<CategoryVO[]>([])
const eventList = ref<EventVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRangeHappenTime = ref<[DateModelType, DateModelType]>(['', ''])
const queryFormRef = ref<ElFormInstance>()
const filterStatus = ref('Assign')
const appStore = useAppStore()
// 添加Category数据存储
const categoryMap = ref<Map<string, string>>(new Map())

// 状态缓存相关
const CACHE_KEY = 'event_list_search_state'

const queryParams = reactive<EventQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    tunnelName: undefined,
    emergencyLevel: undefined,
    eventSubtype: undefined,
    fstrWeather: undefined,
    roadUnavailable: undefined,
    dies: undefined,
    induries: undefined,
    params: {
        happenTime: undefined
    }
})

// 添加筛选状态参数
const currentStatus = ref('')

/** 获取Category数据并构建映射 */
const getCategoryData = async () => {
    try {
        const res = await listCategory({ kind: 'event' })
        const categoryData = proxy?.handleTree<CategoryVO>(res.data, 'id', 'parentId')

        // 构建ID到名称的映射
        const buildCategoryMap = (categories: CategoryVO[]) => {
            categories.forEach((category) => {
                categoryMap.value.set(category.id.toString(), category.name)
                if (category.children && category.children.length > 0) {
                    buildCategoryMap(category.children)
                }
            })
        }

        if (categoryData) {
            categoryList.value = categoryData
            buildCategoryMap(categoryData)
        }

        console.log('Category映射构建完成:', categoryMap.value)
    } catch (error) {
        console.error('获取Category数据失败:', error)
    }
}

/** 根据ID获取Category名称 */
const getCategoryName = (id: string | number | undefined): string => {
    if (!id) return ''
    const name = categoryMap.value.get(id.toString())
    return name || id.toString()
}

// 缓存搜索状态
const saveSearchState = () => {
    try {
        const state = {
            queryParams: {
                emergencyLevel: queryParams.emergencyLevel,
                eventSubtype: queryParams.eventSubtype,
                fstrWeather: queryParams.fstrWeather,
                roadUnavailable: queryParams.roadUnavailable,
                dies: queryParams.dies,
                induries: queryParams.induries
            },
            dateRangeHappenTime: dateRangeHappenTime.value,
            showSearch: showSearch.value,
            filterStatus: filterStatus.value
        }
        sessionStorage.setItem(CACHE_KEY, JSON.stringify(state))
        console.log('保存事件搜索状态:', state)
    } catch (error) {
        console.error('保存事件搜索状态失败:', error)
    }
}

// 恢复搜索状态
const restoreSearchState = () => {
    try {
        const cached = sessionStorage.getItem(CACHE_KEY)
        if (cached) {
            const state = JSON.parse(cached)

            // 恢复查询参数
            if (state.queryParams) {
                queryParams.emergencyLevel = state.queryParams.emergencyLevel
                queryParams.eventSubtype = state.queryParams.eventSubtype
                queryParams.fstrWeather = state.queryParams.fstrWeather
                queryParams.roadUnavailable = state.queryParams.roadUnavailable
                queryParams.dies = state.queryParams.dies
                queryParams.induries = state.queryParams.induries
            }

            // 恢复日期范围
            if (state.dateRangeHappenTime) {
                dateRangeHappenTime.value = state.dateRangeHappenTime
            }

            // 恢复搜索框显示状态
            if (state.showSearch !== undefined) {
                showSearch.value = state.showSearch
            }

            // 恢复筛选状态
            if (state.filterStatus) {
                filterStatus.value = state.filterStatus
            }

            console.log('恢复事件搜索状态:', state)
        }
    } catch (error) {
        console.error('恢复事件搜索状态失败:', error)
    }
}

// 清除缓存状态
const clearSearchState = () => {
    sessionStorage.removeItem(CACHE_KEY)
}

/** 查询事件管理列表 */
const getList = async () => {
    loading.value = true
    queryParams.params = {}

    queryParams.projectId = appStore.projectContext.selectedProjectId
    proxy?.addDateRange(queryParams, dateRangeHappenTime.value, 'HappenTime')
    const res = await listEvent(queryParams)
    eventList.value = res.rows
    total.value = res.total
    loading.value = false
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    dateRangeHappenTime.value = ['', '']
    queryFormRef.value?.resetFields()

    // 重置筛选状态
    filterStatus.value = 'Assign'

    // 清除缓存状态
    clearSearchState()

    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: EventVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    router.push('eventForm')
}

/** 修改按钮操作 */
const handleUpdate = async (row?: EventVO) => {
    const _id = row?.id || ids.value[0]
    router.push('eventForm?id=' + _id)
}
const handleView = async (row?: EventVO) => {
    const _id = row?.id || ids.value[0]
    router.push('eventDetail?id=' + _id)
}

/** 删除按钮操作 */
const handleDelete = async (row?: EventVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除事件管理编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delEvent(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'operation/event/export',
        {
            ...queryParams
        },
        `event_${new Date().getTime()}.xlsx`
    )
}

/** 处理筛选状态改变 */
const handleFilterStatus = (status: string) => {
    currentStatus.value = status
    // 可以根据需要将状态参数添加到查询参数中
    // queryParams.someStatusField = status;
    queryParams.pageNum = 1 // 筛选时重置页码
    getList()
}

onMounted(async () => {
    loading.value = false
    await getCategoryData() // 先加载Category数据

    // 恢复搜索状态
    restoreSearchState()

    getList() // 启用列表自动加载
})

// 组件卸载前保存状态
onBeforeUnmount(() => {
    saveSearchState()
})

// 监听搜索条件变化，实时保存状态
watch(
    [
        () => queryParams.emergencyLevel,
        () => queryParams.eventSubtype,
        () => queryParams.fstrWeather,
        () => queryParams.roadUnavailable,
        () => queryParams.dies,
        () => queryParams.induries,
        dateRangeHappenTime,
        showSearch,
        filterStatus
    ],
    () => {
        // 延迟保存，避免频繁操作
        nextTick(() => {
            saveSearchState()
        })
    },
    { deep: true }
)

// 监听路由变化，当从表单页面返回时刷新列表
watch(
    () => router.currentRoute.value.path,
    async (newPath) => {
        if (newPath.endsWith('/event') || newPath.endsWith('/event/')) {
            // 延迟一下确保页面完全加载
            setTimeout(async () => {
                await getCategoryData() // 重新加载Category数据
                getList()
            }, 100)
        }
    }
)

// 页面激活时刷新列表（处理浏览器前进后退）
// onActivated(async () => {
//     await getCategoryData() // 重新加载Category数据
//     getList()
// })
</script>

<style lang="scss" scoped>
.event-management-page {

    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-tree-select__wrapper),
    :deep(.el-date-editor .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder),
    :deep(.el-tree-select__placeholder) {
        color: #8291A9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)),
    :deep(.el-tree-select .el-tree-select__selected-item > span:not(.el-tree-select__placeholder)) {
        color: #FFFFFF !important;
    }

    :deep(.el-select__wrapper.is-focused),
    :deep(.el-tree-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 日期选择器特殊样式 */
    :deep(.el-date-editor) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-date-editor .el-input__inner) {
        color: #FFFFFF !important;
        background: transparent !important;
    }

    :deep(.el-range-separator) {
        color: #8291A9 !important;
    }

    /* 树形选择器特殊样式 */
    :deep(.el-tree-select) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-tree-select__inner) {
        color: #FFFFFF !important;
        background: transparent !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #AED7F2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #FFFFFF !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 筛选按钮组样式 */
    .btn-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter {
            flex: 1;
        }

        .export {
            margin-left: auto;
        }
    }

    :deep(.el-radio-group .el-radio-button__inner) {
        background: #232d45 !important;
        border: 1px solid #4286F3 !important;
        color: #8291A9 !important;
        border-radius: 6px !important;
        margin-right: 10px;
        height: 36px;
        line-height: 34px;
        padding: 0 16px;
    }

    :deep(.el-radio-group .el-radio-button.is-active .el-radio-button__inner) {
        background: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    /* 头部操作按钮样式 */
    :deep(.btn-box .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 36px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.btn-box .el-button.el-button--primary) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.btn-box .el-button.el-button--success) {
        background-color: #67C23A !important;
        border-color: #67C23A !important;
        color: #FFFFFF !important;
    }

    :deep(.btn-box .el-button.el-button--warning) {
        background-color: #E6A23C !important;
        border-color: #E6A23C !important;
        color: #FFFFFF !important;
    }

    :deep(.btn-box .el-button.el-button--danger) {
        background-color: #F56C6C !important;
        border-color: #F56C6C !important;
        color: #FFFFFF !important;
    }

    /* 操作列图标按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286F3 !important;
    }

    .op-edit {
        color: #42F3E9 !important;
    }

    .op-delete {
        color: #D62121 !important;
    }
}
</style>
