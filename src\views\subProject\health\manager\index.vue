<template>
    <div class="p-2 health-manager-page">
        <!-- 搜索栏 -->
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="120px">
                        <el-form-item label="管理单元" prop="managementUnit" v-if="false">
                            <el-select v-model="queryParams.managementUnit" placeholder="请选择管理单元" clearable>
                                <el-option v-for="item in MANAGEMENT_UNIT_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="测点类型" prop="pointType" v-if="false">
                            <el-select v-model="queryParams.pointType" placeholder="请选择测点类型" clearable>
                                <el-option v-for="item in POINT_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="编码类型" prop="codeType" v-if="false">
                            <el-select v-model="queryParams.codeType" placeholder="请选择编码类型" clearable>
                                <el-option v-for="item in CODE_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="测点名称" prop="pointName">
                            <el-input v-model="queryParams.pointName" placeholder="请输入测点名称" clearable />
                        </el-form-item>
                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">查询</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <!-- 操作栏 -->
            <div class="btn-box">
                <div class="filter">
                    <el-button type="primary" plain icon="Plus" @click="handleAdd">添加</el-button>
                    <el-button type="success" plain icon="Edit" :disabled="single" @click="() => handleUpdate()">修改</el-button>
                    <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="() => handleDelete()">删除</el-button>
                    <el-button type="warning" v-if="false" plain icon="Download" @click="handleExport">导出</el-button>
                </div>
                <div class="export">
                    <right-toolbar :showSearch="showSearch" @update:showSearch="showSearch = $event" @queryTable="getList"></right-toolbar>
                </div>
            </div>

            <!-- 数据表格 -->
            <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange" stripe>
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="序号" type="index" width="70" align="center" />
                <el-table-column label="测点名称" align="center" prop="pointName" />
                <!-- <el-table-column label="管理单元" align="center" prop="managementUnit" /> -->
                <el-table-column label="测点大类" align="center" prop="pointType" />
                <el-table-column label="测点小类" align="center" prop="pointType" />
                <el-table-column label="里程" align="center" prop="startMileage" />
                <el-table-column label="检测单位" align="center" prop="detectionUnit" />
                <el-table-column label="设备编码" align="center" prop="deviceCode" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
                        <el-button link type="primary" icon="Link" v-if="false" @click="handleRelate(scope.row)">关联</el-button>
                        <el-button link type="primary" icon="Switch" v-if="false" @click="handleChange(scope.row)">变更</el-button>
                        <el-button link type="primary" icon="DataLine" @click="handleData(scope.row)">数据</el-button>
                        <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" :page="queryParams.pageNum" :limit="queryParams.pageSize" @pagination="handlePagination" />
        </el-card>
        <!-- 添加或修改设备对话框 -->
        <el-dialog :title="title" v-model="open" width="800px" append-to-body>
            <el-form ref="deviceRef" :model="form" :rules="rules" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="管理单元" prop="managementUnit">
                            <el-select v-model="form.managementUnit" placeholder="请选择管理单元">
                                <el-option v-for="item in MANAGEMENT_UNIT_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="检测单位" prop="detectionUnit">
                            <el-select v-model="form.detectionUnit" placeholder="请选择检测单位">
                                <el-option v-for="item in DETECTION_UNIT_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="测点类型" prop="pointType">
                            <el-select v-model="form.pointType" placeholder="请选择设备系统分类">
                                <el-option v-for="item in POINT_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="测点名称" prop="pointName">
                            <el-input v-model="form.pointName" placeholder="请输入测点名称" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="编码类型" prop="codeType">
                            <el-select v-model="form.codeType" placeholder="请选择编码类型">
                                <el-option v-for="item in CODE_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="起范围" prop="startRange">
                            <el-input v-model="form.startRange" placeholder="请输入编码" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="止范围" prop="endRange">
                            <el-input v-model="form.endRange" placeholder="请输入编码" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="起始里程" prop="startMileage">
                            <el-input v-model="form.startMileage" placeholder="请输入起始里程" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="终止里程" prop="endMileage">
                            <el-input v-model="form.endMileage" placeholder="请输入终止里程" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="PSN码" prop="psnCode">
                            <el-input v-model="form.psnCode" placeholder="请输入PSN码" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="设备编码" prop="deviceCode">
                            <el-input v-model="form.deviceCode" placeholder="自动编码生成">
                                <template #append>
                                    <el-button @click="generateCode">生成</el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="设备归属单位" prop="deviceUnit">
                            <el-select v-model="form.deviceUnit" placeholder="请选择归属单位">
                                <el-option v-for="item in DEVICE_UNIT_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="流水号" prop="flowNumber">
                            <el-input v-model="form.flowNumber" placeholder="自动编码生成" readonly />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="通信编码" prop="communicationCode">
                            <el-input v-model="form.communicationCode" placeholder="请输入通信编码" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 数据查看对话框 -->
        <el-dialog
            v-model="dataDialogVisible"
            :title="`${currentDevice?.pointName || ''} - 数据查看`"
            width="90%"
            :close-on-click-modal="false"
            append-to-body
        >
            <!-- 车流数据页面 -->
            <div v-if="isTrafficDevice && dataDialogVisible" class="traffic-data-placeholder">
                <el-alert title="车流数据展示" :description="`正在显示${currentDevice?.pointName}的车流数据`" type="info" show-icon v-if="false" />
                <!-- <p>测点名称：{{ currentDevice?.pointName }}</p>
                <p>测点大类：{{ currentDevice?.pointType }}</p> -->
                <TrafficData :tunnel-name="currentDevice?.pointName" />
            </div>
            <!-- 泵房液位数据页面 -->
            <div v-else-if="isPumpDevice && dataDialogVisible" class="pump-data-placeholder">
                <el-alert
                    title="泵房液位数据展示"
                    :description="`正在显示${currentDevice?.pointName}的泵房液位数据`"
                    type="info"
                    show-icon
                    v-if="false"
                />
                <PumpData :pump-name="currentDevice?.deviceCode" />
            </div>
            <!-- 其他类型数据页面 -->
            <div v-else-if="dataDialogVisible" class="other-data-page">
                <el-empty :description="`${currentDevice?.pointType}类型的数据展示功能正在开发中`" />
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, getCurrentInstance, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAppStore } from '@/store/modules/app'
import {
    listMonitorDevice,
    getMonitorDevice,
    addMonitorDevice,
    updateMonitorDevice,
    delMonitorDevice,
    exportMonitorDevice,
    generateDeviceCode,
    generateFlowNumber
} from '@/api/subProject/health/monitor'
import {
    MonitorDeviceVO,
    MonitorDeviceForm,
    MonitorDeviceQuery,
    CODE_TYPE_OPTIONS,
    MANAGEMENT_UNIT_OPTIONS,
    DETECTION_UNIT_OPTIONS,
    POINT_TYPE_OPTIONS,
    DEVICE_UNIT_OPTIONS
} from '@/api/subProject/health/monitor/types'
import TrafficData from '@/views/subProject/components/Traffic/TrafficData.vue'
import PumpData from '@/views/subProject/components/Pump/PumpData.vue'
import PumpWarning from '@/views/subProject/components/Pump/PumpWarning.vue'

const { proxy } = getCurrentInstance() as any
const appStore = useAppStore()

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref<Array<string | number>>([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 设备表格数据
const deviceList = ref<MonitorDeviceVO[]>([])
// 弹出层标题
const title = ref('')
// 是否显示弹出层
const open = ref(false)
// 数据查看对话框显示状态
const dataDialogVisible = ref(false)
// 当前选中的设备
const currentDevice = ref<MonitorDeviceVO | null>(null)

// 查询参数
const queryParams = ref<MonitorDeviceQuery>({
    pageNum: 1,
    pageSize: 10,
    managementUnit: '',
    pointType: '',
    codeType: '',
    status: '',
    pointName: ''
})

// 表单参数
const form = ref<MonitorDeviceForm>({})

// 表单校验
const rules = reactive({
    managementUnit: [{ required: true, message: '管理单元不能为空', trigger: 'change' }],
    pointType: [{ required: true, message: '测点类型不能为空', trigger: 'change' }],
    pointName: [{ required: true, message: '测点名称不能为空', trigger: 'blur' }],
    codeType: [{ required: true, message: '编码类型不能为空', trigger: 'change' }],
    startRange: [{ required: true, message: '起范围不能为空', trigger: 'blur' }]
})

// 判断是否是车检器类型（车流数据）
const isTrafficDevice = computed(() => {
    return (
        currentDevice.value?.pointType === '车检器' ||
        currentDevice.value?.pointType?.includes('车流') ||
        currentDevice.value?.pointType?.includes('车检')
    )
})

// 判断是否是泵房类型（泵房液位数据）
const isPumpDevice = computed(() => {
    return (
        currentDevice.value?.pointType === '泵房' ||
        currentDevice.value?.pointType?.includes('泵房') ||
        currentDevice.value?.pointType?.includes('液位')
    )
})

/** 查询设备列表 */
const getList = async () => {
    loading.value = true
    try {
        // 设置项目ID
        queryParams.value.projectId = appStore.projectContext.selectedProjectId?.toString()

        const res = await listMonitorDevice(queryParams.value)
        console.log('API返回数据:', res)

        if (res.rows) {
            deviceList.value = res.rows
            total.value = res.total || 0
        } else if (res.data && res.data.rows) {
            deviceList.value = res.data.rows
            total.value = res.data.total || 0
        } else if (Array.isArray(res.data)) {
            deviceList.value = res.data
            total.value = res.data.length
        } else if (Array.isArray(res)) {
            deviceList.value = res
            total.value = res.length
        } else {
            deviceList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('查询设备列表失败:', error)
        deviceList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

/** 分页处理 */
const handlePagination = (pagination: any) => {
    queryParams.value.pageNum = pagination.page
    queryParams.value.pageSize = pagination.limit
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
        managementUnit: '',
        pointType: '',
        codeType: '',
        status: '',
        pointName: ''
    }
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: MonitorDeviceVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length !== 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    open.value = true
    title.value = '添加监测设备'
}

/** 修改按钮操作 */
const handleUpdate = async (row?: MonitorDeviceVO) => {
    reset()
    const deviceId = row?.id || ids.value[0]
    try {
        const res = await getMonitorDevice(deviceId)
        form.value = res.data
        open.value = true
        title.value = '修改监测设备'
    } catch (error) {
        console.error('获取设备信息失败:', error)
    }
}

/** 提交按钮 */
const submitForm = () => {
    proxy.$refs['deviceRef'].validate(async (valid: boolean) => {
        if (valid) {
            try {
                // 设置项目ID
                form.value.projectId = appStore.projectContext.selectedProjectId?.toString()

                // 创建提交数据，排除时间字段
                const submitData = {
                    id: form.value.id,
                    projectId: form.value.projectId,
                    managementUnit: form.value.managementUnit,
                    detectionUnit: form.value.detectionUnit,
                    pointType: form.value.pointType,
                    pointName: form.value.pointName,
                    codeType: form.value.codeType,
                    startRange: form.value.startRange,
                    endRange: form.value.endRange,
                    startMileage: form.value.startMileage,
                    endMileage: form.value.endMileage,
                    psnCode: form.value.psnCode,
                    deviceCode: form.value.deviceCode,
                    deviceUnit: form.value.deviceUnit,
                    flowNumber: form.value.flowNumber,
                    communicationCode: form.value.communicationCode,
                    status: form.value.status,
                    remark: form.value.remark
                }

                if (form.value.id) {
                    await updateMonitorDevice(submitData)
                    ElMessage.success('修改成功')
                } else {
                    await addMonitorDevice(submitData)
                    ElMessage.success('新增成功')
                }
                open.value = false
                getList()
            } catch (error) {
                console.error('提交失败:', error)
            }
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: MonitorDeviceVO) => {
    const deviceIds = row?.id || ids.value
    try {
        await ElMessageBox.confirm('是否确认删除选中的监测设备？', '警告', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
        await delMonitorDevice(deviceIds)
        ElMessage.success('删除成功')
        getList()
    } catch (error) {
        console.error('删除失败:', error)
    }
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy.download(
        '/health/monitor/export',
        {
            ...queryParams.value
        },
        `monitor_device_${new Date().getTime()}.xlsx`
    )
}

/** 生成设备编码 */
const generateCode = async () => {
    if (!form.value.codeType) {
        ElMessage.warning('请先选择编码类型')
        return
    }
    try {
        const res = await generateDeviceCode(form.value.codeType)
        form.value.deviceCode = res.data
        // 同时生成流水号
        const flowRes = await generateFlowNumber()
        form.value.flowNumber = flowRes.data
        ElMessage.success('编码生成成功')
    } catch (error) {
        console.error('生成设备编码失败:', error)
    }
}

/** 关联操作 */
const handleRelate = (row: MonitorDeviceVO) => {
    ElMessage.info('关联功能待开发')
}

/** 变更操作 */
const handleChange = (row: MonitorDeviceVO) => {
    ElMessage.info('变更功能待开发')
}

/** 数据操作 */
const handleData = (row: MonitorDeviceVO) => {
    console.log('=== 泵房数据调试信息 ===')
    console.log('选中的设备数据:', row)
    console.log('设备名称 (pointName):', row?.pointName)
    console.log('通信编码 (communicationCode):', row?.communicationCode)
    console.log('设备编码 (deviceCode):', row?.deviceCode)
    console.log('测点类型 (pointType):', row?.pointType)
    console.log('========================')

    currentDevice.value = row
    dataDialogVisible.value = true
}

/** 取消按钮 */
const cancel = () => {
    open.value = false
    reset()
}

/** 表单重置 */
const reset = () => {
    form.value = {
        id: undefined,
        projectId: appStore.projectContext.selectedProjectId?.toString(), // 从store获取项目ID
        managementUnit: '',
        detectionUnit: '',
        pointType: '',
        pointName: '',
        codeType: '',
        startRange: '',
        endRange: '',
        startMileage: '',
        endMileage: '',
        psnCode: '',
        deviceCode: '',
        deviceUnit: '',
        flowNumber: '',
        communicationCode: '',
        status: '0',
        remark: ''
    }
    // 重置表单验证状态
    if (proxy.$refs['deviceRef']) {
        proxy.$refs['deviceRef'].resetFields()
    }
}

onMounted(() => {
    getList()
})
</script>

<style scoped lang="scss">
.health-manager-page {
    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-tree-select__wrapper),
    :deep(.el-date-editor .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder),
    :deep(.el-tree-select__placeholder) {
        color: #8291A9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)),
    :deep(.el-tree-select .el-tree-select__selected-item > span:not(.el-tree-select__placeholder)) {
        color: #FFFFFF !important;
    }

    :deep(.el-select__wrapper.is-focused),
    :deep(.el-tree-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #AED7F2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #FFFFFF !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 筛选按钮组样式 */
    .btn-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter {
            flex: 1;
        }

        .export {
            margin-left: auto;
        }
    }
}
.traffic-data-placeholder,
.pump-data-placeholder {
    padding: 10px;

    .el-alert {
        margin-bottom: 16px;
    }

    p {
        margin: 8px 0;
        font-size: 14px;
        color: #606266;
    }
}

.other-data-page {
    padding: 40px;
    text-align: center;
}
</style>
