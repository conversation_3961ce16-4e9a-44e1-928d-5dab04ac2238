<!-- 设备选择组件 -->
<template>
    <el-dialog v-model="dialogVisible" title="选择设备" width="1000px" :before-close="handleClose" @open="handleDialogOpen">
        <!-- 查询条件 -->
        <div style="margin-bottom: 20px">
            <el-row :gutter="10" style="margin-bottom: 10px">
                <el-col :span="4">
                    <el-select
                        v-model="selectedSpecialty"
                        placeholder="请选择专业类型"
                        style="width: 130px"
                        clearable
                        @change="handleSpecialtyChange"
                    >
                        <el-option v-for="item in tnl_specialty" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <el-select
                        v-model="equipmentQuery.unitId"
                        placeholder="请选择管理单元"
                        style="width: 130px"
                        clearable
                        @focus="handleManagementUnitFocus"
                        @change="handleQueryConditionChange"
                    >
                        <el-option v-for="unit in projectManagementUnits" :key="unit.id" :label="unit.name" :value="unit.id"> </el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <el-tree-select
                        v-model="selectedQueryCategoryId"
                        :data="categoryList"
                        :props="{ value: 'id', label: 'name', children: 'children' }"
                        value-key="id"
                        :placeholder="getClassificationPlaceholder()"
                        check-strictly
                        clearable
                        filterable
                        style="width: 130px"
                        :disabled="!selectedSpecialty"
                        @change="handleQueryConditionChange"
                    >
                    </el-tree-select>
                </el-col>
                <el-col :span="4">
                    <el-input
                        v-model="equipmentQuery.remark"
                        placeholder="请输入设备名称"
                        style="width: 130px"
                        clearable
                        @keyup.enter="handleSearchEquipment"
                        @input="handleQueryConditionChange"
                    />
                </el-col>
                <el-col :span="4">
                    <el-input
                        v-model="equipmentQuery.code"
                        placeholder="请输入设备编码"
                        style="width: 130px"
                        clearable
                        @keyup.enter="handleSearchEquipment"
                        @input="handleQueryConditionChange"
                    />
                </el-col>
                <el-col :span="4" style="text-align: right">
                    <el-button type="primary" @click="handleSearchEquipment" :loading="loading">查询</el-button>
                    <el-button @click="handleResetEquipmentQuery">重置</el-button>
                </el-col>
            </el-row>

            <!-- 查询条件提示 -->
            <el-row v-if="hasQueryConditions" style="margin-bottom: 10px">
                <el-col :span="24">
                    <el-alert :title="getQueryConditionsText()" type="info" show-icon :closable="false" style="padding: 8px 12px; font-size: 12px" />
                </el-col>
            </el-row>
        </div>

        <!-- 双表格穿梭选择 -->
        <div style="display: flex; gap: 20px; align-items: flex-start">
            <!-- 待选择设备表格 -->
            <div style="flex: 1">
                <div style="text-align: center; margin-bottom: 10px; font-weight: 500">
                    待选择{{ selectedSpecialty === 'electric' ? '设备' : '设施' }}
                </div>
                <el-table
                    ref="availableTableRef"
                    :data="availableEquipmentList"
                    style="width: 100%; height: 400px"
                    @selection-change="handleAvailableSelectionChange"
                    size="small"
                    stripe
                >
                    <el-table-column prop="remark" :label="selectedSpecialty === 'electric' ? '设备名称' : '设施名称'" width="120px">
                    </el-table-column>

                    <el-table-column prop="unitName" label="管理单元" width="120px">
                        <template #default="scope">
                            <div style="font-size: 12px; color: #409eff">🏢 {{ scope.row.unitName }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" width="120px" prop="name">
                        <template #default="scope">
                            <div style="font-weight: 500">{{ scope.row.name }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" width="80">
                        <template #default="scope">
                            <el-button v-if="!isEquipmentSelected(scope.row)" type="text" size="small" @click="handleSelectEquipment(scope.row)"
                                >选择</el-button
                            >
                            <span v-else class="selected-text">已选择</span>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <div style="margin-top: 15px; display: flex; justify-content: center">
                    <el-pagination
                        v-model:current-page="equipmentQuery.pageNum"
                        v-model:page-size="equipmentQuery.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        small
                    />
                </div>
            </div>

            <!-- 已选择设备表格 -->
            <div style="flex: 1">
                <div style="text-align: center; margin-bottom: 10px; font-weight: 500">
                    已选择{{ selectedSpecialty === 'electric' ? '设备' : '设施' }} ({{ chosenEquipmentList.length }})
                </div>
                <el-table
                    ref="chosenTableRef"
                    :data="chosenEquipmentList"
                    style="width: 100%; height: 400px"
                    @selection-change="handleChosenSelectionChange"
                    size="small"
                    stripe
                >
                    <el-table-column type="selection" width="50" />
                    <el-table-column :label="selectedSpecialty === 'electric' ? '设备名称' : '设施名称'" width="120px">
                        <template #default="scope">
                            <div style="font-weight: 500">{{ scope.row.name }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="unitName" label="管理单元" width="120px">
                        <template #default="scope">
                            <div style="font-size: 12px; color: #409eff">🏢 {{ scope.row.unitName }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" width="80">
                        <template #default="scope">
                            <el-button type="text" size="small" style="color: #f56c6c" @click="handleRemoveEquipment(scope.row)">移除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="confirmSelection">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, getCurrentInstance, ComponentInternalInstance, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { listCategory } from '@/api/common/category'
import { CategoryVO } from '@/api/common/category/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { listDevice } from '@/api/subProject/basic/device'
import { DeviceVO, DeviceQuery } from '@/api/subProject/basic/device/types'
import { useAppStore } from '@/store/modules/app'

interface Props {
    modelValue: boolean
    projectId?: string
    specialty?: string // 专业类型，用于默认选中
}

const props = defineProps<Props>()

const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    'confirm': [selectedDevices: any[]]
}>()

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const appStore = useAppStore()

// 获取专业类型数据字典
const { tnl_specialty } = proxy?.useDict('tnl_specialty')

// 项目管理单元列表
const projectManagementUnits = ref<ManageUnitVO[]>([])

// 对话框控制
const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
})

// 双表格穿梭选择相关
const availableEquipmentList = ref<any[]>([])
const chosenEquipmentList = ref<any[]>([])
const selectedAvailableEquipment = ref<any[]>([])
const selectedChosenEquipment = ref<any[]>([])
const availableTableRef = ref()
const chosenTableRef = ref()

// 设备查询条件
const equipmentQuery = ref<DeviceQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: '',
    unitId: undefined,
    name: undefined,
    code: undefined,
    categoryPath: undefined,
    specialty: undefined,
    params: {}
})

// 分页相关
const total = ref(0)

// 设备分类树形数据
const categoryList = ref<any[]>([])
const selectedQueryCategoryId = ref('')

// 新增变量
const loading = ref(false)
const selectedSpecialty = ref<string>(props.specialty || '')

// 判断设备是否已被选择
const isEquipmentSelected = (equipment: any) => {
    return chosenEquipmentList.value.some((item) => String(item.id) === String(equipment.id))
}

// 单行选择设备
const handleSelectEquipment = (equipment: any) => {
    if (!isEquipmentSelected(equipment)) {
        chosenEquipmentList.value.push(equipment)
        // 从左侧移除
        const index = availableEquipmentList.value.findIndex((item) => item.id === equipment.id)
        if (index !== -1) {
            availableEquipmentList.value.splice(index, 1)
        }
    }
    // 清空选择
    selectedAvailableEquipment.value = []
    availableTableRef.value?.clearSelection()
}

/** 根据分类ID查找分类信息 */
const findCategoryById = (list: any[], id: string): any => {
    for (const item of list) {
        if (item.id === id) return item
        if (item.children) {
            const found = findCategoryById(item.children, id)
            if (found) return found
        }
    }
    return null
}

/** 根据分类ID获取分类名称 */
const getCategoryNameById = async (categoryId: string | number): Promise<string> => {
    if (!categoryId) return '未分类'

    // 如果分类数据还没有加载，先加载
    if (categoryList.value.length === 0) {
        await getEquipmentCategoryList()
    }

    const category = findCategoryById(categoryList.value, categoryId.toString())
    return category ? category.name : '未知分类'
}

/** 专业类型变化处理 */
const handleSpecialtyChange = async (specialty: string) => {
    console.log('专业类型变化:', specialty)

    // 清空当前分类选择
    selectedQueryCategoryId.value = ''
    categoryList.value = []

    // 重新加载分类数据
    if (specialty) {
        await getEquipmentCategoryList()
    }

    // 自动执行查询
    await handleSearchEquipment()
}

/** 查询条件变化处理 */
const handleQueryConditionChange = () => {
    console.log('查询条件已变化')
}

/** 分页大小变化处理 */
const handleSizeChange = (newSize: number) => {
    equipmentQuery.value.pageSize = newSize
    equipmentQuery.value.pageNum = 1 // 重置到第一页
    handleSearchEquipment()
}

/** 当前页变化处理 */
const handleCurrentChange = (newPage: number) => {
    equipmentQuery.value.pageNum = newPage
    handleSearchEquipment()
}

/** 获取分类选择框的占位符文本 */
const getClassificationPlaceholder = () => {
    if (!selectedSpecialty.value) {
        return '请先选择专业类型'
    }
    if (selectedSpecialty.value === 'electric') {
        return '请选择设备分类'
    }
    return '请选择设施分类'
}

/** 是否有查询条件 */
const hasQueryConditions = computed(() => {
    return !!(equipmentQuery.value.unitId || selectedQueryCategoryId.value || equipmentQuery.value.name || equipmentQuery.value.code)
})

/** 获取查询条件文本 */
const getQueryConditionsText = () => {
    const conditions = []

    if (equipmentQuery.value.unitId) {
        const unitName = projectManagementUnits.value.find((unit) => unit.id === equipmentQuery.value.unitId)?.name
        conditions.push(`管理单元: ${unitName || equipmentQuery.value.unitId}`)
    }

    if (selectedQueryCategoryId.value) {
        const categoryName = findCategoryById(categoryList.value, selectedQueryCategoryId.value)?.name
        conditions.push(`分类: ${categoryName || selectedQueryCategoryId.value}`)
    }

    if (equipmentQuery.value.name) {
        conditions.push(`设备名称: ${equipmentQuery.value.name}`)
    }

    if (equipmentQuery.value.code) {
        conditions.push(`设备编码: ${equipmentQuery.value.code}`)
    }

    return `当前查询条件：${conditions.join(' | ')}`
}

/** 查询设备列表 */
const handleSearchEquipment = async () => {
    console.log('开始查询设备列表...')
    loading.value = true
    try {
        // 获取项目ID
        const projectId = props.projectId || appStore.projectContext.selectedProjectId
        console.log('获取到的项目ID:', projectId)
        if (!projectId) {
            console.warn('未获取到项目ID，无法查询设备列表')
            ElMessage.warning('未获取到项目ID，请检查项目设置')
            return
        }

        // 设置查询参数
        equipmentQuery.value.projectId = projectId as string

        // 添加专业类型查询条件
        if (selectedSpecialty.value) {
            equipmentQuery.value.specialty = selectedSpecialty.value
        } else {
            equipmentQuery.value.specialty = undefined
        }

        // 处理设备分类查询
        if (selectedQueryCategoryId.value) {
            const selectedCategory = findCategoryById(categoryList.value, selectedQueryCategoryId.value)
            equipmentQuery.value.categoryPath = selectedCategory?.path || undefined
        } else {
            equipmentQuery.value.categoryPath = undefined
        }

        console.log('设备查询参数:', equipmentQuery.value)
        console.log('当前选择的专业类型:', selectedSpecialty.value)

        // 调用真实的API接口
        console.log('准备调用listDevice API...')
        const response = await listDevice(equipmentQuery.value)
        console.log('API调用完成，响应:', response)
        const equipmentData = response.rows || response.data || []

        // 更新总数
        total.value = response.total || 0

        console.log('设备查询响应:', response, '数据条数:', equipmentData.length, '总数:', total.value)

        // 转换数据格式
        const formattedData = await Promise.all(
            equipmentData.map(async (equipment: DeviceVO) => {
                // 根据设备的unitId查找对应的管理单元名称
                const managementUnit = projectManagementUnits.value.find((unit) => unit.id === equipment.unitId)
                const unitName = managementUnit ? managementUnit.name : '未设置管理单元'

                // 获取设备分类名称
                const categoryId = equipment.categoryIdThird
                const categoryName = await getCategoryNameById(categoryId)

                return {
                    ...equipment,
                    location: equipment.bgnKilometer ? `${equipment.bgnKilometer}km` : '未设置位置',
                    unitName: unitName,
                    categoryName: categoryName
                }
            })
        )

        // 直接将查询结果作为待选择列表（分页数据）
        availableEquipmentList.value = formattedData

        // 过滤掉已选择的设备
        const currentSelectedIds = chosenEquipmentList.value.map((item) => item.id)
        availableEquipmentList.value = availableEquipmentList.value.filter((equipment) => !currentSelectedIds.includes(equipment.id))

        // 清空表格选择状态
        selectedAvailableEquipment.value = []
        availableTableRef.value?.clearSelection()

        // 只在有具体查询条件时显示成功消息
        if (hasQueryConditions.value) {
            ElMessage.success(`查询成功，共找到 ${total.value} 个设备`)
        }
    } catch (error) {
        console.error('查询设备失败:', error)
        ElMessage.error('查询设备失败')
    } finally {
        loading.value = false
    }
}

/** 重置设备查询条件 */
const handleResetEquipmentQuery = async () => {
    // 获取项目ID
    const projectId = props.projectId || appStore.projectContext.selectedProjectId

    // 重置查询条件
    equipmentQuery.value = {
        pageNum: 1,
        pageSize: 10,
        projectId: projectId as string,
        unitId: undefined,
        name: undefined,
        code: undefined,
        categoryPath: undefined,
        specialty: undefined,
        params: {}
    }

    // 重置专业类型和分类选择
    selectedSpecialty.value = ''
    selectedQueryCategoryId.value = ''
    categoryList.value = []

    // 重置后重新执行查询
    await handleSearchEquipment()

    ElMessage.info('已重置查询条件')
}

/** 获取当前项目所有管理单元 */
const getProjectManagementUnits = async () => {
    try {
        const projectId = props.projectId || appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未获取到项目ID，无法加载管理单元')
            return []
        }

        const response = await listProjectManageUnit(projectId as string)
        const managementUnits = response.data || []

        console.log('获取项目管理单元成功:', managementUnits)
        return managementUnits
    } catch (error) {
        console.error('获取项目管理单元失败:', error)
        ElMessage.error('获取项目管理单元失败')
        return []
    }
}

/** 加载项目管理单元到本地变量 */
const loadProjectManagementUnits = async () => {
    try {
        const units = await getProjectManagementUnits()
        projectManagementUnits.value = units
        console.log('管理单元列表已加载:', projectManagementUnits.value)
    } catch (error) {
        console.error('加载项目管理单元失败:', error)
    }
}

/** 管理单元下拉框聚焦事件 */
const handleManagementUnitFocus = () => {
    if (projectManagementUnits.value.length === 0) {
        console.log('备用加载：管理单元数据为空，重新加载')
        loadProjectManagementUnits()
    }
}

/** 获取设备分类列表 */
const getEquipmentCategoryList = async () => {
    try {
        const projectId = props.projectId || appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未获取到项目ID，无法加载设备分类')
            return
        }

        if (!selectedSpecialty.value) {
            console.warn('未指定专业类型，无法加载设备分类')
            categoryList.value = []
            return
        }

        console.log('正在查询分类数据，专业类型:', selectedSpecialty.value)

        let res
        let treeData

        if (selectedSpecialty.value === 'electric') {
            // 机电系统调用listCategory方法，kind参数为equipment
            const queryParams = {
                projectId: projectId,
                kind: 'equipment'
            }
            console.log('调用listCategory，参数:', queryParams)
            res = await listCategory(queryParams)
            console.log('listCategory API响应:', res)
            treeData = proxy?.handleTree<CategoryVO>(res.data, 'id', 'parentId')
        } else {
            // 其他专业类型（如structure、civil等）调用listFacilityCategoryTree方法，获取设施分类
            console.log('调用listFacilityCategoryTree，专业类型:', selectedSpecialty.value)
            const { listFacilityCategoryTree } = await import('@/api/common/category')
            res = await listFacilityCategoryTree(selectedSpecialty.value)
            console.log('listFacilityCategoryTree API响应:', res)
            // listFacilityCategoryTree返回的已经是树形结构，不需要再次处理
            treeData = res.data
        }

        console.log('分类树形数据:', treeData)

        if (treeData && treeData.length > 0) {
            categoryList.value = treeData
            console.log(`获取${selectedSpecialty.value}专业设备分类列表成功，共${categoryList.value.length}个分类`)
        } else {
            console.warn(`${selectedSpecialty.value}专业的分类树形数据为空`)
            categoryList.value = []
        }
    } catch (error) {
        console.error('获取设备分类列表失败:', error)
        ElMessage.error(`获取${selectedSpecialty.value}专业设备分类列表失败`)
        categoryList.value = []
    }
}

/** 待选择设备表格选择变化 */
const handleAvailableSelectionChange = (selection: any[]) => {
    selectedAvailableEquipment.value = selection
}

/** 已选择设备表格选择变化 */
const handleChosenSelectionChange = (selection: any[]) => {
    selectedChosenEquipment.value = selection
}

/** 初始化穿梭框数据 */
const initializeTransferData = () => {
    // 对于分页模式，只需要清空已选择的设备列表
    chosenEquipmentList.value = []

    // 清空表格选择
    selectedAvailableEquipment.value = []
    selectedChosenEquipment.value = []
}

// 新增方法
const handleRemoveEquipment = (equipment: any) => {
    const index = chosenEquipmentList.value.findIndex((item) => String(item.id) === String(equipment.id))
    if (index > -1) {
        chosenEquipmentList.value.splice(index, 1)
        // 移回左侧
        availableEquipmentList.value.push(equipment)
    }
    // 可选：清空右侧表格选择
    selectedChosenEquipment.value = []
    chosenTableRef.value?.clearSelection()
}

/** 打开设备选择弹出框时的处理 */
const handleDialogOpen = async () => {
    // 每次弹出对话框时都重新加载管理单元数据
    await loadProjectManagementUnits()

    // 如果有默认专业类型，先加载对应的分类数据
    if (selectedSpecialty.value && categoryList.value.length === 0) {
        await getEquipmentCategoryList()
    }

    // 初始化双表格数据
    initializeTransferData()

    // 执行初始查询
    await handleSearchEquipment()
}

/** 关闭设备选择弹出框 */
const handleClose = () => {
    dialogVisible.value = false
    initializeTransferData()
}

/** 确认设备选择 */
const confirmSelection = () => {
    const deviceType = selectedSpecialty.value === 'electric' ? '设备' : '设施'

    if (chosenEquipmentList.value.length === 0) {
        ElMessage.warning(`请先选择${deviceType}`)
        return
    }

    // 触发确认事件，传递选中的设备列表
    emit('confirm', chosenEquipmentList.value)

    // 关闭对话框
    dialogVisible.value = false

    ElMessage.success(`成功选择 ${chosenEquipmentList.value.length} 个${deviceType}`)
}

// 监听 specialty 属性变化
watch(
    () => props.specialty,
    (newSpecialty) => {
        if (newSpecialty && newSpecialty !== selectedSpecialty.value) {
            selectedSpecialty.value = newSpecialty
            // 当专业类型变化时，重新加载分类数据并查询
            handleSpecialtyChange(newSpecialty)
        }
    },
    { immediate: true }
)
</script>

<style scoped>
.selected-text {
    color: #67c23a;
    font-size: 12px;
}
</style>
