export interface ResourceViewVO {
    /**
     *
     */
    id: string | number

    /**
     *
     */
    projectId: string | number

    /**
     *
     */
    typeId: string | number

    /**
     *
     */
    typeName: string
    properties: string

    /**
     * 物资单位
     */
    unit: string

    /**
     * 物资性质
     */
    nature: string

    /**
     * 规格型号
     */
    specification: string

    /**
     * 物资单价
     */
    price: number

    /**
     * 库存数量
     */
    balanceAmount: number

    /**
     * 储存地
     */
    storage: string

    /**
     * 库存数量
     */
    pictures: string
}

export interface ResourceForm extends BaseEntity {
    /**
     *
     */
    id?: string | number

    /**
     *
     */
    projectId?: string | number

    /**
     *
     */
    typeId?: string | number
    properties?: string
    unit?: string //冗余字段
    nature?: string

    /**
     * 规格型号
     */
    specification?: string

    /**
     * 物资单价
     */
    price?: number

    /**
     * 库存数量
     */
    balanceAmount?: number

    /**
     * 储存地
     */
    storage?: string

    /**
     * 库存数量
     */
    pictures?: string
}

export interface ResourceViewQuery extends PageQuery {
    /**
     *
     */
    projectId?: string | number
    typeName?: string
    /**
     *
     */
    typeId?: string | number

    /**
     * 物资单位
     */
    unit?: string

    /**
     * 物资性质
     */
    nature?: string

    /**
     * 规格型号
     */
    specification?: string

    /**
     * 物资单价
     */
    price?: number

    /**
     * 日期范围参数
     */
    params?: any
}

// 🔥 物资导入相关类型定义

// 检查重复的单个物资项
export interface ResourceCheckItem {
    typeName: string
    specification: string
    unit: string
}

// 检查重复请求类型
export interface CheckResourceExistsRequest {
    resources: ResourceCheckItem[]
}

// 重复信息响应类型
export interface ResourceDuplicateInfo {
    id: number
    typeName: string
    specification: string
    unit: string
    nature: string
    price: number
    balanceAmount: number
    createTime?: string
}

// 导入的单个物资项
export interface ResourceImportItem {
    typeName: string
    specification: string
    nature: string
    properties: string
    unit: string
    price: number
    balanceAmount: number
}

// 批量导入请求类型
export interface BatchImportResourceRequest {
    resources: ResourceImportItem[]
}

// 物资导入行数据接口（前端使用）
export interface ResourceImportRow {
    rowNumber: number
    typeName: string          // 物资名称
    specification: string     // 规格型号
    nature: string           // 物资性质（中文）
    natureCode: string       // 物资性质代码
    properties: string       // 物资属性（中文）
    propertiesArray: string[] // 物资属性数组
    unit: string             // 计量单位代码
    unitLabel: string        // 计量单位标签（中文）
    price: number            // 单价
    balanceAmount: number    // 数量
    errors: Record<string, string>
    isValid: boolean
    suggestions: {
        natures?: string[]
        properties?: string[]
        units?: string[]
    }
    validationDetails: {
        basicFields: boolean
        uniqueness: boolean
        dictionaries: boolean
    }
}
