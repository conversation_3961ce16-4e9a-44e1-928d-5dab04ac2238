<template>
    <el-dialog v-model="visible" title="选择设备" width="1400px" :before-close="handleCancel" destroy-on-close>
        <!-- 筛选条件 -->
        <div class="filter-section">
            <el-form :model="queryParams" inline>
                <el-form-item label="管理单元">
                    <el-select v-model="queryParams.unitId" placeholder="请选择管理单元" clearable @change="handleSearch">
                        <el-option v-for="unit in manageUnits" :key="unit.id" :label="unit.name" :value="unit.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="房间号">
                    <el-select v-model="queryParams.roomId" placeholder="请选择房间" clearable @change="handleSearch">
                        <el-option v-for="room in roomOptions" :key="room.id" :label="room.roomNane" :value="room.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="设备名称">
                    <el-input v-model="queryParams.name" placeholder="请输入设备名称" clearable @keyup.enter="handleSearch" style="width: 200px" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 设备选择区域 -->
        <div class="tables-container">
            <!-- 左侧：可选设备 -->
            <div class="equipment-select-area">
                <div class="area-header">
                    <span>可选设备</span>
                    <el-tag type="info" size="small">共 {{ pagination.total }} 个</el-tag>
                </div>
                <el-table v-loading="equipmentLoading" :data="availableEquipments" @selection-change="handleEquipmentSelectionChange" height="400">
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column label="设备名称" prop="remark" show-overflow-tooltip width="150px" />
                    <el-table-column label="房间编码" prop="roomCode" width="100" />
                    <el-table-column label="房间名称" prop="roomName" width="120" />
                    <el-table-column label="管理单元" prop="unitName" width="200" />
                    <el-table-column label="备注" prop="name" width="120" />
                </el-table>

                <!-- 分页 -->
                <el-pagination
                    v-model:current-page="pagination.pageNum"
                    v-model:page-size="pagination.pageSize"
                    :total="pagination.total"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>

            <!-- 右侧：已选设备 -->
            <div class="selected-equipment-area">
                <div class="selected-header">
                    <span>已选设备</span>
                    <el-tag type="success" size="small">共 {{ selectedEquipmentList.length }} 个</el-tag>
                </div>
                <el-table :data="selectedEquipmentList" height="400">
                    <el-table-column label="设备名称" prop="remark" show-overflow-tooltip width="250px" />
                    <el-table-column label="房间名称" prop="roomName" width="120" />
                    <el-table-column label="管理单元" prop="unitName" width="120" />
                    <el-table-column label="操作" width="80">
                        <template #default="scope">
                            <el-button type="text" size="small" @click="removeEquipment(scope.row)">移除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <template #footer>
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleConfirm">确定</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { listInspectionLine } from '@/api/subProject/inspection/inspectionLine'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { listRoom } from '@/api/project/room'
import { listInspectionLineDevices } from '@/api/subProject/basic/device'
import type { InspectionLineVO } from '@/api/subProject/inspection/inspectionLine/types'
import type { ManageUnitVO } from '@/api/project/manageUnit/types'
import type { RoomVO } from '@/api/project/room/types'
import type { DeviceVO } from '@/api/subProject/basic/device/types'
import { useAppStore } from '@/store/modules/app'

interface DeviceInfo {
    id: string
    name: string
    remark: string
    roomCode: string
    roomName: string
    unitId: string
    unitName: string
    deviceCategoryId: string
    inspectionLineId: string
    inspectionLineName: string
}

interface Props {
    visible: boolean
    selectedEquipments?: DeviceInfo[]
    availableInspectionLines?: InspectionLineVO[]
    specialtyType?: string
}

interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'confirm', equipments: DeviceInfo[]): void
}

const props = withDefaults(defineProps<Props>(), {
    selectedEquipments: () => [],
    availableInspectionLines: () => [],
    specialtyType: ''
})

const emit = defineEmits<Emits>()

// Store
const appStore = useAppStore()

// 响应式数据
const equipmentLoading = ref(false)
const availableEquipments = ref<DeviceVO[]>([])
const selectedEquipmentList = ref<DeviceInfo[]>([])
const availableLines = ref<InspectionLineVO[]>([])
const manageUnits = ref<ManageUnitVO[]>([])
const roomOptions = ref<RoomVO[]>([])

// 查询参数
const queryParams = reactive({
    unitId: '',
    roomId: '',
    name: ''
})

// 分页信息
const pagination = reactive({
    pageNum: 1,
    pageSize: 20,
    total: 0
})

// 计算属性
const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
})

// 初始化数据
const initData = async () => {
    try {
        // 加载管理单元
        const unitResponse = await listProjectManageUnit(appStore.projectContext.selectedProjectId)

        manageUnits.value = unitResponse.data || []

        // 加载房间数据
        const roomResponse = await listRoom({
            projectId: appStore.projectContext.selectedProjectId,
            pageNum: 1,
            pageSize: 1000
        })
        roomOptions.value = roomResponse.data || []

        // // 使用传入的巡检路线或加载所有巡检路线
        // if (props.availableInspectionLines?.length) {
        //     availableLines.value = props.availableInspectionLines
        // } else {
        //     const lineResponse = await listInspectionLine({
        //         projectId: appStore.projectContext.selectedProjectId,
        //         speciality: props.specialtyType || undefined
        //     })
        //     availableLines.value = lineResponse.data || []
        // }

        // 初始化已选设备
        selectedEquipmentList.value = [...(props.selectedEquipments || [])]

        // 加载设备数据
        await loadAvailableDevices()
    } catch (error) {
        console.error('初始化数据失败:', error)
        ElMessage.error('初始化数据失败')
    }
}

// 加载可选设备
const loadAvailableDevices = async () => {
    try {
        equipmentLoading.value = true

        const params: any = {
            pageNum: pagination.pageNum,
            pageSize: pagination.pageSize,
            projectId: appStore.projectContext.selectedProjectId,
            unitId: queryParams.unitId || undefined,
            roomId: queryParams.roomId || undefined,
            name: queryParams.name || undefined
        }

        // 如果有专业类型，则筛选对应专业的设备
        if (props.specialtyType) {
            params.specialty = props.specialtyType
        }

        const response = await listInspectionLineDevices(params)
        availableEquipments.value = response.rows || []
        pagination.total = response.total || 0
    } catch (error) {
        console.error('加载设备数据失败:', error)
        ElMessage.error('加载设备数据失败')
    } finally {
        equipmentLoading.value = false
    }
}

// 处理设备选择变化
const handleEquipmentSelectionChange = (selection: DeviceVO[]) => {
    selection.forEach((equipment) => {
        const exists = selectedEquipmentList.value.find((item) => item.id === equipment.id)
        if (!exists) {
            const deviceInfo: DeviceInfo = {
                id: String(equipment.id),
                remark: equipment.remark,
                name: equipment.name,
                roomCode: equipment.roomCode || '',
                roomName: equipment.roomName || '',
                unitId: String(equipment.unitId),
                unitName: equipment.unitName || '',
                deviceCategoryId: String(equipment.categoryIdThird || ''),
                inspectionLineId: '',
                inspectionLineName: ''
            }
            selectedEquipmentList.value.push(deviceInfo)
        }
    })
}

// 移除设备
const removeEquipment = (equipment: DeviceInfo) => {
    const index = selectedEquipmentList.value.findIndex((item) => item.id === equipment.id)
    if (index > -1) {
        selectedEquipmentList.value.splice(index, 1)
    }
}

// 查询
const handleSearch = () => {
    pagination.pageNum = 1
    loadAvailableDevices()
}

// 重置查询
const resetQuery = () => {
    queryParams.unitId = ''
    queryParams.roomId = ''
    queryParams.name = ''
    pagination.pageNum = 1
    loadAvailableDevices()
}

// 分页处理
const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    loadAvailableDevices()
}

const handleCurrentChange = (page: number) => {
    pagination.pageNum = page
    loadAvailableDevices()
}

// 确认选择
const handleConfirm = () => {
    emit('confirm', selectedEquipmentList.value)
    visible.value = false
}

// 取消
const handleCancel = () => {
    visible.value = false
}

// 监听管理单元变化，重新加载房间数据
watch(
    () => queryParams.unitId,
    async (newUnitId) => {
        if (newUnitId) {
            try {
                const roomResponse = await listRoom({
                    projectId: appStore.projectContext.selectedProjectId,
                    unitId: newUnitId,
                    pageNum: 1,
                    pageSize: 1000
                })
                roomOptions.value = roomResponse.data || []
            } catch (error) {
                console.error('加载房间数据失败:', error)
            }
        } else {
            // 重新加载所有房间
            try {
                const roomResponse = await listRoom({
                    projectId: appStore.projectContext.selectedProjectId,
                    pageNum: 1,
                    pageSize: 1000
                })
                roomOptions.value = roomResponse.data || []
            } catch (error) {
                console.error('加载房间数据失败:', error)
            }
        }
        // 清空房间选择
        queryParams.roomId = ''
    }
)

// 监听对话框显示状态
watch(
    () => props.visible,
    (newVisible) => {
        if (newVisible) {
            initData()
        }
    }
)

onMounted(() => {
    if (props.visible) {
        initData()
    }
})
</script>

<style scoped>
.filter-section {
    margin-bottom: 16px;
    padding: 16px;
    background-color: transparent;
    border-radius: 4px;
    color: white;
}

.filter-section :deep(.el-form-item__label) {
    color: white !important;
}

.filter-section :deep(.el-input__inner) {
    background-color: transparent;
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.filter-section :deep(.el-input__inner::placeholder) {
    color: rgba(255, 255, 255, 0.6);
}

.filter-section :deep(.el-select .el-input__inner) {
    background-color: transparent;
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.filter-section :deep(.el-button) {
    background-color: transparent;
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.filter-section :deep(.el-button:hover) {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.filter-section :deep(.el-button--primary) {
    background-color: rgba(64, 158, 255, 0.8);
    border-color: rgba(64, 158, 255, 0.8);
    color: white;
}

.filter-section :deep(.el-button--primary:hover) {
    background-color: rgba(64, 158, 255, 1);
    border-color: rgba(64, 158, 255, 1);
}

.tables-container {
    display: flex;
    gap: 16px;
    height: 500px;
}

.equipment-select-area {
    flex: 0 0 auto;
    width: fit-content;
    min-width: 700px;
    display: flex;
    flex-direction: column;
}

.selected-equipment-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-left: 1px solid #e4e7ed;
    padding-left: 16px;
}

.area-header,
.selected-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 600;
    color: white;
}

.el-pagination {
    margin-top: 16px;
    justify-content: center;
}
</style>
