<template>
    <div class="p-2 h-full flex flex-col">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item label="入库单号" prop="orderSn">
                            <el-input v-model="queryParams.orderSn" placeholder="请输入入库单号" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item label="入库时间" style="width: 308px">
                            <el-date-picker
                                v-model="dateRangeInDate"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <!-- 操作按钮区域 -->
        <el-card shadow="never" class="mb-4">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['common:resourceInOrder:add']">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="success"
                            plain
                            icon="Edit"
                            :disabled="single"
                            @click="handleUpdate()"
                            v-hasPermi="['common:resourceInOrder:edit']"
                            >修改</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="danger"
                            plain
                            icon="Delete"
                            :disabled="multiple"
                            @click="handleDelete()"
                            v-hasPermi="['common:resourceInOrder:remove']"
                            >删除</el-button
                        >
                    </el-col>
                    <!-- <el-col :span="1.5">
                        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['common:resourceInOrder:export']"
                            >导出</el-button
                        >
                    </el-col> -->
                    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </template>
        </el-card>

        <!-- 主表区域 (上半部分) -->
        <el-card shadow="never" class="flex-shrink-0" style="height: 45vh; min-height: 400px">
            <template #header>
                <div class="flex justify-between items-center">
                    <span>入库单列表</span>
                    <!-- <el-tag v-if="selectedInOrder" type="primary"> 已选择: {{ selectedInOrder.orderSn }} </el-tag> -->
                </div>
            </template>

            <div class="h-full overflow-hidden flex flex-col">
                <el-table
                    v-loading="loading"
                    :data="resourceInOrderList"
                    @selection-change="handleSelectionChange"
                    @row-click="handleRowClick"
                    :row-class-name="getRowClassName"
                    highlight-current-row
                    height="100%"
                    class="flex-1"
                >
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column label="" align="center" prop="id" v-if="false" />
                    <el-table-column label="入库单号" align="center" prop="orderSn" />
                    <el-table-column label="入库类型" align="center" prop="stockInType">
                        <template #default="scope">
                            <dict-tag :options="resource_instock_way" :value="scope.row.stockInType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="入库金额" align="center" prop="amount" />
                    <el-table-column label="备注" align="center" prop="remark" />

                    <el-table-column label="状态" align="center" prop="status">
                        <template #default="scope">
                            <dict-tag :options="resource_in_order_status" :value="scope.row.status" />
                        </template>
                    </el-table-column>
                    <!-- <el-table-column label="入库单附件" align="center" prop="files" /> -->
                    <el-table-column label="入库时间" align="center" prop="inDate" width="180">
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.inDate, '{y}-{m}-{d}') }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                            <el-button
                                :disabled="scope.row.status != 'toconfirm'"
                                link
                                type="primary"
                                @click="handleConfirmInbound(scope.row)"
                                v-hasPermi="['common:resourceInOrder:confirm']"
                                >确认入库</el-button
                            >
                            <el-button
                                :disabled="scope.row.status == 'confirmed'"
                                link
                                type="primary"
                                @click="handleUpdate(scope.row)"
                                v-hasPermi="['common:resourceInOrder:edit']"
                                >修改</el-button
                            >
                            <el-button
                                link
                                :disabled="scope.row.status == 'confirmed'"
                                type="primary"
                                @click="handleDelete(scope.row)"
                                v-hasPermi="['common:resourceInOrder:remove']"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 主表分页 -->
                <div class="mt-4">
                    <pagination
                        v-show="total > 0"
                        :total="total"
                        v-model:page="queryParams.pageNum"
                        v-model:limit="queryParams.pageSize"
                        @pagination="getList"
                    />
                </div>
            </div>
        </el-card>

        <!-- 明细表区域 (下半部分) -->
        <el-card shadow="never" class="flex-1 mt-4 overflow-hidden">
            <template #header>
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <span>入库明细</span>
                        <el-tag v-if="selectedInOrder" :type="getStatusTagType(selectedInOrder.status)">
                            {{ getStatusText(selectedInOrder.status) }}
                        </el-tag>
                    </div>
                    <div v-if="selectedInOrder" class="text-sm text-gray-500">
                        入库单号: {{ selectedInOrder.orderSn }} | 入库时间: {{ parseTime(selectedInOrder.inDate, '{y}-{m}-{d}') }}
                    </div>
                </div>
            </template>

            <div v-if="selectedInOrder" class="h-full overflow-hidden flex flex-col">
                <el-table v-loading="detailItemLoading" :data="detailItemList" border height="100%" class="flex-1">
                    <el-table-column type="index" label="序号" width="60" />
                    <el-table-column label="物资型号" prop="id" width="150" v-if="false" />
                    <el-table-column label="物资信息" prop="typeName" min-width="200"> </el-table-column>
                    <el-table-column label="物资型号" prop="specification" width="150" />
                    <el-table-column label="物资单位" prop="unit" width="100">
                        <template #default="scope">
                            <dict-tag :options="tnl_resource_unit" :value="scope.row.unit" />
                        </template>
                    </el-table-column>
                    <el-table-column label="物资性质" prop="nature" width="120">
                        <template #default="scope">
                            <dict-tag :options="tnl_resource_nature" :value="scope.row.nature" />
                        </template>
                    </el-table-column>
                    <el-table-column label="入库数量" prop="quantity" width="120" align="right">
                        <template #default="scope">
                            <el-tag type="success">{{ scope.row.quantity }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="单价" prop="price" width="120" align="right">
                        <template #default="scope"> ¥{{ scope.row.price?.toFixed(2) || '0.00' }} </template>
                    </el-table-column>
                    <el-table-column label="金额" prop="amount" width="120" align="right">
                        <template #default="scope">
                            <span class="font-bold text-red-500"> ¥{{ scope.row.amount?.toFixed(2) || '0.00' }} </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" prop="remark" min-width="150" show-overflow-tooltip />
                </el-table>

                <!-- 明细分页和统计 -->
                <div class="mt-4 space-y-4">
                    <!-- 统计信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <el-row :gutter="20">
                            <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-blue-600">{{ getTotalQuantity() }}</div>
                                    <div class="text-sm text-gray-500">总数量</div>
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-green-600">{{ detailItemList.length }}</div>
                                    <div class="text-sm text-gray-500">明细条数</div>
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-red-600">¥{{ getTotalAmount() }}</div>
                                    <div class="text-sm text-gray-500">总金额</div>
                                </div>
                            </el-col>
                            <!-- <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-purple-600">¥{{ getAvgPrice() }}</div>
                                    <div class="text-sm text-gray-500">平均单价</div>
                                </div>
                            </el-col> -->
                        </el-row>
                    </div>

                    <!-- 明细分页 -->
                    <pagination
                        v-show="detailItemTotal > 0"
                        :total="detailItemTotal"
                        v-model:page="detailItemQueryParams.pageNum"
                        v-model:limit="detailItemQueryParams.pageSize"
                        @pagination="getDetailItemList"
                    />
                </div>
            </div>
        </el-card>
        <!-- 添加或修改入库单对话框 -->
        <el-dialog :title="isEditMode ? '修改入库单' : '添加入库单'" v-model="dialog.visible" width="90%" append-to-body>
            <el-form ref="resourceInOrderFormRef" :model="form" :rules="rules" label-width="100px">
                <!-- 基本信息区域 -->
                <el-card shadow="never" class="mb-4">
                    <template #header>
                        <span>基本信息</span>
                    </template>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="入库单号" prop="orderSn">
                                <el-input v-model="form.orderSn" placeholder="自动生成" :disabled="true" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="入库类型" prop="stockInType">
                                <el-select v-model="form.stockInType" placeholder="请选择入库类型" style="width: 300px">
                                    <el-option
                                        v-for="dict in resource_instock_way"
                                        :key="dict.value"
                                        :label="dict.label"
                                        :value="dict.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="入库时间" prop="inDate">
                                <el-date-picker
                                    v-model="form.inDate"
                                    type="datetime"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                    placeholder="请选择入库时间"
                                    style="width: 100%"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="备注" prop="remark">
                                <el-input v-model="form.remark" type="textarea" rows="2" placeholder="请输入备注" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>

                <!-- 入库明细区域 -->
                <el-card shadow="never" class="mb-4">
                    <template #header>
                        <div style="display: flex; justify-content: space-between; align-items: center">
                            <span>入库明细</span>
                            <div>
                                <el-button type="primary" size="small" @click="addItem">
                                    <el-icon><Plus /></el-icon>
                                    添加明细
                                </el-button>
                                <el-button type="success" size="small" @click="showImportDialog">
                                    <el-icon><Upload /></el-icon>
                                    导入明细
                                </el-button>
                                <!-- <el-button type="info" size="small" @click="downloadTemplate">
                                    <el-icon><Download /></el-icon>
                                    下载模板
                                </el-button> -->
                                <el-button type="warning" size="small" @click="clearAllItems" v-if="itemList.length > 0">
                                    <el-icon><Delete /></el-icon>
                                    清空明细
                                </el-button>
                            </div>
                        </div>
                    </template>

                    <el-table
                        :data="itemList"
                        border
                        style="width: 100%"
                        class="inbound-detail-table"
                        @cell-click="handleCellClick"
                        :key="tableRefreshKey"
                        row-key="getRowKey"
                    >
                        <!-- 🔥 增强的序号列，显示调试信息 -->
                        <el-table-column label="序号" width="80" align="center">
                            <template #default="{ row, $index }">
                                <div>
                                    <div style="font-weight: bold">{{ $index + 1 }}</div>
                                    <div v-if="row.tempId" style="font-size: 10px; color: #999">
                                        {{ row.tempId ? '导入' : '手动' }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="物资信息" min-width="250">
                            <template #default="scope">
                                <el-select
                                    v-model="scope.row.resourceId"
                                    placeholder="请输入物资类型名称搜索"
                                    filterable
                                    remote
                                    :remote-method="(keyword) => searchResourceForRow(keyword, scope.$index)"
                                    :loading="scope.row.searching"
                                    @change="(value) => handleResourceSelect(value, scope.$index)"
                                    @keyup.enter="moveToNextCell(scope.$index, 'quantity')"
                                    clearable
                                    style="width: 100%"
                                    :ref="(el) => setResourceSelectRef(el, scope.$index)"
                                >
                                    <!-- 调试信息 -->
                                    <!-- <template #empty>
                                        <div>选项数量: {{ (scope.row.resourceOptions || []).length }}</div>
                                    </template> -->
                                    <!-- 当前选中的物资信息（只显示typeName） -->
                                    <el-option
                                        v-if="
                                            scope.row.resourceInfo &&
                                            scope.row.resourceId &&
                                            !scope.row.resourceOptions?.find((opt) => opt.id === scope.row.resourceId)
                                        "
                                        :key="scope.row.resourceInfo.id"
                                        :label="scope.row.resourceInfo.typeName"
                                        :value="scope.row.resourceInfo.id"
                                    >
                                        <div>
                                            <div style="font-weight: bold; color: #409eff">{{ scope.row.resourceInfo.typeName }}</div>
                                            <div style="font-size: 12px; color: #999">
                                                规格: {{ scope.row.resourceInfo.specification }} | 库存: {{ scope.row.resourceInfo.balanceAmount }}
                                            </div>
                                        </div>
                                    </el-option>
                                    <!-- 搜索结果选项（下拉时显示typeName(specification)） -->
                                    <el-option v-for="item in scope.row.resourceOptions || []" :key="item.id" :label="item.typeName" :value="item.id">
                                        <div>
                                            <div style="font-weight: bold; color: #409eff">{{ item.typeName }}({{ item.specification }})</div>
                                            <div style="font-size: 12px; color: #999">
                                                规格: {{ item.specification }} | 库存: {{ item.balanceAmount }}
                                            </div>
                                        </div>
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="物资型号" width="150">
                            <template #default="scope">
                                <el-input v-model="scope.row.specification" placeholder="自动填充" readonly style="width: 100%" />
                            </template>
                        </el-table-column>
                        <el-table-column label="物资单位" width="120">
                            <template #default="scope">
                                <el-input
                                    :value="getDictLabel(tnl_resource_unit, scope.row.resourceUnit)"
                                    placeholder="自动填充"
                                    readonly
                                    style="width: 100%"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column label="物资性质" width="120">
                            <template #default="scope">
                                <el-input
                                    :value="getDictLabel(tnl_resource_nature, scope.row.resourceNature)"
                                    placeholder="自动填充"
                                    readonly
                                    style="width: 100%"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column label="当前库存" width="100">
                            <template #default="scope">
                                <div style="text-align: center; color: #606266; font-weight: 500">
                                    {{ scope.row.resourceInfo?.balanceAmount || '-' }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="入库数量" width="120">
                            <template #default="scope">
                                <el-input
                                    v-model="scope.row.quantity"
                                    placeholder="请输入数量"
                                    @input="handleQuantityInput(scope.row)"
                                    @keyup.enter="moveToNextCell(scope.$index, 'price')"
                                    style="width: 100%"
                                    :ref="(el) => setQuantityInputRef(el, scope.$index)"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column label="单价" width="120">
                            <template #default="scope">
                                <el-input
                                    v-model="scope.row.price"
                                    placeholder="请输入单价"
                                    @input="handlePriceInput(scope.row)"
                                    @keyup.enter="moveToNextCell(scope.$index, 'remark')"
                                    style="width: 100%"
                                    :ref="(el) => setPriceInputRef(el, scope.$index)"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column label="金额" width="100">
                            <template #default="scope">
                                <span style="color: #f56c6c; font-weight: bold"> ¥{{ scope.row.amount?.toFixed(2) || '0.00' }} </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="备注" min-width="150">
                            <template #default="scope">
                                <el-input
                                    v-model="scope.row.remark"
                                    placeholder="请输入备注"
                                    @keyup.enter="moveToNextCell(scope.$index, 'next')"
                                    :ref="(el) => setRemarkInputRef(el, scope.$index)"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="80">
                            <template #default="scope">
                                <el-button type="text" @click="removeItem(scope.$index)" style="color: #f56c6c"> 删除 </el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 合计信息 -->
                    <div v-if="validItemsCount > 0" style="margin-top: 16px; padding: 16px; background-color: transparent; border-radius: 4px">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div style="font-size: 16px; font-weight: bold">
                                    总数量: <span style="color: #409eff">{{ totalQuantity }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div style="font-size: 16px; font-weight: bold">
                                    总金额: <span style="color: #f56c6c">¥{{ totalAmount.toFixed(2) }}</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-card>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">
                        {{ isEditMode ? '更 新' : '确 定' }}
                    </el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 🔥 批量导入对话框 -->
        <el-dialog v-model="importDialog.visible" title="批量导入入库明细" width="900px" append-to-body>
            <!-- 文件上传区域 -->
            <el-card shadow="never" class="mb-4">
                <template #header>
                    <span>选择Excel文件</span>
                </template>

                <el-upload
                    ref="uploadRef"
                    :limit="1"
                    accept=".xlsx,.xls"
                    :auto-upload="false"
                    :on-change="handleFileChange"
                    :on-remove="handleFileRemove"
                    drag
                    style="width: 100%"
                >
                    <el-icon class="el-icon--upload" style="font-size: 67px; color: #c0c4cc">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
                    <template #tip>
                        <div class="el-upload__tip" style="color: #999; font-size: 12px">
                            只能上传xlsx/xls文件，且不超过10MB
                            <el-link type="primary" @click="downloadTemplate" style="margin-left: 10px"> 下载导入模板 </el-link>
                        </div>
                    </template>
                </el-upload>
            </el-card>

            <!-- 解析结果预览 -->
            <el-card v-if="importPreviewData.length > 0" shadow="never">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center">
                        <span>数据预览 (共 {{ importPreviewData.length }} 条)</span>
                        <div>
                            <el-tag v-if="validImportCount > 0" type="success"> {{ validImportCount }} 条有效 </el-tag>
                            <el-tag v-if="errorImportCount > 0" type="danger" style="margin-left: 8px"> {{ errorImportCount }} 条错误 </el-tag>
                        </div>
                    </div>
                </template>

                <!-- 简化的预览表格 -->
                <el-table :data="importPreviewData" border style="width: 100%" max-height="400" size="small" :row-class-name="getImportRowClassName">
                    <el-table-column prop="rowNumber" label="行号" width="60" align="center" />

                    <!-- 物资类别列 -->
                    <el-table-column label="物资类别" width="120">
                        <template #default="{ row }">
                            <div :class="getFieldClassName(row, 'typeName')">
                                {{ row.typeName }}
                                <el-icon v-if="row.errors.typeName" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 物资单位列 -->
                    <el-table-column label="物资单位" width="100">
                        <template #default="{ row }">
                            <div :class="getFieldClassName(row, 'unit')">
                                {{ row.unitLabel || getUnitLabelByCode(row.unit) }}
                                <el-icon v-if="row.errors.unit" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 规格型号列 -->
                    <el-table-column label="规格型号" width="150">
                        <template #default="{ row }">
                            <div :class="getFieldClassName(row, 'specification')">
                                {{ row.specification }}
                                <el-icon v-if="row.errors.specification" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 数量列 -->
                    <el-table-column prop="amount" label="数量" width="80" align="right">
                        <template #default="{ row }">
                            <div :class="getFieldClassName(row, 'amount')">
                                {{ row.amount }}
                                <el-icon v-if="row.errors.amount" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 单价列 -->
                    <el-table-column prop="price" label="单价" width="80" align="right">
                        <template #default="{ row }">
                            <div :class="getFieldClassName(row, 'price')">
                                {{ row.price }}
                                <el-icon v-if="row.errors.price" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="remark" label="备注" min-width="100" />

                    <!-- 验证状态列 -->
                    <el-table-column label="验证状态" width="100" align="center">
                        <template #default="{ row }">
                            <div>
                                <el-tag v-if="row.isValid" type="success" size="small">通过</el-tag>
                                <el-tag v-else type="danger" size="small">失败</el-tag>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 🔥 行级错误提示区域 -->
                <div v-if="importPreviewData.some((row) => !row.isValid)" style="margin-top: 16px">
                    <el-card shadow="never" style="background-color: #fef0f0; border: 1px solid #fbc4c4">
                        <template #header>
                            <div style="color: #f56c6c; font-weight: bold">
                                <el-icon><Warning /></el-icon>
                                数据验证错误详情
                            </div>
                        </template>

                        <div class="error-details">
                            <div
                                v-for="row in importPreviewData.filter((r) => !r.isValid)"
                                :key="row.rowNumber"
                                class="error-row-detail"
                                style="margin-bottom: 12px; padding: 8px; background-color: #fff; border-radius: 4px"
                            >
                                <div style="font-weight: bold; color: #f56c6c; margin-bottom: 4px">第 {{ row.rowNumber }} 行错误：</div>

                                <!-- 基础字段错误 -->
                                <div v-if="Object.keys(row.errors).some((key) => key !== 'resourceMatch')" style="margin-bottom: 4px">
                                    <span style="color: #909399">基础字段：</span>
                                    <span v-for="(error, field) in row.errors" :key="field" style="color: #f56c6c">
                                        <span v-if="field !== 'resourceMatch'">{{ error }}；</span>
                                    </span>
                                </div>

                                <!-- 物资匹配错误 -->
                                <div v-if="row.errors.resourceMatch" style="margin-bottom: 4px">
                                    <span style="color: #909399">物资匹配：</span>
                                    <span style="color: #f56c6c">{{ row.errors.resourceMatch }}</span>
                                </div>

                                <!-- 🔥 智能建议信息 -->
                                <div v-if="row.suggestions" style="font-size: 12px; color: #606266; margin-top: 4px">
                                    <div v-if="row.suggestions.typeNames?.length > 0" style="margin-bottom: 2px">
                                        <span style="color: #909399">建议的物资类别：</span>
                                        <span style="color: #409eff">{{ row.suggestions.typeNames.join('、') }}</span>
                                    </div>
                                    <div v-if="row.suggestions.units?.length > 0" style="margin-bottom: 2px">
                                        <span style="color: #909399">建议的计量单位：</span>
                                        <span style="color: #409eff">{{ row.suggestions.units.join('、') }}</span>
                                    </div>
                                    <div v-if="row.suggestions.specifications?.length > 0" style="margin-bottom: 2px">
                                        <span style="color: #909399">建议的规格型号：</span>
                                        <span style="color: #409eff">{{ row.suggestions.specifications.join('、') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </div>
            </el-card>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelImport">取消</el-button>
                    <el-button type="primary" @click="parseExcelData" :loading="parsing" v-if="selectedFile && importPreviewData.length === 0">
                        解析数据
                    </el-button>
                    <el-button
                        type="success"
                        @click="confirmImport"
                        :disabled="errorImportCount > 0 || importPreviewData.length === 0"
                        :loading="importing"
                    >
                        确认导入 ({{ validImportCount }} 条)
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ResourceInOrder" lang="ts">
import {
    listResourceInOrder,
    getResourceInOrder,
    delResourceInOrder,
    addResourceInOrder,
    updateResourceInOrder,
    confirmResourceInOrder
} from '@/api/common/resourceInOrder'
import { ResourceInOrderVO, ResourceInOrderQuery, ResourceInOrderForm } from '@/api/common/resourceInOrder/types'
import { ResourceInOrderItemForm, ResourceInOrderItemViewVO } from '@/api/common/resourceInOrderItem/types'
import { getResourceInforderItemView } from '@/api/common/resourceInOrderItem'
import { listResource } from '@/api/common/resource'
import { ResourceViewVO, ResourceViewQuery } from '@/api/common/resource/types'
import * as XLSX from 'xlsx'
import dayjs from 'dayjs'
import { useDict } from '@/utils/dict'
import { Plus, Upload, Download, Delete, Warning, UploadFilled } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance

// 字典数据
const { tnl_resource_unit, tnl_resource_nature, resource_instock_way, resource_in_order_status } = toRefs<any>(
    proxy?.useDict('tnl_resource_unit', 'tnl_resource_nature', 'resource_instock_way', 'resource_in_order_status')
)

// 🔥 导入相关数据类型定义
interface ImportRow {
    rowNumber: number
    typeName: string
    unit: string
    unitLabel: string
    specification: string
    amount: number
    price: number
    remark: string
    errors: {
        typeName?: string
        unit?: string
        specification?: string
        amount?: string
        price?: string
        resourceMatch?: string
    }
    isValid: boolean
    resourceId: string | null
    resourceInfo: any
    suggestions: {
        typeNames?: string[]
        units?: string[]
        specifications?: string[]
    }
    validationDetails: {
        basicFields: boolean
        resourceMatch: boolean
    }
}

// 🔥 导入相关响应式数据
const importDialog = reactive({
    visible: false
})

const selectedFile = ref<any>(null)
const importPreviewData = ref<ImportRow[]>([])
const validImportCount = ref(0)
const errorImportCount = ref(0)
const parsing = ref(false)
const importing = ref(false)
const uploadRef = ref()

// 🔥 表格刷新相关
const tableRefreshKey = ref(0)

// 生成行的唯一key
const getRowKey = (row: any) => {
    // 优先使用临时ID，其次使用resourceId，最后使用索引
    return row.tempId || row.resourceId || `row_${Date.now()}_${Math.random()}`
}

// 主从表相关数据
const selectedInOrderId = ref<string | number | null>(null)
const selectedInOrder = ref<ResourceInOrderVO | null>(null)

// 明细查看表相关数据
const detailItemList = ref<ResourceInOrderItemViewVO[]>([])
const detailItemLoading = ref(false)
const detailItemTotal = ref(0)
const detailItemQueryParams = ref({
    pageNum: 1,
    pageSize: 10
})

// 编辑状态管理
const isEditMode = ref(false) // 是否为编辑模式
const editingOrderId = ref<string>('') // 正在编辑的入库单ID

// 扩展表单类型，添加前端需要的字段
interface ExtendedResourceInOrderForm extends ResourceInOrderForm {
    resourceId?: string | number
}

// 扩展明细项类型，添加前端需要的字段
interface ExtendedResourceInOrderItemForm extends ResourceInOrderItemForm {
    resourceOptions?: ResourceViewVO[] // 当前行的物资搜索选项
    resourceInfo?: ResourceViewVO // 选中的物资信息
    searching?: boolean // 搜索状态
    specification?: string // 物资型号
    resourceUnit?: string // 物资单位
    resourceNature?: string // 物资性质
}

const resourceInOrderList = ref<ResourceInOrderVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRangeInDate = ref<[DateModelType, DateModelType]>(['', ''])

// 入库明细
const itemList = ref<ExtendedResourceInOrderItemForm[]>([])
const totalQuantity = ref(0)
const totalAmount = ref(0)

// 计算有效明细数量（填写了物资信息的行）
const validItemsCount = computed(() => {
    return itemList.value.filter((item) => item.resourceId).length
})

// 表格输入框引用
const resourceSelectRefs = ref<any[]>([])
const unitSelectRefs = ref<any[]>([])
const natureSelectRefs = ref<any[]>([])
const quantityInputRefs = ref<any[]>([])
const priceInputRefs = ref<any[]>([])
const remarkInputRefs = ref<any[]>([])

// 设置引用的方法
const setResourceSelectRef = (el: any, index: number) => {
    if (el) resourceSelectRefs.value[index] = el
}
const setUnitSelectRef = (el: any, index: number) => {
    if (el) unitSelectRefs.value[index] = el
}
const setNatureSelectRef = (el: any, index: number) => {
    if (el) natureSelectRefs.value[index] = el
}
const setQuantityInputRef = (el: any, index: number) => {
    if (el) quantityInputRefs.value[index] = el
}
const setPriceInputRef = (el: any, index: number) => {
    if (el) priceInputRefs.value[index] = el
}
const setRemarkInputRef = (el: any, index: number) => {
    if (el) remarkInputRefs.value[index] = el
}

const queryFormRef = ref<ElFormInstance>()
const resourceInOrderFormRef = ref<ElFormInstance>()

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

const initFormData: ExtendedResourceInOrderForm = {
    id: undefined,
    resourceId: undefined,
    orderSn: undefined,
    stockInType: undefined,
    amount: undefined,
    remark: undefined,
    status: undefined,
    files: undefined,
    inDate: undefined
}
const data = reactive<PageData<ExtendedResourceInOrderForm, ResourceInOrderQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderSn: undefined,
        stockInType: undefined,
        params: {
            inDate: undefined
        }
    },
    rules: {
        stockInType: [{ required: true, message: '请选择入库类型', trigger: 'change' }],
        inDate: [{ required: true, message: '请选择入库时间', trigger: 'change' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询入库单列表 */
const getList = async () => {
    loading.value = true
    queryParams.value.params = {}
    proxy?.addDateRange(queryParams.value, dateRangeInDate.value, 'InDate')
    const res = await listResourceInOrder(queryParams.value)
    resourceInOrderList.value = res.rows
    total.value = res.total
    loading.value = false
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    resourceInOrderFormRef.value?.resetFields()
    // 重置明细数据
    itemList.value = []
    totalQuantity.value = 0
    totalAmount.value = 0
    // 重置编辑状态
    isEditMode.value = false
    editingOrderId.value = ''
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    dateRangeInDate.value = ['', '']
    queryFormRef.value?.resetFields()
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ResourceInOrderVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    isEditMode.value = false
    editingOrderId.value = ''

    // 生成入库单号
    form.value.orderSn = generateOrderSn()
    // 设置默认入库时间为当前时间
    form.value.inDate = proxy?.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
    // 初始化5行明细
    initializeItems()
    dialog.visible = true
    dialog.title = '添加入库单'
}

/** 初始化明细行 */
const initializeItems = () => {
    itemList.value = []
    for (let i = 0; i < 5; i++) {
        addItem()
    }
}

/** 生成入库单号 */
const generateOrderSn = (): string => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hour = String(now.getHours()).padStart(2, '0')
    const minute = String(now.getMinutes()).padStart(2, '0')
    const second = String(now.getSeconds()).padStart(2, '0')
    const millisecond = String(now.getMilliseconds()).padStart(3, '0')

    return `${year}${month}${day}-${hour}${minute}${second}-${millisecond}`
}

/** 获取编辑模式的入库明细 */
const getInOrderItemsForEdit = async (orderId: string | number) => {
    try {
        // 使用现有的getResourceInforderItemView API
        const response = await getResourceInforderItemView(orderId)

        // 处理返回的明细数据
        const itemsData = Array.isArray(response.data) ? response.data : [response.data]

        // 清空现有明细
        itemList.value = []

        // 转换明细数据为表单编辑格式
        for (const item of itemsData) {
            // 创建物资信息对象
            const resourceInfo = {
                id: item.resourceId,
                projectId: '',
                typeId: '',
                typeName: item.typeName,
                properties: '',
                specification: item.specification,
                unit: item.unit,
                nature: item.nature,
                balanceAmount: item.balanceAmount,
                price: item.price,
                storage: '',
                pictures: ''
            }

            // 创建新的明细项
            const newItem = reactive<ExtendedResourceInOrderItemForm>({
                resourceId: item.resourceId,
                quantity: item.quantity,
                price: item.price,
                amount: item.amount,
                remark: item.remark,
                resourceOptions: [resourceInfo], // 将当前物资信息添加到选项中
                resourceInfo: resourceInfo,
                searching: false,
                specification: item.specification,
                resourceUnit: item.unit,
                resourceNature: item.nature
            })

            itemList.value.push(newItem)
        }

        // 如果明细少于5行，补充空行到5行
        while (itemList.value.length < 5) {
            addItem()
        }

        // 重新计算总计
        calculateTotal()
    } catch (error) {
        console.error('获取入库明细失败:', error)
        proxy?.$modal.msgError('获取入库明细失败')
        // 如果获取失败，至少提供5个空行
        itemList.value = []
        for (let i = 0; i < 5; i++) {
            addItem()
        }
    }
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ResourceInOrderVO) => {
    loading.value = true
    try {
        reset()
        isEditMode.value = true
        const _id = row?.id || ids.value[0]
        editingOrderId.value = _id.toString()

        // 获取入库单基本信息
        const res = await getResourceInOrder(_id)
        Object.assign(form.value, res.data)

        // 获取入库明细列表
        await getInOrderItemsForEdit(_id)

        dialog.visible = true
        dialog.title = '修改入库单'
    } catch (error) {
        console.error('获取入库单信息失败:', error)
        proxy?.$modal.msgError('获取入库单信息失败')
    } finally {
        loading.value = false
    }
}

/** 提交按钮 */
const submitForm = () => {
    resourceInOrderFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            // 过滤出填写了物资信息的明细行
            const validItems = itemList.value.filter((item) => item.resourceId)

            // 验证明细
            if (validItems.length === 0) {
                proxy?.$modal.msgWarning('请至少添加一条有效的入库明细')
                return
            }

            // 验证每个有效明细的数据
            for (let i = 0; i < validItems.length; i++) {
                const item = validItems[i]
                const originalIndex = itemList.value.findIndex((originalItem) => originalItem === item)

                if (!item.quantity || item.quantity <= 0) {
                    proxy?.$modal.msgWarning(`第${originalIndex + 1}行明细的入库数量必须大于0`)
                    return
                }
                if (!item.price || item.price <= 0) {
                    proxy?.$modal.msgWarning(`第${originalIndex + 1}行明细的单价必须大于0`)
                    return
                }
            }

            buttonLoading.value = true
            try {
                // 过滤出有效的明细数据
                const validItems = itemList.value.filter((item) => item.resourceId)

                // 准备明细数据，移除前端扩展字段
                const resourceInOrderItems = validItems.map((item) => ({
                    resourceId: item.resourceId,
                    quantity: item.quantity,
                    price: item.price,
                    amount: item.amount,
                    remark: item.remark
                }))

                // 准备提交数据，包含明细
                const submitData = {
                    id: form.value.id,
                    orderSn: form.value.orderSn,
                    stockInType: form.value.stockInType,
                    amount: form.value.amount,
                    remark: form.value.remark,
                    // status: form.value.status,
                    status: 'toconfirm',
                    files: form.value.files,
                    inDate: form.value.inDate,
                    resourceInOrderItems: resourceInOrderItems
                }

                if (isEditMode.value) {
                    // 更新模式
                    await updateResourceInOrder(submitData)
                    proxy?.$modal.msgSuccess('修改成功')
                } else {
                    // 新增模式
                    submitData.status = 'toconfirm'
                    await addResourceInOrder(submitData)
                    proxy?.$modal.msgSuccess('新增成功')
                }
                dialog.visible = false
                await getList()
            } catch (error) {
                console.error('提交失败:', error)
                proxy?.$modal.msgError('操作失败')
            } finally {
                buttonLoading.value = false
            }
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: ResourceInOrderVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除入库单编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delResourceInOrder(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 确认入库按钮操作 */
const handleConfirmInbound = async (row: ResourceInOrderVO) => {
    await proxy?.$modal.confirm(`是否确认入库单号为"${row.orderSn}"的入库操作？确认后将更新物资库存。`)
    loading.value = true
    try {
        await confirmResourceInOrder(row.id!)
        proxy?.$modal.msgSuccess('确认入库成功')
        await getList()
    } catch (error) {
        console.error('确认入库失败:', error)
        proxy?.$modal.msgError('确认入库失败')
    } finally {
        loading.value = false
    }
}

/** 主表行点击事件 */
const handleRowClick = (row: ResourceInOrderVO) => {
    selectedInOrderId.value = row.id
    selectedInOrder.value = row
    detailItemQueryParams.value.pageNum = 1 // 重置到第一页
    getDetailItemList()
}

/** 获取入库明细列表 */
const getDetailItemList = async () => {
    if (!selectedInOrderId.value) return

    detailItemLoading.value = true
    try {
        const response = await getResourceInforderItemView(selectedInOrderId.value)

        // 根据API返回的单个对象结构调整
        if (response.data) {
            // 如果返回的是单个ResourceInOrderItemViewVO对象，转换为数组
            detailItemList.value = Array.isArray(response.data) ? response.data : [response.data]
            detailItemTotal.value = detailItemList.value.length
        } else {
            detailItemList.value = []
            detailItemTotal.value = 0
        }
    } catch (error) {
        console.error('获取入库明细失败:', error)
        proxy?.$modal.msgError('获取入库明细失败')
        detailItemList.value = []
        detailItemTotal.value = 0
    } finally {
        detailItemLoading.value = false
    }
}

/** 获取行样式类名 */
const getRowClassName = ({ row }: { row: ResourceInOrderVO }) => {
    return selectedInOrderId.value === row.id ? 'selected-row' : ''
}

/** 统计方法 */
const getTotalQuantity = () => {
    return detailItemList.value.reduce((sum, item) => sum + (item.quantity || 0), 0)
}

const getTotalAmount = () => {
    const total = detailItemList.value.reduce((sum, item) => sum + (item.amount || 0), 0)
    return total.toFixed(2)
}

const getAvgPrice = () => {
    if (detailItemList.value.length === 0) return '0.00'
    const total = detailItemList.value.reduce((sum, item) => sum + (item.price || 0), 0)
    return (total / detailItemList.value.length).toFixed(2)
}

/** 状态相关方法 */
const getStatusTagType = (status: string) => {
    const statusMap = {
        'waiting': 'warning',
        'confirmed': 'success',
        'cancelled': 'danger'
    }
    return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
    const statusMap = {
        'waiting': '待入库',
        'confirmed': '已确认',
        'cancelled': '已取消'
    }
    return statusMap[status] || status
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'common/resourceInOrder/export',
        {
            ...queryParams.value
        },
        `resourceInOrder_${new Date().getTime()}.xlsx`
    )
}

/** 添加明细 */
const addItem = () => {
    const newItem = reactive<ExtendedResourceInOrderItemForm>({
        resourceId: undefined,
        quantity: 1,
        price: 0,
        amount: 0,
        remark: '',
        resourceOptions: [],
        resourceInfo: undefined,
        searching: false,
        specification: undefined,
        resourceUnit: undefined,
        resourceNature: undefined
    })

    itemList.value.push(newItem)
    calculateTotal()
}

/** 清空所有明细 */
const clearAllItems = () => {
    itemList.value = []
    calculateTotal()
}

// 🔥 ==================== 批量导入相关方法 ====================

// 显示导入对话框
const showImportDialog = () => {
    importDialog.visible = true
    resetImportDialog()
}

// 重置导入对话框
const resetImportDialog = () => {
    selectedFile.value = null
    importPreviewData.value = []
    validImportCount.value = 0
    errorImportCount.value = 0
    parsing.value = false
    importing.value = false

    // 清空上传组件
    if (uploadRef.value) {
        uploadRef.value.clearFiles()
    }
}

// 取消导入
const cancelImport = () => {
    importDialog.visible = false
    resetImportDialog()
}

// 文件选择处理
const handleFileChange = (file: any) => {
    selectedFile.value = file
    importPreviewData.value = []
    validImportCount.value = 0
    errorImportCount.value = 0

    // 验证文件类型和大小
    const isExcel = file.name.endsWith('.xlsx') || file.name.endsWith('.xls')
    const isLt10M = file.size / 1024 / 1024 < 10

    if (!isExcel) {
        proxy?.$modal.msgError('只能上传Excel文件!')
        handleFileRemove()
        return false
    }

    if (!isLt10M) {
        proxy?.$modal.msgError('文件大小不能超过10MB!')
        handleFileRemove()
        return false
    }

    return true
}

// 文件移除处理
const handleFileRemove = () => {
    selectedFile.value = null
    importPreviewData.value = []
    validImportCount.value = 0
    errorImportCount.value = 0
}

// 🔥 数据字典转换方法

// 中文单位转英文代码
const getUnitCodeByLabel = (unitLabel: string): string | null => {
    if (!unitLabel?.trim()) return null

    // 从数据字典中查找对应的代码
    const unitItem = tnl_resource_unit.value?.find((item: any) => item.label === unitLabel.trim())

    if (unitItem) {
        return unitItem.value
    }

    // 如果字典中没找到，使用本地映射作为备用
    const unitMapping: Record<string, string> = {
        '米': 'm',
        '个': 'pcs',
        '千克': 'kg',
        '套': 'set',
        '吨': 'ton',
        '箱': 'box',
        '瓶': 'bottle',
        '平方米': 'm2',
        '立方米': 'm3',
        '公斤': 'kg',
        '克': 'g'
    }

    return unitMapping[unitLabel.trim()] || null
}

// 英文代码转中文单位
const getUnitLabelByCode = (unitCode: string): string => {
    if (!unitCode?.trim()) return '未知'

    // 从数据字典中查找对应的中文名称
    const unitItem = tnl_resource_unit.value?.find((item: any) => item.value === unitCode.trim())

    if (unitItem) {
        return unitItem.label
    }

    // 如果字典中没找到，使用本地映射作为备用
    const unitMapping: Record<string, string> = {
        'm': '米',
        'pcs': '个',
        'kg': '千克',
        'set': '套',
        'ton': '吨',
        'box': '箱',
        'bottle': '瓶',
        'm2': '平方米',
        'm3': '立方米',
        'g': '克'
    }

    return unitMapping[unitCode.trim()] || unitCode
}

// 🔥 样式相关方法

// 获取导入行样式类名
const getImportRowClassName = ({ row }: { row: ImportRow }) => {
    if (!row.isValid) {
        return 'error-row'
    }
    return ''
}

// 获取字段样式类名
const getFieldClassName = (row: ImportRow, field: string) => {
    const hasFieldError = row.errors[field as keyof typeof row.errors]

    if (hasFieldError) {
        return 'error-text'
    }
    return ''
}

// 🔥 Excel处理方法

// 下载导入模板
const downloadTemplate = () => {
    const templateData = [
        // 标题行
        ['物资类别*', '物资单位*', '规格型号*', '入库数量*', '单价*', '备注'],
        // 示例数据使用中文单位
        ['建筑材料', '吨', 'HRB400 Φ12', '10', '4500.00', '用于主体结构'],
        ['建筑材料', '吨', 'P.O 42.5', '50', '350.00', '普通硅酸盐水泥'],
        ['电气材料', '米', 'YJV 3×120+1×70', '100', '85.50', '主干线电缆']
    ]

    // 创建工作簿
    const ws = XLSX.utils.aoa_to_sheet(templateData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '入库明细')

    // 添加单位对照说明
    const instructionData = [
        ['入库明细导入说明'],
        [''],
        ['1. 支持的物资单位：'],
        ['   - 米、千克、吨、个、套、箱、瓶'],
        ['   - 平方米、立方米、克等'],
        ['   - 请使用中文单位名称'],
        [''],
        ['2. 验证规则：'],
        ['   - 系统将根据"物资类别+物资单位+规格型号"三个字段组合验证'],
        ['   - 物资单位会自动转换为系统代码进行匹配'],
        ['   - 三个字段必须完全匹配系统中的物资信息'],
        [''],
        ['3. 常用单位对照：'],
        ['   - 米 → m', '千克 → kg', '吨 → ton'],
        ['   - 个 → pcs', '套 → set', '箱 → box']
    ]

    const instructionWs = XLSX.utils.aoa_to_sheet(instructionData)
    XLSX.utils.book_append_sheet(wb, instructionWs, '填写说明')

    // 下载文件
    XLSX.writeFile(wb, `入库明细导入模板_${dayjs().format('YYYYMMDD')}.xlsx`)
}

// 解析Excel文件
const parseExcelFile = async (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()

        reader.onload = (e) => {
            try {
                const data = new Uint8Array(e.target?.result as ArrayBuffer)
                const workbook = XLSX.read(data, { type: 'array' })

                // 读取第一个工作表
                const firstSheetName = workbook.SheetNames[0]
                const worksheet = workbook.Sheets[firstSheetName]

                // 转换为JSON数据
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

                // 跳过标题行，转换数据格式
                const rows = jsonData.slice(1) as any[]
                const parsedData = rows
                    .filter((row) => row && row.length > 0 && row.some((cell: any) => cell !== undefined && cell !== ''))
                    .map((row: any[]) => ({
                        typeName: row[0]?.toString()?.trim() || '',
                        unit: row[1]?.toString()?.trim() || '',
                        specification: row[2]?.toString()?.trim() || '',
                        amount: row[3]?.toString()?.trim() || '',
                        price: row[4]?.toString()?.trim() || '',
                        remark: row[5]?.toString()?.trim() || ''
                    }))

                resolve(parsedData)
            } catch (error) {
                reject(new Error('Excel文件格式错误，请检查文件内容'))
            }
        }

        reader.onerror = () => {
            reject(new Error('文件读取失败'))
        }

        reader.readAsArrayBuffer(file)
    })
}

// 解析Excel数据
const parseExcelData = async () => {
    if (!selectedFile.value) return

    parsing.value = true
    try {
        // 解析Excel文件
        const rawData = await parseExcelFile(selectedFile.value.raw!)

        if (rawData.length === 0) {
            proxy?.$modal.msgError('Excel文件中没有有效数据')
            return
        }

        // 转换为预览数据格式
        importPreviewData.value = rawData.map((row: any, index: number) => {
            // 将中文单位转换为英文代码
            const unitCode = getUnitCodeByLabel(row.unit?.trim() || '')

            return {
                rowNumber: index + 2, // Excel行号（跳过标题行）
                typeName: row.typeName?.trim() || '', // 物资类别
                unit: unitCode || row.unit?.trim() || '', // 转换后的单位代码
                unitLabel: row.unit?.trim() || '', // 保留原始中文单位用于显示
                specification: row.specification?.trim() || '', // 规格型号
                amount: parseFloat(row.amount) || 0, // 入库数量
                price: parseFloat(row.price) || 0, // 单价
                remark: row.remark?.trim() || '', // 备注
                errors: {},
                isValid: false,
                resourceId: null,
                resourceInfo: null,
                suggestions: {},
                validationDetails: {
                    basicFields: false,
                    resourceMatch: false
                }
            }
        })

        // 执行数据验证
        await validateImportData()
    } catch (error: any) {
        proxy?.$modal.msgError('Excel文件解析失败: ' + error.message)
    } finally {
        parsing.value = false
    }
}

// 🔥 数据验证方法

// 验证导入数据
const validateImportData = async () => {
    validImportCount.value = 0
    errorImportCount.value = 0

    for (const row of importPreviewData.value) {
        // 重置验证状态
        row.errors = {}
        row.isValid = true
        row.validationDetails = {
            basicFields: false,
            resourceMatch: false
        }

        // 1. 基础字段验证
        validateBasicFields(row)

        // 2. 物资匹配验证（只有基础验证通过才进行）
        if (row.isValid) {
            row.validationDetails.basicFields = true
            await validateResourceMatch(row)
        }

        // 3. 统计结果
        if (row.isValid) {
            validImportCount.value++
        } else {
            errorImportCount.value++
        }
    }
}

// 基础字段验证
const validateBasicFields = (row: ImportRow) => {
    // 物资类别验证
    if (!row.typeName?.trim()) {
        row.errors.typeName = '物资类别不能为空'
        row.isValid = false
    }

    // 物资单位验证（支持中文转换）
    if (!row.unitLabel?.trim()) {
        row.errors.unit = '物资单位不能为空'
        row.isValid = false
    } else if (!row.unit) {
        // 如果中文单位无法转换为代码
        row.errors.unit = `无法识别的单位"${row.unitLabel}"，请检查是否为系统支持的单位`
        row.isValid = false
    }

    // 规格型号验证
    if (!row.specification?.trim()) {
        row.errors.specification = '规格型号不能为空'
        row.isValid = false
    }

    // 入库数量验证
    if (!row.amount || isNaN(row.amount) || Number(row.amount) <= 0) {
        row.errors.amount = '入库数量必须为正数'
        row.isValid = false
    }

    // 单价验证
    if (row.price === undefined || isNaN(row.price) || Number(row.price) < 0) {
        row.errors.price = '单价必须为非负数'
        row.isValid = false
    }
}

// 物资匹配验证
const validateResourceMatch = async (row: ImportRow) => {
    try {
        const result = await validateResourceExists({
            typeName: row.typeName.trim(),
            unit: row.unit.trim(),
            specification: row.specification.trim()
        })

        if (result.data.exists) {
            // 验证通过：找到了同时满足三个字段的物资记录
            row.resourceId = (result.data as any).resourceId
            row.resourceInfo = (result.data as any).resourceInfo
            row.validationDetails.resourceMatch = true

            console.log(`第${row.rowNumber}行验证通过:`, {
                物资ID: row.resourceId,
                物资信息: row.resourceInfo
            })
        } else {
            // 验证失败：没有找到同时满足三个字段的物资记录
            row.errors.resourceMatch = result.data.message
            row.isValid = false
            row.validationDetails.resourceMatch = false

            // 保存建议信息用于用户参考
            if ((result.data as any).suggestions) {
                row.suggestions = (result.data as any).suggestions
            }

            console.log(`第${row.rowNumber}行验证失败:`, {
                错误信息: result.data.message,
                建议信息: (result.data as any).suggestions
            })
        }
    } catch (error) {
        row.errors.resourceMatch = '验证物资信息时发生错误'
        row.isValid = false
        row.validationDetails.resourceMatch = false
    }
}

// 物资存在性验证（核心验证逻辑）
const validateResourceExists = async (matchData: {
    typeName: string // 物资类别名称
    unit: string // 计量单位（英文代码）
    specification: string // 规格型号
}) => {
    try {
        // 使用现有接口进行查询
        const response = await listResource({
            typeName: matchData.typeName,
            unit: matchData.unit,
            specification: matchData.specification,
            pageNum: 1,
            pageSize: 100 // 增加查询数量以获取更完整的数据
        })

        const resources = response.rows || []

        // 正确的逻辑：查找同时满足三个字段的记录
        const exactMatch = resources.find(
            (resource) =>
                resource.typeName === matchData.typeName && resource.unit === matchData.unit && resource.specification === matchData.specification
        )

        if (exactMatch) {
            // 找到完全匹配的记录
            return {
                data: {
                    exists: true,
                    resourceId: exactMatch.id,
                    resourceInfo: exactMatch,
                    message: '验证通过'
                }
            }
        } else {
            // 没有找到完全匹配的记录，进行智能错误分析
            return await analyzeMatchFailure(matchData, resources)
        }
    } catch (error) {
        throw new Error('验证物资信息时发生错误')
    }
}

// 智能错误分析方法
const analyzeMatchFailure = async (
    matchData: {
        typeName: string
        unit: string
        specification: string
    },
    resources: any[]
) => {
    // 分析可能的匹配情况
    const analysis = {
        // 类别名称匹配的记录
        typeMatches: resources.filter((r) => r.typeName === matchData.typeName),
        // 单位匹配的记录
        unitMatches: resources.filter((r) => r.unit === matchData.unit),
        // 规格匹配的记录
        specMatches: resources.filter((r) => r.specification === matchData.specification),
        // 类别+单位匹配的记录
        typeUnitMatches: resources.filter((r) => r.typeName === matchData.typeName && r.unit === matchData.unit),
        // 类别+规格匹配的记录
        typeSpecMatches: resources.filter((r) => r.typeName === matchData.typeName && r.specification === matchData.specification),
        // 单位+规格匹配的记录
        unitSpecMatches: resources.filter((r) => r.unit === matchData.unit && r.specification === matchData.specification)
    }

    // 生成具体的错误信息和建议
    let errorMessage = ''
    const suggestions = {
        typeNames: [] as string[],
        units: [] as string[],
        specifications: [] as string[]
    }

    if (analysis.typeUnitMatches.length > 0) {
        // 类别和单位都正确，但规格型号不匹配
        errorMessage = `物资类别"${matchData.typeName}"和单位"${getUnitLabelByCode(matchData.unit)}"匹配，但规格型号"${matchData.specification}"不存在`
        suggestions.specifications = [...new Set(analysis.typeUnitMatches.map((r) => r.specification))].slice(0, 5)
    } else if (analysis.typeSpecMatches.length > 0) {
        // 类别和规格都正确，但单位不匹配
        errorMessage = `物资类别"${matchData.typeName}"和规格型号"${matchData.specification}"匹配，但单位"${getUnitLabelByCode(matchData.unit)}"不正确`
        suggestions.units = [...new Set(analysis.typeSpecMatches.map((r) => getUnitLabelByCode(r.unit)))].slice(0, 5)
    } else if (analysis.unitSpecMatches.length > 0) {
        // 单位和规格都正确，但类别不匹配
        errorMessage = `单位"${getUnitLabelByCode(matchData.unit)}"和规格型号"${matchData.specification}"匹配，但物资类别"${matchData.typeName}"不正确`
        suggestions.typeNames = [...new Set(analysis.unitSpecMatches.map((r) => r.typeName))].slice(0, 5)
    } else if (analysis.typeMatches.length > 0) {
        // 只有类别正确
        errorMessage = `物资类别"${matchData.typeName}"存在，但该类别下没有单位为"${getUnitLabelByCode(matchData.unit)}"、规格为"${matchData.specification}"的物资`
        suggestions.units = [...new Set(analysis.typeMatches.map((r) => getUnitLabelByCode(r.unit)))].slice(0, 5)
        suggestions.specifications = [...new Set(analysis.typeMatches.map((r) => r.specification))].slice(0, 5)
    } else if (analysis.unitMatches.length > 0) {
        // 只有单位正确
        errorMessage = `单位"${getUnitLabelByCode(matchData.unit)}"存在，但没有类别为"${matchData.typeName}"、规格为"${matchData.specification}"的物资`
        suggestions.typeNames = [...new Set(analysis.unitMatches.map((r) => r.typeName))].slice(0, 5)
        suggestions.specifications = [...new Set(analysis.unitMatches.map((r) => r.specification))].slice(0, 5)
    } else if (analysis.specMatches.length > 0) {
        // 只有规格正确
        errorMessage = `规格型号"${matchData.specification}"存在，但没有类别为"${matchData.typeName}"、单位为"${getUnitLabelByCode(matchData.unit)}"的物资`
        suggestions.typeNames = [...new Set(analysis.specMatches.map((r) => r.typeName))].slice(0, 5)
        suggestions.units = [...new Set(analysis.specMatches.map((r) => getUnitLabelByCode(r.unit)))].slice(0, 5)
    } else {
        // 三个字段都不匹配
        errorMessage = `未找到匹配的物资：类别"${matchData.typeName}"、单位"${getUnitLabelByCode(matchData.unit)}"、规格"${matchData.specification}"均不存在于系统中`

        // 如果完全没有匹配，可能需要扩大查询范围
        const broaderResponse = await listResource({
            pageNum: 1,
            pageSize: 100
        })
        const allResources = broaderResponse.rows || []

        suggestions.typeNames = [...new Set(allResources.map((r) => r.typeName))].slice(0, 5)
        suggestions.units = [...new Set(allResources.map((r) => getUnitLabelByCode(r.unit)))].slice(0, 5)
        suggestions.specifications = [...new Set(allResources.map((r) => r.specification))].slice(0, 5)
    }

    return {
        data: {
            exists: false,
            message: errorMessage,
            suggestions: suggestions,
            analysis: analysis // 保留分析结果用于调试
        }
    }
}

// 🔥 确认导入方法

// 数据完整性检查和清理
const validateDataIntegrity = () => {
    console.log('🔍 开始数据完整性检查...')

    const originalLength = itemList.value.length

    // 检查并清理空行或无效数据
    itemList.value = itemList.value.filter((item) => {
        const isValid = item.resourceId && item.quantity && item.quantity > 0 && item.price !== undefined && item.price >= 0

        if (!isValid) {
            console.log('🗑️ 清理无效数据:', {
                resourceId: item.resourceId,
                quantity: item.quantity,
                price: item.price,
                specification: item.specification
            })
        }

        return isValid
    })

    // 重新计算索引和序号（通过表格的index列自动处理，无需手动设置）
    // 表格会自动根据数组索引显示正确的序号

    const cleanedLength = itemList.value.length
    if (originalLength !== cleanedLength) {
        console.log(`✅ 数据完整性检查完成，清理了 ${originalLength - cleanedLength} 条无效数据`)
    }
}

// 严格的导入确认（只有全部验证通过才允许导入）
const confirmImport = () => {
    // 检查是否有验证失败的数据
    if (errorImportCount.value > 0) {
        proxy?.$modal.msgError(`存在 ${errorImportCount.value} 条验证失败的数据，请修正后重新导入`)
        return
    }

    // 检查是否有有效数据
    if (validImportCount.value === 0) {
        proxy?.$modal.msgError('没有有效的数据可以导入')
        return
    }

    // 二次确认
    proxy?.$modal
        .confirm(`确认导入 ${validImportCount.value} 条有效数据？`)
        .then(() => {
            importing.value = true

            try {
                const validItems = importPreviewData.value.filter((item) => item.isValid)

                console.log('📊 导入前状态:', {
                    现有明细数量: itemList.value.length,
                    准备导入数量: validItems.length,
                    总计: itemList.value.length + validItems.length
                })

                // 🔥 1. 按Excel行号排序，确保数据顺序正确
                const sortedValidItems = validItems.sort((a, b) => a.rowNumber - b.rowNumber)

                console.log(
                    '📋 准备导入的数据:',
                    sortedValidItems.map((item) => ({
                        Excel行号: item.rowNumber,
                        物资类别: item.typeName,
                        规格型号: item.specification,
                        数量: item.amount,
                        单价: item.price
                    }))
                )

                // 2. 转换为主表格数据格式
                const newItems = sortedValidItems.map((item, index) => ({
                    // 添加临时ID用于调试追踪
                    tempId: `import_${Date.now()}_${index}`,
                    resourceId: item.resourceId,
                    resourceInfo: item.resourceInfo,
                    specification: item.specification,
                    resourceUnit: item.unit, // 使用转换后的英文代码
                    resourceNature: item.resourceInfo?.nature || '',
                    quantity: item.amount, // 使用number类型
                    price: item.price, // 使用number类型
                    amount: item.amount * item.price,
                    remark: item.remark,
                    resourceOptions: [],
                    // 添加导入标识
                    isImported: true,
                    importTime: new Date().toISOString()
                }))

                console.log(
                    '🔄 转换后的数据:',
                    newItems.map((item, index) => ({
                        索引: index,
                        临时ID: item.tempId,
                        物资类别: item.resourceInfo?.typeName,
                        规格型号: item.specification,
                        数量: item.quantity,
                        金额: item.amount
                    }))
                )

                // 🔥 3. 添加到列表末尾（保持原有逻辑）
                itemList.value.push(...newItems)

                console.log('📈 导入后状态:', {
                    总明细数量: itemList.value.length,
                    最后几条数据: itemList.value.slice(-3).map((item, index) => ({
                        相对索引: index,
                        临时ID: (item as any).tempId,
                        规格: item.specification,
                        是否导入: (item as any).isImported
                    }))
                })

                // 🔥 4. 数据完整性检查和清理
                validateDataIntegrity()

                // 5. 重新计算总计
                calculateTotal()

                // 🔥 6. 强制刷新表格以确保正确显示
                nextTick(() => {
                    tableRefreshKey.value++
                    console.log('🔄 表格已刷新，key值:', tableRefreshKey.value)
                })

                // 关闭导入对话框
                importDialog.visible = false
                proxy?.$modal.msgSuccess(`成功导入 ${newItems.length} 条明细`)

                // 清理导入数据
                resetImportDialog()

                console.log('✅ 导入完成，最终数据状态:', {
                    总数量: itemList.value.length,
                    总金额: (form.value as any).totalAmount || '计算中...'
                })
            } catch (error: any) {
                console.error('❌ 导入失败:', error)
                proxy?.$modal.msgError('导入失败: ' + error.message)
            } finally {
                importing.value = false
            }
        })
        .catch(() => {
            console.log('🚫 用户取消导入')
        })
}

/** 删除明细 */
const removeItem = (index: number) => {
    itemList.value.splice(index, 1)
    calculateTotal()
}

/** 计算明细金额 */
const calculateItemAmount = (item: ResourceInOrderItemForm) => {
    item.amount = (item.quantity || 0) * (item.price || 0)
    calculateTotal()
}

/** 处理数量输入 - 允许数字和小数点 */
const handleQuantityInput = (item: ExtendedResourceInOrderItemForm) => {
    // 移除非数字字符，只保留数字和小数点
    let value = String(item.quantity || '').replace(/[^\d.]/g, '')

    // 确保只有一个小数点
    const parts = value.split('.')
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('')
    }

    // 限制小数位数为3位（允许更精确的数量）
    if (parts[1] && parts[1].length > 3) {
        value = parts[0] + '.' + parts[1].substring(0, 3)
    }

    // 转换为数字，允许小数
    const numValue = parseFloat(value)
    item.quantity = !isNaN(numValue) && numValue >= 0 ? numValue : 0

    // 重新计算金额
    calculateItemAmount(item)
}

/** 处理单价输入 - 允许数字和小数点 */
const handlePriceInput = (item: ExtendedResourceInOrderItemForm) => {
    // 移除非数字字符，只保留数字和小数点
    let value = String(item.price || '').replace(/[^\d.]/g, '')

    // 确保只有一个小数点
    const parts = value.split('.')
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('')
    }

    // 限制小数位数为2位（金额通常保留2位小数）
    if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2)
    }

    // 转换为数字，允许小数
    const numValue = parseFloat(value)
    item.price = !isNaN(numValue) && numValue >= 0 ? numValue : 0

    // 重新计算金额
    calculateItemAmount(item)
}

/** 计算总计 */
const calculateTotal = () => {
    // 只统计填写了物资信息的行
    const validItems = itemList.value.filter((item) => item.resourceId)
    totalQuantity.value = validItems.reduce((sum, item) => sum + (item.quantity || 0), 0)
    totalAmount.value = validItems.reduce((sum, item) => sum + (item.amount || 0), 0)
    form.value.amount = totalAmount.value
}

/** 为指定行搜索物资 */
const searchResourceForRow = async (keyword: string, rowIndex: number) => {
    if (!keyword || keyword.length < 2) {
        if (itemList.value[rowIndex]) {
            itemList.value[rowIndex].resourceOptions = []
        }
        return
    }

    if (!itemList.value[rowIndex]) {
        return
    }

    itemList.value[rowIndex].searching = true
    try {
        const queryParams: ResourceViewQuery = {
            typeName: keyword,
            pageNum: 1,
            pageSize: 20
        }

        console.log('🔍 搜索物资参数:', queryParams)
        const response = await listResource(queryParams)
        console.log('🔍 API响应:', response)

        // 确保响应式更新
        const options = response.rows || []
        console.log('🔍 设置选项:', options, '行索引:', rowIndex)

        // 使用Vue的响应式更新 - 强制触发响应式
        if (itemList.value[rowIndex]) {
            // 方法1：直接赋值
            itemList.value[rowIndex].resourceOptions = [...options]

            // 方法2：强制触发响应式更新
            nextTick(() => {
                // 强制重新渲染
                const currentItem = itemList.value[rowIndex]
                if (currentItem) {
                    currentItem.resourceOptions = [...options]
                }
            })
        }

        console.log('🔍 设置后的选项:', itemList.value[rowIndex]?.resourceOptions)
        console.log('🔍 当前行数据:', itemList.value[rowIndex])
    } catch (error) {
        console.error('搜索物资失败:', error)
        proxy?.$modal.msgError('搜索物资失败')
        if (itemList.value[rowIndex]) {
            itemList.value[rowIndex].resourceOptions = []
        }
    } finally {
        if (itemList.value[rowIndex]) {
            itemList.value[rowIndex].searching = false
        }
    }
}

/** 处理物资选择 */
const handleResourceSelect = (resourceId: string | number, rowIndex: number) => {
    if (resourceId) {
        const selected = itemList.value[rowIndex].resourceOptions?.find((item) => item.id === resourceId)
        if (selected) {
            itemList.value[rowIndex].resourceInfo = selected
            itemList.value[rowIndex].resourceId = resourceId
            itemList.value[rowIndex].price = selected.price || 0

            // 自动填充物资型号、单位和性质
            itemList.value[rowIndex].specification = selected.specification || undefined
            itemList.value[rowIndex].resourceUnit = selected.unit || undefined
            itemList.value[rowIndex].resourceNature = selected.nature || undefined

            calculateItemAmount(itemList.value[rowIndex])
        }
    } else {
        itemList.value[rowIndex].resourceInfo = undefined
        itemList.value[rowIndex].resourceId = undefined
        itemList.value[rowIndex].price = 0
        itemList.value[rowIndex].specification = undefined
        itemList.value[rowIndex].resourceUnit = undefined
        itemList.value[rowIndex].resourceNature = undefined
        calculateItemAmount(itemList.value[rowIndex])
    }
}

/** 移动到下一个单元格 */
const moveToNextCell = (rowIndex: number, nextField: string) => {
    nextTick(() => {
        if (nextField === 'quantity') {
            quantityInputRefs.value[rowIndex]?.focus()
        } else if (nextField === 'price') {
            priceInputRefs.value[rowIndex]?.focus()
        } else if (nextField === 'remark') {
            remarkInputRefs.value[rowIndex]?.focus()
        } else if (nextField === 'next') {
            // 如果是最后一个字段，移动到下一行或新增行
            if (rowIndex === itemList.value.length - 1) {
                // 如果是最后一行，新增一行
                addItem()
                nextTick(() => {
                    resourceSelectRefs.value[rowIndex + 1]?.focus()
                })
            } else {
                // 移动到下一行的第一个字段
                resourceSelectRefs.value[rowIndex + 1]?.focus()
            }
        }
    })
}

/** 处理单元格点击 */
const handleCellClick = () => {
    // 可以在这里处理单元格点击事件，如果需要的话
}

/** 根据字典值获取中文标题 */
const getDictLabel = (dictOptions: DictDataOption[], value: string): string => {
    if (!value || !dictOptions) return ''
    const option = dictOptions.find((item) => item.value === value)
    return option ? option.label : value
}

onMounted(() => {
    getList()
})
</script>

<style scoped>
/* 页面整体布局 */
.h-full {
    height: 100%;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.flex-1 {
    flex: 1;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

/* 选中行高亮 */
:deep(.selected-row) {
    background-color: #ecf5ff !important;
}

:deep(.selected-row:hover) {
    background-color: #d9ecff !important;
}

/* 去掉表格行hover时的白色背景 */
:deep(.el-table__row:hover) {
    background-color: transparent !important;
}

:deep(.el-table__row:hover > td) {
    background-color: transparent !important;
}

/* 去掉表格行的默认背景 */
:deep(.el-table__row) {
    background-color: transparent !important;
}

:deep(.el-table__row > td) {
    background-color: transparent !important;
}

/* 去掉表格体的背景 */
:deep(.el-table__body-wrapper) {
    background-color: transparent !important;
}

/* 去掉统计区域的背景 */
:deep(.p-4.rounded-lg) {
    background-color: transparent !important;
}

/* 表格高度调整 */
:deep(.el-table) {
    height: 100%;
}

:deep(.el-table__body-wrapper) {
    overflow-y: auto;
}

/* 空状态样式 */
.text-gray-400 {
    color: #9ca3af;
}

.text-gray-500 {
    color: #6b7280;
}

/* 统计信息样式 */
.bg-gray-50 {
    background-color: transparent !important;
}

.text-blue-600 {
    color: #2563eb;
}

.text-green-600 {
    color: #16a34a;
}

.text-red-600 {
    color: #dc2626;
}

.text-purple-600 {
    color: #9333ea;
}

.mb-4 {
    margin-bottom: 16px;
}

.text-info {
    font-size: 12px;
    color: #909399;
}

:deep(.el-descriptions__label) {
    font-weight: 600;
}

:deep(.el-table .el-input-number) {
    width: 100%;
}

:deep(.el-table .el-input-number .el-input__inner) {
    text-align: center;
}

.resource-info-card {
    border: 1px solid #e4e7ed;
}

.resource-info-card :deep(.el-card__header) {
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
}

.inbound-detail-table :deep(.el-table__cell) {
    padding: 8px 4px;
}

.inbound-detail-table :deep(.el-select) {
    width: 100%;
}

.inbound-detail-table :deep(.el-input-number) {
    width: 100%;
}

.inbound-detail-table :deep(.el-input-number .el-input__inner) {
    text-align: center;
}

.inbound-detail-table :deep(.el-table__row:hover) {
    background-color: #f5f7fa;
}

.inbound-detail-table :deep(.el-table__header-wrapper th) {
    background-color: #fafafa;
    color: #606266;
    font-weight: 600;
}

.inbound-detail-table :deep(.el-select .el-input__inner) {
    text-align: center;
}

.inbound-detail-table :deep(.el-select .el-input__suffix) {
    right: 8px;
}

/* 响应式调整 */
@media (max-width: 1024px) {
    .el-card {
        margin-bottom: 16px;
    }

    /* 小屏幕时调整高度比例 */
    .el-card:first-of-type {
        height: 50vh !important;
        min-height: 300px !important;
    }
}

@media (max-width: 768px) {
    .el-col {
        margin-bottom: 10px;
    }

    /* 移动端时统计信息垂直排列 */
    .el-row .el-col {
        width: 100% !important;
        max-width: 100% !important;
    }
}

/* 🔥 导入相关样式 */
.error-text {
    color: #f56c6c;
    font-weight: bold;
}

.error-icon {
    font-size: 14px;
}

.error-row {
    background-color: #fef0f0 !important;
}

.error-details {
    max-height: 300px;
    overflow-y: auto;
}

.error-row-detail {
    border-left: 3px solid #f56c6c;
}

/* 预览表格样式 */
:deep(.el-table .error-row td) {
    background-color: #fef0f0 !important;
}

:deep(.el-table .error-text) {
    color: #f56c6c;
    font-weight: bold;
}

/* 🔥 上传组件样式优化 - 参考出库单样式 */
:deep(.el-upload-dragger) {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    width: 100%;
    height: 180px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color 0.3s;

    &:hover {
        border-color: #409eff;
    }
}

:deep(.el-upload-dragger .el-icon--upload) {
    font-size: 67px;
    color: #c0c4cc;
    margin: 20px 0 16px; /* 🔥 与出库单保持一致的上移样式 */
    line-height: 50px;
}

:deep(.el-upload__text) {
    color: #606266;
    font-size: 14px;
    text-align: center;
    margin-top: -20px; /* 🔥 与出库单保持一致的上移样式 */

    em {
        color: #409eff;
        font-style: normal;
    }
}

:deep(.el-upload-dragger.is-dragover) {
    background-color: rgba(64, 158, 255, 0.06);
    border: 2px dashed #409eff;
}
</style>
