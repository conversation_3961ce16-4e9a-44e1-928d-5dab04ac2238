<!-- 天气预警 -->
<template>
    <div class="weath-warning">
        <!-- 有预警时的显示 -->
        <div class="warning-card" :class="warningCardClass" v-if="hasWarning">
            <div class="warning-header">
                <div class="warning-title">
                    <span class="warning-level">{{ warningTitle }}</span>
                </div>
                <div class="warning-icon">
                    <i class="warning-triangle" :style="{ color: warningIconColor }">⚠️</i>
                </div>
            </div>

            <div class="warning-content">
                {{ warningContent }}
            </div>
        </div>

        <!-- 无预警时的显示 -->
        <div class="warning-card no-warning-card" v-else>
            <!-- 右上角角标 -->
            <div class="no-warning-badge">
                <img src="@/assets/images/bigscreen/icons/<EMAIL>" alt="无预警" />
            </div>

            <div class="warning-header">
                <div class="warning-title">
                    <span class="warning-level">正常</span>
                    <span class="update-time">{{ updateTimeText }}</span>
                </div>
            </div>

            <div class="warning-content">当前暂无天气预警。</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { getWeatherWarning } from '@/api/weather'
import type { WeatherWarningItem } from '@/api/weather'

// 预警数据
const currentWarning = ref<WeatherWarningItem | null>(null)
const hasWarning = computed(() => currentWarning.value !== null && currentWarning.value.hasWarning)

// 最后更新时间
const lastUpdateTime = ref<Date>(new Date())

// 定时器引用
let warningUpdateTimer: NodeJS.Timeout | null = null

// 预警颜色映射
const severityColorMap: Record<string, string> = {
    'Red': '#FF4500', // 红色预警
    'Orange': '#FF8C00', // 橙色预警
    'Yellow': '#FFD700', // 黄色预警
    'Blue': '#1E90FF' // 蓝色预警
}

// 预警颜色中文映射
const severityColorNameMap: Record<string, string> = {
    'Red': '红色',
    'Orange': '橙色',
    'Yellow': '黄色',
    'Blue': '蓝色'
}

// 计算属性
const warningTitle = computed(() => {
    if (!currentWarning.value) return ''
    const { severityColor, typeName } = currentWarning.value
    const colorName = severityColorNameMap[severityColor] || severityColor
    return `${colorName}${typeName}预警`
})

const warningContent = computed(() => {
    return currentWarning.value?.content || ''
})

const warningIconColor = computed(() => {
    if (!currentWarning.value) return '#FFA500'
    return severityColorMap[currentWarning.value.severityColor] || '#FFA500'
})

const warningCardClass = computed(() => {
    if (!currentWarning.value) return ''
    const color = currentWarning.value.severityColor.toLowerCase()
    return `warning-${color}`
})

// 更新时间文本
const updateTimeText = computed(() => {
    const now = new Date()
    const diffMs = now.getTime() - lastUpdateTime.value.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))

    if (diffHours === 0) {
        const diffMinutes = Math.floor(diffMs / (1000 * 60))
        return diffMinutes <= 1 ? '刚刚更新' : `${diffMinutes}分钟前更新`
    } else if (diffHours === 1) {
        return '1小时前更新'
    } else {
        return `${diffHours}小时前更新`
    }
})

// 更新预警数据
const updateWarningData = async () => {
    try {
        console.log('开始获取天气预警数据...')

        // 直接调用后端API获取预警数据
        const warningResponse = await getWeatherWarning()

        currentWarning.value = warningResponse
        // 更新最后更新时间
        lastUpdateTime.value = new Date()

        if (warningResponse.hasWarning) {
            console.log('获取到天气预警:', {
                type: warningResponse.typeName,
                severity: warningResponse.severity,
                color: warningResponse.severityColor,
                title: warningResponse.title
            })
        } else {
            console.log('当前无活跃的天气预警')
        }
    } catch (error) {
        console.error('获取天气预警数据失败:', error)
        currentWarning.value = {
            title: '',
            content: '',
            severityColor: '',
            typeName: '',
            severity: '',
            status: '',
            pubTime: '',
            startTime: '',
            endTime: '',
            hasWarning: false
        }
        // 即使出错也更新时间戳
        lastUpdateTime.value = new Date()
    }
}

onMounted(() => {
    // 立即更新预警数据
    updateWarningData()

    // 每1小时更新预警数据
    warningUpdateTimer = setInterval(updateWarningData, 60 * 60 * 1000)
})

onUnmounted(() => {
    // 清理定时器
    if (warningUpdateTimer) {
        clearInterval(warningUpdateTimer)
    }
})
</script>

<style lang="scss" scoped>
.weath-warning {
    width: 592px;

    // background: red;
    .warning-card {
        background: rgba(3, 16, 37, 0.55);
        border-radius: 20px 20px 20px 20px;
        border: 1px solid #01effe;
        padding: 12px;
        backdrop-filter: blur(4px);
        height: 100%;

        // 不同预警等级的边框颜色
        &.warning-red {
            border-color: #ff4500;
            box-shadow: 0 0 10px rgba(255, 69, 0, 0.3);
        }

        &.warning-orange {
            border-color: #ff8c00;
            box-shadow: 0 0 10px rgba(255, 140, 0, 0.3);
        }

        &.warning-yellow {
            border-color: #ffd700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        &.warning-blue {
            border-color: #1e90ff;
            box-shadow: 0 0 10px rgba(30, 144, 255, 0.3);
        }

        // 无预警状态样式
        &.no-warning-card {
            position: relative;
            border-color: #01effe;
            box-shadow: 0 0 10px rgba(1, 239, 254, 0.2);
        }

        .warning-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .warning-icon {
                margin-right: 8px;

                .warning-triangle {
                    font-size: 16px;
                    color: #ffa500;
                }
            }

            .warning-title {
                display: flex;
                flex-direction: column;

                .warning-level {
                    font-family: MiSans, MiSans;
                    font-weight: 500;
                    font-size: 32px;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    background: linear-gradient(180deg, #00f0ff 0%, #fff 100%);
                    -webkit-background-clip: text;
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                    color: transparent;
                }

                .warning-type {
                    font-size: 12px;
                    color: #ffffff;
                    opacity: 0.8;
                    line-height: 1;
                }

                .update-time {
                    margin-top: 20px;
                    font-family: MiSans, MiSans;
                    font-weight: 400;
                    font-size: 20px;
                    color: rgba(255, 255, 255, 0.7);
                    margin-top: 4px;
                    display: block;
                    line-height: 1.2;
                    // color: red;
                }
            }
        }

        // 右上角角标样式
        .no-warning-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 32px;
            height: 32px;
            z-index: 10;

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }

        .warning-content {
            margin-top: 5px;
            font-family: MiSans, MiSans;
            font-weight: 400;
            font-size: 20px;
            color: #ffffff;
            line-height: 38px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            max-height: 180px;
            overflow-y: auto;
            padding-right: 8px;
            &::-webkit-scrollbar {
                width: 4px;
            }

            &::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba(1, 239, 254, 0.6);
                border-radius: 2px;

                &:hover {
                    background: rgba(1, 239, 254, 0.8);
                }
            }
        }
    }
}
</style>
