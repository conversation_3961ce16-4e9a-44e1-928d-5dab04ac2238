<!-- 维养任务取消 -->
<template>
    <div class="p-2" style="margin-top: -10px">
        <el-card class="box-card">
            <template v-slot:header>
                <div class="clearfix">
                    <span>作业单信息</span>
                </div>
            </template>
            <!-- 作业单任务基本信息 -->
            <BaseInfoSealing :taskId="taskId" from="task" />
        </el-card>

        <el-card class="box-card">
            <template v-slot:header>
                <div class="clearfix">
                    <span>取消操作</span>
                </div>
            </template>
            <div class="text item">
                <el-form ref="projectFormRef" :model="form" :rules="rules" label-width="160px">
                    <el-row :gutter="gutter" v-if="form.task.currentStatus != 'Pending_Cancellation'">
                        <el-col :span="12">
                            <el-form-item label="取消原因" prop="nextAssignee.opinion">
                                <el-input v-model="form.nextAssignee.opinion" type="textarea" placeholder="请输入取消原因" :rows="4" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter" v-if="form.task.currentStatus == 'Pending_Cancellation'">
                        <el-col :span="12">
                            <el-form-item label="确认取消">
                                <el-radio-group v-model="form.nextAssignee.wfOperation">
                                    <el-radio v-for="option in approvalOptions" :key="option.value" :value="option.value">
                                        {{ option.label }}
                                    </el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter" v-if="form.task.currentStatus == 'Pending_Cancellation'">
                        <el-col :span="12">
                            <el-form-item label="意见" prop="nextAssignee.opinion">
                                <el-input v-model="form.nextAssignee.opinion" type="textarea" placeholder="请输入意见" :rows="4" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div class="text item">
                        <el-row :gutter="gutter">
                            <el-col :span="24">
                                <el-button type="primary" :loading="submitLoading" @click="handleSubmit">提交</el-button>
                                <el-button @click="handleCancel">返回</el-button>
                            </el-col>
                        </el-row>
                    </div>
                </el-form>
            </div>
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { maintainAssign, sealingAssign } from '@/api/plan/task'
import { AssignMaintainFlowForm, AssignMaintainFlowVo, AssignMaintainResourceVo } from '@/api/plan/assignMaintain/types'
import { getTaskAssignmentByTaskId } from '@/api/plan/assignMaintain'
import { ElMessage } from 'element-plus'
import BaseInfoMaintain from '../../components/BaseInfoMaintain.vue'
import { AssignSealingFlowForm, AssignSealingFlowVO, AssignSealingRoadVO } from '@/api/plan/assignSealing/types'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const route = useRoute()
const router = useRouter()

const gutter = ref(50)
const taskId = ref((route.query.taskId as string) || '')
const projectFormRef = ref()
const submitLoading = ref(false)
// 审核结果选项
const approvalOptions = ref([
    { label: '通过', value: 'TERMINATE' },
    { label: '退回', value: 'ROLLBACK' }
    // { label: '延期', value: 'SUSPENDED' }
])

// 表单数据 - 只保留取消操作所需的字段
const form = reactive<AssignSealingFlowForm>({
    task: {
        tempTask: 'no',
        tempResourceId: ''
    },
    assignSealing: {
        taskId: taskId.value,
        roadContent: '',
        // 封闭线路/位置列表（初始为空，根据封道类型动态添加）
        sealingItems: [] as AssignSealingRoadVO[]
    } as any,
    nextAssignee: {
        nextAssignees: undefined,
        wfOperation: 'APPROVE',
        opinion: '',
        businessData: {
            CANCEL_OPTION: 'CANCEL'
        }
    }
})

// 表单验证规则 - 只验证取消原因
const rules = reactive({
    'nextAssignee.opinion': [
        { required: true, message: '请输入取消原因', trigger: 'blur' },
        { min: 5, message: '取消原因至少5个字符', trigger: 'blur' }
    ]
} as any)

// 提交封道任务分配
const handleSubmit = async () => {
    submitLoading.value = true
    try {
        // 表单验证
        const valid = await projectFormRef.value?.validate()
        if (!valid) {
            return
        }
        // 准备提交数据
        //@todo 指定分配环节的人员
        submitLoading.value = true

        const processedForm = {
            ...form
        }

        // 将sealingItems转换为JSON保存到roadContent字段
        if (processedForm.assignSealing) {
            const sealingItems = (processedForm.assignSealing as any).sealingItems
            if (sealingItems && Array.isArray(sealingItems) && sealingItems.length > 0) {
                // 有封闭项时，转换为JSON字符串

                console.log('sealingItems:', sealingItems)
                processedForm.assignSealing.roadContent = JSON.stringify(sealingItems)
                console.log('将sealingItems转换为JSON:', processedForm.assignSealing.roadContent)
            } else {
                // 如果没有封闭项，设置为空字符串
                processedForm.assignSealing.roadContent = ''
                console.log('没有封闭项，设置roadContent为空字符串')
            }
            // 移除sealingItems字段，避免发送数组到后端
            delete (processedForm.assignSealing as any).sealingItems

            // 将teams数组转换为字符串
            if (processedForm.assignSealing.teams && Array.isArray(processedForm.assignSealing.teams)) {
                processedForm.assignSealing.teams = processedForm.assignSealing.teams.join(',')
                console.log('将teams数组转换为字符串:', processedForm.assignSealing.teams)
            }
        }

        switch (form.task.currentStatus) {
            // 指派环节需要指定处理人
            case 'START':
                processedForm.nextAssignee = {
                    ...form.nextAssignee,
                    nextAssignees: form.nextAssignee?.nextAssignees
                        ? Array.isArray(form.nextAssignee.nextAssignees)
                            ? form.nextAssignee.nextAssignees
                            : [form.nextAssignee.nextAssignees]
                        : []
                }

                console.log('processedForm:', processedForm)
                break
            default:
                // 其他环节不需要指定处理人
                processedForm.nextAssignee = {
                    ...form.nextAssignee,
                    nextAssignees: []
                }
                break
        }
        // 调用API提交
        await sealingAssign(processedForm)

        proxy?.$modal.msgSuccess('封道任务分配成功')

        // 返回列表页或关闭页面

        router.push('sealing')
    } catch (error) {
        console.error('封道任务分配失败:', error)
        proxy?.$modal.msgError('封道任务分配失败，请重试')
    } finally {
        submitLoading.value = false
    }
}

const getTaskAssignmentData = async () => {
    try {
        const response = await getTaskAssignmentByTaskId(taskId.value)
        if (response.data) {
            const assignmentData: AssignSealingFlowVO = response.data

            // 初始化form数据
            if (assignmentData.task) {
                Object.assign(form.task, assignmentData.task)
                console.log('任务信息:', {
                    id: assignmentData.task.id,
                    name: assignmentData.task.name,
                    taskType: assignmentData.task.taskType,
                    bgnDate: assignmentData.task.bgnDate,
                    endDate: assignmentData.task.endDate
                })
            }

            if (assignmentData.assignSealing) {
                Object.assign(form.assignSealing, assignmentData.assignSealing)

                // 如果roadContent包含JSON数据，解析为sealingItems数组
                if (assignmentData.assignSealing.roadContent) {
                    try {
                        const parsedItems = JSON.parse(assignmentData.assignSealing.roadContent)
                        if (Array.isArray(parsedItems)) {
                            ;(form.assignSealing as any).sealingItems = parsedItems
                            console.log('从roadContent解析得到sealingItems:', parsedItems)
                        }
                    } catch (error) {
                        console.warn('解析roadContent失败:', error)
                        ;(form.assignSealing as any).sealingItems = []
                    }
                } else {
                    // 如果没有roadContent数据，初始化空数组
                    ;(form.assignSealing as any).sealingItems = []
                }

                // 如果teams是字符串，解析为数组
                if (assignmentData.assignSealing.teams && typeof assignmentData.assignSealing.teams === 'string') {
                    ;(form.assignSealing as any).teams = assignmentData.assignSealing.teams.split(',').filter((team) => team.trim() !== '')
                    console.log('从teams字符串解析得到数组:', (form.assignSealing as any).teams)
                } else if (!assignmentData.assignSealing.teams) {
                    // 如果没有teams数据，初始化空数组
                    ;(form.assignSealing as any).teams = []
                }

                console.log('封道分配信息:', {
                    id: assignmentData.assignSealing.id,
                    taskId: assignmentData.assignSealing.taskId,
                    name: assignmentData.assignSealing.name,
                    roadType: assignmentData.assignSealing.roadType,
                    safeManId: assignmentData.assignSealing.safeManId,
                    managerId: assignmentData.assignSealing.managerId,
                    teams: (form.assignSealing as any).teams,
                    roadContent: assignmentData.assignSealing.roadContent,
                    sealingItems: (form.assignSealing as any).sealingItems
                })
            }

            console.log('获取任务分配数据成功:', assignmentData)
        }
    } catch (error) {
        console.error('获取任务分配数据失败:', error)
        ElMessage.warning('获取任务数据失败，将使用默认数据')
    }
}
// 返回列表
const handleCancel = () => {
    router.push('/subProject/circle/maintain/list')
}

onMounted(async () => {
    await getTaskAssignmentData()
})
</script>

<style lang="scss" scoped>
.box-card {
    margin-top: 10px;
}
</style>
