<!-- 待办事项 -->
<template>
    <el-card shadow="hover">
        <div class="service-container">
            <!-- 标题栏 -->
            <div class="header">
                <div class="title">维养管理</div>
                <div class="count">{{ taskCount }} 条</div>
                <div class="filter">
                    <el-date-picker
                        v-model="date"
                        type="date"
                        placeholder="选择日期"
                        :clearable="false"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        @change="onDateChange"
                    />
                </div>
                <!-- 标签页 -->
                <div class="tabs">
                    <el-tabs v-model="activeTab" @tab-change="onTabChange">
                        <el-tab-pane label="养护" name="curing">
                            <div class="tab-badge">{{ getTabCount('curing') }}</div>
                        </el-tab-pane>
                        <!-- @todo 原系统中没有维修项，所以暂时隐藏 -->
                        <el-tab-pane label="维修" name="repair">
                            <div class="tab-badge">3</div>
                        </el-tab-pane>
                        <el-tab-pane label="巡检" name="inspect">
                            <div class="tab-badge">{{ getTabCount('inspect') }}</div>
                        </el-tab-pane>
                        <el-tab-pane label="封道" name="sealing">
                            <div class="tab-badge">{{ getTabCount('sealing') }}</div>
                        </el-tab-pane>
                        <el-tab-pane label="缺陷" name="defect" v-if="defectVisible">
                            <div class="tab-badge">{{ getTabCount('defect') }}</div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
            <!-- 表格 -->
            <div class="table-container">
                <el-table :data="filteredTableData" style="width: 100%" :border="false" :show-header="true" v-loading="loading">
                    <el-table-column type="index" label="序号" width="80" :index="indexMethod" />
                    <el-table-column prop="name" label="作业单名称">
                        <template #default="scope">
                            {{ scope.row.name }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="bgnDate" label="计划作业日期" width="180">
                        <template #default="scope">
                            <span>{{ formatDate(scope.row.bgnDate) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="shiftType" label="班次" width="100" />
                    <el-table-column prop="currentStatus" label="状态" width="100">
                        <template #default="scope">
                            <span class="task-name-link" @click="goTaskDetail(scope.row.id, scope.row.taskType)">
                                <dict-tag :options="task_status" :value="scope.row.currentStatus" />
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    @pagination="getList"
                />
            </div>
        </div>
    </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, getCurrentInstance, toRefs, reactive, watch } from 'vue'
import { listTaskView } from '@/api/plan/taskView'
import { TaskViewVO, TaskViewQuery } from '@/api/plan/taskView/types'
import { useDict } from '@/hooks/useDict'
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'

// 定义组件属性
const props = defineProps({
    date: {
        type: String,
        default: ''
    },
    defectVisible: {
        type: Boolean,
        default: true
    }
})

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { task_status } = toRefs<any>(proxy?.useDict('task_status'))
const userStore = useUserStore()
const appStore = useAppStore()

// 内部日期状态
const date = ref(props.date)

// 缺陷可见性状态
const defectVisible = ref(props.defectVisible)

// 监听props变化
watch(
    () => props.date,
    (newValue) => {
        date.value = newValue
        loadTaskData()
    }
)

watch(
    () => props.defectVisible,
    (newValue) => {
        defectVisible.value = newValue
        loadTaskData()
    }
)

// 当前激活的标签页
const activeTab = ref('curing')

// 表格数据
const tableData = ref<TaskViewVO[]>([])

// 缺陷模拟数据
const defectMockData = ref([
    {
        id: '1',
        name: '隧道渗漏缺陷处理',
        bgnDate: '2025-01-15',
        shiftType: '白班',
        currentStatus: 'Assign',
        taskType: 'defect'
    },
    {
        id: '2',
        name: '照明设备故障修复',
        bgnDate: '2025-01-16',
        shiftType: '夜班',
        currentStatus: 'Assign',
        taskType: 'defect'
    }
])

// 加载状态
const loading = ref(false)

// 总记录数
const total = ref(0)

// 查询参数
const queryParams = ref<TaskViewQuery>({
    pageNum: 1,
    pageSize: 10,
    taskType: 'curing'
})

// 根据当前标签页过滤数据
const filteredTableData = computed(() => {
    if (activeTab.value === 'defect') {
        // 缺陷数据使用模拟数据
        return defectMockData.value
    }
    return tableData.value
})

// 计算总任务数量
const taskCount = computed(() => {
    if (activeTab.value === 'defect') {
        return defectMockData.value.length
    }
    return total.value
})

// 获取各标签页的数量
const getTabCount = (taskType: string) => {
    if (taskType === 'defect') {
        return defectMockData.value.length
    }
    // 这里应该根据实际需求调整，可能需要单独的API来获取各类型的数量
    return taskType === activeTab.value ? total.value : 0
}

// 序号方法
const indexMethod = (index: number) => {
    return (queryParams.value.pageNum! - 1) * queryParams.value.pageSize! + index + 1
}

const goTaskDetail = (id: string, taskType: string) => {
    switch (taskType) {
        case 'curing':
            proxy?.$router.push({
                path: '/subProject/circle/maintain/maintainAssign',
                query: { taskId: id }
            })
            break
        case 'inspect':
            proxy?.$router.push({
                path: '/subProject/circle/inspection/assign',
                query: { taskId: id }
            })
            break
        case 'sealing':
            proxy?.$router.push({
                path: '/subProject/circle/maintain/sealingAssign',
                query: { taskId: id }
            })
            break
        case 'defect':
            proxy?.$router.push({
                path: '/plan/task/detail',
                query: { taskId: id }
            })
            break
    }
}

// 日期格式化函数
const formatDate = (date: string) => {
    if (!date) return ''
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
}

// 查询任务列表（分页版本）
const getList = async () => {
    if (activeTab.value === 'defect') {
        // 缺陷数据不调用API，直接返回
        return
    }

    try {
        loading.value = true

        // 设置查询参数
        const query: TaskViewQuery = {
            pageNum: queryParams.value.pageNum,
            pageSize: queryParams.value.pageSize,
            taskStep: 'task',
            taskType: activeTab.value,
            assignee: userStore.userId,
            projectId: appStore.projectContext.selectedProjectId,
            todoStatus: 'ACTIVE' //此处必须传ACTIVE，而不是PENDING
        }

        // 只有当日期有值时才添加bgnDate过滤条件
        if (date.value) {
            query.bgnDate = date.value
        }

        const response = await listTaskView(query)
        tableData.value = response.rows || []
        total.value = response.total || 0
    } catch (error) {
        console.error('加载任务数据失败:', error)
        tableData.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

// 加载任务数据（兼容原有调用）
const loadTaskData = async () => {
    // 重置到第一页
    queryParams.value.pageNum = 1
    await getList()
}

// 日期变化处理
const onDateChange = () => {
    loadTaskData()
}

// 标签页变化处理
const onTabChange = () => {
    // 标签页变化时重新加载对应类型的数据
    queryParams.value.taskType = activeTab.value
    loadTaskData()
}

// 组件挂载时加载数据
onMounted(() => {
    loadTaskData()
})
</script>

<style lang="scss" scoped>
.service-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .header {
        display: flex;
        align-items: center;
        padding: 16px 0px;
        //border-bottom: 1px solid #ebeef5;

        .title {
            font-size: 16px;
            font-weight: bold;
            color: #fff;
        }

        .count {
            margin-left: 10px;
            font-size: 14px;
            color: #909399;
        }

        .filter {
            margin-left: 20px;
        }

        .tabs {
            margin-left: auto;
            padding: 0 20px;
            //border-bottom: 1px solid #ebeef5;

            :deep(.el-tabs__header) {
                margin-bottom: -5px;
            }

            :deep(.el-tabs__nav-wrap::after) {
                display: none;
            }

            .tab-badge {
                position: absolute;
                top: 10px;
                right: 0;
                min-width: 16px;
                height: 16px;
                line-height: 16px;
                padding: 0 4px;
                border-radius: 8px;
                background-color: #f56c6c;
                color: #fff;
                font-size: 12px;
                text-align: center;
            }
        }
    }

    .table-container {
        flex: 1;
        padding: 10px 0px;
        overflow-y: auto;

        .task-name-link {
            color: #409eff;
            cursor: pointer;

            &:hover {
                color: #66b1ff;
                text-decoration: underline;
            }
        }

        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .status-progress {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .status-done {
            background-color: #f6ffed;
            color: #52c41a;
        }
    }
}
</style>
