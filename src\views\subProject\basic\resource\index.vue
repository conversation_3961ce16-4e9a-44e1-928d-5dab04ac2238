<template>
    <div class="p-2">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <!-- <el-form-item label="" prop="projectId">
              <el-input v-model="queryParams.projectId" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
                        <el-form-item label="" prop="typeId">
                            <el-select v-model="form.typeId" placeholder="请输入" @change="handleQuery">
                                <el-option v-for="item in resourceTypeList" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                            <!-- <el-input v-model="queryParams.typeId" placeholder="请输入" clearable @keyup.enter="handleQuery" /> -->
                        </el-form-item>
                        <el-form-item label="物资性质" prop="nature">
                            <el-select v-model="queryParams.nature" placeholder="请选择物资性质" clearable>
                                <el-option v-for="dict in tnl_resource_nature" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basic:resource:add']">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['basic:resource:edit']"
                            >修改</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="danger"
                            plain
                            icon="Delete"
                            :disabled="multiple"
                            @click="handleDelete()"
                            v-hasPermi="['basic:resource:remove']"
                            >删除</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['basic:resource:export']">导出</el-button>
                    </el-col>
                    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </template>

            <el-table v-loading="loading" :data="resourceList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <!-- <el-table-column label="" align="center" prop="projectId" /> -->
                <el-table-column label="物资类型" align="center" prop="typeId">
                    <template #default="scope">
                        {{ getTypeName(scope.row.typeId) }}
                    </template>
                </el-table-column>
                <el-table-column label="物资单位" align="center" prop="unit">
                    <template #default="scope">
                        <dict-tag :options="tnl_resource_unit" :value="scope.row.unit" />
                    </template>
                </el-table-column>
                <el-table-column label="物资性质" align="center" prop="nature">
                    <template #default="scope">
                        <dict-tag :options="tnl_resource_nature" :value="scope.row.nature" />
                    </template>
                </el-table-column>
                <el-table-column label="规格型号" align="center" prop="specification" />
                <el-table-column label="物资单价" align="center" prop="price" />
                <el-table-column label="库存数量" align="center" prop="balanceAmount" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-tooltip content="修改" placement="top">
                            <el-button
                                link
                                type="primary"
                                icon="Edit"
                                @click="handleUpdate(scope.row)"
                                v-hasPermi="['basic:resource:edit']"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top">
                            <el-button
                                link
                                type="primary"
                                icon="Delete"
                                @click="handleDelete(scope.row)"
                                v-hasPermi="['basic:resource:remove']"
                            ></el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <!-- 添加或修改物资信息对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
            <el-form ref="resourceFormRef" :model="form" :rules="rules" label-width="80px">
                <!-- <el-form-item label="" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入" />
        </el-form-item> -->
                <el-form-item label="物资类别" prop="typeId">
                    <el-select v-model="form.typeId" placeholder="请输入" @change="handleChangeResourceType">
                        <el-option v-for="item in resourceTypeList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="物资单位" prop="unit">
                    <el-select v-model="form.unit" disabled placeholder="请选择物资单位">
                        <el-option v-for="dict in tnl_resource_unit" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="物资性质" prop="nature">
                    <el-select v-model="form.nature" disabled placeholder="请选择物资性质">
                        <el-option v-for="dict in tnl_resource_nature" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="规格型号" prop="specification">
                    <el-input v-model="form.specification" placeholder="请输入规格型号" />
                </el-form-item>
                <el-form-item label="物资单价" prop="price">
                    <el-input v-model="form.price" placeholder="请输入物资单价" />
                </el-form-item>
                <el-form-item label="库存数量" prop="balanceAmount">
                    <el-input v-model="form.balanceAmount" disabled placeholder="请输入库存数量" />
                </el-form-item>
                <el-form-item label="储存地" prop="storage">
                    <el-input v-model="form.storage" placeholder="请输入储存地" />
                </el-form-item>
                <el-form-item label="库存数量" prop="pictures">
                    <el-input v-model="form.pictures" type="textarea" placeholder="请输入内容" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Resource" lang="ts">
import { listResource, getResource, delResource, addResource, updateResource } from '@/api/subProject/basic/resource'
import { ResourceVO, ResourceQuery, ResourceForm } from '@/api/subProject/basic/resource/types'
import { listResourceType } from '@/api/common/resourceType'
import { ResourceTypeQuery, ResourceTypeVO } from '@/api/common/resourceType/types'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { tnl_resource_unit, tnl_resource_nature } = toRefs<any>(proxy?.useDict('tnl_resource_unit', 'tnl_resource_nature'))

const resourceList = ref<ResourceVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const resourceTypeList = ref<ResourceTypeVO[]>([])
const queryFormRef = ref<ElFormInstance>()
const resourceFormRef = ref<ElFormInstance>()

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

const initFormData: ResourceForm = {
    id: undefined,
    projectId: undefined,
    typeId: undefined,
    unit: undefined,
    nature: undefined,
    specification: undefined,
    price: undefined,
    balanceAmount: 0,
    storage: undefined,
    pictures: undefined
}
const resourceTypeQueryParams = ref<ResourceTypeQuery>({
    pageNum: 1,
    pageSize: 1000
})
const data = reactive<PageData<ResourceForm, ResourceQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        typeId: undefined,
        nature: undefined,
        params: {}
    },
    rules: {
        projectId: [{ required: true, message: '不能为空', trigger: 'blur' }],
        typeId: [{ required: true, message: '不能为空', trigger: 'blur' }],
        unit: [{ required: true, message: '物资单位不能为空', trigger: 'change' }],
        nature: [{ required: true, message: '物资性质不能为空', trigger: 'change' }],
        specification: [{ required: true, message: '规格型号不能为空', trigger: 'blur' }],
        price: [{ required: true, message: '物资单价不能为空', trigger: 'blur' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询物资信息列表 */
const getList = async () => {
    loading.value = true
    const res = await listResource(queryParams.value)
    resourceList.value = res.rows
    total.value = res.total
    loading.value = false
}
const getResourceTypreList = async () => {
    loading.value = true
    const res = await listResourceType(resourceTypeQueryParams.value)
    resourceTypeList.value = res.rows
}
const getTypeName = (typeId: string | number) => {
    const type = resourceTypeList.value.find((item) => item.id === typeId)
    return type ? type.name : '未知类型' // 如果未找到匹配项，显示“未知类型”
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    resourceFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ResourceVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    dialog.visible = true
    dialog.title = '添加物资信息'
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ResourceVO) => {
    reset()
    const _id = row?.id || ids.value[0]
    const res = await getResource(_id)
    Object.assign(form.value, res.data)
    dialog.visible = true
    dialog.title = '修改物资信息'
}

/** 提交按钮 */
const submitForm = () => {
    resourceFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true
            form.value.projectId = '' //stor
            if (form.value.id) {
                await updateResource(form.value).finally(() => (buttonLoading.value = false))
            } else {
                await addResource(form.value).finally(() => (buttonLoading.value = false))
            }
            proxy?.$modal.msgSuccess('操作成功')
            dialog.visible = false
            await getList()
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: ResourceVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除物资信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delResource(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'basic/resource/export',
        {
            ...queryParams.value
        },
        `resource_${new Date().getTime()}.xlsx`
    )
}

const handleChangeResourceType = (typeId: string | number) => {
    const type = resourceTypeList.value.find((item) => item.id === typeId)
    form.value.unit = type.unit
    form.value.nature = type.nature
    // return type ? type.name : "未知类型"; // 如果未找到匹配项，显示“未知类型”
}

onMounted(() => {
    getResourceTypreList()
    getList()
})
</script>
