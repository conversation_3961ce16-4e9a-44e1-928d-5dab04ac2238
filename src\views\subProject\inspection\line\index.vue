<template>
    <div class="p-2 inspection-line-page">
        <el-row :gutter="20">
            <!-- 左侧巡检线路树 -->
            <el-col :span="6">
                <el-card class="box-card">
                    <template #header>
                        <div class="card-header">
                            <span>巡检线路</span>
                            <el-button class="toolbar-btn btn-add" @click="handleAddLine"> 新增巡检线路组 </el-button>
                        </div>
                    </template>
                    <el-tree
                        ref="lineTreeRef"
                        :data="lineTreeData"
                        :props="treeProps"
                        node-key="id"
                        :expand-on-click-node="false"
                        :highlight-current="true"
                        :default-expand-all="true"
                        @node-click="handleNodeClick"
                        class="line-tree"
                    >
                        <template #default="{ node, data }">
                            <div class="tree-node">
                                <span class="node-label">{{ node.label }}</span>
                                <!-- 第一级路线组显示新增巡检路线按钮 -->
                                <div class="node-actions" v-if="data.level === 1">
                                    <el-link type="text" size="small" @click.stop="handleAddInspectionLine(data)" class="action-btn"> 添加 </el-link>
                                    <el-link type="text" size="small" @click.stop="handleDelete(data)" class="action-btn delete-btn"> 删除 </el-link>
                                </div>
                                <!-- 第二级及以下节点显示操作按钮 -->
                                <div class="node-actions" v-if="data.level > 1">
                                    <el-link type="text" size="small" @click.stop="handleConfig(data)" class="action-btn"> 配置 </el-link>
                                    <el-link type="text" size="small" @click.stop="handleModify(data)" class="action-btn"> 修改 </el-link>
                                    <el-link type="text" size="small" @click.stop="handleDelete(data)" class="action-btn delete-btn"> 删除 </el-link>
                                </div>
                            </div>
                        </template>
                    </el-tree>
                </el-card>
            </el-col>

            <!-- 右侧设备列表 -->
            <el-col :span="18">
                <el-card class="box-card">
                    <template #header>
                        <div class="card-header">
                            <div class="search-bar">
                                <el-select
                                    v-model="queryParams.unitId"
                                    placeholder="请选择管理单元"
                                    clearable
                                    @change="handleUnitChange"
                                    style="width: 200px; margin-right: 10px"
                                >
                                    <el-option v-for="unit in manageUnits" :key="unit.id" :label="unit.name" :value="unit.id" />
                                </el-select>
                                <el-select
                                    v-model="queryParams.roomId"
                                    placeholder="请选择房间"
                                    clearable
                                    @change="handleQuery"
                                    style="width: 200px; margin-right: 10px"
                                >
                                    <el-option v-for="room in roomOptions" :key="room.id" :label="room.roomNane" :value="room.id" />
                                </el-select>
                                <el-tree-select
                                    v-model="queryParams.categoryId"
                                    :data="categoryTreeData"
                                    :props="{ value: 'id', label: 'name', children: 'children' }"
                                    value-key="id"
                                    placeholder="请选择设备系统分类"
                                    check-strictly
                                    clearable
                                    filterable
                                    :disabled="false"
                                    @change="handleQuery"
                                    style="width: 200px; margin-right: 10px"
                                />
                                <el-input
                                    v-model="queryParams.name"
                                    placeholder="请输入设备设施名称"
                                    clearable
                                    @keyup.enter="handleQuery"
                                    style="width: 200px; margin-right: 10px"
                                />
                                <el-button type="primary" @click="handleQuery">查询</el-button>
                                <el-button @click="resetQuery">重置</el-button>
                            </div>
                        </div>
                    </template>

                    <el-table v-loading="loading" stripe :data="equipmentList" @selection-change="handleSelectionChange" height="600">
                        <el-table-column type="selection" width="55" align="center" />
                        <el-table-column label="序号" type="index" width="60" align="center" />
                        <el-table-column label="房间编码" align="center" prop="roomCode" />
                        <el-table-column label="设备设施名称" align="center" prop="name" />
                        <!-- <el-table-column label="名称" align="center" prop="name" /> -->
                        <el-table-column label="管理单元" align="center" prop="unitName" />
                        <el-table-column label="操作" align="center" width="120">
                            <template #default="scope">
                                <div class="op-actions">
                                    <el-button
                                        v-if="!currentLineData"
                                        link
                                        type="primary"
                                        class="op-link op-edit"
                                        @click="handleAddToLine(scope.row)"
                                    >
                                        <img class="op-icon" src="@/assets/images/add-icon.png" alt="加入线路" />
                                        加入线路
                                    </el-button>
                                    <el-button v-else link type="danger" class="op-link op-delete" @click="handleRemoveFromLine(scope.row)">
                                        <img class="op-icon" src="@/assets/images/delete-icon.png" alt="移除设备" />
                                        移除设备
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>

                    <pagination
                        v-show="total > 0"
                        :total="total"
                        v-model:page="queryParams.pageNum"
                        v-model:limit="queryParams.pageSize"
                        @pagination="getList"
                    />
                </el-card>
            </el-col>
        </el-row>

        <!-- 新增巡检线路组对话框 -->
        <el-dialog title="新增巡检线路组" v-model="dialogVisible" width="500px" append-to-body>
            <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="线路组名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入线路组名称" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancel">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 新增/修改巡检路线对话框 -->
        <el-dialog :title="lineDialogTitle" v-model="lineDialogVisible" width="500px" append-to-body>
            <el-form ref="lineFormRef" :model="lineForm" :rules="lineRules" label-width="120px">
                <el-form-item label="路线名称" prop="name">
                    <el-input v-model="lineForm.name" placeholder="请输入巡检路线名称" />
                </el-form-item>
                <el-form-item label="专业类型" prop="speciality">
                    <el-radio-group v-model="lineForm.speciality">
                        <el-radio v-for="dict in tnl_specialty" :key="dict.value" :label="dict.value">
                            {{ dict.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelLine">取 消</el-button>
                    <el-button type="primary" @click="submitLineForm">确 定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 设备选择组件 -->
        <EquipmentSelect
            v-model:visible="equipmentDialogVisible"
            :line-data="currentLineData"
            :selected-equipments="selectedEquipmentList"
            :specialty-type="currentLineData?.speciality"
            @confirm="handleEquipmentConfirm"
            @cancel="handleEquipmentCancel"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, toRefs, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAppStore } from '@/store/modules/app'
import {
    addInspectionLine,
    listInspectionLine,
    getInspectionLine,
    updateInspectionLine,
    getLineDevices,
    delInspectionLine
} from '@/api/subProject/inspection/inspectionLine'
import type { InspectionLineForm } from '@/api/subProject/inspection/inspectionLine/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import type { ManageUnitVO } from '@/api/project/manageUnit/types'
import { listRoom } from '@/api/project/room'
import type { RoomVO } from '@/api/project/room/types'
import type { DeviceVO } from '@/api/subProject/basic/device/types'
import { listDeviceCategoryTree } from '@/api/common/category'
import EquipmentSelect from './EquipmentSelect.vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const appStore = useAppStore()

// 获取数据字典
const { tnl_specialty } = toRefs<any>(proxy?.useDict('tnl_specialty'))

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const lineDialogVisible = ref(false)
const lineDialogTitle = ref('新增巡检路线')
const isEditMode = ref(false)
const equipmentDialogVisible = ref(false)
const currentLineData = ref(null)
const total = ref(0)
const lineTreeRef = ref()
const formRef = ref()
const lineFormRef = ref()

// 树形数据
const lineTreeData = ref([])

const treeProps = {
    children: 'children',
    label: 'label'
}

// 管理单元数据
const manageUnits = ref<ManageUnitVO[]>([])

// 分类选项
const categoryOptions = ref([])

// 分类树形数据
const categoryTreeData = ref([])

// 房间选项
const roomOptions = ref<RoomVO[]>([])

// 设备列表数据
const equipmentList = ref<DeviceVO[]>([])

// 设备选择相关数据
const selectedEquipmentList = ref([])

// 查询参数
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    unitId: undefined,
    roomId: undefined,
    categoryId: undefined,
    name: undefined,
    projectId: undefined,
    lineId: undefined
})

// 表单数据
const form = reactive<InspectionLineForm>({
    name: '',
    prorjectId: undefined,
    level: 1,
    speciality: undefined,
    parentId: undefined
})

// 巡检路线表单数据
const lineForm = reactive<InspectionLineForm>({
    name: '',
    prorjectId: undefined,
    level: 2,
    speciality: undefined,
    parentId: undefined
})

// 表单验证规则
const rules = {
    name: [{ required: true, message: '线路组名称不能为空', trigger: 'blur' }]
}

// 巡检路线表单验证规则
const lineRules = {
    name: [{ required: true, message: '巡检路线名称不能为空', trigger: 'blur' }],
    speciality: [{ required: true, message: '请选择专业类型', trigger: 'change' }]
}

// 选中的设备
const selectedEquipments = ref([])

// 方法定义
const handleAddLine = () => {
    // 设置项目ID
    form.prorjectId = appStore.projectContext.selectedProjectId
    dialogVisible.value = true
    resetForm()
}

const handleAddInspectionLine = (parentData: any) => {
    console.log('新增巡检路线，父节点:', parentData)
    // 设置为新增模式
    isEditMode.value = false
    lineDialogTitle.value = '新增巡检路线'
    // 设置项目ID和父节点ID
    lineForm.prorjectId = appStore.projectContext.selectedProjectId
    lineForm.parentId = parentData.id
    lineDialogVisible.value = true
    resetLineForm()
}

const handleNodeClick = async (data: any) => {
    console.log('点击节点:', data)
    // 如果是叶子节点（具体的巡检路线），加载该路线的设备配置
    if (data.isLeaf && data.level > 1) {
        currentLineData.value = data
        console.log('选中巡检路线，专业类型:', data.speciality)
        // 重新加载设备分类数据，根据当前线路的专业类型
        await loadCategoryOptions()
        await getList()
    } else {
        // 如果点击的是路线组，清空设备列表
        currentLineData.value = null
        equipmentList.value = []
        total.value = 0
        // 重新加载设备分类数据
        await loadCategoryOptions()
    }
}

const handleConfig = async (data: any) => {
    loading.value = true
    console.log('配置线路:', data)
    console.log('线路专业类型:', data.speciality)
    currentLineData.value = data

    try {
        // 加载该线路已配置的设备（使用视图数据，包含设备详细信息）
        const response = await getLineDevices({
            lineId: data.id,
            projectId: appStore.projectContext.selectedProjectId
        })
        console.log('加载线路已配置设备:', response)

        // 处理分页数据结构
        if (response && response.rows && response.rows.length > 0) {
            // 将分页数据转换为设备列表格式，供EquipmentSelect组件使用
            const configuredDevices = response.rows.map((config: any) => ({
                id: config.deviceId,
                projectId: config.projectId,
                name: config.deviceName || '未知设备',
                code: config.deviceCode || '',
                unitId: config.unitId,
                unitName: config.unitName || '',
                roomId: config.roomId,
                roomName: config.roomName || '',
                roomCode: config.roomCode || '',
                categoryIdThird: config.categoryIdThird,
                categoryPath: config.categoryPath || '',
                kind: config.kind,
                specialty: data.speciality || '',
                bgnKilometer: 0,
                endKilometer: 0,
                categoryId: config.categoryIdThird || '',
                categoryName: '',
                installationDate: '',
                manufacturer: '',
                model: '',
                serialNumber: '',
                status: '',
                remark: ''
            }))

            selectedEquipmentList.value = configuredDevices
            console.log('已配置设备列表:', configuredDevices)
        } else {
            selectedEquipmentList.value = []
            console.log('该线路未配置任何设备')
        }
    } catch (error) {
        console.error('加载线路设备配置失败:', error)
        loading.value = false
        selectedEquipmentList.value = []
        ElMessage.warning('加载线路设备配置失败')
    }
    loading.value = false
    equipmentDialogVisible.value = true
}

const handleModify = async (data: any) => {
    console.log('修改线路:', data)
    try {
        // 设置为编辑模式
        isEditMode.value = true
        lineDialogTitle.value = '修改巡检路线'

        // 获取线路详细信息
        const response = await getInspectionLine(data.id)
        const lineData = response.data

        // 填充表单数据
        lineForm.id = lineData.id
        lineForm.name = lineData.name
        lineForm.speciality = lineData.speciality
        lineForm.prorjectId = lineData.prorjectId
        lineForm.parentId = lineData.parentId
        lineForm.level = lineData.level

        console.log('加载线路数据:', lineData)

        // 打开对话框
        lineDialogVisible.value = true
    } catch (error) {
        console.error('获取线路详情失败:', error)
        ElMessage.error('获取线路信息失败')
    }
}

const handleDelete = async (data: any) => {
    try {
        // 检查是否存在子节点
        if (data.children && data.children.length > 0) {
            ElMessage.warning('该线路组下有巡检线路，不能删除')
            return
        }

        await ElMessageBox.confirm(`确定要删除线路"${data.label}"吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })

        // 调用删除API
        await delInspectionLine(data.id)

        ElMessage.success('删除成功')

        // 如果删除的是当前选中的线路，清空右侧设备列表
        if (currentLineData.value?.id === data.id) {
            currentLineData.value = null
            equipmentList.value = []
            total.value = 0
        }

        // 刷新线路树数据
        await loadLineTreeData()
    } catch (error) {
        if (error === 'cancel') {
            ElMessage.info('已取消删除')
        } else {
            console.error('删除线路失败:', error)
            ElMessage.error('删除失败，请重试')
        }
    }
}

const handleQuery = () => {
    queryParams.pageNum = 1
    getList()
}

const handleUnitChange = (unitId: string | number | undefined) => {
    // 清空房间选择
    queryParams.roomId = undefined
    roomOptions.value = []

    // 如果选择了管理单元，加载对应房间
    if (unitId) {
        loadRoomsByUnit(unitId)
    }

    // 触发查询
    handleQuery()
}

const resetQuery = () => {
    queryParams.unitId = undefined
    queryParams.roomId = undefined
    queryParams.categoryId = undefined
    queryParams.name = undefined
    roomOptions.value = []
    // 不清空分类数据，因为分类是根据专业类型动态加载的
    handleQuery()
}

const handleSelectionChange = (selection: any[]) => {
    selectedEquipments.value = selection
}

const handleBatchAdd = () => {
    if (selectedEquipments.value.length === 0) {
        ElMessage.warning('请先选择要添加的设备')
        return
    }
    ElMessage.info('批量新增功能开发中...')
}

const handleAddToLine = (row: any) => {
    console.log('加入线路:', row)
    ElMessage.info('加入线路功能开发中...')
}

const handleRemoveFromLine = async (row: any) => {
    try {
        await ElMessageBox.confirm(`确定要从线路"${currentLineData.value?.label}"中移除设备"${row.name}"吗？`, '确认移除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })

        // 获取当前线路的所有设备配置
        const response = await getLineDevices({
            lineId: currentLineData.value.id,
            projectId: appStore.projectContext.selectedProjectId
        })
        if (response && response.rows && response.rows.length > 0) {
            // 过滤掉要移除的设备
            const remainingDeviceIds = response.rows.filter((config: any) => config.deviceId !== String(row.id)).map((config: any) => config.deviceId)

            // 保存更新后的设备配置
            const { saveLineDevices } = await import('@/api/subProject/inspection/inspectionLine')
            await saveLineDevices(currentLineData.value.id, remainingDeviceIds)

            ElMessage.success('设备移除成功')

            // 重新加载线路设备列表
            await getList()
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('移除设备失败:', error)
            ElMessage.error('移除设备失败')
        }
    }
}

const getList = async () => {
    loading.value = true
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取设备')
            return
        }

        // 如果选中了巡检路线，显示该路线已配置的设备
        if (currentLineData.value?.id) {
            console.log('选中巡检路线，加载该路线已配置的设备')
            queryParams.lineId = currentLineData.value.id
            queryParams.projectId = projectId

            const response = await getLineDevices(queryParams)

            console.log('线路设备配置数据:', response)

            // 处理分页数据结构
            if (response && response.rows && response.rows.length > 0) {
                // 将分页数据转换为设备列表格式
                const deviceList = response.rows.map((config: any) => ({
                    id: config.deviceId,
                    projectId: config.projectId,
                    name: config.deviceName || '未知设备',
                    code: config.deviceCode || '',
                    unitId: config.unitId,
                    unitName: config.unitName || '',
                    roomId: config.roomId,
                    roomName: config.roomName || '',
                    roomCode: config.roomCode || '',
                    categoryIdThird: config.categoryIdThird,
                    categoryPath: config.categoryPath || '',
                    kind: config.kind,
                    specialty: currentLineData.value?.speciality || '',
                    bgnKilometer: 0,
                    endKilometer: 0,
                    categoryId: config.categoryIdThird || '',
                    categoryName: '',
                    installationDate: '',
                    manufacturer: '',
                    model: '',
                    serialNumber: '',
                    status: '',
                    remark: ''
                }))

                equipmentList.value = deviceList
                total.value = response.total || 0
                console.log('成功加载线路设备详情:', deviceList)
                console.log('总设备数:', total.value)
            } else {
                // 没有配置设备
                equipmentList.value = []
                total.value = 0
                console.log('该线路未配置任何设备')
            }
            return
        }

        // 如果没有选中巡检路线，显示所有设备
        console.log('未选中巡检路线，显示所有设备')
        // 这里可以添加显示所有设备的逻辑
        equipmentList.value = []
        total.value = 0
    } catch (error) {
        console.error('获取设备列表失败:', error)
        equipmentList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

const submitForm = async () => {
    try {
        await formRef.value?.validate()

        // 获取当前项目ID
        const currentProjectId = appStore.projectContext.selectedProjectId

        // 准备提交数据
        const submitData: InspectionLineForm = {
            name: form.name,
            prorjectId: currentProjectId, // 确保使用当前项目ID
            level: 1, // 线路组为第一级
            speciality: undefined, // 线路组不指定专业
            parentId: undefined // 线路组的父节点为空
        }

        console.log('提交巡检线路组数据:', submitData)
        console.log('当前项目ID:', currentProjectId)

        // 调用API新增巡检线路组
        const result = await addInspectionLine(submitData)
        console.log('新增巡检线路组结果:', result)

        ElMessage.success('新增巡检线路组成功')
        dialogVisible.value = false

        // 刷新树形数据
        await loadLineTreeData()
    } catch (error) {
        console.error('新增巡检线路组失败:', error)
        ElMessage.error('新增失败，请重试')
    }
}

// 加载巡检线路树形数据
const loadLineTreeData = async () => {
    try {
        const response = await listInspectionLine({
            prorjectId: appStore.projectContext.selectedProjectId
        })

        // 构建树形结构
        const treeData = buildTreeData(response.data || [])
        lineTreeData.value = treeData

        console.log('加载巡检线路树形数据:', treeData)
    } catch (error) {
        console.error('加载巡检线路数据失败:', error)
    }
}

// 构建树形数据结构
const buildTreeData = (data: any[]) => {
    const map = new Map()
    const roots: any[] = []

    // 先将所有节点放入map
    data.forEach((item) => {
        map.set(item.id, {
            id: item.id,
            label: item.name,
            level: item.level,
            speciality: item.speciality,
            parentId: item.parentId,
            children: [],
            isLeaf: item.level > 1 // level > 1 的为叶子节点（具体线路）
        })
    })

    // 构建父子关系
    data.forEach((item) => {
        const node = map.get(item.id)
        if (item.parentId && map.has(item.parentId)) {
            // 有父节点，添加到父节点的children中
            const parent = map.get(item.parentId)
            parent.children.push(node)
        } else {
            // 没有父节点，是根节点
            roots.push(node)
        }
    })

    return roots
}

const cancel = () => {
    dialogVisible.value = false
    resetForm()
}

const resetForm = () => {
    // 保存项目ID，只重置其他字段
    const projectId = form.prorjectId
    form.name = ''
    form.prorjectId = projectId // 保持项目ID不变
    nextTick(() => {
        formRef.value?.clearValidate()
    })
}

const resetLineForm = () => {
    if (isEditMode.value) {
        // 编辑模式下不重置，保持原有数据
        return
    }

    // 新增模式下重置表单
    const projectId = lineForm.prorjectId
    const parentId = lineForm.parentId

    // 重置所有字段
    lineForm.id = undefined
    lineForm.name = ''
    lineForm.speciality = undefined
    lineForm.level = 2
    lineForm.prorjectId = projectId // 保持项目ID不变
    lineForm.parentId = parentId // 保持父节点ID不变

    nextTick(() => {
        lineFormRef.value?.clearValidate()
    })
}

const submitLineForm = async () => {
    try {
        await lineFormRef.value?.validate()

        // 获取当前项目ID
        const currentProjectId = appStore.projectContext.selectedProjectId

        // 验证父节点ID是否存在（新增模式需要验证）
        if (!isEditMode.value && !lineForm.parentId) {
            ElMessage.error('父节点ID不能为空')
            return
        }

        // 准备提交数据
        const submitData: InspectionLineForm = {
            name: lineForm.name,
            prorjectId: currentProjectId, // 确保使用当前项目ID
            level: lineForm.level || 2, // 巡检路线为第二级
            speciality: lineForm.speciality,
            parentId: lineForm.parentId // 设置父节点ID
        }

        // 如果是编辑模式，添加ID
        if (isEditMode.value) {
            submitData.id = lineForm.id
        }

        console.log('提交巡检路线数据:', submitData)
        console.log('当前项目ID:', currentProjectId)
        console.log('父节点ID:', lineForm.parentId)
        console.log('编辑模式:', isEditMode.value)

        // 调用对应的API
        let result
        if (isEditMode.value) {
            result = await updateInspectionLine(submitData)
            console.log('修改巡检路线结果:', result)
            ElMessage.success('修改巡检路线成功')
        } else {
            result = await addInspectionLine(submitData)
            console.log('新增巡检路线结果:', result)
            ElMessage.success('新增巡检路线成功')
        }

        lineDialogVisible.value = false

        // 刷新树形数据
        await loadLineTreeData()
    } catch (error) {
        console.error('操作巡检路线失败:', error)
        ElMessage.error('操作失败，请重试')
    }
}

const cancelLine = () => {
    lineDialogVisible.value = false
    // 重置编辑状态
    isEditMode.value = false
    lineDialogTitle.value = '新增巡检路线'
    // 清空表单数据
    lineForm.id = undefined
    lineForm.name = ''
    lineForm.speciality = undefined
    lineForm.level = 2
    lineForm.prorjectId = undefined
    lineForm.parentId = undefined
    nextTick(() => {
        lineFormRef.value?.clearValidate()
    })
}

// 设备选择组件事件处理
const handleEquipmentConfirm = async (equipments: any[]) => {
    console.log('确认选择设备:', equipments)
    selectedEquipmentList.value = equipments
    ElMessage.success(`已为线路配置 ${equipments.length} 个设备`)

    // 刷新设备列表显示最新的配置
    await getList()
}

const handleEquipmentCancel = () => {
    console.log('取消设备选择')
}

// 清除线路选择
const clearLineSelection = () => {
    currentLineData.value = null
    equipmentList.value = []
    total.value = 0
    console.log('已清除线路选择，显示所有设备')
    // 重新加载设备分类数据
    loadCategoryOptions()
    // 重新加载所有设备（不包含线路过滤）
    getList()
}

// 加载管理单元数据
const loadManageUnits = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取管理单元')
            return
        }

        console.log('开始获取项目管理单元，projectId:', projectId)
        const response = await listProjectManageUnit(projectId)

        if (response && response.data) {
            manageUnits.value = Array.isArray(response.data) ? response.data : [response.data]
            console.log('获取管理单元成功:', manageUnits.value)
        } else if (response && Array.isArray(response)) {
            manageUnits.value = response
            console.log('获取管理单元成功:', manageUnits.value)
        } else {
            console.warn('获取管理单元返回数据格式异常:', response)
            manageUnits.value = []
        }
    } catch (error) {
        console.error('获取管理单元失败:', error)
        ElMessage.warning('获取管理单元失败')
        manageUnits.value = []
    }
}

// 根据管理单元加载房间数据
const loadRoomsByUnit = async (unitId: string | number) => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取房间')
            return
        }

        console.log('开始获取管理单元房间，unitId:', unitId, 'projectId:', projectId)
        const queryParams = {
            projectId: projectId,
            unitId: unitId,
            pageNum: 1,
            pageSize: 1000 // 获取所有房间
        }

        const response = await listRoom(queryParams)
        console.log('API返回的房间数据:', response)

        // 兼容不同的API返回结构
        if (response && response.rows) {
            roomOptions.value = response.rows
            console.log('获取房间成功:', roomOptions.value)
        } else if (response && response.data) {
            roomOptions.value = Array.isArray(response.data) ? response.data : [response.data]
            console.log('获取房间成功:', roomOptions.value)
        } else if (response && Array.isArray(response)) {
            roomOptions.value = response
            console.log('获取房间成功:', roomOptions.value)
        } else {
            console.warn('获取房间返回数据格式异常:', response)
            roomOptions.value = []
        }

        if (roomOptions.value.length === 0) {
            console.warn('该管理单元下没有房间数据')
        } else {
            console.log(`成功获取 ${roomOptions.value.length} 个房间`)
        }
    } catch (error) {
        console.error('获取房间失败:', error)
        ElMessage.warning('获取房间失败')
        roomOptions.value = []
    }
}

// 加载设备分类数据
const loadCategoryOptions = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            console.warn('未选择项目，无法获取设备分类')
            categoryTreeData.value = []
            return
        }

        const specialty = currentLineData.value?.speciality
        if (!specialty) {
            categoryOptions.value = []
            categoryTreeData.value = []
            console.log('未选择专业类型，清空分类选项')
            return
        }

        console.log('开始加载设备分类，专业类型:', specialty, '项目ID:', projectId)

        // 使用listDeviceCategoryTree方法获取设备分类树形数据
        const queryParams = {
            projectId: projectId,
            specialty: specialty
        }
        console.log('查询设备分类树形数据参数:', queryParams)
        const res = await listDeviceCategoryTree(queryParams)
        const treeData = res.data
        console.log('API返回的设备分类树形数据:', treeData)

        if (treeData && treeData.length > 0) {
            // 将树形数据赋值给categoryTreeData
            categoryTreeData.value = formatCategoryData(treeData)
            console.log('成功加载设备分类树形数据:', categoryTreeData.value)
            validateTreeData(treeData)
        } else {
            categoryOptions.value = []
            categoryTreeData.value = []
            console.log('未获取到设备分类数据')
        }
    } catch (error) {
        console.error('获取设备分类失败:', error)
        categoryOptions.value = []
        categoryTreeData.value = []
    }
}

/** 验证树形数据结构 */
const validateTreeData = (data: any[], level = 0): void => {
    data.forEach((item, index) => {
        console.log(`${'  '.repeat(level)}${level > 0 ? '├─ ' : ''}${item.name} (ID: ${item.id})`)
        if (item.children && item.children.length > 0) {
            validateTreeData(item.children, level + 1)
        }
    })
}

/** 格式化分类数据，保持树形结构 */
const formatCategoryData = (data: any[]): any[] => {
    // 直接返回原始树形数据，不进行扁平化处理
    return data.map((item) => ({
        ...item,
        children: item.children && item.children.length > 0 ? formatCategoryData(item.children) : undefined
    }))
}

// 初始化
onMounted(() => {
    queryParams.projectId = appStore.projectContext.selectedProjectId
    currentLineData.value = null // 默认不选择任何线路
    loadLineTreeData() // 加载真实的树形数据
    loadManageUnits() // 加载管理单元数据
    loadCategoryOptions() // 加载设备分类数据
    // 默认不加载设备列表，等用户选择线路后再加载
})
</script>

<style scoped lang="scss">
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

/* 统一深色主题与表格皮肤（参考岗位管理页） */
.inspection-line-page {
    /* 卡片透明、去边框与阴影 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-tree-select .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder),
    :deep(.el-tree-select .el-input__inner::placeholder) {
        color: #8291a9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)),
    :deep(.el-tree-select .el-input__inner) {
        color: #ffffff !important;
    }

    :deep(.el-select__wrapper.is-focused),
    :deep(.el-tree-select .el-input__wrapper.is-focus) {
        box-shadow: none !important;
    }

    /* 树形选择器特殊样式 */
    :deep(.el-tree-select) {
        width: 100%;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #aed7f2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #ffffff !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 隔行渐变与 hover 效果 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 顶部工具按钮 */
    :deep(.toolbar-btn) {
        border: none !important;
        color: #ffffff !important;
        font-size: 14px !important;
        height: 40px;
        padding: 0 16px 0 42px;
        border-radius: 8px;
        position: relative;
    }

    :deep(.toolbar-btn::before) {
        content: '';
        position: absolute;
        left: 14px;
        top: 50%;
        transform: translateY(-50%);
        width: 14px;
        height: 14px;
        background-repeat: no-repeat;
        background-size: contain;
    }

    :deep(.btn-add) {
        background-color: #2a59c4 !important;
    }

    :deep(.btn-add::before) {
        background-image: url('@/assets/images/add-icon.png');
    }

    /* 操作列图标式按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286f3 !important;
    }

    .op-edit {
        color: #42f3e9 !important;
    }

    .op-delete {
        color: #d62121 !important;
    }

    /* 搜索栏样式 */
    .search-bar {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
    }

    /* 树形组件样式 */
    .line-tree {
        height: 100%;
        overflow-y: auto;
        padding: 16px;
    }

    .tree-node {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding-right: 8px;

        .node-label {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-actions {
            position: absolute;
            right: 0;
            display: flex;
        }

        .action-btn {
            padding: 2px 6px;
            font-size: 12px;
            color: #ffffff !important;

            &.delete-btn {
                color: #ffffff !important;
            }
        }
    }

    :deep(.el-tree-node__content) {
        height: 32px;
    }

    :deep(.el-tree-node__content:hover) {
        background-color: transparent !important;
    }

    // 专业类型radio样式
    :deep(.el-radio-group) {
        display: flex;
        flex-direction: row;
        gap: 16px;
        flex-wrap: wrap;
    }

    :deep(.el-radio) {
        margin-right: 0;
        margin-bottom: 0;
    }
}
</style>
