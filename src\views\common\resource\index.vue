<template>
    <div class="p-2">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <!-- <el-form-item label="" prop="projectId">
              <el-input v-model="queryParams.projectId" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
                        <el-form-item label="" prop="typeId">
                            <el-select v-model="form.typeId" clearable placeholder="请输入" @change="handleQuery">
                                <el-option v-for="item in resourceTypeList" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                            <!-- <el-input v-model="queryParams.typeId" placeholder="请输入" clearable @keyup.enter="handleQuery" /> -->
                        </el-form-item>
                        <el-form-item label="物资性质" prop="nature">
                            <el-select v-model="queryParams.nature" placeholder="请选择物资性质" clearable>
                                <el-option v-for="dict in tnl_resource_nature" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['common:resource:add']">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['common:resource:edit']"
                            >修改</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="danger"
                            plain
                            icon="Delete"
                            :disabled="multiple"
                            @click="handleDelete()"
                            v-hasPermi="['common:resource:remove']"
                            >删除</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="warning"
                            plain
                            icon="Download"
                            :loading="exportLoading"
                            :disabled="exportLoading || resourceList.length === 0"
                            @click="handleExport"
                            v-hasPermi="['common:resource:export']"
                        >
                            {{ exportLoading ? '导出中...' : '导出' }}
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="Upload" @click="showResourceImportDialog" v-hasPermi="['common:resource:import']"
                            >批量导入</el-button
                        >
                    </el-col>
                    <!-- <el-col :span="1.5">
                        <el-button type="text" plain @click="handleStockFlow">查看出入库记录</el-button>
                    </el-col> -->
                    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </template>

            <el-table v-loading="loading" :data="resourceList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <!-- <el-table-column label="" align="center" prop="projectId" /> -->
                <el-table-column label="物资类型" align="center" prop="typeId">
                    <template #default="scope">
                        {{ getTypeName(scope.row.typeId) }}
                    </template>
                </el-table-column>

                <el-table-column label="物资性质" align="center" prop="nature">
                    <template #default="scope">
                        <dict-tag :options="tnl_resource_nature" :value="scope.row.nature" />
                    </template>
                </el-table-column>
                <el-table-column label="物资属性" align="center" prop="properties">
                    <template #default="scope">
                        <span v-if="scope.row.properties">
                            {{ getPropertiesLabels(scope.row.properties) }}
                        </span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column label="规格型号" align="center" prop="specification" />
                <el-table-column label="物资单价" align="center" prop="price" />
                <el-table-column label="库存数量" align="center" prop="balanceAmount" />
                <el-table-column label="物资单位" align="center" prop="unit">
                    <template #default="scope">
                        <dict-tag :options="tnl_resource_unit" :value="scope.row.unit" />
                    </template>
                </el-table-column>
                <!-- <el-table-column label="库存数量" align="center" prop="balanceAmount" /> -->
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <!-- <el-tooltip content="出库" placement="top">
                            <el-button link type="primary" @click="handleStock(scope.row, 'in')" v-hasPermi="['basic:resource:instock']"
                                >入库</el-button
                            >
                        </el-tooltip>
                        <el-tooltip content="出库" placement="top">
                            <el-button link type="primary" @click="handleStock(scope.row, 'out')" v-hasPermi="['basic:resource:instock']"
                                >出库</el-button
                            >
                        </el-tooltip> -->
                        <el-tooltip content="修改" placement="top">
                            <el-button
                                link
                                type="primary"
                                icon="Edit"
                                @click="handleUpdate(scope.row)"
                                v-hasPermi="['basic:resource:edit']"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top">
                            <el-button
                                link
                                type="primary"
                                icon="Delete"
                                @click="handleDelete(scope.row)"
                                v-hasPermi="['basic:resource:remove']"
                            ></el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <!-- 添加或修改物资信息对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body :draggable="true">
            <el-form ref="resourceFormRef" :model="form" :rules="rules" label-width="80px">
                <!-- <el-form-item label="" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入" />
        </el-form-item> -->
                <el-form-item label="物资类别" prop="typeId">
                    <el-select v-model="form.typeId" placeholder="请输入" @change="handleChangeResourceType">
                        <el-option v-for="item in resourceTypeList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="物资单位" prop="unit">
                    <el-select v-model="form.unit" disabled placeholder="请选择物资单位">
                        <el-option v-for="dict in tnl_resource_unit" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="物资性质" prop="nature">
                    <el-select v-model="form.nature" disabled placeholder="请选择物资性质">
                        <el-option v-for="dict in tnl_resource_nature" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="物资属性" prop="properties">
                    <el-select :multiple="true" v-model="form.properties" placeholder="请选择物资属性">
                        <el-option v-for="dict in resource_property" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="规格型号" prop="specification">
                    <el-input v-model="form.specification" placeholder="请输入规格型号" />
                </el-form-item>
                <el-form-item label="物资单价" prop="price">
                    <el-input v-model="form.price" placeholder="请输入物资单价" />
                </el-form-item>
                <!-- <el-form-item label="库存数量" prop="balanceAmount">
                    <el-input v-model="form.balanceAmount" disabled placeholder="请输入库存数量" />
                </el-form-item> -->
                <el-form-item label="储存地" prop="storage">
                    <el-input v-model="form.storage" placeholder="请输入储存地" />
                </el-form-item>
                <!-- <el-form-item label="库存数量" prop="pictures">
                    <el-input v-model="form.pictures" type="textarea" placeholder="请输入内容" />
                </el-form-item> -->
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 入库对话框 -->
        <el-dialog title="物资入库" v-model="inStockDialog.visible" width="600px" append-to-body>
            <el-form ref="inStockFormRef" :model="inStockForm" :rules="inStockRules" label-width="80px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="物资名称">
                            <el-text>{{ selectedResource?.typeName }}</el-text>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="计量单位">
                            <el-text>
                                {{
                                    selectedResource?.unit
                                        ? tnl_resource_unit.find((dict) => dict.value === selectedResource.unit)?.label || selectedResource.unit
                                        : '-'
                                }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="物资性质">
                            <el-text>
                                {{
                                    selectedResource?.nature
                                        ? tnl_resource_nature.find((dict) => dict.value === selectedResource.nature)?.label || selectedResource.nature
                                        : '-'
                                }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="规格型号">
                            <el-text>{{ selectedResource?.specification }}</el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="物资属性">
                            <el-text>
                                {{ selectedResource?.properties ? getPropertiesLabels(selectedResource.properties) : '-' }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="所属部门" prop="targetDeptId">
                            <el-tree-select
                                v-model="inStockForm.targetDeptId"
                                :data="deptTreeData"
                                :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
                                value-key="deptId"
                                placeholder="请选择所属部门"
                                style="width: 100%"
                                clearable
                                filterable
                                check-strictly
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="入库时间" prop="handleTime">
                            <el-date-picker
                                v-model="inStockForm.handleTime"
                                type="datetime"
                                placeholder="请选择入库时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="需要盘点" prop="needCheck">
                            <el-select v-model="inStockForm.needCheck" placeholder="是否需要盘点" style="width: 100%">
                                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="最新单价" prop="price">
                            <el-input v-model="inStockForm.price" placeholder="请输入最新单价" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="入库数量" prop="amount">
                            <el-input v-model="inStockForm.amount" placeholder="请输入该批数量" type="number" min="0" step="0.01" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="入库类型" prop="stockWay">
                            <el-select v-model="inStockForm.stockWay" placeholder="请选择入库类型" style="width: 100%">
                                <el-option v-for="dict in stockInTypeList" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="描述" prop="description">
                            <el-input v-model="inStockForm.description" placeholder="请输入" type="textarea" :rows="3" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitInStockForm">确 定</el-button>
                    <el-button @click="cancelInStock">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 出库对话框 -->
        <el-dialog title="物资出库" v-model="outStockDialog.visible" width="600px" append-to-body>
            <el-form ref="outStockFormRef" :model="outStockForm" :rules="outStockRules" label-width="80px">
                <!-- 物资相关详情信息 -->
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="物资名称">
                            <el-text>{{ selectedResource?.typeName }}</el-text>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="规格型号">
                            <el-text>{{ selectedResource?.specification }}</el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="计量单位">
                            <el-text>
                                {{
                                    selectedResource?.unit
                                        ? tnl_resource_unit.find((dict) => dict.value === selectedResource.unit)?.label || selectedResource.unit
                                        : '-'
                                }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="物资性质">
                            <el-text>
                                {{
                                    selectedResource?.nature
                                        ? tnl_resource_nature.find((dict) => dict.value === selectedResource.nature)?.label || selectedResource.nature
                                        : '-'
                                }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="物资属性">
                            <el-text>
                                {{ selectedResource?.properties ? getPropertiesLabels(selectedResource.properties) : '-' }}
                            </el-text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 出库相关信息 -->
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="出库部门" prop="targetDeptId">
                            <el-tree-select
                                v-model="outStockForm.targetDeptId"
                                :data="deptTreeData"
                                :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
                                value-key="deptId"
                                placeholder="请选择出库部门"
                                style="width: 100%"
                                clearable
                                filterable
                                check-strictly
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="项目名称" prop="projectId">
                            <el-select v-model="outStockForm.projectId" placeholder="请选择项目编号" @change="handleChangeProject">
                                <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="管理单元" prop="unitId">
                            <el-select v-model="outStockForm.unitId" placeholder="请选择管理单元" style="width: 100%">
                                <el-option v-for="item in manageUnitList" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <!-- 空列，保持布局平衡 -->
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="出库时间" prop="handleTime">
                            <el-date-picker
                                v-model="outStockForm.handleTime"
                                type="datetime"
                                placeholder="请选择出库时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="需要盘点" prop="needCheck">
                            <el-select v-model="outStockForm.needCheck" placeholder="是否需要盘点" style="width: 100%">
                                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="最新单价" prop="price">
                            <el-input v-model="outStockForm.price" placeholder="请输入最新单价" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="出库数量" prop="amount">
                            <el-input v-model="outStockForm.amount" placeholder="请输入出库数量" type="number" min="0" step="0.01" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="出库类型" prop="stockWay">
                            <el-select v-model="outStockForm.stockWay" placeholder="请选择出库类型">
                                <el-option v-for="dict in stockOutTypeList" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="申请人" prop="handlerName">
                            <el-input v-model="outStockForm.handlerName" placeholder="请输入领用人" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="描述" prop="description">
                            <el-input v-model="outStockForm.description" placeholder="请输入" type="textarea" :rows="3" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitOutStockForm">确 定</el-button>
                    <el-button @click="cancelOutStock">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 🔥 物资基本信息批量导入对话框 -->
        <el-dialog v-model="resourceImportDialog.visible" title="批量导入物资基本信息" width="900px" :close-on-click-modal="false" append-to-body>
            <!-- 文件上传区域 -->
            <el-card shadow="never" class="mb-4">
                <template #header>
                    <span>选择Excel文件</span>
                </template>

                <el-upload
                    ref="resourceUploadRef"
                    :limit="1"
                    accept=".xlsx,.xls"
                    :auto-upload="false"
                    :on-change="handleResourceFileChange"
                    :on-remove="handleResourceFileRemove"
                    drag
                    style="width: 100%"
                >
                    <el-icon class="el-icon--upload" style="font-size: 67px; color: #c0c4cc">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
                    <template #tip>
                        <div class="el-upload__tip" style="color: #999; font-size: 12px">
                            只能上传xlsx/xls文件，且不超过10MB
                            <el-link type="primary" @click="downloadResourceTemplate" style="margin-left: 10px"> 下载导入模板 </el-link>
                        </div>
                    </template>
                </el-upload>
            </el-card>

            <!-- 解析结果预览 -->
            <el-card v-if="resourceImportPreviewData.length > 0" shadow="never">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center">
                        <span>数据预览 (共 {{ resourceImportPreviewData.length }} 条)</span>
                        <div>
                            <el-tag v-if="resourceValidImportCount > 0" type="success"> {{ resourceValidImportCount }} 条有效 </el-tag>
                            <el-tag v-if="resourceErrorImportCount > 0" type="danger" style="margin-left: 8px">
                                {{ resourceErrorImportCount }} 条错误
                            </el-tag>
                        </div>
                    </div>
                </template>

                <!-- 简化的预览表格 -->
                <el-table
                    :data="resourceImportPreviewData"
                    border
                    style="width: 100%"
                    max-height="400"
                    size="small"
                    :row-class-name="getResourceImportRowClassName"
                >
                    <el-table-column prop="rowNumber" label="行号" width="60" align="center" />

                    <!-- 物资名称列 -->
                    <el-table-column label="物资名称" width="120">
                        <template #default="{ row }">
                            <div :class="getResourceFieldClassName(row, 'typeName')">
                                {{ row.typeName }}
                                <el-icon v-if="row.errors.typeName" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 规格型号列 -->
                    <el-table-column label="规格型号" width="150">
                        <template #default="{ row }">
                            <div :class="getResourceFieldClassName(row, 'specification')">
                                {{ row.specification }}
                                <el-icon v-if="row.errors.specification" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 物资性质列 -->
                    <el-table-column label="物资性质" width="100">
                        <template #default="{ row }">
                            <div :class="getResourceFieldClassName(row, 'nature')">
                                {{ row.nature }}
                                <el-icon v-if="row.errors.nature" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 物资属性列 -->
                    <el-table-column label="物资属性" width="150">
                        <template #default="{ row }">
                            <div :class="getResourceFieldClassName(row, 'properties')">
                                {{ row.properties }}
                                <el-icon v-if="row.errors.properties" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 计量单位列 -->
                    <el-table-column label="计量单位" width="100">
                        <template #default="{ row }">
                            <div :class="getResourceFieldClassName(row, 'unit')">
                                {{ row.unitLabel || getResourceUnitLabelByCode(row.unit) }}
                                <el-icon v-if="row.errors.unit" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 单价列 -->
                    <el-table-column prop="price" label="单价" width="80" align="right">
                        <template #default="{ row }">
                            <div :class="getResourceFieldClassName(row, 'price')">
                                {{ row.price }}
                                <el-icon v-if="row.errors.price" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 数量列 -->
                    <el-table-column prop="balanceAmount" label="数量" width="80" align="right">
                        <template #default="{ row }">
                            <div :class="getResourceFieldClassName(row, 'balanceAmount')">
                                {{ row.balanceAmount }}
                                <el-icon v-if="row.errors.balanceAmount" class="error-icon" style="color: #f56c6c; margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 验证状态列 -->
                    <el-table-column label="验证状态" width="100" align="center">
                        <template #default="{ row }">
                            <div>
                                <el-tag v-if="row.isValid" type="success" size="small">通过</el-tag>
                                <el-tag v-else type="danger" size="small">失败</el-tag>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 🔥 行级错误提示区域 -->
                <div v-if="resourceImportPreviewData.some((row) => !row.isValid)" style="margin-top: 16px">
                    <el-card shadow="never" style="background-color: #fef0f0; border: 1px solid #fbc4c4">
                        <template #header>
                            <div style="color: #f56c6c; font-weight: bold">
                                <el-icon><Warning /></el-icon>
                                数据验证错误详情
                            </div>
                        </template>

                        <div class="error-details">
                            <div
                                v-for="row in resourceImportPreviewData.filter((r) => !r.isValid)"
                                :key="row.rowNumber"
                                class="error-row-detail"
                                style="margin-bottom: 12px; padding: 8px; background-color: #fff; border-radius: 4px"
                            >
                                <div style="font-weight: bold; color: #f56c6c; margin-bottom: 4px">第 {{ row.rowNumber }} 行错误：</div>

                                <!-- 基础字段错误 -->
                                <div v-if="Object.keys(row.errors).some((key) => key !== 'uniqueness')" style="margin-bottom: 4px">
                                    <span style="color: #909399">基础字段：</span>
                                    <span v-for="(error, field) in row.errors" :key="field" style="color: #f56c6c">
                                        <span v-if="field !== 'uniqueness'">{{ error }}；</span>
                                    </span>
                                </div>

                                <!-- 唯一性错误 -->
                                <div v-if="row.errors.uniqueness" style="margin-bottom: 4px">
                                    <span style="color: #909399">唯一性检查：</span>
                                    <span style="color: #f56c6c">{{ row.errors.uniqueness }}</span>
                                </div>

                                <!-- 🔥 智能建议信息 -->
                                <div v-if="row.suggestions" style="font-size: 12px; color: #606266; margin-top: 4px">
                                    <div v-if="row.suggestions.natures?.length > 0" style="margin-bottom: 2px">
                                        <span style="color: #909399">建议的物资性质：</span>
                                        <span style="color: #409eff">{{ row.suggestions.natures.join('、') }}</span>
                                    </div>
                                    <div v-if="row.suggestions.properties?.length > 0" style="margin-bottom: 2px">
                                        <span style="color: #909399">建议的物资属性：</span>
                                        <span style="color: #409eff">{{ row.suggestions.properties.join('、') }}</span>
                                    </div>
                                    <div v-if="row.suggestions.units?.length > 0" style="margin-bottom: 2px">
                                        <span style="color: #909399">建议的计量单位：</span>
                                        <span style="color: #409eff">{{ row.suggestions.units.join('、') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </div>
            </el-card>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelResourceImport">取消</el-button>
                    <el-button
                        type="primary"
                        @click="parseResourceExcelData"
                        :loading="resourceParsing"
                        v-if="resourceSelectedFile && resourceImportPreviewData.length === 0"
                    >
                        解析数据
                    </el-button>
                    <el-button
                        type="success"
                        @click="confirmResourceImport"
                        :disabled="resourceErrorImportCount > 0 || resourceImportPreviewData.length === 0"
                        :loading="resourceImporting"
                    >
                        确认导入 ({{ resourceValidImportCount }} 条)
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Resource" lang="ts">
import { nextTick } from 'vue'
import {
    listResource,
    getResourceView,
    delResource,
    addResource,
    updateResource,
    checkResourceExists,
    batchImportResource
} from '@/api/common/resource'
import * as XLSX from 'xlsx'
import { ElLoading } from 'element-plus'
import { ResourceViewVO, ResourceViewQuery, ResourceForm, ResourceImportRow } from '@/api/common/resource/types'
import { listResourceType } from '@/api/common/resourceType'
import { ResourceTypeQuery, ResourceTypeVO } from '@/api/common/resourceType/types'
import { addResourceStockFlow, updateResourceStockFlow } from '@/api/common/resourceStockFlow'
import { ResourceStockFlowVO, ResourceStockFlowQuery, ResourceStockFlowForm } from '@/api/common/resourceStockFlow/types'
import { listProject } from '@/api/project/project'
import { ProjectVO } from '@/api/project/project/types'
import { listDept } from '@/api/system/dept'
import { DeptVO } from '@/api/system/dept/types'
import { getProjectResourceStock } from '@/api/common/resourceStock'
import { ResourceStockVO } from '@/api/common/resourceStock/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import router from '@/router'
import { dir } from 'console'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { tnl_resource_unit, tnl_resource_nature, sys_yes_no, resource_property } = toRefs<any>(
    proxy?.useDict('tnl_resource_unit', 'tnl_resource_nature', 'sys_yes_no', 'resource_property')
)

const resourceList = ref<ResourceViewVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 导出相关状态
const exportLoading = ref(false)

const resourceTypeList = ref<ResourceTypeVO[]>([])
const queryFormRef = ref<ElFormInstance>()
const resourceFormRef = ref<ElFormInstance>()
const resourceStockFlowFormRef = ref<ElFormInstance>()
const inStockFormRef = ref<ElFormInstance>()
const outStockFormRef = ref<ElFormInstance>()
const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})
const inStockDialog = reactive<DialogOption>({
    visible: false,
    title: ''
})
const outStockDialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

const initFormData: ResourceForm = {
    id: undefined,
    //projectId: undefined,
    typeId: undefined,
    properties: [] as string[],
    unit: undefined,
    nature: undefined,
    specification: undefined,
    price: undefined,
    balanceAmount: 0,
    storage: undefined,
    pictures: undefined
}

const resourceTypeQueryParams = ref<ResourceTypeQuery>({
    pageNum: 1,
    pageSize: 1000
})
const data = reactive<PageData<ResourceForm, ResourceViewQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        typeId: undefined,
        nature: undefined,
        params: {}
    },
    rules: {
        projectId: [{ required: true, message: '不能为空', trigger: 'blur' }],
        typeId: [{ required: true, message: '不能为空', trigger: 'blur' }],
        unit: [{ required: true, message: '物资单位不能为空', trigger: 'change' }],
        nature: [{ required: true, message: '物资性质不能为空', trigger: 'change' }],
        specification: [{ required: true, message: '规格型号不能为空', trigger: 'blur' }],
        price: [{ required: true, message: '物资单价不能为空', trigger: 'blur' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

// 🔥 物资导入相关响应式数据
const resourceImportDialog = reactive({
    visible: false
})

const resourceSelectedFile = ref<File | null>(null)
const resourceUploadRef = ref()
const resourceParsing = ref(false)
const resourceImporting = ref(false)
const resourceImportPreviewData = ref<ResourceImportRow[]>([])

// 导入统计数据
const resourceValidImportCount = computed(() => {
    return resourceImportPreviewData.value.filter((item) => item.isValid).length
})

const resourceErrorImportCount = computed(() => {
    return resourceImportPreviewData.value.filter((item) => !item.isValid).length
})
const initStockFlowFormData: ResourceStockFlowForm = {
    id: undefined,
    projectId: undefined,
    resourceId: undefined,
    targetDeptId: undefined,
    unitId: undefined,
    needCheck: undefined,
    handlerName: undefined,
    handleName: undefined,
    taskId: undefined,
    taskType: undefined,
    handleTime: undefined,
    price: undefined,
    amount: undefined,
    stockWay: undefined,
    description: undefined
}

// 入库表单验证规则
const inStockRules = {
    targetDeptId: [{ required: true, message: '所属部门不能为空', trigger: 'change' }],
    handleTime: [{ required: true, message: '入库时间不能为空', trigger: 'change' }],
    needCheck: [{ required: true, message: '是否需要盘点不能为空', trigger: 'change' }],
    price: [{ required: true, message: '最新单价不能为空', trigger: 'blur' }],
    amount: [
        { required: true, message: '入库数量不能为空', trigger: 'blur' },
        {
            validator: (rule: any, value: any, callback: any) => {
                if (!value) {
                    callback()
                    return
                }
                const num = Number(value)
                if (isNaN(num)) {
                    callback(new Error('入库数量必须为数字'))
                } else if (num <= 0) {
                    callback(new Error('入库数量必须大于0'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ],
    stockWay: [{ required: true, message: '入库类型不能为空', trigger: 'change' }]
}

// 出库表单验证规则
const outStockRules = {
    projectId: [{ required: true, message: '项目名称不能为空', trigger: 'change' }],
    targetDeptId: [{ required: true, message: '出库部门不能为空', trigger: 'change' }],
    unitId: [{ required: true, message: '管理单元不能为空', trigger: 'change' }],
    handleTime: [{ required: true, message: '出库时间不能为空', trigger: 'change' }],
    needCheck: [{ required: true, message: '是否需要盘点不能为空', trigger: 'change' }],
    price: [{ required: true, message: '最新单价不能为空', trigger: 'blur' }],
    amount: [
        { required: true, message: '出库数量不能为空', trigger: 'blur' },
        {
            validator: (rule: any, value: any, callback: any) => {
                if (!value) {
                    callback()
                    return
                }
                const num = Number(value)
                if (isNaN(num)) {
                    callback(new Error('出库数量必须为数字'))
                } else if (num <= 0) {
                    callback(new Error('出库数量必须大于0'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ],
    stockWay: [{ required: true, message: '出库类型不能为空', trigger: 'change' }],
    handlerName: [{ required: true, message: '申请人不能为空', trigger: 'blur' }]
}
const selectedResource = ref<ResourceViewVO>() //
const stockFlowData = reactive<PageData<ResourceStockFlowForm, ResourceStockFlowQuery>>({
    form: { ...initStockFlowFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        resourceId: undefined,
        typeId: undefined,

        params: {}
    },
    rules: {
        id: [{ required: true, message: '不能为空', trigger: 'blur' }]
    }
})
const stockDirection = ref<string>('in')
const stockInTypeList = ref<any[]>([
    { value: 'rkdj', label: '入库登记' },
    { value: 'back', label: '归还' }
])
const stockOutTypeList = ref<any[]>([
    { value: 'use', label: '领用' },
    { value: 'borrow', label: '借用' }
])

const { queryParams: queryStockFlowParams, form: stockFlowForm, rules: stockFlowRules } = toRefs(stockFlowData)

// 入库表单数据
const inStockForm = ref<ResourceStockFlowForm>({ ...initStockFlowFormData })
// 出库表单数据
const outStockForm = ref<ResourceStockFlowForm>({ ...initStockFlowFormData })
/** 查询物资信息列表 */
const getList = async () => {
    //debugger;
    loading.value = true
    const res = await listResource(queryParams.value)
    resourceList.value = res.rows
    total.value = res.total
    loading.value = false
}
const getResourceTypreList = async () => {
    loading.value = true
    const res = await listResourceType(resourceTypeQueryParams.value)

    resourceTypeList.value = res.rows
}
const getTypeName = (typeId: string | number) => {
    const type = resourceTypeList.value.find((item) => item.id === typeId)
    return type ? type.name : '未知类型' // 如果未找到匹配项，显示"未知类型"
}

/** 获取物资属性标签，将逗号分隔的属性值转换为对应的标签 */
const getPropertiesLabels = (properties: string) => {
    if (!properties) return '-'

    const propertyValues = properties.split(',').filter((item) => item.trim() !== '')
    const labels = propertyValues.map((value) => {
        const dict = resource_property.value?.find((item) => item.value === value.trim())
        return dict ? dict.label : value.trim()
    })

    return labels.join('，')
}

/** 数据字典转换函数 - 物资性质 */
const convertNature = (natureCode: string) => {
    if (!natureCode) return '-'
    const natureDict = tnl_resource_nature.value?.find((item) => item.value === natureCode)
    return natureDict ? natureDict.label : natureCode
}

/** 数据字典转换函数 - 计量单位 */
const convertUnit = (unitCode: string) => {
    if (!unitCode) return '-'
    const unitDict = tnl_resource_unit.value?.find((item) => item.value === unitCode)
    return unitDict ? unitDict.label : unitCode
}

/** 数据字典转换函数 - 物资属性（用于导出） */
const convertPropertiesForExport = (properties: string) => {
    if (!properties) return '-'

    const propertyValues = properties.split(/[,，、\/]/).filter((item) => item.trim() !== '')
    const labels = propertyValues.map((value) => {
        const trimmedValue = value.trim()
        const dict = resource_property.value?.find((item) => item.value === trimmedValue)
        return dict ? dict.label : trimmedValue
    })

    return labels.join('、')
}

/** 获取导出数据 */
const getExportData = async () => {
    try {
        // 构建查询参数，移除分页参数以获取所有数据
        const params = { ...queryParams.value }
        delete params.pageNum
        delete params.pageSize

        const response = await listResource(params)
        return response.rows || []
    } catch (error) {
        console.error('获取导出数据失败:', error)
        throw new Error('获取导出数据失败')
    }
}

/** 转换数据为导出格式 */
const convertDataForExport = (data: ResourceViewVO[]) => {
    return data.map((item) => ({
        '物资名称': item.typeName || '-',
        '型号': item.specification || '-',
        '物资性质': convertNature(item.nature),
        '属性': convertPropertiesForExport(item.properties),
        '计量单位': convertUnit(item.unit),
        '价格': item.price || 0,
        '库存': item.balanceAmount || 0
    }))
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}
const cancelStock = () => {
    resetStockFlow()
    // 这个方法已经被 cancelInStock 和 cancelOutStock 替代
    // stockFlowDialog.visible = false;
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    resourceFormRef.value?.resetFields()
}
const resetStockFlow = () => {
    stockFlowForm.value = { ...initStockFlowFormData }
    resourceStockFlowFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ResourceViewVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    dialog.visible = true
    dialog.title = '添加物资信息'
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ResourceViewVO) => {
    reset()
    const _id = row?.id || ids.value[0]
    const res = await getResourceView(_id)
    const data = res.data

    // 创建一个副本来处理类型转换
    const formData = { ...data } as any

    // 将逗号分隔的字符串转换为数组
    if (data.properties) {
        formData.properties = data.properties.split(',').filter((item) => item.trim() !== '')
    } else {
        formData.properties = []
    }

    Object.assign(form.value, formData)
    dialog.visible = true
    dialog.title = '修改物资信息'
}

/** 提交按钮 */
const submitForm = () => {
    resourceFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true

            // 创建一个副本用于提交，避免修改原始表单数据
            const submitData = { ...form.value }

            // 将properties数组转换为逗号分隔的字符串
            if (Array.isArray(submitData.properties)) {
                submitData.properties = submitData.properties.join(',')
            }

            if (form.value.id) {
                await updateResource(submitData).finally(() => (buttonLoading.value = false))
            } else {
                await addResource(submitData).finally(() => (buttonLoading.value = false))
            }
            proxy?.$modal.msgSuccess('操作成功')
            dialog.visible = false
            await getList()
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: ResourceViewVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除物资信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delResource(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 导出按钮操作 */
const handleExport = async () => {
    if (exportLoading.value) return

    try {
        exportLoading.value = true

        // 显示加载提示
        const loadingInstance = ElLoading.service({
            lock: true,
            text: '正在导出数据，请稍候...',
            background: 'rgba(0, 0, 0, 0.7)'
        })

        // 获取导出数据
        const data = await getExportData()

        if (!data || data.length === 0) {
            proxy?.$modal.msgWarning('没有可导出的数据')
            return
        }

        // 转换数据格式
        const exportData = convertDataForExport(data)

        // 创建工作表
        const ws = XLSX.utils.json_to_sheet(exportData)

        // 设置列宽
        const colWidths = [
            { wch: 20 }, // 物资名称
            { wch: 20 }, // 型号
            { wch: 12 }, // 物资性质
            { wch: 30 }, // 属性
            { wch: 12 }, // 计量单位
            { wch: 12 }, // 价格
            { wch: 12 } // 库存
        ]
        ws['!cols'] = colWidths

        // 创建工作簿
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, '物资信息')

        // 生成文件名
        const fileName = `物资信息_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`

        // 导出文件
        XLSX.writeFile(wb, fileName)

        // 关闭加载提示
        loadingInstance?.close()

        // 显示成功消息
        proxy?.$modal.msgSuccess(`成功导出 ${exportData.length} 条物资信息`)
    } catch (error) {
        console.error('导出失败:', error)
        proxy?.$modal.msgError('导出失败：' + (error as Error).message)
    } finally {
        exportLoading.value = false
    }
}

const handleChangeResourceType = (typeId: string | number) => {
    const type = resourceTypeList.value.find((item) => item.id === typeId)
    form.value.unit = type.unit
    form.value.nature = type.nature
    // return type ? type.name : "未知类型"; // 如果未找到匹配项，显示"未知类型"
}
const handleChangeProject = async () => {
    // 当项目改变时，重新加载该项目的管理单元
    if (outStockForm.value.projectId) {
        await getProjectManageUnits(outStockForm.value.projectId)
        // 清空已选择的管理单元
        outStockForm.value.unitId = undefined
    } else {
        manageUnitList.value = []
        outStockForm.value.unitId = undefined
    }
}

const handleStock = async (row?: ResourceViewVO, direction?: string) => {
    const res = await getResourceView(row.id)
    selectedResource.value = res.data

    if (direction === 'in') {
        // 完全重置入库表单
        inStockForm.value = { ...initStockFlowFormData }
        inStockForm.value.resourceId = res.data.id
        inStockForm.value.price = res.data.price

        // 清除表单验证状态
        nextTick(() => {
            inStockFormRef.value?.clearValidate()
        })

        inStockDialog.visible = true
    } else if (direction === 'out') {
        // 完全重置出库表单
        outStockForm.value = { ...initStockFlowFormData }
        outStockForm.value.resourceId = res.data.id
        outStockForm.value.price = res.data.price

        // 清空管理单元列表，等待选择项目后重新加载
        manageUnitList.value = []

        // 清除表单验证状态
        nextTick(() => {
            outStockFormRef.value?.clearValidate()
        })

        outStockDialog.visible = true
    }
}

// 取消入库
const cancelInStock = () => {
    inStockForm.value = { ...initStockFlowFormData }
    // 清除表单验证状态
    nextTick(() => {
        inStockFormRef.value?.clearValidate()
    })
    inStockDialog.visible = false
}

// 取消出库
const cancelOutStock = () => {
    outStockForm.value = { ...initStockFlowFormData }
    // 清空管理单元列表
    manageUnitList.value = []
    // 清除表单验证状态
    nextTick(() => {
        outStockFormRef.value?.clearValidate()
    })
    outStockDialog.visible = false
}

// 提交入库表单
const submitInStockForm = () => {
    inStockFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true
            try {
                // 准备提交数据
                const submitData = { ...inStockForm.value }

                // 设置入库方向
                submitData.direction = 'in'

                // 确保 targetDeptId 和 handleTime 字段正确传递
                console.log('入库表单原始数据:', inStockForm.value)
                console.log('targetDeptId:', inStockForm.value.targetDeptId)
                console.log('handleTime:', inStockForm.value.handleTime)
                console.log('needCheck:', inStockForm.value.needCheck)

                // 确保字段值不为空
                if (!submitData.targetDeptId) {
                    proxy?.$modal.msgError('请选择所属部门')
                    return
                }

                if (!submitData.handleTime) {
                    proxy?.$modal.msgError('请选择入库时间')
                    return
                }

                if (!submitData.needCheck) {
                    proxy?.$modal.msgError('请选择是否需要盘点')
                    return
                }

                console.log('提交入库表单数据:', submitData)

                // 调用入库API
                await addResourceStockFlow(submitData)

                proxy?.$modal.msgSuccess('入库成功')
                cancelInStock()
                await getList() // 刷新物资列表
            } catch (error) {
                console.error('入库失败:', error)
                console.error('错误详情:', error)
                proxy?.$modal.msgError('入库失败: ' + ((error as any)?.message || '未知错误'))
            } finally {
                buttonLoading.value = false
            }
        } else {
            console.log('表单验证失败')
        }
    })
}

// 提交出库表单
const submitOutStockForm = () => {
    outStockFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true
            try {
                // 准备提交数据
                const submitData = { ...outStockForm.value }

                // 设置出库方向
                submitData.direction = 'out'

                // 确保 targetDeptId、handleTime 和 needCheck 字段正确传递
                console.log('出库表单原始数据:', outStockForm.value)
                console.log('targetDeptId:', outStockForm.value.targetDeptId)
                console.log('handleTime:', outStockForm.value.handleTime)
                console.log('needCheck:', outStockForm.value.needCheck)

                // 确保字段值不为空
                if (!submitData.targetDeptId) {
                    proxy?.$modal.msgError('请选择出库部门')
                    return
                }

                if (!submitData.projectId) {
                    proxy?.$modal.msgError('请选择项目名称')
                    return
                }

                if (!submitData.unitId) {
                    proxy?.$modal.msgError('请选择管理单元')
                    return
                }

                if (!submitData.handleTime) {
                    proxy?.$modal.msgError('请选择出库时间')
                    return
                }

                if (!submitData.needCheck) {
                    proxy?.$modal.msgError('请选择是否需要盘点')
                    return
                }

                console.log('提交出库表单数据:', submitData)

                // 调用出库API
                await addResourceStockFlow(submitData)

                proxy?.$modal.msgSuccess('出库成功')
                cancelOutStock()
                await getList() // 刷新物资列表
            } catch (error) {
                console.error('出库失败:', error)
                console.error('错误详情:', error)
                proxy?.$modal.msgError('出库失败: ' + ((error as any)?.message || '未知错误'))
            } finally {
                buttonLoading.value = false
            }
        } else {
            console.log('表单验证失败')
        }
    })
}
const projectList = ref<ProjectVO[]>([])
const deptList = ref<DeptVO[]>([])
const deptTreeData = ref<DeptVO[]>([])
const manageUnitList = ref<ManageUnitVO[]>([])

const getAllProjects = async () => {
    try {
        const res = await listProject({
            pageNum: 1,
            pageSize: 1000,
            params: {}
        })

        projectList.value = res.rows
    } catch (error) {
        proxy?.$modal.msgError('获取项目列表失败')
    }
}

// 使用系统提供的handleTree方法处理部门树形结构
const buildDeptTreeOptions = (depts: DeptVO[]) => {
    console.log('原始部门数据:', depts)

    if (!depts || !Array.isArray(depts)) {
        console.warn('部门数据为空或格式不正确')
        return []
    }

    // 使用系统的handleTree方法，参考系统部门管理页面的实现
    const treeData = proxy?.handleTree<DeptVO>(depts, 'deptId')
    console.log('处理后的部门树形数据:', treeData)

    return treeData || []
}

const getAllDepts = async () => {
    try {
        const res = await listDept({
            pageNum: 1,
            pageSize: 1000
        })
        console.log('部门API返回数据:', res)

        // 检查返回数据结构
        if (res.data && Array.isArray(res.data)) {
            deptList.value = res.data
            console.log('部门列表数据:', res.data)
            // 构建树形结构
            deptTreeData.value = buildDeptTreeOptions(res.data)
            console.log('部门树形数据:', deptTreeData.value)
        } else if (res.rows && Array.isArray(res.rows)) {
            // 有些API返回的是rows字段
            deptList.value = res.rows
            console.log('部门列表数据(rows):', res.rows)
            deptTreeData.value = buildDeptTreeOptions(res.rows)
            console.log('部门树形数据:', deptTreeData.value)
        } else {
            console.error('部门API返回数据格式异常:', res)
            proxy?.$modal.msgError('部门数据格式异常')

            // 添加测试数据以便调试，使用与系统部门管理一致的数据结构
            const testDepts: DeptVO[] = [
                {
                    id: 1,
                    deptName: '总公司',
                    parentId: 0,
                    parentName: '',
                    children: [],
                    deptId: 1,
                    deptCategory: 'company',
                    orderNum: 1,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0',
                    menuId: ''
                },
                {
                    id: 2,
                    deptName: '技术部',
                    parentId: 1,
                    parentName: '总公司',
                    children: [],
                    deptId: 2,
                    deptCategory: 'dept',
                    orderNum: 2,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1',
                    menuId: ''
                },
                {
                    id: 3,
                    deptName: '财务部',
                    parentId: 1,
                    parentName: '总公司',
                    children: [],
                    deptId: 3,
                    deptCategory: 'dept',
                    orderNum: 3,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1',
                    menuId: ''
                },
                {
                    id: 4,
                    deptName: '开发组',
                    parentId: 2,
                    parentName: '技术部',
                    children: [],
                    deptId: 4,
                    deptCategory: 'group',
                    orderNum: 4,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1,2',
                    menuId: ''
                },
                {
                    id: 5,
                    deptName: '前端小组',
                    parentId: 4,
                    parentName: '开发组',
                    children: [],
                    deptId: 5,
                    deptCategory: 'team',
                    orderNum: 5,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1,2,4',
                    menuId: ''
                },
                {
                    id: 6,
                    deptName: '后端小组',
                    parentId: 4,
                    parentName: '开发组',
                    children: [],
                    deptId: 6,
                    deptCategory: 'team',
                    orderNum: 6,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1,2,4',
                    menuId: ''
                },
                {
                    id: 7,
                    deptName: '测试组',
                    parentId: 2,
                    parentName: '技术部',
                    children: [],
                    deptId: 7,
                    deptCategory: 'group',
                    orderNum: 7,
                    leader: '',
                    phone: '',
                    email: '',
                    status: '0',
                    delFlag: '0',
                    ancestors: '0,1,2',
                    menuId: ''
                }
            ]
            deptList.value = testDepts
            deptTreeData.value = buildDeptTreeOptions(testDepts)
            console.log('使用测试部门数据')
        }
    } catch (error) {
        console.error('获取部门列表失败:', error)
        proxy?.$modal.msgError('获取部门列表失败')
    }
}

// 获取项目管理单元
const getProjectManageUnits = async (projectId: string | number) => {
    try {
        const res = await listProjectManageUnit(projectId.toString())
        if (res && res.data) {
            manageUnitList.value = Array.isArray(res.data) ? res.data : [res.data]
        } else {
            manageUnitList.value = []
        }
    } catch (error) {
        console.error('获取管理单元失败:', error)
        proxy?.$modal.msgError('获取管理单元失败')
        manageUnitList.value = []
    }
}
const resourceOptions = ref<ResourceViewVO[]>([]) // 定义一个数组来存储资源列表
const getAllResouces = async () => {
    try {
        const res = await listResource({
            pageNum: 1,
            pageSize: 1000,
            params: {}
        })

        resourceOptions.value = res.rows
    } catch (error) {
        proxy?.$modal.msgError('获取项目列表失败')
    }
}
const handleSelectResource = (resourceId: string | number) => {
    const selectedResource = resourceOptions.value.find((item) => item.id === resourceId)

    if (selectedResource) {
        stockFlowForm.value.resourceId = selectedResource.id

        //stockFlowForm.value.price = selectedResource.price;
    }
}
const handleStockFlow = () => {
    router.push({ path: '/standard/resouceStockFlow/index' })
}

// 🔥 ==================== 物资导入相关方法 ====================

/** 显示导入对话框 */
const showResourceImportDialog = () => {
    resourceImportDialog.visible = true
    resetResourceImportDialog()
}

/** 重置导入对话框 */
const resetResourceImportDialog = () => {
    resourceSelectedFile.value = null
    resourceImportPreviewData.value = []
    resourceParsing.value = false
    resourceImporting.value = false

    // 清空上传组件
    if (resourceUploadRef.value) {
        resourceUploadRef.value.clearFiles()
    }
}

/** 取消导入 */
const cancelResourceImport = () => {
    resourceImportDialog.visible = false
    resetResourceImportDialog()
}

/** 文件选择处理 */
const handleResourceFileChange = (file: any) => {
    console.log('🔥 选择文件:', file)

    if (!file.raw) {
        proxy?.$modal.msgError('文件选择失败')
        return
    }

    // 文件大小检查（10MB）
    const maxSize = 10 * 1024 * 1024
    if (file.raw.size > maxSize) {
        proxy?.$modal.msgError('文件大小不能超过10MB')
        resourceUploadRef.value?.clearFiles()
        return
    }

    // 文件类型检查
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel' // .xls
    ]

    if (!allowedTypes.includes(file.raw.type)) {
        proxy?.$modal.msgError('只支持Excel文件格式(.xlsx, .xls)')
        resourceUploadRef.value?.clearFiles()
        return
    }

    resourceSelectedFile.value = file.raw
    console.log('✅ 文件选择成功:', file.raw.name)
}

/** 文件移除处理 */
const handleResourceFileRemove = () => {
    console.log('🔥 移除文件')
    resourceSelectedFile.value = null
    resourceImportPreviewData.value = []
}

/** 下载导入模板 */
const downloadResourceTemplate = () => {
    console.log('🔥 下载物资导入模板')

    try {
        const templateData = [
            ['物资名称*', '规格型号*', '物资性质*', '物资属性', '计量单位*', '单价', '数量'],
            ['建筑材料', 'HRB400 Φ12', '主要材料', '钢筋,结构材料', '吨', '4500.00', '0'],
            ['电气材料', 'YJV 3×120+1×70', '主要材料', '电缆,低压电缆', '米', '85.50', '0'],
            ['五金材料', 'M16×80螺栓', '辅助材料', '紧固件,标准件', '个', '2.50', '0'],
            ['建筑材料', 'P.O 42.5', '主要材料', '水泥,建筑材料', '吨', '380.00', '0']
        ]

        // 创建工作簿和工作表
        const workbook = XLSX.utils.book_new()
        const worksheet = XLSX.utils.aoa_to_sheet(templateData)

        // 设置列宽
        worksheet['!cols'] = [
            { wch: 15 }, // 物资名称
            { wch: 20 }, // 规格型号
            { wch: 12 }, // 物资性质
            { wch: 25 }, // 物资属性
            { wch: 12 }, // 计量单位
            { wch: 10 }, // 单价
            { wch: 8 } // 数量
        ]

        XLSX.utils.book_append_sheet(workbook, worksheet, '物资基本信息')
        XLSX.writeFile(workbook, '物资基本信息导入模板.xlsx')

        proxy?.$modal.msgSuccess('模板下载成功')
    } catch (error: any) {
        console.error('模板下载失败:', error)
        proxy?.$modal.msgError('模板下载失败: ' + error.message)
    }
}

/** Excel数据解析 */
const parseResourceExcelData = async () => {
    if (!resourceSelectedFile.value) {
        proxy?.$modal.msgError('请先选择Excel文件')
        return
    }

    try {
        resourceParsing.value = true
        console.log('🔥 开始解析Excel文件:', resourceSelectedFile.value.name)

        const fileData = await readFileAsArrayBuffer(resourceSelectedFile.value)
        const workbook = XLSX.read(fileData, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]

        // 转换为JSON数据，跳过标题行
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, range: 1 })
        console.log('📊 Excel原始数据:', jsonData)

        if (!jsonData || jsonData.length === 0) {
            proxy?.$modal.msgError('Excel文件中没有数据')
            return
        }

        // 转换为预览格式
        const previewData: ResourceImportRow[] = []

        for (let i = 0; i < jsonData.length; i++) {
            const row = jsonData[i] as any[]
            const rowNumber = i + 2 // Excel行号（从2开始，因为第1行是标题）

            // 跳过空行
            if (!row || row.every((cell) => !cell || String(cell).trim() === '')) {
                continue
            }

            const importRow: ResourceImportRow = {
                rowNumber,
                typeName: String(row[0] || '').trim(),
                specification: String(row[1] || '').trim(),
                nature: String(row[2] || '').trim(),
                natureCode: '',
                properties: String(row[3] || '').trim(),
                propertiesArray: [],
                unit: String(row[4] || '').trim(),
                unitLabel: '',
                price: parseFloat(String(row[5] || '0')) || 0,
                balanceAmount: parseFloat(String(row[6] || '0')) || 0,
                errors: {},
                isValid: true,
                suggestions: {},
                validationDetails: {
                    basicFields: false,
                    uniqueness: false,
                    dictionaries: false
                }
            }

            previewData.push(importRow)
        }

        console.log('📋 转换后的预览数据:', previewData)

        if (previewData.length === 0) {
            proxy?.$modal.msgError('没有找到有效的数据行')
            return
        }

        // 设置预览数据并进行验证
        resourceImportPreviewData.value = previewData

        // 执行数据验证
        await validateResourceImportData()

        proxy?.$modal.msgSuccess(`成功解析 ${previewData.length} 条数据`)
    } catch (error: any) {
        console.error('Excel文件解析失败:', error)
        proxy?.$modal.msgError('Excel文件解析失败: ' + error.message)
    } finally {
        resourceParsing.value = false
    }
}

/** 读取文件为ArrayBuffer */
const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (e) => {
            if (e.target?.result) {
                resolve(e.target.result as ArrayBuffer)
            } else {
                reject(new Error('文件读取失败'))
            }
        }
        reader.onerror = () => reject(new Error('文件读取出错'))
        reader.readAsArrayBuffer(file)
    })
}

/** 数据验证 */
const validateResourceImportData = async () => {
    console.log('🔥 开始验证导入数据')

    for (const row of resourceImportPreviewData.value) {
        // 重置验证状态
        row.errors = {}
        row.isValid = true
        row.suggestions = {}

        // 1. 基础字段验证
        validateResourceBasicFields(row)

        // 2. 数据字典验证
        validateResourceDictionaries(row)

        // 3. 数据转换
        convertResourceData(row)
    }

    // 4. 唯一性验证（批量）
    await validateResourceUniqueness()

    console.log('✅ 数据验证完成')
}

/** 基础字段验证 */
const validateResourceBasicFields = (row: ResourceImportRow) => {
    // 必填字段验证
    if (!row.typeName) {
        row.errors.typeName = '物资名称不能为空'
        row.isValid = false
    }

    if (!row.specification) {
        row.errors.specification = '规格型号不能为空'
        row.isValid = false
    }

    if (!row.nature) {
        row.errors.nature = '物资性质不能为空'
        row.isValid = false
    }

    if (!row.unit) {
        row.errors.unit = '计量单位不能为空'
        row.isValid = false
    }

    // 数值字段验证
    if (row.price < 0) {
        row.errors.price = '单价不能为负数'
        row.isValid = false
    }

    if (row.balanceAmount < 0) {
        row.errors.balanceAmount = '数量不能为负数'
        row.isValid = false
    }

    row.validationDetails.basicFields = Object.keys(row.errors).length === 0
}

/** 数据字典验证 */
const validateResourceDictionaries = (row: ResourceImportRow) => {
    // 物资性质验证
    const natureDict = tnl_resource_nature.value || []
    console.log('🔍 物资性质数据字典:', natureDict)
    console.log('🔍 输入的物资性质:', `"${row.nature.trim()}"`)

    const nature = natureDict.find((item: any) => item.label === row.nature.trim())
    console.log('🔍 找到的匹配项:', nature)

    if (!nature) {
        row.errors.nature = `物资性质"${row.nature}"不存在`
        row.isValid = false
        // 提供建议
        row.suggestions.natures = natureDict.slice(0, 5).map((item: any) => item.label)
        console.log('❌ 物资性质验证失败，可用选项:', row.suggestions.natures)
    } else {
        row.natureCode = nature.value
        console.log('✅ 物资性质验证成功，代码:', nature.value)
    }

    // 计量单位验证
    const unitDict = tnl_resource_unit.value || []
    const unit = unitDict.find((item: any) => item.label === row.unit.trim())
    if (!unit) {
        row.errors.unit = `计量单位"${row.unit}"不存在`
        row.isValid = false
        // 提供建议
        row.suggestions.units = unitDict.slice(0, 5).map((item: any) => item.label)
    } else {
        row.unit = unit.value
        row.unitLabel = unit.label
    }

    // 物资属性验证（可选字段）
    if (row.properties) {
        const propertiesArray = parseResourceProperties(row.properties)
        const propertyDict = resource_property.value || []
        const validProps = propertyDict.map((item: any) => item.label)

        const invalidProps = propertiesArray.filter((prop) => !validProps.includes(prop))
        if (invalidProps.length > 0) {
            row.errors.properties = `物资属性"${invalidProps.join('、')}"不存在`
            row.isValid = false
            // 提供建议
            row.suggestions.properties = propertyDict.slice(0, 8).map((item: any) => item.label)
        } else {
            row.propertiesArray = propertiesArray
        }
    }

    row.validationDetails.dictionaries = !row.errors.nature && !row.errors.unit && !row.errors.properties
}

/** 数据转换 */
const convertResourceData = (row: ResourceImportRow) => {
    // 物资属性转换为逗号分隔的字符串
    if (row.propertiesArray.length > 0) {
        row.properties = row.propertiesArray.join(',')
    }
}

/** 物资属性解析 */
const parseResourceProperties = (propertiesStr: string): string[] => {
    if (!propertiesStr) return []

    // 支持多种分隔符：中英文逗号、斜杠、点号等
    const separators = /[,，\/、。.]/
    return propertiesStr
        .split(separators)
        .map((prop) => prop.trim())
        .filter((prop) => prop.length > 0)
}

/** 唯一性验证 */
const validateResourceUniqueness = async () => {
    console.log('🔥 开始唯一性验证')

    // 1. 检查导入数据内部重复
    const internalDuplicates = new Map<string, number[]>()

    resourceImportPreviewData.value.forEach((row, index) => {
        const key = `${row.typeName}|${row.specification}|${row.unit}`
        if (!internalDuplicates.has(key)) {
            internalDuplicates.set(key, [])
        }
        internalDuplicates.get(key)!.push(row.rowNumber)
    })

    // 标记内部重复
    internalDuplicates.forEach((rowNumbers, key) => {
        if (rowNumbers.length > 1) {
            rowNumbers.forEach((rowNum) => {
                const row = resourceImportPreviewData.value.find((r) => r.rowNumber === rowNum)
                if (row) {
                    row.errors.uniqueness = `第${rowNumbers.join('、')}行数据重复（物资名称+规格型号+计量单位相同）`
                    row.isValid = false
                }
            })
        }
    })

    // 2. 检查与数据库现有数据重复
    const uniqueKeys = Array.from(internalDuplicates.keys()).filter((key) => internalDuplicates.get(key)!.length === 1)

    if (uniqueKeys.length > 0) {
        try {
            const checkData = {
                resources: uniqueKeys.map((key) => {
                    const [typeName, specification, unit] = key.split('|')
                    return { typeName, specification, unit }
                })
            }

            console.log('🔍 检查数据库重复:', checkData)
            const response = await checkResourceExists(checkData)

            if (response.data && response.data.duplicates) {
                // 标记数据库重复
                response.data.duplicates.forEach((duplicate: any) => {
                    const key = `${duplicate.typeName}|${duplicate.specification}|${duplicate.unit}`
                    const rowNumbers = internalDuplicates.get(key) || []
                    rowNumbers.forEach((rowNum) => {
                        const row = resourceImportPreviewData.value.find((r) => r.rowNumber === rowNum)
                        if (row) {
                            row.errors.uniqueness = `数据库中已存在相同的物资信息（ID: ${duplicate.id}）`
                            row.isValid = false
                        }
                    })
                })
            }
        } catch (error) {
            console.error('检查数据库重复失败:', error)
            proxy?.$modal.msgWarning('无法检查数据库重复，请确保数据唯一性')
        }
    }

    // 更新唯一性验证状态
    resourceImportPreviewData.value.forEach((row) => {
        row.validationDetails.uniqueness = !row.errors.uniqueness
    })

    console.log('✅ 唯一性验证完成')
}

/** 确认导入 */
const confirmResourceImport = async () => {
    try {
        resourceImporting.value = true

        const validItems = resourceImportPreviewData.value.filter((item) => item.isValid)

        if (validItems.length === 0) {
            proxy?.$modal.msgError('没有有效的数据可以导入')
            return
        }

        // 转换为后端需要的格式
        const importData = {
            resources: validItems.map((item) => ({
                typeName: item.typeName,
                specification: item.specification,
                nature: item.natureCode,
                properties: item.properties,
                unit: item.unit,
                price: item.price,
                balanceAmount: item.balanceAmount
            }))
        }

        console.log('📤 提交导入数据:', importData)

        // 调用批量导入接口
        const response = await batchImportResource(importData)

        if (response.code === 200) {
            proxy?.$modal.msgSuccess(`成功导入 ${validItems.length} 条物资信息`)

            // 刷新列表
            getList()

            // 关闭对话框
            resourceImportDialog.visible = false
            resetResourceImportDialog()
        } else {
            proxy?.$modal.msgError('导入失败: ' + response.msg)
        }
    } catch (error: any) {
        console.error('导入失败:', error)
        proxy?.$modal.msgError('导入失败: ' + error.message)
    } finally {
        resourceImporting.value = false
    }
}

/** 获取行样式类名 */
const getResourceImportRowClassName = ({ row }: { row: ResourceImportRow }) => {
    return row.isValid ? 'success-row' : 'error-row'
}

/** 获取字段样式类名 */
const getResourceFieldClassName = (row: ResourceImportRow, field: string) => {
    return row.errors[field] ? 'error-field' : ''
}

/** 根据代码获取单位标签 */
const getResourceUnitLabelByCode = (code: string) => {
    const unitDict = tnl_resource_unit.value || []
    const unit = unitDict.find((item: any) => item.value === code)
    return unit ? unit.label : code
}

onMounted(() => {
    getResourceTypreList()
    getAllResouces()
    getAllProjects()
    getAllDepts()
    getList()

    // 🔍 调试：打印数据字典内容
    nextTick(() => {
        console.log('🔍 页面加载完成，数据字典内容:')
        console.log('物资性质字典:', tnl_resource_nature.value)
        console.log('计量单位字典:', tnl_resource_unit.value)
        console.log('物资属性字典:', resource_property.value)
    })
})
</script>

<style scoped>
/* 🔥 物资导入相关样式 */

/* 文件上传区域样式 */
:deep(.el-upload-dragger) {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    width: 100%;
    height: 180px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color 0.3s;

    &:hover {
        border-color: #409eff;
    }
}

:deep(.el-upload-dragger.is-dragover) {
    background-color: rgba(64, 158, 255, 0.06);
    border: 2px dashed #409eff;
}

:deep(.el-upload-dragger .el-icon--upload) {
    font-size: 67px;
    color: #c0c4cc;
    margin: 20px 0 16px;
    line-height: 50px;
}

:deep(.el-upload__text) {
    color: #606266;
    font-size: 14px;
    text-align: center;
    margin-top: -20px;

    em {
        color: #409eff;
        font-style: normal;
    }
}

/* 预览表格样式 */
.success-row {
    background-color: #f0f9ff;
}

.error-row {
    background-color: #fef0f0;
}

.error-field {
    color: #f56c6c;
    font-weight: bold;
}

.error-icon {
    font-size: 14px;
}

/* 错误详情样式 */
.error-details {
    max-height: 300px;
    overflow-y: auto;
}

.error-row-detail {
    border-left: 3px solid #f56c6c;
    padding-left: 8px;
}

/* 对话框样式优化 */
:deep(.el-dialog__body) {
    padding: 20px;
}

/* 只针对导入对话框中的卡片样式 */
:deep(.el-dialog .el-card) {
    border-radius: 8px;
}

:deep(.el-dialog .el-card__header) {
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
    padding: 12px 20px;
}

/* 按钮样式 */
.dialog-footer {
    text-align: right;
    padding-top: 20px;
}

.dialog-footer .el-button {
    margin-left: 10px;
}

/* 标签样式 */
:deep(.el-tag) {
    margin-right: 8px;
}

/* 表格样式优化 */
:deep(.el-table) {
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table th) {
    background-color: #fafafa;
    color: #606266;
    font-weight: 600;
}

:deep(.el-table td) {
    padding: 8px 0;
}

/* 上传提示样式 */
:deep(.el-upload__tip) {
    margin-top: 8px;
    line-height: 1.5;
}
</style>
