<template>
    <div class="navbar">
        <!-- 隐藏头部切换按钮 -->
        <hamburger
            v-if="false"
            id="hamburger-container"
            :is-active="appStore.sidebar.opened"
            class="hamburger-container"
            @toggle-click="toggleSideBar"
        />
        <!-- 在较小分辨率下隐藏面包屑，为right-menu腾出空间 -->
        <breadcrumb v-if="!settingsStore.topNav && !isSmallScreen" id="breadcrumb-container" class="breadcrumb-container" />
        <top-nav v-if="settingsStore.topNav && !isSmallScreen" id="topmenu-container" class="topmenu-container" />

        <div class="right-menu flex align-center">
            <!-- 项目选择下拉 - 始终显示，但在移动端简化 -->
            <div class="project-sel" v-if="appStore.device !== 'mobile'">
                <el-tooltip content="返回管理平台" effect="dark" placement="bottom">
                    <el-button type="text" @click="goToHome" style="margin-right: 10px">
                        <el-icon size="22">
                            <HomeFilled />
                        </el-icon>
                    </el-button>
                </el-tooltip>
                <el-popover placement="bottom" trigger="hover" :width="140" :show-arrow="true">
                    <template #reference>
                        <div class="app-download-btn">
                            <el-button type="text" @click="openAppDownload">
                                <el-icon size="22">
                                    <Iphone />
                                </el-icon>
                            </el-button>
                        </div>
                    </template>
                    <template #default>
                        <div class="qr-container">
                            <img src="/images/app-download-qr.png" alt="扫码下载App" class="qr-image" />
                            <p class="qr-text">扫码下载App</p>
                        </div>
                    </template>
                </el-popover>
                <el-select class="project-select" v-model="currentProject" placeholder="请选择项目" @change="handleProjectChange">
                    <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </div>

            <!-- 移动端简化的项目选择 -->
            <div class="mobile-project-sel" v-else>
                <el-select class="mobile-project-select" v-model="currentProject" placeholder="项目" @change="handleProjectChange">
                    <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </div>

            <template v-if="appStore.device !== 'mobile'">
                <!-- false-隐藏租户下拉 -->
                <el-select
                    v-if="userId === 1 && tenantEnabled && false"
                    v-model="companyName"
                    class="min-w-244px"
                    clearable
                    filterable
                    reserve-keyword
                    :placeholder="proxy.$t('navbar.selectTenant')"
                    @change="dynamicTenantEvent"
                    @clear="dynamicClearEvent"
                >
                    <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId"> </el-option>
                    <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
                </el-select>

                <search-menu ref="searchMenuRef" />
                <el-tooltip content="搜索" effect="dark" placement="bottom">
                    <div class="right-menu-item hover-effect" @click="openSearchMenu">
                        <svg-icon class-name="search-icon" icon-class="search" />
                    </div>
                </el-tooltip>
                <!-- 消息 -->
                <el-tooltip :content="proxy.$t('navbar.message')" effect="dark" placement="bottom">
                    <div>
                        <el-popover placement="bottom" trigger="click" transition="el-zoom-in-top" :width="300" :persistent="false">
                            <template #reference>
                                <el-badge :value="newNotice > 0 ? newNotice : ''" :max="99">
                                    <svg-icon icon-class="message" />
                                </el-badge>
                            </template>
                            <template #default>
                                <notice></notice>
                            </template>
                        </el-popover>
                    </div>
                </el-tooltip>
                <!-- Github -->
                <el-tooltip v-if="false" content="Github" effect="dark" placement="bottom">
                    <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
                </el-tooltip>
                <!-- 项目文档 -->
                <el-tooltip v-if="false" :content="proxy.$t('navbar.document')" effect="dark" placement="bottom">
                    <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
                </el-tooltip>

                <el-tooltip :content="proxy.$t('navbar.full')" effect="dark" placement="bottom">
                    <screenfull id="screenfull" class="right-menu-item hover-effect" />
                </el-tooltip>
                <!-- 语言切换 -->
                <el-tooltip v-if="false" :content="proxy.$t('navbar.language')" effect="dark" placement="bottom">
                    <lang-select id="lang-select" class="right-menu-item hover-effect" />
                </el-tooltip>

                <el-tooltip :content="proxy.$t('navbar.layoutSize')" effect="dark" placement="bottom">
                    <size-select id="size-select" class="right-menu-item hover-effect" />
                </el-tooltip>
            </template>
            <div class="avatar-container">
                <el-dropdown class="right-menu-item hover-effect" trigger="click" @command="handleCommand">
                    <div class="avatar-wrapper">
                        <div class="user-info-flex">
                            <img :src="userStore.avatar" class="user-avatar" />
                            <span class="user-info-text">{{ userStore.nickname }} ({{ userStore.name }})</span>
                            <el-icon class="user-drop-icon"><caret-bottom /></el-icon>
                        </div>
                    </div>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <router-link v-if="!dynamic" to="/user/profile">
                                <el-dropdown-item>{{ proxy.$t('navbar.personalCenter') }}</el-dropdown-item>
                            </router-link>
                            <!-- 布局设置 yekd 目前隐藏 -->
                            <!-- <el-dropdown-item v-if="settingsStore.showSettings" command="setLayout"> -->
                            <el-dropdown-item command="setLayout" v-if="false">
                                <span>{{ proxy.$t('navbar.layoutSetting') }}</span>
                            </el-dropdown-item>
                            <el-dropdown-item divided command="logout">
                                <span>{{ proxy.$t('navbar.logout') }}</span>
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import SearchMenu from './TopBar/search.vue'
import { useRoute } from 'vue-router'
import { RouteRecordRaw } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import { useSettingsStore } from '@/store/modules/settings'
import { usePermissionStore } from '@/store/modules/permission'
import { useNoticeStore } from '@/store/modules/notice'
import { getTenantList } from '@/api/login'
import { dynamicClear, dynamicTenant } from '@/api/system/tenant'
import { TenantVO } from '@/api/types'
import { getAllProjects } from '@/api/project/project'
import { ProjectVO } from '@/api/project/project/types'
import notice from './notice/index.vue'
import router from '@/router'
import { ElMessageBoxOptions } from 'element-plus/es/components/message-box/src/message-box.type'
import { getCurrentUserPermissionInProject } from '@/api/system/user'
import { Iphone } from '@element-plus/icons-vue'

const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()
const noticeStore = storeToRefs(useNoticeStore())
const route = useRoute()
const newNotice = ref(<number>0)

// 添加小屏幕检测
const { width } = useWindowSize()
const isSmallScreen = computed(() => width.value < 1800) // 在1800px以下隐藏面包屑

const { proxy } = getCurrentInstance() as ComponentInternalInstance

const userId = ref(userStore.userId)
const companyName = ref(undefined)
const currentProject = ref('')
const projectList = ref<ProjectVO[]>([])
// const projectList = ref([
//     { value: '1', label: '项目A' },
//     { value: '2', label: '项目B' },
//     { value: '3', label: '项目C' },
//     { value: '4', label: '项目D' }
// ]);
const tenantList = ref<TenantVO[]>([])
// 是否切换了租户
const dynamic = ref(false)
// 租户开关
const tenantEnabled = ref(false)
// 搜索菜单
const searchMenuRef = ref<InstanceType<typeof SearchMenu>>()

const openSearchMenu = () => {
    searchMenuRef.value?.openSearch()
}

// 动态切换
const dynamicTenantEvent = async (tenantId: string) => {
    if (companyName.value != null && companyName.value !== '') {
        await dynamicTenant(tenantId)
        dynamic.value = true
        await proxy?.$router.push('/')
        await proxy?.$tab.closeAllPage()
        await proxy?.$tab.refreshPage()
    }
}

const dynamicClearEvent = async () => {
    await dynamicClear()
    dynamic.value = false
    await proxy?.$router.push('/')
    await proxy?.$tab.closeAllPage()
    await proxy?.$tab.refreshPage()
}

/** 租户列表 */
const initTenantList = async () => {
    const { data } = await getTenantList(true)
    tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled
    if (tenantEnabled.value) {
        tenantList.value = data.voList
    }
}

defineExpose({
    initTenantList
})

const toggleSideBar = () => {
    appStore.toggleSideBar(false)
}

const logout = async () => {
    await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    } as ElMessageBoxOptions)
    userStore.logout().then(() => {
        router.replace({
            path: '/login',
            query: {
                redirect: encodeURIComponent(router.currentRoute.value.fullPath || '/')
            }
        })
    })
}

const emits = defineEmits(['setLayout'])
const setLayout = () => {
    emits('setLayout')
}
// 定义Command方法对象 通过key直接调用方法
const commandMap: { [key: string]: any } = {
    setLayout,
    logout
}
const handleCommand = (command: string) => {
    // 判断是否存在该方法
    if (commandMap[command]) {
        commandMap[command]()
    }
}

const handleProjectChange = async (value: string) => {
    appStore.projectContext.isPlatform = false
    appStore.projectContext.selectedProjectId = value
    const permissionCodes = (await getCurrentUserPermissionInProject(value)).data
    appStore.postPermissionCodes = permissionCodes
    console.log('权限列表')
    console.log(permissionCodes)
    //debugger;
    // 切换项目后，跳转到项目的首页
    window.location.href = '/subProject/dashboard'
}

const goToHome = () => {
    appStore.projectContext.isPlatform = true
    appStore.projectContext.selectedProjectId = ''
    // 跳转到平台首页
    window.location.href = '/index'
}

const openAppDownload = () => {
    window.open('https://zhgk.suzhousyt.com:48081/app-download.html', '_blank')
}
//用深度监听 消息
watch(
    () => noticeStore.state.value.notices,
    (newVal) => {
        newNotice.value = newVal.filter((item: any) => !item.read).length
    },
    { deep: true }
)

// 检查路径是否在指定路由的子树中
const isRouteInSubtree = (targetPath: string, parentRoute: RouteRecordRaw): boolean => {
    // 检查直接匹配
    if (parentRoute.path === targetPath) {
        return true
    }

    // 检查子路由
    if (parentRoute.children) {
        for (const child of parentRoute.children) {
            // 递归检查每个子路由
            if (isRouteInSubtree(targetPath, child)) {
                return true
            }
        }
    }

    return false
}

// 检查路由是否属于项目级菜单的辅助函数
const checkIfProjectRoute = (currentPath: string, routes: RouteRecordRaw[]): boolean => {
    // 快速判断：如果路径以 /subProject/ 开头，直接认为是项目级路由
    if (currentPath.startsWith('/subProject/')) {
        return true
    }

    // 递归检查菜单树（保留原有逻辑作为备用）
    for (const route of routes) {
        // 检查是否是"单项目系统"菜单
        if (route.meta?.title === '单项目系统') {
            // 找到了"单项目系统"菜单，检查当前路径是否在其子树中
            return isRouteInSubtree(currentPath, route)
        }
        // 递归检查其他路由的子路由
        if (route.children && checkIfProjectRoute(currentPath, route.children)) {
            return true
        }
    }
    return false
}

const initialize = async () => {
    console.log('route.path', route.path)

    const allRoutes = permissionStore.getSidebarRoutes()
    const isProjectRoute = checkIfProjectRoute(route.path, allRoutes)

    if (route.path === '/index') {
        // 明确的平台首页
        appStore.projectContext.selectedProjectId = ''
        appStore.projectContext.isPlatform = true
    } else if (isProjectRoute) {
        // 项目级路由
        appStore.projectContext.isPlatform = false
        // 注意：不要清空 selectedProjectId，保持用户的选择
    } else {
        // 其他平台级路由
        // 只有当前没有选中项目时，才设置为平台级
        if (!appStore.projectContext.selectedProjectId) {
            appStore.projectContext.isPlatform = true
        }
    }

    getAllProjects().then((res) => {
        projectList.value = res.data
        // 确保下拉框显示正确的选中项目
        currentProject.value = appStore.projectContext.selectedProjectId
    })
}

initialize()
</script>

<style lang="scss" scoped>
:deep(.el-select .el-input__wrapper) {
    height: 30px;
}

:deep(.el-badge__content.is-fixed) {
    top: 12px;
}

.flex {
    display: flex;
}

.align-center {
    align-items: center;
}

.navbar {
    height: 50px;
    overflow: hidden;
    position: relative;
    //background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .hamburger-container {
        line-height: 46px;
        height: 100%;
        float: left;
        cursor: pointer;
        transition: background 0.3s;
        -webkit-tap-highlight-color: transparent;

        &:hover {
            background: rgba(0, 0, 0, 0.025);
        }
    }

    .breadcrumb-container {
        float: left;
    }

    .topmenu-container {
        position: absolute;
        left: 50px;
    }

    .errLog-container {
        display: inline-block;
        vertical-align: top;
    }

    .right-menu {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        line-height: 50px;
        display: flex;
        align-items: center;
        flex-shrink: 0; /* 防止被压缩 */
        min-width: 0; /* 允许子元素收缩 */
        z-index: 10; /* 确保在其他元素之上 */
        background: inherit; /* 继承父元素背景 */

        &:focus {
            outline: none;
        }

        .right-menu-item {
            display: inline-block;
            padding: 0 8px;
            height: 100%;
            font-size: 18px;
            color: #5a5e66;
            vertical-align: text-bottom;

            &.hover-effect {
                cursor: pointer;
                transition: background 0.3s;

                &:hover {
                    background: rgba(0, 0, 0, 0.025);
                }
            }
        }

        .avatar-container {
            margin-right: 40px;

            .avatar-wrapper {
                margin-top: 5px;
                position: relative;

                .user-info-flex {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .user-avatar {
                    cursor: pointer;
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    margin-top: 10px;
                }

                .user-info-text {
                    margin-top: 10px;
                    font-size: 14px;
                    color: #b5b3b3;
                    margin-left: 8px;
                    transition: color 0.2s;
                    user-select: none;
                    flex-shrink: 1; /* 允许文本收缩 */
                    min-width: 0; /* 允许文本收缩 */
                }
                .user-drop-icon {
                    margin-top: 10px;
                }
                .user-account {
                    font-size: 13px;
                    color: #aaa;
                }
                .user-info-text:hover {
                    color: #fff;
                }

                i {
                    cursor: pointer;
                    position: static;
                    margin-left: 4px;
                    font-size: 12px;
                }
            }
        }
    }
}

.project-sel {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-right: 10px;

    .project-select {
        width: 200px;
        min-width: 150px;
        max-width: 250px;
    }
}

/* 响应式设计 - 针对中等分辨率优化 */
@media (max-width: 1800px) {
    .project-sel {
        gap: 6px;
        margin-right: 8px;

        .project-select {
            width: 160px;
            min-width: 140px;
        }
    }

    .right-menu {
        .right-menu-item {
            padding: 0 6px;
        }
    }

    .user-info-text {
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

@media (max-width: 1600px) {
    .project-sel {
        gap: 4px;
        margin-right: 6px;

        .project-select {
            width: 140px;
            min-width: 120px;
        }
    }

    .right-menu {
        .right-menu-item {
            padding: 0 4px;
        }
    }

    .user-info-text {
        max-width: 120px;
    }
}

@media (max-width: 1400px) {
    .project-sel {
        .project-select {
            width: 120px;
            min-width: 100px;
        }
    }

    .user-info-text {
        max-width: 100px;
    }

    /* 在小分辨率下隐藏部分次要功能，保留核心功能 */
    .app-download-btn {
        display: none;
    }
}

/* 移动端项目选择样式 */
.mobile-project-sel {
    display: flex;
    align-items: center;
    margin-right: 10px;

    .mobile-project-select {
        width: 120px;
        min-width: 100px;
    }
}

/* 小屏幕下的额外优化 */
@media (max-width: 1800px) {
    .navbar {
        /* 当面包屑隐藏时，为right-menu提供更多空间 */
        .breadcrumb-container,
        .topmenu-container {
            display: none !important;
        }

        .right-menu {
            /* 在小屏幕下可以占用更多空间 */
            max-width: calc(100% - 100px); /* 为左侧logo等预留空间 */
        }
    }
}

/* App下载按钮样式 */
.app-download-btn {
    position: relative;

    .el-button {
        position: relative;

        &::after {
            content: '下载App';
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            z-index: 1000;
        }

        &:hover::after {
            opacity: 1;
        }
    }
}

.qr-container {
    text-align: center;
    padding: 8px;

    .qr-image {
        width: 120px;
        height: 120px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: block;
        margin: 0 auto;
    }

    .qr-text {
        margin: 8px 0 0 0;
        font-size: 12px;
        color: #ccc;
        line-height: 1.2;
    }
}
</style>
