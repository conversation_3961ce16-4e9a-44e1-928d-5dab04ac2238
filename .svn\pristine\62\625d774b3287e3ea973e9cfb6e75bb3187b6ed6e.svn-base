<!-- 任务（维养、巡检、封道）列表-选择、批量确认组件 -->
<template>
    <div>
        <!-- 任务类型Tab -->
        <div class="task-type-tabs">
            <el-tabs v-model="activeTaskType" type="card" @tab-change="handleTaskTypeChange">
                <el-tab-pane label="维养" name="curing"></el-tab-pane>
                <el-tab-pane label="巡检" name="inspect"></el-tab-pane>
                <el-tab-pane label="封道" name="sealing"></el-tab-pane>
            </el-tabs>
        </div>

        <el-table ref="taskTableRef" v-loading="loading" :data="filteredTaskList" @selection-change="handleSelectionChange" row-key="id">
            <el-table-column type="selection" align="center" />
            <el-table-column label="序号" align="center" width="50">
                <template #default="scope">
                    {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
                </template>
            </el-table-column>
            <el-table-column label="计划作业时间" align="center">
                <template #default="scope">
                    <span>{{ formatDate(scope.row.taskDate) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="作业单名称" align="center" prop="name" />
            <el-table-column label="专业类型" align="center">
                <template #default="scope">
                    <dict-tag :options="tnl_specialty" :value="scope.row.speciality" />
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center">
                <template #default="scope">
                    <dict-tag :options="task_status" :value="scope.row.currentStatus" />
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 按钮区域 -->
        <div class="dialog-footer">
            <el-button :loading="buttonLoading" type="primary" @click="handleConfirm">确 定</el-button>
            <el-button @click="handleCancel">取 消</el-button>
        </div>
    </div>

    <!-- 图片上传对话框 -->
    <el-dialog v-model="imageUploadDialog.visible" :title="`${props.operationType} - 上传安全图片`" width="600px" :close-on-click-modal="false">
        <div class="upload-section">
            <el-form :model="uploadForm" label-width="120px">
                <el-form-item :label="getImageUploadLabel()">
                    <imageUpload v-model="uploadForm.safeImages" @update:modelValue="handleImageUploadChange" />
                </el-form-item>
            </el-form>

            <div class="upload-info">
                <p class="info-text">已选择 {{ selectedTasksForUpload.length }} 个任务进行批量处理</p>
                <p class="tip-text">上传的图片将应用到所有选中的任务中</p>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancelImageUpload">取消</el-button>
                <el-button type="primary" @click="confirmImageUpload" :loading="uploadLoading" :disabled="!uploadForm.safeImages">
                    确定并提交
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, nextTick, computed, onMounted, watch } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { listPageAllTaskView } from '@/api/plan/taskView'
import { TaskViewVO, TaskViewQuery } from '@/api/plan/taskView/types'
import { getCurrentInstance, type ComponentInternalInstance } from 'vue'
import { maintainAssign, inspectionAssign, sealingAssign } from '@/api/plan/task'
import { getTaskAssignmentByTaskId } from '@/api/plan/assignMaintain'
import { getTaskAssignmentByTaskId as getInspectionTaskAssignmentByTaskId } from '@/api/plan/assignInspection'
import { getTaskAssignmentByTaskId as getSealingTaskAssignmentByTaskId } from '@/api/plan/assignSealing'
import type { AssignMaintainFlowForm } from '@/api/plan/assignMaintain/types'
import type { AssignInspectionFlowForm } from '@/api/plan/assignInspection/types'
import type { AssignSealingFlowForm } from '@/api/plan/assignSealing/types'

const userStore = useUserStore()

const props = defineProps({
    // 状态配置
    statusConfig: {
        type: Object,
        required: false,
        default: () => ({
            filterStatus: '', // 用于筛选的状态
            updateStatus: '' // 确认后要更新的状态
        })
    },
    // 其他筛选条件，外部未使用
    filterConfig: {
        type: Object,
        required: false,
        default: () => ({})
    },
    // 项目ID
    projectId: {
        type: String,
        required: false
    },
    // 操作类型
    operationType: {
        type: String,
        required: false,
        default: ''
    },
    // 任务类型（用于初始化Tab状态）
    taskType: {
        type: String,
        required: false,
        default: 'all' // 默认为全部
    },
    // 日期过滤配置
    dateFilter: {
        type: Object,
        required: false,
        default: () => ({
            startTaskDate: '',
            endTaskDate: ''
        })
    }
})

const emit = defineEmits(['selectionChange', 'confirm', 'cancel'])

const { proxy } = getCurrentInstance() as ComponentInternalInstance

// 日期格式化函数
const formatDate = (date: string) => {
    if (!date) return ''
    const d = new Date(date)
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
}

const { task_status, tnl_specialty } = toRefs<any>(proxy?.useDict('task_status', 'tnl_specialty'))

const taskList = ref<TaskViewVO[]>([])
const loading = ref(true)
const total = ref(0)
const buttonLoading = ref(false)
const selectedIds = ref<(string | number)[]>([])

// 当前激活的任务类型Tab
const activeTaskType = ref('curing')

const queryParams = reactive<
    TaskViewQuery & {
        pageNum: number
        pageSize: number
    }
>({
    pageNum: 1,
    pageSize: 50,
    projectId: props.projectId,
    currentStatus: props.statusConfig.filterStatus,
    ...props.filterConfig
})

// 根据Tab筛选后的任务列表（现在直接使用taskList，因为后端查询已经按类型筛选）
const filteredTaskList = computed(() => {
    return taskList.value
})

// 添加表格ref
const taskTableRef = ref()

// 图片上传相关数据
const imageUploadDialog = ref({
    visible: false
})

const uploadForm = reactive({
    safeImages: ''
})

const selectedTasksForUpload = ref([])
const uploadLoading = ref(false)

/** 检查是否支持的任务类型 */
const isSupportedTaskType = (taskType: string) => {
    const supportedTypes = ['curing', 'inspect', 'sealing']
    return supportedTypes.includes(taskType)
}

/** 获取图片上传标签 */
const getImageUploadLabel = () => {
    switch (props.taskType) {
        case 'curing':
            return '安全交底图片'
        case 'inspect':
            return '安全交底图片'
        case 'sealing':
            return '安全交底图片'
        default:
            return '安全图片'
    }
}

/** Tab切换处理 */
const handleTaskTypeChange = async (tabName: string) => {
    activeTaskType.value = tabName
    // Tab切换时清除选中状态
    selectedIds.value = []
    nextTick(() => {
        taskTableRef.value?.clearSelection()
    })
    // 🎯 Tab切换时重新从后端查询数据
    await getList()
}

/** 显示图片上传对话框 */
const showImageUploadDialog = (tasks: TaskViewVO[]) => {
    selectedTasksForUpload.value = tasks
    uploadForm.safeImages = ''
    imageUploadDialog.value.visible = true
}

/** 处理图片上传变化 */
const handleImageUploadChange = (ossIds: string) => {
    uploadForm.safeImages = ossIds
    console.log('图片上传变化:', ossIds)
}

/** 取消上传 */
const cancelImageUpload = () => {
    imageUploadDialog.value.visible = false
    uploadForm.safeImages = ''
    selectedTasksForUpload.value = []
}

/** 确认上传并提交 */
const confirmImageUpload = async () => {
    if (!uploadForm.safeImages) {
        proxy?.$modal.msgError('请先上传图片')
        return
    }

    uploadLoading.value = true
    try {
        await processBatchAssign(selectedTasksForUpload.value, uploadForm.safeImages)

        imageUploadDialog.value.visible = false
        emit('confirm', {
            ids: selectedTasksForUpload.value.map((task) => task.id),
            tasks: selectedTasksForUpload.value,
            operationType: props.operationType,
            success: true,
            ossIds: uploadForm.safeImages
        })
    } catch (error) {
        console.error('批量操作失败:', error)
        proxy?.$modal.msgError('操作失败')

        emit('confirm', {
            ids: selectedTasksForUpload.value.map((task) => task.id),
            tasks: [],
            operationType: props.operationType,
            success: false,
            error: error
        })
    } finally {
        uploadLoading.value = false
    }
}

/** 批量assign处理方法 */
const processBatchAssign = async (selectedTasks: TaskViewVO[], ossIdString?: string) => {
    const results = []
    const errors = []

    // 使用 for...of 循环以便更好地处理单个任务的错误
    for (const task of selectedTasks) {
        try {
            switch (task.taskType) {
                case 'curing': // 维养任务
                    await processMaintainAssign(task, ossIdString)
                    results.push(task.id)
                    break
                case 'inspect': // 巡检任务
                    await processInspectionAssign(task, ossIdString)
                    results.push(task.id)
                    break
                case 'sealing': // 封道任务
                    await processSealingAssign(task, ossIdString)
                    results.push(task.id)
                    break
                default:
                    throw new Error(`不支持的任务类型: ${task.taskType}`)
            }
        } catch (error) {
            console.error(`任务${task.id}处理失败:`, error)
            errors.push({ taskId: task.id, taskName: task.name, error })
        }
    }

    // 根据处理结果显示相应的消息
    if (errors.length === 0) {
        proxy?.$modal.msgSuccess(`${props.operationType}操作成功，共处理 ${results.length} 条记录`)
    } else if (results.length === 0) {
        proxy?.$modal.msgError(`${props.operationType}操作失败，所有任务都处理失败`)
        throw new Error(`批量${props.operationType}操作完全失败`)
    } else {
        proxy?.$modal.msgWarning(`${props.operationType}操作部分成功：成功 ${results.length} 条，失败 ${errors.length} 条`)
        console.warn('失败的任务详情:', errors)
    }
}

/** 维养任务的assign参数构建 */
const processMaintainAssign = async (task: TaskViewVO, ossIdString?: string) => {
    try {
        // 1. 获取现有的完整指派数据
        console.log(`正在获取任务${task.id}的现有指派数据...`)
        const response = await getTaskAssignmentByTaskId(task.id.toString())
        const existingData = response.data

        console.log(`任务${task.id}的现有数据:`, existingData)

        // 2. 验证数据完整性 - 批量交底的任务应该已有完整的指派数据
        if (!existingData.assignMaintain) {
            throw new Error(`任务${task.id}缺少指派数据，无法执行批量${props.operationType}操作`)
        }

        // 3. 在现有数据基础上构建表单，保留现有指派信息，只更新下一环节处理人信息
        const assignForm: AssignMaintainFlowForm = {
            task: existingData.task,
            //  || {
            //     // 使用TaskViewVO的数据作为后备
            //     id: task.id,
            //     projectId: props.projectId,
            //     name: task.name,
            //     bgnDate: task.bgnDate,
            //     endDate: task.endDate,
            //     taskStartDate: task.taskStartDate || '',
            //     taskFinishDate: task.taskFinishDate || '',
            //     currentStatus: task.currentStatus,
            //     tempTask: 'no',
            //     tempResourceId: ''
            // }
            assignMaintain: {
                ...existingData.assignMaintain,
                // 如果有图片参数，则设置安全交底图片
                ...(ossIdString && { safeImages: ossIdString })
            },
            nextAssignee: {
                // 维养任务特定的下一环节配置
                nextAssignees: [] as number[],
                wfOperation: 'APPROVE',
                opinion: `批量${props.operationType}`
            }
        }

        console.log(`任务${task.id}的批量${props.operationType}表单数据:`, assignForm)
        return maintainAssign(assignForm)
    } catch (error) {
        // 如果获取数据失败或数据不完整，记录错误并重新抛出
        console.error(`任务${task.id}批量${props.operationType}失败:`, error)
        throw error
    }
}

/** 巡检任务的assign参数构建 */
const processInspectionAssign = async (task: TaskViewVO, ossIdString?: string) => {
    try {
        // 1. 获取现有的完整指派数据
        console.log(`正在获取巡检任务${task.id}的现有指派数据...`)
        const response = await getInspectionTaskAssignmentByTaskId(task.id.toString())
        const existingData = response.data

        console.log(`巡检任务${task.id}的现有数据:`, existingData)

        // 2. 验证数据完整性 - 批量操作的任务应该已有完整的指派数据
        if (!existingData.assignInspection) {
            throw new Error(`巡检任务${task.id}缺少指派数据，无法执行批量${props.operationType}操作`)
        }

        // 3. 在现有数据基础上构建表单，保留现有指派信息，只更新下一环节处理人信息
        const assignForm: AssignInspectionFlowForm = {
            task: existingData.task,
            //  || {
            //     // 使用TaskViewVO的数据作为后备
            //     id: task.id,
            //     projectId: props.projectId,
            //     name: task.name,
            //     bgnDate: task.bgnDate,
            //     endDate: task.endDate,
            //     taskStartDate: task.taskStartDate || '',
            //     taskFinishDate: task.taskFinishDate || '',
            //     currentStatus: task.currentStatus,
            //     tempTask: 'no',
            //     tempResourceId: ''
            // },
            assignInspection: {
                ...existingData.assignInspection,
                // 如果有图片参数，则设置安全图片
                ...(ossIdString && { safeImages: ossIdString })
            },
            nextAssignee: {
                // 巡检任务特定的下一环节配置
                nextAssignees: [] as number[],
                wfOperation: 'APPROVE',
                opinion: `批量${props.operationType}`
            }
        }

        console.log(`巡检任务${task.id}的批量${props.operationType}表单数据:`, assignForm)
        return inspectionAssign(assignForm)
    } catch (error) {
        // 如果获取数据失败或数据不完整，记录错误并重新抛出
        console.error(`巡检任务${task.id}批量${props.operationType}失败:`, error)
        throw error
    }
}

/** 封道任务的assign参数构建 */
const processSealingAssign = async (task: TaskViewVO, ossIdString?: string) => {
    try {
        // 1. 获取现有的完整指派数据
        console.log(`正在获取封道任务${task.id}的现有指派数据...`)
        const response = await getSealingTaskAssignmentByTaskId(task.id.toString())
        const existingData = response.data

        console.log(`封道任务${task.id}的现有数据:`, existingData)

        // 2. 验证数据完整性 - 批量操作的任务应该已有完整的指派数据
        if (!existingData.assignSealing) {
            throw new Error(`封道任务${task.id}缺少指派数据，无法执行批量${props.operationType}操作`)
        }

        // 3. 在现有数据基础上构建表单，保留现有指派信息，只更新下一环节处理人信息
        const assignForm: AssignSealingFlowForm = {
            task: existingData.task,
            //  || {
            //     // 使用TaskViewVO的数据作为后备
            //     id: task.id,
            //     projectId: props.projectId,
            //     name: task.name,
            //     bgnDate: task.bgnDate,
            //     endDate: task.endDate,
            //     taskStartDate: task.taskStartDate || '',
            //     taskFinishDate: task.taskFinishDate || '',
            //     currentStatus: task.currentStatus,
            //     tempTask: 'no',
            //     tempResourceId: ''
            // },
            assignSealing: {
                ...existingData.assignSealing,
                // 如果有图片参数，则设置安全交底图片
                ...(ossIdString && { safeBriefImages: ossIdString })
            },
            nextAssignee: {
                // 封道任务特定的下一环节配置
                nextAssignees: [] as number[],
                wfOperation: 'APPROVE',
                opinion: `批量${props.operationType}`
            }
        }

        console.log(`封道任务${task.id}的批量${props.operationType}表单数据:`, assignForm)
        return sealingAssign(assignForm)
    } catch (error) {
        // 如果获取数据失败或数据不完整，记录错误并重新抛出
        console.error(`封道任务${task.id}批量${props.operationType}失败:`, error)
        throw error
    }
}

/** 查询任务列表 */
const getList = async () => {
    loading.value = true
    try {
        // 更新查询参数
        queryParams.projectId = props.projectId

        // 🎯 根据任务类型和状态配置动态设置查询条件
        if (props.statusConfig.filterStatus === 'Safety_Briefing') {
            // 批量交底场景：根据当前Tab确定要查询的状态
            if (activeTaskType.value === 'inspect') {
                queryParams.currentStatus = 'START' // 巡检任务的安全交底环节状态
            } else if (activeTaskType.value === 'curing' || activeTaskType.value === 'sealing') {
                queryParams.currentStatus = 'Safety_Briefing' // 维养和封道任务的安全交底环节状态
            }
            // 限制任务类型为当前Tab对应的类型
            queryParams.taskType = activeTaskType.value
        } else {
            // 其他场景：使用原有逻辑
            queryParams.currentStatus = props.statusConfig.filterStatus
        }

        queryParams.taskStep = 'task'
        queryParams.todoStatus = 'ACTIVE'
        //必须设置当前操作人为代办人
        queryParams.assignee = userStore.userId.toString()
        Object.assign(queryParams, props.filterConfig)

        // 直接在queryParams上添加日期区间查询字段
        // 使用与TaskWeekListSel一致的参数名：startTaskDate和endTaskDate
        if (props.dateFilter.startTaskDate && props.dateFilter.endTaskDate) {
            // 使用any类型来绕过TypeScript类型检查，因为这些字段会在后端处理
            ;(queryParams as any).startTaskDate = props.dateFilter.startTaskDate
            ;(queryParams as any).endTaskDate = props.dateFilter.endTaskDate
        }

        console.log(`TaskListSel - 任务查询参数:`, queryParams)
        console.log(`TaskListSel - 日期过滤参数:`, props.dateFilter)
        const res = await listPageAllTaskView(queryParams)
        console.log(`TaskListSel - 任务查询结果:`, res)

        // 对获取的TaskViewVo数据进行按taskDate的倒序排列
        const sortedRows = res.rows.sort((a, b) => {
            // 将taskDate字符串转换为Date对象进行比较
            const dateA = new Date(a.taskDate)
            const dateB = new Date(b.taskDate)
            // 倒序排列：较新的日期排在前面
            return dateB.getTime() - dateA.getTime()
        })

        taskList.value = sortedRows
        total.value = res.total
    } catch (error) {
        console.error(`获取任务列表失败:`, error)
    } finally {
        loading.value = false
    }
}

/** 重置并查询任务列表（用于初始加载和筛选条件改变） */
const resetAndGetList = async () => {
    // 重置分页
    queryParams.pageNum = 1

    // 清除选中状态
    selectedIds.value = []
    // 清除表格选中状态
    nextTick(() => {
        taskTableRef.value?.clearSelection()
    })

    // 查询数据
    await getList()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: TaskViewVO[]) => {
    selectedIds.value = selection.map((item) => item.id)
    emit('selectionChange', selectedIds.value)
}

/** 确定按钮处理 */
const handleConfirm = async () => {
    if (selectedIds.value.length === 0) {
        proxy?.$modal.msgError('请至少选择一条记录')
        return
    }

    const selectedTasks = filteredTaskList.value.filter((item) => selectedIds.value.includes(item.id))

    // 检查选中的任务是否都是支持的类型
    const hasUnsupportedTasks = selectedTasks.some((task) => !isSupportedTaskType(task.taskType))
    if (hasUnsupportedTasks) {
        proxy?.$modal.msgError('选中的任务中包含不支持的任务类型')
        return
    }

    // 根据操作类型调用对应的处理方法
    if (props.operationType) {
        // 显示图片上传对话框
        showImageUploadDialog(selectedTasks)
        return // 等待用户上传图片后再处理
    } else {
        // 直接处理（不需要图片上传的情况）
        buttonLoading.value = true
        try {
            await processBatchAssign(selectedTasks)
            emit('confirm', {
                ids: selectedIds.value,
                tasks: selectedTasks,
                operationType: props.operationType,
                success: true
            })
        } catch (error) {
            console.error('批量操作失败:', error)
            proxy?.$modal.msgError('操作失败')
            emit('confirm', {
                ids: selectedIds.value,
                tasks: [],
                operationType: props.operationType,
                success: false,
                error: error
            })
        } finally {
            buttonLoading.value = false
        }
    }
}

/** 取消按钮处理 */
const handleCancel = () => {
    emit('cancel')
}

// 监听日期过滤参数变化
watch(
    () => props.dateFilter,
    (newDateFilter) => {
        console.log('TaskListSeV2 - dateFilter变化:', newDateFilter)
        // 当日期过滤参数变化时，不需要自动重新查询
        // 因为父组件会调用getList方法
    },
    { deep: true }
)

// 初始化Tab状态
onMounted(() => {
    if (props.taskType && ['curing', 'inspect', 'sealing'].includes(props.taskType)) {
        activeTaskType.value = props.taskType
    } else {
        activeTaskType.value = 'curing' // 默认选择维养
    }
})

// 暴露方法给父组件
defineExpose({
    getList: resetAndGetList, // 外部调用时重置分页并查询
    clearSelection: () => {
        selectedIds.value = []
        nextTick(() => {
            taskTableRef.value?.clearSelection()
        })
    }
})
</script>

<style lang="scss" scoped>
.task-type-tabs {
    margin-bottom: 16px;

    :deep(.el-tabs__header) {
        margin-bottom: 0;
    }
}

.dialog-footer {
    margin-top: 20px;
    text-align: right;
}

.upload-section {
    .upload-info {
        margin-top: 16px;
        padding: 12px;
        //background-color: #f5f7fa;
        border-radius: 4px;

        .info-text {
            margin: 0 0 8px 0;
            color: #409eff;
            font-weight: 500;
        }

        .tip-text {
            margin: 0;
            color: #909399;
            font-size: 12px;
        }
    }
}
</style>
