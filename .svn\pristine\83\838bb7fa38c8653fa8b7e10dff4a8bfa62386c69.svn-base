<template>
    <div class="life-cycle-container p-2">
        <!-- 工具栏 -->
        <div class="toolbar">
            <!-- 日期选择器 - 改为年月范围选择 -->
            <div class="date-range-picker">
                <el-date-picker
                    v-model="dateRange"
                    type="monthrange"
                    range-separator="至"
                    start-placeholder="开始年月"
                    end-placeholder="结束年月"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                    @change="handleDateChange"
                ></el-date-picker>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons" v-show="selectedCategory !== 'all'">
                <el-button type="primary" @click="handleAdd"> <i class="fa fa-plus"></i> 新增 </el-button>
            </div>
        </div>

        <!-- 单选按钮组 -->
        <div class="category-selector">
            <el-radio-group v-model="selectedCategory" @change="handleCategoryChange">
                <el-radio label="all">全部</el-radio>
                <el-radio v-for="item in filteredSpecialty" :key="item.value" :label="item.value">
                    {{ item.label }}
                </el-radio>
            </el-radio-group>
        </div>

        <!-- 上方图表区域 -->
        <div class="charts-container">
            <!-- 左侧柱状图 -->
            <div class="chart-item">
                <div class="score-chart-title">分值</div>
                <div ref="scoreChartRef" class="score-chart"></div>
            </div>

            <!-- 右侧雷达图 -->
            <div class="chart-item">
                <div class="radar-chart-title">数据项评分</div>
                <div ref="radarChartRef" class="radar-chart"></div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container" v-show="selectedCategory !== 'all'">
            <el-table :data="lifeCycleList" style="width: 100%" @selection-change="handleSelectionChange" v-loading="loading">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="专业类别" align="center" prop="specialtyCode">
                    <template #default="scope">
                        {{ getSpecialtyLabel(scope.row.specialtyCode) }}
                    </template>
                </el-table-column>
                <el-table-column label="分值" align="center" prop="score" />
                <el-table-column label="年份" align="center" prop="year" />
                <el-table-column label="月份" align="center" prop="month" />
                <!-- <el-table-column label="创建时间" align="center" prop="createTime" /> -->
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button type="primary" size="small" @click="handleUpdate(scope.row)"> 修改 </el-button>
                        <el-button type="danger" size="small" @click="handleDelete(scope.row)"> 删除 </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <pagination v-show="total > 0" :total="total" :page="queryParams.pageNum" :limit="queryParams.pageSize" @pagination="getList" />
        </div>

        <!-- 下方流量图 -->
        <div class="chart-container" v-if="false">
            <div class="flow-chart-title">
                <span>车流量/辆</span>
                <span class="maintenance-label">维修量/条</span>
            </div>
            <div ref="flowChartRef" class="flow-chart"></div>
        </div>

        <!-- 添加/修改对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
            <el-form ref="lifeCycleFormRef" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="专业类别" prop="specialtyCode">
                    <el-radio-group v-model="form.specialtyCode" @change="handleSpecialtyChange" :disabled="!!form.id">
                        <el-radio v-for="item in filteredSpecialty" :key="item.value" :label="item.value" :disabled="isSpecialtyDisabled(item.value)">
                            {{ item.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="数据项评分" prop="detailScores" v-if="dataItems.length > 0">
                    <div v-for="item in dataItems" :key="item.id" class="score-item">
                        <span class="item-name">{{ item.name }}：</span>
                        <el-input-number v-model="detailScoresObj[item.id]" :min="0" :max="100" :precision="2" placeholder="请输入分值" />
                    </div>
                </el-form-item>
                <el-form-item label="平均分值" prop="score">
                    <el-input-number v-model="form.score" :min="0" :max="100" :precision="2" :disabled="true" placeholder="自动计算" />
                </el-form-item>
                <el-form-item label="日期" prop="selectedDate">
                    <el-date-picker
                        v-model="form.selectedDate"
                        type="month"
                        placeholder="请选择年月"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        :picker-options="pickerOptions"
                        :disabled="!!form.id"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancel">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, reactive, toRefs, getCurrentInstance, computed, nextTick } from 'vue'
import { ComponentInternalInstance } from 'vue'
import * as echarts from 'echarts'
import { ElForm, ElMessage, ElMessageBox } from 'element-plus'
import {
    listLifeCycle,
    getLifeCycle,
    addLifeCycle,
    updateLifeCycle,
    delLifeCycle,
    getLifeCycleChartData,
    getDataItemsBySpecialty,
    getDetailScores,
    getAverageAllData,
    getLatestLifeCycleScoreInRange,
    getRadarAllRangeData
} from '@/api/subProject/lifeCycle'
import { LifeCycleVO, LifeCycleForm, LifeCycleQuery } from '@/api/subProject/lifeCycle/types'
import { useAppStore } from '@/store/modules/app'

// 获取组件实例
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { tnl_specialty } = toRefs<any>(proxy?.useDict('tnl_specialty'))
const appStore = useAppStore()

// 过滤专业类型选项
const filteredSpecialty = computed(() => {
    if (!tnl_specialty.value) return []

    // 排除桥梁(bridge)、路面(road)
    const filtered = tnl_specialty.value.filter((item: any) => !['bridge', 'road'].includes(item.value))

    // 添加运营服务选项
    filtered.push({
        value: 'operation',
        label: '运营服务'
    })

    return filtered
})

// 获取专业类别显示名称
const getSpecialtyLabel = (specialtyCode: string) => {
    if (!specialtyCode) return ''

    // 先从filteredSpecialty中查找
    const specialty = filteredSpecialty.value.find((item: any) => item.value === specialtyCode)
    if (specialty) {
        return specialty.label
    }

    // 如果没找到，返回原始代码
    return specialtyCode
}

// 判断专业类别是否应该禁用
const isSpecialtyDisabled = (itemValue: string) => {
    // 修改模式下，所有选项都禁用（由整个radio-group的disabled控制）
    if (form.value.id) {
        return false // 这里返回false，因为整个radio-group已经disabled了
    }

    // 新增模式下，如果当前选中的不是"全部"，则只允许选择当前选中的专业类别
    if (selectedCategory.value !== 'all') {
        return itemValue !== selectedCategory.value
    }

    // 如果当前选中的是"全部"，则所有选项都可选
    return false
}

// 计算最近1年的年月范围
const getDefaultDateRange = () => {
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentMonth = now.getMonth() + 1 // getMonth()返回0-11

    // 结束年月：当前年月
    const endYearMonth = `${currentYear}-${String(currentMonth).padStart(2, '0')}`

    // 开始年月：12个月前
    let startYear = currentYear
    let startMonth = currentMonth - 11

    if (startMonth <= 0) {
        startYear = currentYear - 1
        startMonth = startMonth + 12
    }

    const startYearMonth = `${startYear}-${String(startMonth).padStart(2, '0')}`

    return [startYearMonth, endYearMonth]
}

// 日期范围 - 默认最近1年
const dateRange = ref(getDefaultDateRange())

// 选中的类别 - 默认选中全部
const selectedCategory = ref('all')

// 数据项列表
const dataItems = ref<any[]>([])

// 详细评分数据对象（用于表单编辑）
const detailScoresObj = ref<Record<string, number>>({})

// 图表DOM引用
const scoreChartRef = ref(null)
const flowChartRef = ref(null)
const radarChartRef = ref(null)

// 表单引用
const lifeCycleFormRef = ref<InstanceType<typeof ElForm>>()

// 图表实例
let scoreChart: any = null
let flowChart: any = null
let radarChart: any = null

// 数据列表
const lifeCycleList = ref<LifeCycleVO[]>([])
const loading = ref(false)
const total = ref(0)

// 选中的行
const ids = ref<Array<string | number>>([])

// 对话框
const dialog = reactive({
    visible: false,
    title: ''
})

// 表单数据
const initFormData: LifeCycleForm = {
    id: undefined,
    projectId: 'project_001', // 默认项目ID
    category: 'default',
    specialtyCode: undefined,
    score: 80,
    year: undefined,
    month: undefined,
    detailScores: undefined,
    selectedDate: undefined
}

const form = ref<LifeCycleForm>({ ...initFormData })

// 查询参数
const queryParams = ref<LifeCycleQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    specialtyCode: undefined,
    year: undefined,
    month: undefined,
    // 年月范围参数
    startYearMonth: undefined,
    endYearMonth: undefined
})

// 表单验证规则
const rules = {
    specialtyCode: [{ required: true, message: '专业类别不能为空', trigger: 'change' }],
    score: [{ required: true, message: '分值不能为空', trigger: 'blur' }],
    selectedDate: [{ required: true, message: '日期不能为空', trigger: 'change' }]
}

// 日期选择器配置
const pickerOptions = {
    disabledDate(time: Date) {
        const currentDate = new Date()
        const minDate = new Date(2020, 0, 1) // 2020年1月1日
        return time < minDate || time > currentDate
    }
}

// 模拟数据 - 流量和维修数据（调整到7月）
const flowData = {
    dates: [
        '2024-05',
        '2024-06',
        '2024-07',
        '2024-08',
        '2024-09',
        '2024-10',
        '2024-11',
        '2024-12',
        '2025-01',
        '2025-02',
        '2025-03',
        '2025-04',
        '2025-05',
        '2025-06',
        '2025-07'
    ],
    flowValues: [
        8500000, 8300000, 8600000, 8400000, 8200000, 8300000, 7500000, 7800000, 7200000, 7600000, 8700000, 8900000, 4500000, 8800000, 9100000
    ],
    maintenanceValues: [0.85, 0.83, 0.86, 0.84, 0.82, 0.83, 0.75, 0.78, 0.72, 0.76, 0.87, 0.89, 0.45, 0.88, 0.91]
}

// 查询列表数据
const getList = async () => {
    loading.value = true
    try {
        // 设置项目ID
        queryParams.value.projectId = appStore.projectContext.selectedProjectId?.toString()
        // 设置当前选中的专业类别
        queryParams.value.specialtyCode = selectedCategory.value
        // 设置年月范围
        if (dateRange.value && dateRange.value.length === 2) {
            queryParams.value.startYearMonth = dateRange.value[0]
            queryParams.value.endYearMonth = dateRange.value[1]
        }

        const res = await listLifeCycle(queryParams.value)
        lifeCycleList.value = res.rows
        total.value = res.total
    } catch (error) {
        console.error('获取数据失败:', error)
    } finally {
        loading.value = false
    }
}

// 初始化分值图表
const initScoreChart = async () => {
    if (!scoreChartRef.value) return

    // 销毁旧实例
    if (scoreChart) {
        scoreChart.dispose()
        scoreChart = null
    }

    // 等待一小段时间确保DOM稳定
    await new Promise((resolve) => setTimeout(resolve, 50))

    // 再次检查DOM元素是否存在
    if (!scoreChartRef.value) {
        console.warn('图表DOM元素不存在，跳过初始化')
        return
    }

    scoreChart = echarts.init(scoreChartRef.value)

    try {
        // 检查必要参数
        const projectId = queryParams.value.projectId || appStore.projectContext.selectedProjectId?.toString()

        if (!projectId) {
            console.warn('项目ID为空，使用默认图表')
            throw new Error('参数不完整')
        }

        let chartData
        if (selectedCategory.value === 'all') {
            // 获取全部类别的平均值数据（年月范围筛选）
            const res = await getAverageAllData(projectId, dateRange.value[0], dateRange.value[1])
            chartData = res.data
            console.log('柱状图数据:', chartData) // 添加调试日志
        } else {
            // 获取单个专业的数据（年月范围筛选）
            const res = await getLifeCycleChartData(projectId, selectedCategory.value, dateRange.value[0], dateRange.value[1])
            chartData = res.data
            console.log('柱状图数据:', chartData) // 添加调试日志
        }

        // 确保数据格式正确，防止空数据导致错误
        if (!chartData || !Array.isArray(chartData.dates) || !Array.isArray(chartData.scores)) {
            console.warn('图表数据格式不正确，使用空数据:', chartData)
            chartData = { dates: [], scores: [] }
        }

        const option = {
            grid: {
                top: 30,
                bottom: 30,
                left: 60,
                right: 30
            },
            xAxis: {
                type: 'category',
                data: chartData.dates,
                axisLine: {
                    lineStyle: {
                        color: '#E0E6F1'
                    }
                },
                axisLabel: {
                    color: '#fff'
                }
            },
            yAxis: {
                type: 'value',
                min: 0,
                max: 100,
                interval: 20,
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#E0E6F1'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: '#E0E6F1',
                        type: 'dashed'
                    }
                },
                axisLabel: {
                    color: '#fff'
                }
            },
            series: [
                {
                    data: chartData.scores,
                    type: 'bar',
                    barWidth: 40,
                    itemStyle: {
                        color: '#0D5CDB'
                    },
                    label: {
                        show: true,
                        position: 'top',
                        color: '#fff'
                    }
                }
            ]
        }

        scoreChart.setOption(option)

        // 添加点击事件
        scoreChart.on('click', async (params: any) => {
            try {
                const dateStr = params.name // 如 "2024-12"
                const [year, month] = dateStr.split('-')
                if (selectedCategory.value === 'all') {
                    // 全部模式下，雷达图显示时间区间内的平均分，不需要特定时间点
                    await loadRadarAllData()
                } else {
                    await loadRadarChartDataByDate(parseInt(year), parseInt(month))
                }
            } catch (error) {
                console.error('处理图表点击事件时出错:', error)
            }
        })
    } catch (error) {
        console.error('获取图表数据失败:', error)
        // 使用默认数据
        const option = {
            grid: { top: 30, bottom: 30, left: 60, right: 30 },
            xAxis: {
                type: 'category',
                data: [],
                axisLine: { lineStyle: { color: '#E0E6F1' } },
                axisLabel: { color: '#fff' }
            },
            yAxis: {
                type: 'value',
                min: 0,
                max: 100,
                interval: 20,
                axisLine: { show: true, lineStyle: { color: '#E0E6F1' } },
                splitLine: { lineStyle: { color: '#E0E6F1', type: 'dashed' } },
                axisLabel: { color: '#fff' }
            },
            series: [
                {
                    data: [],
                    type: 'bar',
                    barWidth: 40,
                    itemStyle: { color: '#0D5CDB' }
                }
            ]
        }
        scoreChart.setOption(option)
    }
}

// 初始化流量图表
const initFlowChart = () => {
    if (!flowChartRef.value) return

    // 销毁旧实例
    if (flowChart) {
        flowChart.dispose()
    }

    flowChart = echarts.init(flowChartRef.value)

    const option = {
        grid: {
            top: 30,
            bottom: 30,
            left: 60,
            right: 60
        },
        xAxis: {
            type: 'category',
            data: flowData.dates,
            axisLine: {
                lineStyle: {
                    color: '#E0E6F1'
                }
            },
            axisLabel: {
                color: '#fff'
            }
        },
        yAxis: [
            {
                type: 'value',
                name: '',
                min: 0,
                max: 10000000,
                interval: 2000000,
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#E0E6F1'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: '#E0E6F1',
                        type: 'dashed'
                    }
                },
                axisLabel: {
                    color: '#fff',
                    formatter: function (value: number) {
                        return value / 1000000 + ',000,000'
                    }
                }
            },
            {
                type: 'value',
                name: '',
                min: 0,
                max: 1,
                interval: 0.2,
                position: 'right',
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#E0E6F1'
                    }
                },
                splitLine: {
                    show: false
                },
                axisLabel: {
                    color: '#fff'
                }
            }
        ],
        series: [
            {
                name: '车流量',
                type: 'bar',
                barWidth: 20,
                data: flowData.flowValues,
                itemStyle: {
                    color: '#4B91FF'
                }
            },
            {
                name: '维修量',
                type: 'line',
                yAxisIndex: 1,
                data: flowData.maintenanceValues,
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: '#52C41A'
                },
                lineStyle: {
                    color: '#52C41A',
                    width: 2
                }
            }
        ]
    }

    flowChart.setOption(option)
}

// 初始化雷达图
const initRadarChart = (data: any) => {
    if (!radarChartRef.value) return

    // 销毁旧实例
    if (radarChart) {
        radarChart.dispose()
    }

    radarChart = echarts.init(radarChartRef.value)

    // 确保数据格式正确
    const indicators = Array.isArray(data.indicators) ? data.indicators : []
    const values = Array.isArray(data.values) ? data.values : []

    // 如果没有指标数据，显示空图表
    if (indicators.length === 0) {
        const option = {
            radar: {
                indicator: [{ name: '暂无数据', max: 100 }],
                radius: '70%',
                axisLine: {
                    lineStyle: { color: '#E0E6F1' }
                },
                splitLine: {
                    lineStyle: { color: '#E0E6F1' }
                },
                axisLabel: { color: '#fff' }
            },
            series: [
                {
                    type: 'radar',
                    data: []
                }
            ]
        }
        radarChart.setOption(option)
        return
    }

    const option = {
        radar: {
            indicator: indicators, // [{ name: '服务质量', max: 100 }, ...]
            radius: '70%',
            axisLine: {
                lineStyle: { color: '#E0E6F1' }
            },
            splitLine: {
                lineStyle: { color: '#E0E6F1' }
            },
            axisLabel: { color: '#fff' }
        },
        series: [
            {
                type: 'radar',
                data: [
                    {
                        value: values, // 确保是数组格式 [80, 90, 75, 85, 70]
                        name: '评分',
                        itemStyle: { color: '#4B91FF' },
                        areaStyle: {
                            color: 'rgba(75, 145, 255, 0.3)'
                        }
                    }
                ]
            }
        ]
    }

    radarChart.setOption(option)
}

// 获取数据项
const getDataItems = async (specialtyCode: string) => {
    if (!specialtyCode) return

    try {
        const res = await getDataItemsBySpecialty(specialtyCode)
        dataItems.value = res.data

        // 初始化详细评分对象
        dataItems.value.forEach((item: any) => {
            if (!(item.id in detailScoresObj.value)) {
                detailScoresObj.value[item.id] = 0
            }
        })

        // 更新雷达图
        updateRadarChart()
    } catch (error) {
        console.error('获取数据项失败:', error)
    }
}

// 更新雷达图
const updateRadarChart = () => {
    if (!dataItems.value.length) return

    const indicators = dataItems.value.map((item: any) => ({
        name: item.name,
        max: 100
    }))

    const values = dataItems.value.map((item: any) => detailScoresObj.value[item.id] || 0)

    initRadarChart({ indicators, values })
}

// 加载雷达图数据（显示选择专业在时间范围内最新一个月的数据）
const loadRadarChartData = async () => {
    if (!selectedCategory.value) return

    try {
        const projectId = appStore.projectContext.selectedProjectId?.toString()
        if (!projectId) return

        // 获取该专业在时间范围内的最新数据
        const res = await getLatestLifeCycleScoreInRange(projectId, selectedCategory.value, dateRange.value[0], dateRange.value[1])
        const latestData = res.data

        if (!latestData || !latestData.detailScores) {
            // 如果没有数据，显示空雷达图
            initRadarChart({ indicators: [], values: [] })
            return
        }

        // 获取数据项结构
        const dataItemsRes = await getDataItemsBySpecialty(selectedCategory.value)
        const currentDataItems = dataItemsRes.data

        //console.log('获取到的数据项(loadRadarChartData):', currentDataItems) // 调试日志

        if (!currentDataItems || !Array.isArray(currentDataItems) || currentDataItems.length === 0) {
            console.warn('数据项为空或格式不正确(loadRadarChartData)')
            initRadarChart({ indicators: [], values: [] })
            return
        }

        // 解析详细评分数据
        let detailScores: Record<string, number> = {}
        try {
            if (latestData.detailScores && typeof latestData.detailScores === 'string') {
                detailScores = JSON.parse(latestData.detailScores)
            } else if (latestData.detailScores && typeof latestData.detailScores === 'object') {
                detailScores = latestData.detailScores
            }
        } catch (e) {
            console.warn('解析详细评分数据失败:', e, 'detailScores:', latestData.detailScores)
            detailScores = {}
        }

        // 构建雷达图数据
        const indicators = currentDataItems.map((item: any) => ({
            name: item.name || '未知',
            max: 100
        }))

        const values = currentDataItems.map((item: any) => {
            const score = detailScores[item.id] || 0
            return typeof score === 'number' ? score : 0
        })

        // console.log('雷达图数据(loadRadarChartData):', {
        //     indicators,
        //     values,
        //     detailScores,
        //     currentDataItems,
        //     latestDataDetailScores: latestData.detailScores
        // }) // 调试日志

        // 确保数据格式正确
        if (indicators.length > 0 && values.length === indicators.length) {
            initRadarChart({ indicators, values })
        } else {
            console.warn('雷达图数据长度不匹配(loadRadarChartData):', indicators.length, values.length)
            initRadarChart({ indicators: [], values: [] })
        }
    } catch (error) {
        console.error('加载雷达图数据失败:', error)
        // 显示空雷达图
        initRadarChart({ indicators: [], values: [] })
    }
}

// 根据特定年月加载雷达图数据
const loadRadarChartDataByDate = async (year: number, month: number) => {
    if (!selectedCategory.value) return

    try {
        const projectId = appStore.projectContext.selectedProjectId?.toString()
        if (!projectId) return

        // 获取特定年月的详细评分数据
        const res = await getDetailScores(projectId, selectedCategory.value, year, month)
        const detailData = res.data

        if (!detailData || !detailData.detailScores) {
            // 如果没有数据，显示空雷达图
            initRadarChart({ indicators: [], values: [] })
            return
        }

        // 获取数据项结构
        const dataItemsRes = await getDataItemsBySpecialty(selectedCategory.value)
        const currentDataItems = dataItemsRes.data

        console.log('获取到的数据项:', currentDataItems) // 调试日志

        if (!currentDataItems || !Array.isArray(currentDataItems) || currentDataItems.length === 0) {
            console.warn('数据项为空或格式不正确')
            initRadarChart({ indicators: [], values: [] })
            return
        }

        // 解析详细评分数据
        let detailScores: Record<string, number> = {}
        try {
            if (detailData.detailScores && typeof detailData.detailScores === 'string') {
                detailScores = JSON.parse(detailData.detailScores)
            } else if (detailData.detailScores && typeof detailData.detailScores === 'object') {
                detailScores = detailData.detailScores
            }
        } catch (e) {
            console.warn('解析详细评分数据失败:', e, 'detailScores:', detailData.detailScores)
            detailScores = {}
        }

        // 构建雷达图数据
        const indicators = currentDataItems.map((item: any) => ({
            name: item.name || '未知',
            max: 100
        }))

        const values = currentDataItems.map((item: any) => {
            const score = detailScores[item.id] || 0
            return typeof score === 'number' ? score : 0
        })

        // console.log('特定年月雷达图数据:', {
        //     indicators,
        //     values,
        //     detailScores,
        //     currentDataItems,
        //     detailData: detailData.detailScores
        // }) // 调试日志

        // 确保数据格式正确
        if (indicators.length > 0 && values.length === indicators.length) {
            initRadarChart({ indicators, values })
        } else {
            console.warn('雷达图数据长度不匹配:', indicators.length, values.length)
            initRadarChart({ indicators: [], values: [] })
        }
    } catch (error) {
        console.error('加载特定年月雷达图数据失败:', error)
        // 显示空雷达图
        initRadarChart({ indicators: [], values: [] })
    }
}

// 加载全部类别的雷达图数据
const loadRadarAllData = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId?.toString()
        if (!projectId) return

        // 获取4个专业在整个时间区间内的平均分数据
        const res = await getRadarAllRangeData(projectId, dateRange.value[0], dateRange.value[1])
        const radarData = res.data

        initRadarChart(radarData)
    } catch (error) {
        console.error('加载全部类别雷达图数据失败:', error)
        initRadarChart({ indicators: [], values: [] })
    }
}

// 计算平均分
const calculateAverageScore = () => {
    if (dataItems.value.length === 0) return 0

    // 计算所有计分项的总分（包括0分）
    const sum = dataItems.value.reduce((acc: number, item: any) => {
        const score = detailScoresObj.value[item.id] || 0
        return acc + score
    }, 0)

    // 分母是所有计分项的个数
    return Number((sum / dataItems.value.length).toFixed(2))
}

// 处理专业类型变化
const handleSpecialtyChange = (newVal: string) => {
    if (newVal) {
        getDataItems(newVal)
    }
}

// 处理类别变化
const handleCategoryChange = async () => {
    try {
        // 等待Vue完成DOM更新
        await nextTick()

        if (selectedCategory.value === 'all') {
            // 选择全部时，不获取列表数据，直接初始化图表
            await initScoreChart()
            await loadRadarAllData() // 加载全部类别的雷达图
        } else {
            // 选择具体专业时，正常处理
            await initScoreChart()
            await getList() // 切换专业类别时重新获取列表数据
            await loadRadarChartData() // 加载雷达图数据
        }
    } catch (error) {
        console.error('处理类别变化时出错:', error)
    }
}

// 处理日期变化
const handleDateChange = async () => {
    console.log('年月范围变化:', dateRange.value)

    try {
        // 更新查询参数
        if (dateRange.value && dateRange.value.length === 2) {
            queryParams.value.startYearMonth = dateRange.value[0]
            queryParams.value.endYearMonth = dateRange.value[1]
        }

        // 重新加载图表数据
        await initScoreChart()

        // 重新加载雷达图数据
        if (selectedCategory.value === 'all') {
            await loadRadarAllData()
        } else {
            await loadRadarChartData()
        }

        // 如果不是全部类别，重新加载列表数据
        if (selectedCategory.value !== 'all') {
            await getList()
        }
    } catch (error) {
        console.error('处理日期变化时出错:', error)
    }
}

// 多选框选中数据
const handleSelectionChange = (selection: LifeCycleVO[]) => {
    ids.value = selection.map((item) => item.id)
}

// 新增按钮操作
const handleAdd = async () => {
    // 安全检查：确保不在"全部"模式下新增
    if (selectedCategory.value === 'all') {
        ElMessage.warning('请先选择具体的专业类别')
        return
    }

    reset()
    // 设置默认专业类别为当前选中的类别
    form.value.specialtyCode = selectedCategory.value

    // 如果有选中的专业类别，立即加载对应的数据项
    if (selectedCategory.value) {
        await getDataItems(selectedCategory.value)
    }

    dialog.visible = true
    dialog.title = '添加全生命周期评价'
}

// 修改按钮操作
const handleUpdate = async (row: LifeCycleVO) => {
    reset()
    const res = await getLifeCycle(row.id)
    Object.assign(form.value, res.data)

    // 设置日期选择器的值
    if (res.data.year && res.data.month) {
        ;(form.value as any).selectedDate = `${res.data.year}-${String(res.data.month).padStart(2, '0')}`
    }

    // 解析详细评分数据
    if (res.data.detailScores) {
        try {
            detailScoresObj.value = JSON.parse(res.data.detailScores)
        } catch (e) {
            detailScoresObj.value = {}
        }
    } else {
        detailScoresObj.value = {}
    }

    // 获取对应专业的数据项
    if (form.value.specialtyCode) {
        await getDataItems(form.value.specialtyCode)
    }

    dialog.visible = true
    dialog.title = '修改全生命周期评价'
}

// 删除按钮操作
const handleDelete = async (row: LifeCycleVO) => {
    await ElMessageBox.confirm('是否确认删除该数据项？', '系统提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    })
    await delLifeCycle(row.id)
    ElMessage.success('删除成功')

    // 刷新图表和列表
    await initScoreChart()

    // 根据当前选中的类别刷新雷达图
    if (selectedCategory.value === 'all') {
        await loadRadarAllData()
    } else {
        await loadRadarChartData()
    }

    await getList()
}

// 导出按钮操作
const handleExport = () => {
    proxy?.download(
        'subProject/lifeCycle/export',
        {
            ...queryParams.value
        },
        `lifecycle_${new Date().getTime()}.xlsx`
    )
}

// 取消按钮
const cancel = () => {
    reset()
    dialog.visible = false
}

// 表单重置
const reset = () => {
    form.value = { ...initFormData }
    detailScoresObj.value = {}
    lifeCycleFormRef.value?.resetFields()
}

// 提交按钮
const submitForm = () => {
    lifeCycleFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            // 设置项目ID
            form.value.projectId = appStore.projectContext.selectedProjectId?.toString()

            // 处理日期选择器的值
            if ((form.value as any).selectedDate) {
                const dateParts = (form.value as any).selectedDate.split('-')
                form.value.year = parseInt(dateParts[0])
                form.value.month = parseInt(dateParts[1])
            }

            // 序列化详细评分数据
            form.value.detailScores = JSON.stringify(detailScoresObj.value)
            console.log('提交前的详细评分数据:', {
                detailScoresObj: detailScoresObj.value,
                serialized: form.value.detailScores,
                formData: form.value
            })

            if (form.value.id) {
                await updateLifeCycle(form.value)
                ElMessage.success('修改成功')
            } else {
                await addLifeCycle(form.value)
                ElMessage.success('新增成功')
            }

            // 切换到对应的专业类别
            if (form.value.specialtyCode) {
                selectedCategory.value = form.value.specialtyCode
            }

            dialog.visible = false

            // 刷新图表和列表
            await initScoreChart()

            // 根据当前选中的类别刷新雷达图
            if (selectedCategory.value === 'all') {
                await loadRadarAllData()
            } else {
                await loadRadarChartData()
            }

            await getList()
        }
    })
}

// 监听窗口大小变化
const handleResize = () => {
    if (scoreChart) scoreChart.resize()
    if (flowChart) flowChart.resize()
    if (radarChart) radarChart.resize()
}

// 监听详细评分变化
watch(
    detailScoresObj,
    () => {
        form.value.score = calculateAverageScore()
        updateRadarChart()
    },
    { deep: true }
)

// 监听日期变化
watch(dateRange, () => {
    // 在实际应用中，这里可以根据日期范围重新获取数据
    console.log('日期范围变化:', dateRange.value)
})

// 初始化默认专业类别
const initDefaultCategory = () => {
    // 默认选中"全部"，不需要等待字典数据
    selectedCategory.value = 'all'
}

// 组件挂载时初始化
onMounted(() => {
    // 初始化默认专业类别
    initDefaultCategory()

    // 延迟一帧初始化图表，确保DOM已完全渲染
    setTimeout(() => {
        initFlowChart() // 流量图表不依赖专业类别，可以直接初始化
        // 默认选中"全部"，初始化对应的图表
        if (selectedCategory.value === 'all') {
            initScoreChart()
            loadRadarAllData() // 加载全部类别的雷达图数据
        }
    }, 0)

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize)

    // 获取数据列表（只有非"全部"时才获取）
    if (selectedCategory.value !== 'all') {
        getList()
    }
})

// 监听字典数据变化，设置默认选中项
watch(
    filteredSpecialty,
    (newVal) => {
        if (newVal && newVal.length > 0 && selectedCategory.value === 'all') {
            // 字典数据加载完成后，初始化分值图表和雷达图
            setTimeout(() => {
                initScoreChart()
                loadRadarAllData()
            }, 100)
        }
    },
    { immediate: true }
)

// 组件卸载时清理资源
onUnmounted(() => {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', handleResize)

    // 销毁图表实例
    if (scoreChart) scoreChart.dispose()
    if (flowChart) flowChart.dispose()
    if (radarChart) radarChart.dispose()
})
</script>

<style lang="scss" scoped>
.life-cycle-container {
    padding: 20px;
}

.date-range-picker {
    margin-bottom: 20px;
}

.category-selector {
    margin-bottom: 20px;
}

.charts-container {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-item {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.score-chart,
.radar-chart {
    height: 300px;
}

.flow-chart {
    height: 300px;
}

.score-chart-title,
.radar-chart-title,
.flow-chart-title {
    font-size: 14px;
    //color: #606266;
    color: white;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
}

.score-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .item-name {
        width: 120px;
        color: #fff;
    }
}

.maintenance-label {
    margin-right: 60px;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.table-container {
    margin-bottom: 30px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 20px;
}
</style>
