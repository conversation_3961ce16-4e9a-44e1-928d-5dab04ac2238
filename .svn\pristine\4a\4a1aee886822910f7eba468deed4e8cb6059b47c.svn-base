<!-- 增加了个专题首页【全寿命周期档案】展示该项目的统计数据、设施设备列表。
车辆统计、排班统计是当天数据展示； -->
<template>
    <div class="p-2">
        <!-- <div class="chart-container">
            <ArchiveUpdateComponent />
        </div> -->

        <!-- 档案更新组件 -->
        <div class="archive-update-section">
            <div class="custom-tab-title" style="margin-bottom: 20px; margin-left: 15px;">档案更新</div>
            <ArchiveUpdateComponent />
        </div>
        <!-- <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item label="管理单元" prop="unitId">
                            <el-select v-model="queryParams.unitId" placeholder="请输入管理单元" clearable @keyup.enter="handleQuery">
                                <el-option v-for="(item, key) in manageUntList" :value="item.id" :label="item.name"></el-option>
                            </el-select>
                        </el-form-item> -->

                        <!-- <el-form-item label="设备类别" prop="categoryIdThird">
                            <el-input v-model="queryParams.categoryIdThird" placeholder="请输入" clearable @keyup.enter="handleQuery" />

                        </el-form-item> -->
                        <!-- <el-form-item label="设备分类" prop="categoryIdSecond"> -->
                            <!-- <el-input v-model="queryParams.categoryIdThird" placeholder="请输入设施分类" clearable @keyup.enter="handleQuery" /> -->
                            <!-- <el-tree-select
                                clearable
                                @change="handleQuery"
                                v-model="selectedQueryCategoryId"
                                :data="categoryList"
                                :props="{ value: 'id', label: 'name', children: 'children' }"
                                value-key="id"
                                placeholder="请选择父节点编号"
                                check-strictly
                            />
                        </el-form-item>
                        <el-form-item label="设备名称" prop="name">
                            <el-input v-model="queryParams.name" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item label="备注名称" prop="remark">
                            <el-input v-model="queryParams.remark" placeholder="请输入备注名称" clearable @keyup.enter="handleQuery" />
                        </el-form-item> -->
                        <!-- <el-form-item label="设备编码" prop="code">
                            <el-input v-model="queryParams.code" placeholder="请输入" clearable @keyup.enter="handleQuery" />
                        </el-form-item> -->
                        <!-- <el-form-item label="" prop="categoryIdFirst">
              <el-input v-model="queryParams.categoryIdFirst" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="" prop="categoryIdSecond">
              <el-input v-model="queryParams.categoryIdSecond" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
<!--
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition> -->

        <el-card shadow="never" class="equip-card">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <!-- 自定义标题 + 自定义 Tabs -->
                    <el-col :span="18">
                        <div class="custom-tab-header">
                            <div class="custom-tab-title">设施设备</div>
                            <ul class="custom-tabs">
                                <li
                                    v-for="specialty in tnl_specialty"
                                    :key="specialty.value"
                                    :class="['custom-tab-item', { active: activeSpecialtyTab === specialty.value }]"
                                    @click="onSelectSpecialty(specialty.value)"
                                >
                                    {{ specialty.label }}
                                </li>
                            </ul>
                        </div>
                    </el-col>
                    <!-- 右侧操作区（原搜索/隐藏图标已移除） -->
                </el-row>
            </template>

            <el-table v-loading="loading" :data="equipmentList">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />

                <el-table-column label="设备名称" align="center" prop="name" />
                <el-table-column label="备注" align="center" prop="remark" width="140" show-overflow-tooltip />
                <el-table-column label="管理单元" align="center" prop="unitId">
                    <template #default="scope">
                        {{ manageUntList.find((unit) => unit.id === scope.row.unitId)?.name }}
                    </template>
                </el-table-column>

                <el-table-column :label="isElectricTab ? '机电分系统' : '结构属性'" align="center" prop="categoryIdFirst">
                    <template #default="scope">
                        {{ getFirstLevelCategory(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <el-table-column :label="isElectricTab ? '机电子系统' : '结构类别'" align="center" prop="categoryIdThird" width="140" show-overflow-tooltip>
                    <template #default="scope">
                        {{ getSecondLevelCategory(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <el-table-column :label="isElectricTab ? '设备类型' : '构建类别'" align="center" prop="categoryIdThird">
                    <template #default="scope">
                        {{ getCategoryNameById(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <!-- <el-table-column label="模型ID" align="center" prop="modleId" /> -->
                <!--
                <el-table-column label="品牌" align="center" prop="brand">
                    <template #default="scope">
                        <dict-tag :options="device_brands_new" :value="scope.row.brand || ''" />
                    </template>
                </el-table-column>
                -->

                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <div class="op-actions">
                            <el-button link type="primary" class="op-link op-info" @click="handleInformation(scope.row)">
                                <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="档案" />
                                设施档案
                            </el-button>
                            <el-button link type="primary" class="op-link op-edit" @click="handleUpdate(scope.row)">
                                <img class="op-icon" src="@/assets/images/edit-icon.png" alt="修改" />
                                修改
                            </el-button>
                            <el-button link type="danger" class="op-link op-delete" @click="handleDelete(scope.row)">
                                <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/store/modules/app'
import { DeviceQuery } from '@/api/subProject/basic/device/types'
import { EquipmentForm } from '@/api/subProject/basic/equipment/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { CategoryVO } from '@/api/common/category/types'
import { listCategory } from '@/api/common/category'
import { TunnelTreeNode } from '@/api/types'
import { useRouter } from 'vue-router'
import { listDevice } from '@/api/subProject/basic/device'
import { DeviceVO } from '@/api/subProject/basic/device/types'
import { watchEffect, nextTick, computed } from 'vue'
// import CarChart from '@/views/subProject/components/Chart/CarChart.vue'
// import TeamTaskChart from '../components/Chart/TeamTaskChart.vue'
import ArchiveUpdateComponent from './components/index.vue'


const { proxy } = getCurrentInstance() as ComponentInternalInstance
const appStore = useAppStore()
const router = useRouter()
const { code_type, device_owner, device_brands_new, tnl_specialty } = toRefs<any>(
    proxy?.useDict('code_type', 'device_owner', 'device_brands_new', 'tnl_specialty')
)

const selectedQueryCategoryId = ref('')
const equipmentList = ref<DeviceVO[]>([])
const buttonLoading = ref(false)
const activeSpecialtyTab = ref('')
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const activeTabName = ref('basic')
const queryFormRef = ref<ElFormInstance>()
const equipmentFormRef = ref<ElFormInstance>()
const gutter = ref(10)
const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})
const onSelectSpecialty = (value: string) => {
    if (activeSpecialtyTab.value === value) return
    activeSpecialtyTab.value = value
    handleSpecialtyChange(value)
}

const initFormData: EquipmentForm = {
    id: undefined,
    unitId: undefined,
    name: undefined,
    code: undefined,
    seq: undefined,
    roadwayId: undefined,
    codeType: undefined,
    timeArrangeBgn: undefined,
    timeArrangeEnd: undefined,
    bgnKilometer: undefined,
    equipmentOwner: undefined,
    endKilometer: undefined,
    modelId: undefined,
    remark: undefined,
    owner: undefined,
    installDate: undefined,
    brand: undefined,
    categoryIdFirst: undefined,
    categoryIdSecond: undefined,
    categoryIdThird: undefined,
    specification: undefined,
    images: undefined,
    files: undefined,
    projectId: undefined
}
const data = reactive<PageData<EquipmentForm, DeviceQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        unitId: undefined,
        roomId: undefined,
        name: undefined,
        code: undefined,
        remark: undefined,
        categoryPath: undefined,
        kind: 'facility',
        specialty: undefined,
        params: {}
    },
    rules: {
        unitId: [{ required: true, message: '请选择管理单元', trigger: 'blur' }],
        categoryIdThird: [{ required: true, message: '请选择设备类型', trigger: 'blur' }],
        remark: [{ required: true, message: '请填写备注信息', trigger: 'blur' }],
        codeType: [{ required: true, message: '请选择编码类型', trigger: 'blur' }],
        bgnCode: [{ required: true, message: '请填写起范围编码', trigger: 'blur' }],
        bgnKilometer: [{ required: true, message: '请填写起始里程', trigger: 'blur' }],
        owner: [{ required: true, message: '请选择设备归属单位', trigger: 'blur' }],
        installDate: [{ required: true, message: '请输入安装时间', trigger: 'blur' }],
        roomId: [{ required: true, message: '请选择房间编码', trigger: 'change' }]
    }
})

const { queryParams, form, rules } = toRefs(data)
const currentProjectId = ref('')
const manageUntList = ref<ManageUnitVO[]>([])
const categoryList = ref<CategoryVO[]>([])
const lineTree = ref<TunnelTreeNode[]>([])
// 是否为机电系统 Tab
const isElectricTab = computed(() => !!(activeSpecialtyTab.value && activeSpecialtyTab.value.includes('electric')))
/** 查询设施即设备信息列表 */
const getList = async () => {
    loading.value = true
    // 确保在调用接口前设置项目ID
    if (!queryParams.value.projectId) {
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
    }

    // 设置专业类型查询参数
    queryParams.value.specialty = activeSpecialtyTab.value || undefined

    console.log('activeSpecialtyTab', activeSpecialtyTab.value)
    // 根据专业类型设置kind值
    if (activeSpecialtyTab.value && activeSpecialtyTab.value.includes('electric')) {
        queryParams.value.kind = 'equipment'
    } else {
        queryParams.value.kind = 'facility'
    }

    const res = await listDevice(queryParams.value)
    equipmentList.value = res.rows
    total.value = res.total
    loading.value = false
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    equipmentFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    if (!selectedQueryCategoryId.value) {
        queryParams.value.categoryPath = ''
    } else {
        const selectFacatity = findCategoryById(categoryList.value, selectedQueryCategoryId.value)
        queryParams.value.categoryPath = selectFacatity.path
        console.log('selectFacatity', selectFacatity.path)
    }
    queryParams.value.pageNum = 1
    getList()
}

/** 专业类型切换处理函数 */
const handleSpecialtyChange = (tabName: string) => {
    activeSpecialtyTab.value = tabName
    queryParams.value.pageNum = 1
    // 先刷新分类树（按项目/专业/类型），再加载列表，避免三级名称映射为空
    getEquipmentCategoryList().then(() => getList())
}

function findCategoryById(list, id) {
    for (const item of list) {
        if (item.id === id) return item
        if (item.children) {
            const found = findCategoryById(item.children, id)
            if (found) return found
        }
    }
    return null
}

/** 重置按钮操作 */
const resetQuery = () => {
    selectedQueryCategoryId.value = ''
    // 重置时设置为第一个专业类型
    if (tnl_specialty.value && tnl_specialty.value.length > 0) {
        activeSpecialtyTab.value = tnl_specialty.value[0].value
        console.log('重置时设置专业类型:', activeSpecialtyTab.value)
    }
    queryFormRef.value?.resetFields()
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: DeviceVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 设备档案按钮操作 */
const handleInformation = (row: DeviceVO) => {
    // 根据专业类型判断跳转页面
    if (row.specialty && row.specialty.includes('electric')) {
        // 机电专业跳转到设备档案页
        router.push('/subProject/data/equipmentInformation?id=' + row.id)
    } else {
        // 其他专业跳转到设施档案页
        router.push('/subProject/data/facilityInformation?id=' + row.id)
    }
}

/** 修改按钮操作（占位实现，可对接实际编辑页或弹窗） */
const handleUpdate = (row: DeviceVO) => {
    console.log('修改', row)
}

/** 删除按钮操作（占位实现，可接入后端删除接口与二次确认） */
const handleDelete = (row: DeviceVO) => {
    console.log('删除', row)
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'basic/equipment/export',
        {
            ...queryParams.value
        },
        `equipment_${new Date().getTime()}.xlsx`
    )
}
const getManageUnitList = async () => {
    currentProjectId.value = appStore.projectContext.selectedProjectId
    manageUntList.value = (await listProjectManageUnit(currentProjectId.value)).data
}
/** 查询category列表 */
const getEquipmentCategoryList = async () => {
    loading.value = true
    // 根据当前 Tab 设置参数
    const projectId = appStore.projectContext.selectedProjectId
    const kind = isElectricTab.value ? 'equipment' : 'facility'
    const specialty = activeSpecialtyTab.value || undefined

    const res = await listCategory({ projectId, kind, specialty })

    const data = proxy?.handleTree<CategoryVO>(res.data, 'id', 'parentId')
    console.log('category tree:', kind, specialty, data)
    categoryList.value = data || []
    loading.value = false
}

const getFirstLevelCategory = (categoryId: string) => {
    if (!categoryId || !categoryList.value) return ''

    for (const firstLevel of categoryList.value) {
        for (const secondLevel of firstLevel.children || []) {
            for (const thirdLevel of secondLevel.children || []) {
                if (thirdLevel.id === categoryId) {
                    return firstLevel.name
                }
            }
        }
    }
    return ''
}

// 获取第二级分类名称
const getSecondLevelCategory = (categoryId: string) => {
    if (!categoryId || !categoryList.value) return ''

    for (const firstLevel of categoryList.value) {
        for (const secondLevel of firstLevel.children || []) {
            for (const thirdLevel of secondLevel.children || []) {
                if (thirdLevel.id === categoryId) {
                    return secondLevel.name
                }
            }
        }
    }
    return ''
}

const getCategoryNameById = (id: string) => {
    if (!id) return ''
    const findCategory = (list: any[], targetId: string): string => {
        for (const item of list) {
            if (item.id === targetId) {
                return item.name
            }
            if (item.children && item.children.length) {
                const found = findCategory(item.children, targetId)
                if (found) return found
            }
        }
        return ''
    }
    return findCategory(categoryList.value, id)
}

// 监听字典数据变化，设置默认选中第一个专业类型
watchEffect(() => {
    if (tnl_specialty.value && tnl_specialty.value.length > 0 && !activeSpecialtyTab.value) {
        nextTick(() => {
            activeSpecialtyTab.value = tnl_specialty.value[0].value
            console.log('设置默认专业类型:', activeSpecialtyTab.value)
            // 默认设置完成后，加载分类树与列表
            getEquipmentCategoryList().then(() => getList())
        })
    }
})

onMounted(() => {
    // 首先设置项目ID
    queryParams.value.projectId = appStore.projectContext.selectedProjectId

    getManageUnitList()
    getEquipmentCategoryList()
    getList()
})
</script>
<style lang="scss" scoped>
.chart-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    gap: 16px;
    flex-wrap: nowrap;
    // background: red;
    height: 600px;
}

/* 两个区域容器：负责分栏与内部居中，不靠 padding/gap 造空白 */
.chart-slot {
    flex: 1 1 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 360px;
}

.chart-slot--wide { flex: 1 1 0; }

/* 图表本体尺寸由自身最大宽度和高度决定，居中于各自区域 */
// .chart-slot > .chart {
//     width: 100%;
//     max-width: 760px;
//     height: 280px;
// }

@media (max-width: 1440px) {
    .chart-container {
        gap: 12px;
    }
    .chart-slot > .chart { max-width: 560px; height: 280px; }
}

@media (max-width: 1200px) {
    .chart-container {
        flex-direction: column;
        align-items: center;
    }
    .chart-slot { min-height: 0; }
    .chart-slot > .chart { max-width: 720px; height: 280px; }
}

.specialty-tabs {
    :deep(.el-tabs__header) {
        margin-bottom: 0;
    }

    :deep(.el-tabs__nav-wrap) {
        &::after {
            display: none;
        }
    }

    :deep(.el-tabs__item) {
        color: #7ecfff;
        font-size: 14px;

        &.is-active {
            color: #2de1fc;
            font-weight: bold;
        }

        &:hover {
            color: #2de1fc;
        }
    }

    :deep(.el-tabs__active-bar) {
        background-color: #2de1fc;
    }
}

.text-right {
    //text-align: right;
    display: flex;
    justify-content: flex-end;
}

/* 自定义 Tab Header */
.custom-tab-header {
    display: flex;
    align-items: center;
    gap: 16px;
}
.custom-tab-title {
    font-size: 26px;
    font-weight: bold;
    font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
    letter-spacing: 1px;
    color: #e9f2ff; /* 回退色 */
    background: linear-gradient(to top, #497ef1 0%, #ffffff 60%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    line-height: 26px;
    display: inline-block;
}
.custom-tabs {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
    padding: 0;
    list-style: none;
}
.custom-tab-item {
    min-width: 160px;
    height: 56px;
    padding: 0 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #cfe6ff;
    font-size: 18px;
    background: url('@/assets/images/tab-unselected.png') no-repeat center / 100% 100%;
    cursor: pointer;
    user-select: none;
    transition: filter 160ms ease;
}
.custom-tab-item:hover { filter: brightness(1.05); }
.custom-tab-item.active {
    background: url('@/assets/images/tab-selected.png') no-repeat center / 100% 100%;
    color: #00F0FF;
}
/* 设施设备卡片与表格去除灰白背景与边框 */
:deep(.equip-card.el-card) {
    background-color: transparent;
    border: none;
}
:deep(.equip-card .el-card__header) {
    background-color: transparent;
    border-bottom: none;
}
/* 表格主体区域背景渐变（不影响上方 header 标题与 tabs） */
:deep(.equip-card .el-card__body) {
    background: linear-gradient(to top, rgba(13, 24, 58, 0.01) 0%, rgba(15, 24, 57, 0.42) 60%, rgba(22, 33, 69, 1) 100%);
}

/* 表格区域透明化（如需保留行高亮可后续单独设定） */
:deep(.equip-card .el-table) {
    --el-table-bg-color: transparent;
    --el-table-header-bg-color: transparent;
    --el-table-tr-bg-color: transparent;
    --el-table-row-hover-bg-color: rgba(255, 255, 255, 0.06);
    --el-table-border-color: rgba(255, 255, 255, 0.08);
    background-color: transparent;
    color: #ffffff;
}
:deep(.equip-card .el-table__inner-wrapper),
:deep(.equip-card .el-table__header-wrapper),
:deep(.equip-card .el-table__body-wrapper) {
    background-color: transparent;
}
:deep(.equip-card .el-table th.el-table__cell),
:deep(.equip-card .el-table td.el-table__cell),
:deep(.equip-card .el-table tr) {
    background-color: transparent !important;
}

/* 表头区域独立色块（非整行上色，左右与上方留白） */
:deep(.equip-card .el-table__header-wrapper) {
    position: relative;
}
:deep(.equip-card .el-table__header-wrapper)::before {
    content: '';
    position: absolute;
    left: 0px;
    right: 0px;
    top: 0px;
    height: 44px;
    background: rgb(31, 43, 78);
    border-radius: 6px;
    pointer-events: none;
    z-index: 0;
}
:deep(.equip-card .el-table thead),
:deep(.equip-card .el-table th.el-table__cell) {
    position: relative;
    z-index: 1;
    background-color: transparent !important;
}

/* 表体文字颜色与字号 */
:deep(.equip-card .el-table td.el-table__cell .cell) {
    color: #ffffff;
    font-size: 14px;
}

/* 表头文字颜色、加粗与比表体大 2px */
:deep(.equip-card .el-table th.el-table__cell .cell) {
    color: #ffffff;
    font-weight: bold;
    font-size: 16px;
}

/* 操作列样式 */
.op-actions { display: flex; align-items: center; gap: 12px; justify-content: center; }
.op-link { display: inline-flex; align-items: center; gap: 6px; padding: 0 6px; }
.op-icon { width: 14px; height: 14px; display: inline-block; margin-right: 4px; }
.op-info { color: #4286F3 !important; }
.op-edit { color: #42F3E9 !important; }
.op-delete { color: #D62121 !important; }

/* 档案更新区域样式 */
.archive-update-section {
    margin-top: 20px;
    margin-bottom: 20px;

}
</style>
