import request from '@/utils/request'
import { AxiosPromise } from 'axios'
import { CategoryVO, CategoryForm, CategoryQuery, TunnelTreeNode, CategoryHierarchyVO, CategoryNameInfo } from '@/api/common/category/types'
import { queryObjects } from 'v8'
import { TreeVO } from '@/api/demo/tree/types'

/**
 * 查询category列表
 * @param query
 * @returns {*}
 */

export const listCategory = (query?: CategoryQuery): AxiosPromise<CategoryVO[]> => {
    return request({
        url: '/common/category/list',
        method: 'get',
        params: query
    })
}

/**查询设施类别列表 */
export const listFacilityCategoryTree = (speciality?: string): AxiosPromise<TunnelTreeNode[]> => {
    return request({
        url: '/common/category/listFacilityCategoryTree/' + speciality,
        method: 'get'
    })
}
///根据专业查询设施设备分类
export const listDeviceCategoryBySpeciality = (speciality?: string): AxiosPromise<CategoryVO[]> => {
    return request({
        url: '/common/category/listDeviceCategoryBySpecialty/' + speciality,
        method: 'get'
    })
}
///根据专业查询设施设设备类别列表
export const listDeviceCategoryTree = (query?: CategoryQuery): AxiosPromise<TunnelTreeNode[]> => {
    return request({
        url: '/common/category/listDeviceCategoryTree',
        method: 'get',
        params: query
    })
}
/*查询可以关联到缺陷的构件列表，仅包含路面和桥梁系的专业 */
export const listLinkableToDefect = (speciality?: string): AxiosPromise<CategoryVO[]> => {
    return request({
        url: '/common/category/listRelatedableToDefect/' + speciality,
        method: 'get'
    })
}

/**
 * 查询category详细
 * @param id
 */
export const getCategory = (id: string | number): AxiosPromise<CategoryVO> => {
    return request({
        url: '/common/category/' + id,
        method: 'get'
    })
}

/**
 * 新增category
 * @param data
 */
export const addCategory = (data: CategoryForm) => {
    return request({
        url: '/common/category',
        method: 'post',
        data: data
    })
}

/**
 * 修改category
 * @param data
 */
export const updateCategory = (data: CategoryForm) => {
    return request({
        url: '/common/category',
        method: 'put',
        data: data
    })
}

/**
 * 删除category
 * @param id
 */
export const delCategory = (id: string | number | Array<string | number>) => {
    return request({
        url: '/common/category/' + id,
        method: 'delete'
    })
}

/**
 * 根据分类ID获取父节点层级信息
 * @param categoryId 分类ID
 * @returns 包含当前节点、父节点、祖父节点的信息
 */
export const getCategoryParentHierarchy = (categoryId: string | number): AxiosPromise<CategoryHierarchyVO> => {
    return request({
        url: '/common/category/getParentHierarchy/' + categoryId,
        method: 'get'
    })
}

/**
 * 根据分类ID获取父节点层级名称列表
 * @param categoryId 分类ID
 * @returns 层级名称列表 [祖父节点名称, 父节点名称, 当前节点名称]
 */
export const getCategoryHierarchyNames = (categoryId: string | number): AxiosPromise<string[]> => {
    return request({
        url: '/common/category/getHierarchyNames/' + categoryId,
        method: 'get'
    })
}

/**
 * 批量获取分类层级名称
 * @param categoryIds 分类ID数组
 * @returns 分类ID到分类名称信息的映射
 */
export const getCategoryNamesByIds = (categoryIds: string[]): AxiosPromise<Record<string, CategoryNameInfo>> => {
    return request({
        url: '/common/category/getCategoryNames',
        method: 'get',
        params: { categoryIds: categoryIds.join(',') }
    })
}
