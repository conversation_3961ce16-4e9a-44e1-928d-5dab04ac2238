<!-- 领航页 -->
<template>
    <div class="navigation-container">
        <div class="screen" :style="screenStyle">
            <div class="select-area" ref="selectArea">
                <div class="select-frame">
                    <div class="select-box" @click="toggleOpen" :class="{ 'is-open': isOpen }">
                        <input class="select-input" :value="displayText" readonly />
                        <img class="select-arrow" :src="arrowDownIcon" alt="下拉箭头" />
                    </div>
                </div>

                <transition name="fade-slide">
                    <div v-if="isOpen" class="select-dropdown" @click.stop>
                        <div class="select-list">
                            <button
                                v-for="p in projects"
                                :key="p.id"
                                class="select-item"
                                :class="{ 'is-active': p.id === selectedProject }"
                                @click="choose(p)"
                            >
                                {{ p.name }}
                            </button>
                        </div>
                    </div>
                </transition>
            </div>

            <div class="cards">
                <div
                    class="card"
                    v-for="card in cards"
                    :key="card.id"
                    @click="enter(card)"
                    @mouseenter="hoverCard = card.id"
                    @mouseleave="hoverCard = ''"
                    :style="{
                        '--bg': `url(${card.icon})`,
                        '--bg-selected': `url(${card.iconSelected})`,
                        '--selected-opacity': hoverCard === card.id ? 1 : 0
                    }"
                >
                    <h3 class="card__title">{{ card.title }}</h3>
                    <p class="card__desc">{{ card.desc }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { getAllProjects } from '@/api/project/project'
import { ProjectVO } from '@/api/project/project/types'
import { getCurrentUserPermissionInProject } from '@/api/system/user'

// 导入图片资源
import arrowDownIcon from '@/assets/images/navigation/arrow-down.png'
import quanshoumingdanganIcon from '@/assets/images/navigation/全寿命周期档案.png'
import quanshoumingdanganSelectIcon from '@/assets/images/navigation/全寿命周期档案选中.png'

import bihuanguankongIcon from '@/assets/images/navigation/闭环管控.png'
import bihuanguankongSelectIcon from '@/assets/images/navigation/闭环管控选中.png'
import zhihuijianceIcon from '@/assets/images/navigation/智慧监测.png'
import zhihuijianceSelectIcon from '@/assets/images/navigation/智慧监测选中.png'
import quanshoumingpingjiaIcon from '@/assets/images/navigation/全寿命.png'
import quanshoumingpingjiaSelectIcon from '@/assets/images/navigation/评价选中.png'
import zhihuidiaoduIcon from '@/assets/images/navigation/智慧调度.png'
import zhihuidiaoduSelectIcon from '@/assets/images/navigation/智慧调度选中.png'

const appStore = useAppStore()

const design = { width: 1920, height: 1080 }
const scale = ref(1)
const selectArea = ref<HTMLElement | null>(null)

const updateScale = () => {
    const s = Math.min(window.innerWidth / design.width, window.innerHeight / design.height)
    scale.value = Math.max(0.5, s) // 限制最小缩放，避免过小
}

onMounted(() => {
    updateScale()
    window.addEventListener('resize', updateScale)
    document.addEventListener('click', onClickOutside)
    initializeProjects()
})

onUnmounted(() => {
    window.removeEventListener('resize', updateScale)
    document.removeEventListener('click', onClickOutside)
})

const screenStyle = computed(() => ({ '--scale': scale.value }))

const placeholderText = '请先选择项目'
const projects = ref<ProjectVO[]>([])

const selectedProject = ref('')
const isOpen = ref(false)
const displayText = computed(() => {
    if (selectedProject.value) {
        const project = projects.value.find((p) => p.id === selectedProject.value)
        return project ? project.name : placeholderText
    }
    return placeholderText
})

const toggleOpen = () => {
    isOpen.value = !isOpen.value
}

const choose = async (project: ProjectVO) => {
    selectedProject.value = project.id
    isOpen.value = false
    await handleProjectChange(project.id)
}

const onClickOutside = (e: Event) => {
    if (!selectArea.value) return
    if (!selectArea.value.contains(e.target as Node)) isOpen.value = false
}

// 项目选择处理函数
const handleProjectChange = async (value: string) => {
    appStore.projectContext.isPlatform = false
    appStore.projectContext.selectedProjectId = value
    const permissionCodes = (await getCurrentUserPermissionInProject(value)).data
    appStore.postPermissionCodes = permissionCodes
    console.log('权限列表')
    console.log(permissionCodes)
    // 这里可以根据需要添加其他业务逻辑，比如跳转到项目首页
    window.location.href = '/subProject/dashboard'
}

// 初始化项目列表
const initializeProjects = async () => {
    try {
        const res = await getAllProjects()
        projects.value = res.data

        // 优先从store中读取选中的项目ID（支持从地图页面跳转过来）
        if (appStore.projectContext.selectedProjectId) {
            selectedProject.value = appStore.projectContext.selectedProjectId
            console.log('从store中恢复选中的项目:', selectedProject.value)
        } else if (projects.value.length > 0) {
            // 如果store中没有选中项目，则默认选择第一个项目
            selectedProject.value = projects.value[0].id
            console.log('默认选择第一个项目:', selectedProject.value)
        }
    } catch (error) {
        console.error('获取项目列表失败:', error)
    }
}

const cards = ref([
{
        id: 'control',
        title: '闭环管理',
        desc: '从计划制定到执行，反馈的全流程管理',
        icon: zhihuidiaoduIcon,
        iconSelected: zhihuidiaoduSelectIcon,
        url: '/subProject/dashboard'
    },
    {
        id: 'archive',
        title: '全寿命周期档案',
        desc: '每个阶段都有详细的记录和文档',
        icon: quanshoumingdanganIcon,
        iconSelected: quanshoumingdanganSelectIcon,
        url: '/subProject/data/equipmentFile'
    },

    {
        id: 'dispatch',
        title: '智慧调度',
        desc: '对机电设备进行智慧调度',
        icon: bihuanguankongIcon,
        iconSelected: bihuanguankongSelectIcon,
        url: 'http://**********:8081/'
    },
    {
        id: 'monitor',
        title: '智慧监测',
        desc: '实时监控设备与设施的健康状态',
        icon: zhihuijianceIcon,
        iconSelected: zhihuijianceSelectIcon,
        url: '/subProject/health/healthSummary'
    },
    {
        id: 'assessment',
        title: '全寿命评价',
        desc: '对隧道进行综合评价提供科学依据',
        icon: quanshoumingpingjiaIcon,
        iconSelected: quanshoumingpingjiaSelectIcon,
        url: '/subProject/lifeCycle'
    }
])

const hoverCard = ref('')

const enter = (card: any) => {

    if (card.url.startsWith('http://') || card.url.startsWith('https://')) {
        window.open(card.url, '_blank')
        return
    }

    // 未选择项目则不响应点击（仅对内部链接有效）
    if (!selectedProject.value) return

    // 在打开新窗口前，确保store中的项目ID是最新的
    appStore.projectContext.selectedProjectId = selectedProject.value

    // 点击事件 - 内部链接
    window.open(card.url, 'subProjectWindow')
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/navigation/fonts.css';
@import '@/assets/styles/navigation/navigation.css';

/* 领航页组件定位样式 - 占中间40% */
.navigation-container {
    position: absolute !important;
    left: 35% !important;
    top: 0 !important;
    width: 30% !important;
    height: 100% !important;
    overflow-y: auto;
    overflow-x: hidden;
    // background: none;
}
</style>
