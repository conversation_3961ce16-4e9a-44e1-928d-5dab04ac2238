<!-- 按巡检作业查询组件 -->
<template>
    <div class="p-2 inspection-task-page">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item>
                            <!-- 作业单日期 -->
                            <!-- <el-date-picker
                                v-model="queryParams.params.taskDate"
                                clearable
                                type="datetimerange"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            /> -->

                            <el-date-picker
                                v-model="dateRangeTaskDate"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                                clearable
                            />
                        </el-form-item>
                        <el-form-item prop="name">
                            <el-input v-model="queryParams.name" placeholder="请输入作业单名称" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">搜索</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>
        <el-card shadow="never">
            <el-table v-loading="loading" stripe :data="taskList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <el-table-column label="作业单名称" align="center" prop="name" />
                <el-table-column label="计划作业日期" align="center" prop="taskDate">
                    <template #default="scope">
                        {{ formatDate(scope.row.taskDate) }}
                    </template>
                </el-table-column>
                <el-table-column label="作业类型" align="center"> 年度巡检 </el-table-column>
                <el-table-column label="班次" align="center" prop="shiftType">
                    <template #default="scope">
                        <dict-tag :options="work_order_white_evening" :value="scope.row.shiftType" />
                    </template>
                </el-table-column>
                <!-- <el-table-column label="巡检班组" align="center" prop="teamId" >

								</el-table-column> -->
                <el-table-column label="巡检路线" align="center" prop="inspectionLineNameLabel">
                    <template #default="scope">
                        <span v-if="scope.row.inspectionLineNameLabel">
                            {{ scope.row.inspectionLineNameLabel }}
                        </span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column label="实际开工日期" align="center" prop="taskStartDate">
                    <template #default="scope">
                        {{ formatDate(scope.row.taskStartDate) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
                    <template #default="scope">
                        <div class="op-actions">
                            <el-button
                                link
                                type="primary"
                                class="op-link op-edit"
                                :disabled="scope.row.currentStatus == 'START'"
                                @click="handleInspectDevice(scope.row)"
                            >
                                <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="巡检设备" />
                                巡检设备
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { listInspectionTasks, getTask } from '@/api/subProject/plan/task'
import { TaskVO, TaskForm, TaskQuery } from '@/api/plan/task/types'
import { useAppStore } from '@/store/modules/app'
import { useRouter } from 'vue-router'
import { ref, reactive, toRefs, onMounted, onBeforeUnmount, watch, getCurrentInstance, ComponentInternalInstance, nextTick } from 'vue'

const appStore = useAppStore()
const router = useRouter()
const { proxy } = getCurrentInstance() as ComponentInternalInstance

const taskList = ref<TaskVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const queryFormRef = ref<ElFormInstance>()

const initFormData: TaskForm = {
    id: undefined,
    projectId: undefined,
    parentId: undefined,
    name: undefined,
    taskType: undefined,
    defineId: undefined,
    weekTaskId: undefined,
    year: undefined,
    month: undefined,
    week: undefined,
    shiftType: undefined,
    taskDate: undefined,
    taskStep: undefined,
    bgnDate: undefined,
    endDate: undefined,
    taskStartDate: undefined,
    taskFinishDate: undefined,
    teamId: undefined,
    sort: undefined,
    publishState: undefined,
    status: undefined,
    currentStatus: undefined,
    tempTask: 'no',
    tempResourceId: ''
}

const data = reactive<PageData<TaskForm, TaskQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        parentId: undefined,
        name: undefined,
        taskType: 'inspect',
        defineId: undefined,
        weekTaskId: undefined,
        year: undefined,
        month: undefined,
        week: undefined,
        shiftType: undefined,
        taskDate: undefined,
        taskStep: 'task',
        bgnDate: undefined,
        endDate: undefined,
        teamId: undefined,
        sort: undefined,
        publishState: undefined,
        currentStatus: 'END',
        tempTask: 'no',
        tempResourceId: '',
        params: {}
    },
    rules: {
        id: [{ required: true, message: '不能为空', trigger: 'blur' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

const dateRangeTaskDate = ref<[string, string]>(['', ''])

// 状态缓存相关
const CACHE_KEY = 'inspection_task_search_state'

const { maintenance_strategy, work_order_white_evening } = toRefs<any>(proxy?.useDict('maintenance_strategy', 'work_order_white_evening'))
/** 查询任务列表 */
const getList = async () => {
    loading.value = true

    // 确保项目ID已设置
    if (!queryParams.value.projectId) {
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
    }

    // 设置项目ID
    queryParams.value.projectId = appStore.projectContext.selectedProjectId
    queryParams.value.currentStatus = 'END'
    // 使用独立的日期范围变量进行日期范围查询
    proxy?.addDateRange(queryParams.value, dateRangeTaskDate.value, 'BgnDate')

    const res = await listInspectionTasks(queryParams.value)
    taskList.value = res.rows
    total.value = res.total

    loading.value = false
}

// 缓存搜索状态
const saveSearchState = () => {
    try {
        const state = {
            queryParams: {
                name: queryParams.value.name
            },
            dateRangeTaskDate: dateRangeTaskDate.value,
            showSearch: showSearch.value
        }
        sessionStorage.setItem(CACHE_KEY, JSON.stringify(state))
        console.log('保存巡检任务搜索状态:', state)
    } catch (error) {
        console.error('保存巡检任务搜索状态失败:', error)
    }
}

// 恢复搜索状态
const restoreSearchState = () => {
    try {
        const cached = sessionStorage.getItem(CACHE_KEY)
        if (cached) {
            const state = JSON.parse(cached)

            // 恢复查询参数
            if (state.queryParams) {
                queryParams.value.name = state.queryParams.name
            }

            // 恢复日期范围
            if (state.dateRangeTaskDate) {
                dateRangeTaskDate.value = state.dateRangeTaskDate
            }

            // 恢复搜索框显示状态
            if (state.showSearch !== undefined) {
                showSearch.value = state.showSearch
            }

            console.log('恢复巡检任务搜索状态:', state)
        }
    } catch (error) {
        console.error('恢复巡检任务搜索状态失败:', error)
    }
}

// 清除缓存状态
const clearSearchState = () => {
    sessionStorage.removeItem(CACHE_KEY)
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    queryParams.value.projectId = appStore.projectContext.selectedProjectId
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    // 清空日期范围
    dateRangeTaskDate.value = ['', '']
    queryFormRef.value?.resetFields()

    // 清除缓存状态
    clearSearchState()

    handleQuery()
}
// 格式化日期
const formatDate = (date: string) => {
    if (!date) return '-'
    return date
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: TaskVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    // 移除新增功能
}

/** 导航到查看、指派页面 */
const handleAssign = (operationName: string) => {
    // reset();
    // dialog.visible = true;
    // dialog.title = '添加年度计划目录';
    // proxy?.$router.push('assign');
}

/** 审批按钮操作 */
const handleConfirm = (operationName: string) => {
    // 移除审批功能
}

/** 修改按钮操作 */
const handleUpdate = async (row?: TaskVO) => {
    // 移除修改功能
}

/** 提交按钮 */
const submitForm = () => {
    // 移除提交功能
}

/** 删除按钮操作 */
const handleDelete = async (row?: TaskVO) => {
    // 移除删除功能
}

/** 巡检设备按钮操作 */
const handleInspectDevice = (row: TaskVO) => {
    console.log('巡检设备，任务信息:', row)
    // 跳转到巡检设备详情页面
    router.push({
        name: 'multiRouteDetail',
        query: {
            taskId: row.id
        }
    })
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'plan/task/export',
        {
            ...queryParams
        },
        `task_${new Date().getTime()}.xlsx`
    )
}

onMounted(() => {
    queryParams.value.projectId = appStore.projectContext.selectedProjectId

    // 恢复搜索状态
    restoreSearchState()

    getList()
})

// 组件卸载前保存状态
onBeforeUnmount(() => {
    saveSearchState()
})

// 监听搜索条件变化，实时保存状态
watch(
    [() => queryParams.value.name, dateRangeTaskDate, showSearch],
    () => {
        // 延迟保存，避免频繁操作
        nextTick(() => {
            saveSearchState()
        })
    },
    { deep: true }
)
</script>

<style lang="scss" scoped>
.inspection-task-page {

    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-date-editor .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder) {
        color: #8291A9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)) {
        color: #FFFFFF !important;
    }

    :deep(.el-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 日期选择器特殊样式 */
    :deep(.el-date-editor) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-date-editor .el-input__inner) {
        color: #FFFFFF !important;
        background: transparent !important;
    }

    :deep(.el-range-separator) {
        color: #8291A9 !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #AED7F2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #FFFFFF !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 操作列图标按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286F3 !important;
    }

    .op-edit {
        color: #42F3E9 !important;
    }

    .op-delete {
        color: #D62121 !important;
    }
}

.loading-text {
    color: #909399;
    font-size: 12px;
}

.error-text {
    color: #f56c6c;
    font-size: 12px;
}

.inspection-lines {
    color: #606266;
    font-size: 12px;
    word-break: break-all;
    line-height: 1.4;
}

.btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .filter {
        flex: 1;
    }
    .export {
        margin-left: auto;
    }
}
</style>
