/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ApprovalRecord: typeof import('./../components/Process/approvalRecord.vue')['default']
    BigScreen: typeof import('./../components/Dashboard/ProjectMapBaidu/BigScreen.vue')['default']
    Breadcrumb: typeof import('./../components/Breadcrumb/index.vue')['default']
    ChartBox: typeof import('./../components/ChartBox/index.vue')['default']
    CodeForm: typeof import('./../components/Project/codeForm.vue')['default']
    copy: typeof import('./../components/Dashboard/ProjectMapBaidu/index copy.vue')['default']
    DictTag: typeof import('./../components/DictTag/index.vue')['default']
    Document: typeof import('./../components/Document/index.vue')['default']
    EditForm: typeof import('./../components/Project/editForm.vue')['default']
    Editor: typeof import('./../components/Editor/index.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Event: typeof import('./../components/Dashboard/Event/index.vue')['default']
    FileManager: typeof import('./../components/FileManager/index.vue')['default']
    FileUpload: typeof import('./../components/FileUpload/index.vue')['default']
    Flow: typeof import('./../components/Dashboard/Flow/index.vue')['default']
    FlowBig: typeof import('./../components/Dashboard/FlowBig/index.vue')['default']
    Hamburger: typeof import('./../components/Hamburger/index.vue')['default']
    IconSelect: typeof import('./../components/IconSelect/index.vue')['default']
    IFrame: typeof import('./../components/iFrame/index.vue')['default']
    ImageForm: typeof import('./../components/Project/imageForm.vue')['default']
    ImagePreview: typeof import('./../components/ImagePreview/index.vue')['default']
    ImageUpload: typeof import('./../components/ImageUpload/index.vue')['default']
    IndexV2: typeof import('./../components/Dashboard/ProjectMapBaidu/indexV2.vue')['default']
    LangSelect: typeof import('./../components/LangSelect/index.vue')['default']
    LineForm: typeof import('./../components/Project/lineForm.vue')['default']
    MaintainAreaForm: typeof import('./../components/Project/maintainAreaForm.vue')['default']
    ManageUnitForm: typeof import('./../components/Project/manageUnitForm.vue')['default']
    Monitor: typeof import('./../components/Dashboard/Monitor/index.vue')['default']
    MultiImageViewer: typeof import('./../components/MultiImageViewer/index.vue')['default']
    Pagination: typeof import('./../components/Pagination/index.vue')['default']
    ParentView: typeof import('./../components/ParentView/index.vue')['default']
    ProcessMeddle: typeof import('./../components/Process/processMeddle.vue')['default']
    ProjectMap: typeof import('./../components/Dashboard/ProjectMap/index.vue')['default']
    ProjectMapBaidu: typeof import('./../components/Dashboard/ProjectMapBaidu/index.vue')['default']
    RightToolbar: typeof import('./../components/RightToolbar/index.vue')['default']
    RoleSelect: typeof import('./../components/RoleSelect/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RuoYiDoc: typeof import('./../components/RuoYiDoc/index.vue')['default']
    RuoYiGit: typeof import('./../components/RuoYiGit/index.vue')['default']
    Screenfull: typeof import('./../components/Screenfull/index.vue')['default']
    SizeSelect: typeof import('./../components/SizeSelect/index.vue')['default']
    Structure: typeof import('./../components/Dashboard/Structure/index.vue')['default']
    SubmitVerify: typeof import('./../components/Process/submitVerify.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    Task: typeof import('./../components/Dashboard/Task/index.vue')['default']
    Team: typeof import('./../components/Team/index.vue')['default']
    Temperature: typeof import('./../components/Dashboard/Temperature/index.vue')['default']
    TopNav: typeof import('./../components/TopNav/index.vue')['default']
    TreeSelect: typeof import('./../components/TreeSelect/index.vue')['default']
    TunnelCategory: typeof import('./../components/TunnelCategory/index.vue')['default']
    UserSelect: typeof import('./../components/UserSelect/index.vue')['default']
    Water: typeof import('./../components/Dashboard/Water/index.vue')['default']
    Widget: typeof import('./../components/Dashboard/Widget/index.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
