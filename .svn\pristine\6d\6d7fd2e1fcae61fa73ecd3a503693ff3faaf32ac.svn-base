import request from '@/utils/request'
import { AxiosPromise } from 'axios'
import { DeviceCodeRuleVO, DeviceCodeRuleForm, DeviceCodeRuleQuery } from '@/api/common/deviceCodeRule/types'

/**
 * 查询设施设备编码规则列表
 * @param query
 * @returns {*}
 */

export const listDeviceCodeRule = (query?: DeviceCodeRuleQuery): AxiosPromise<DeviceCodeRuleVO[]> => {
    return request({
        url: '/common/deviceCodeRule/list',
        method: 'get',
        params: query
    })
}

/**
 * 查询设施设备编码规则详细
 * @param id
 */
export const getDeviceCodeRule = (id: string | number): AxiosPromise<DeviceCodeRuleVO> => {
    return request({
        url: '/common/deviceCodeRule/' + id,
        method: 'get'
    })
}

/**
 * 新增设施设备编码规则
 * @param data
 */
export const addDeviceCodeRule = (data: DeviceCodeRuleForm) => {
    return request({
        url: '/common/deviceCodeRule',
        method: 'post',
        data: data
    })
}

/**
 * 修改设施设备编码规则
 * @param data
 */
export const updateDeviceCodeRule = (data: DeviceCodeRuleForm) => {
    return request({
        url: '/common/deviceCodeRule',
        method: 'put',
        data: data
    })
}

/**
 * 删除设施设备编码规则
 * @param id
 */
export const delDeviceCodeRule = (id: string | number | Array<string | number>) => {
    return request({
        url: '/common/deviceCodeRule/' + id,
        method: 'delete'
    })
}

/**
 * 启用/禁用设施设备编码规则
 * @param id 规则ID
 * @param enabled 启用状态 Y:启用 N:禁用
 */
export const toggleDeviceCodeRuleStatus = (id: string | number, enabled: string) => {
    return request({
        url: '/common/deviceCodeRule/toggleStatus',
        method: 'put',
        data: { id, enabled }
    })
}

/**
 * 应用设施设备编码规则
 * @param ruleId 规则ID
 */
export const applyDeviceCodeRule = (ruleId: string | number) => {
    return request({
        url: '/common/deviceCodeRule/apply',
        method: 'post',
        data: { ruleId }
    })
}

/**
 * 生成设备编码
 * @param projectId 项目ID
 * @param categoryId 分类ID
 */
export const generateEquipmentCode = (projectId: string, categoryId: string) => {
    return request({
        url: '/common/deviceCodeRule/generateEquipmentCode',
        method: 'get',
        params: { projectId, categoryId }
    })
}

/**
 * 生成设施编码
 * @param projectId 项目ID
 * @param categoryId 分类ID
 */
export const generateFacilityCode = (projectId: string, categoryId: string) => {
    return request({
        url: '/common/deviceCodeRule/generateFacilityCode',
        method: 'get',
        params: { projectId, categoryId }
    })
}
