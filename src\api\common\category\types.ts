export interface CategoryVO {
    /**
     *
     */
    id: string | number;
    level?: number;
	parentId?: string | number;

    /**
     * 名称
     */
    name: string;

    /**
     * 编码
     */
    code: string;

    /**
     * 描述
     */
    description: string;

    /**
     * 重要程度-字典项：important
     */
    important: string;

    /**
     * 单位-字典项：device_unit
     */
    unit: string;
    /**
     * 专业类型-字典项：specialty（只在设施分类中出现）
     */
    specialty?: string;

	 /**
   * 分类的路径层级，每一层级在上一层的基础上+id/
   */
  path: string;
    /**
     * 子对象
     */
    children: CategoryVO[];
}

export interface CategoryForm extends BaseEntity {
    /**
     *
     */
    id?: string | number;

    /**
     *
     */
    parentId?: string | number;

    /**
     * 项目层级
     */
    level?: number;

    /**
     * 名称
     */
    name?: string;

    /**
     * 编码
     */
    code?: string;

    /**
     * 描述
     */
    description?: string;

    /**
     * 重要程度-字典项：important
     */
    important?: string;

    /**
     * 单位-字典项：device_unit
     */
    unit?: string;

    /**
     * 专业类型-字典项：specialty（只在设施分类中出现）
     */
    specialty?: string;

    /**
     * 分类的类型，设施：facility，设备分类：equipment；监测类型：monitor
     */
    kind?: string;

    /**
     * 上传的文件id列表，以逗号分隔
     */
    ossIds?: string | number;
	  /**
   * 分类的路径层级，每一层级在上一层的基础上+id/
   */
  path?: string;
}

export interface CategoryQuery {
	projectId?:string,
    /**
     * 名称
     */
    name?: string;

    /**
     * 编码
     */
    code?: string;
    kind?: string;

    /**
     * 专业类型-字典项：specialty（只在设施分类中出现）
     */
    specialty?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}
export interface TunnelTreeNode {
    id: string | number;
    name: string;
    parentId: string;
    children?: TunnelTreeNode[];
}

/**
 * 分类层级信息接口
 */
export interface CategoryHierarchyVO {
    /**
     * 当前分类
     */
    current: CategoryVO;

    /**
     * 父分类
     */
    parent?: CategoryVO;

    /**
     * 祖父分类
     */
    grandParent?: CategoryVO;
}

/**
 * 分类名称信息接口
 */
export interface CategoryNameInfo {
    /**
     * 一级分类名称
     */
    firstName: string;

    /**
     * 二级分类名称
     */
    secondName: string;

    /**
     * 三级分类名称
     */
    thirdName: string;
}
