<!-- 变更计划日期组件 -->
<template>
    <el-dialog v-model="visible" title="变更计划日期" width="500px" :draggable="true" :before-close="handleClose">
        <el-form :model="changePlanForm" :rules="changePlanRules" ref="changePlanFormRef" label-width="120px">
            <el-form-item label="计划作业日期" prop="plannedDate" required>
                <el-date-picker
                    v-model="changePlanForm.plannedDate"
                    type="date"
                    placeholder="请选择计划作业日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                />
            </el-form-item>
            <el-form-item label="变更原因" prop="changeReason" required>
                <el-input
                    v-model="changePlanForm.changeReason"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入变更原因"
                    maxlength="500"
                    show-word-limit
                />
            </el-form-item>
            <!-- 显示任务类型和处理方式 -->
            <el-form-item label="处理方式" v-if="isSpecialTask">
                <el-alert :title="`${getTaskTypeName()}任务延期将自动回退到安全交底环节`" type="info" :closable="false" show-icon />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleConfirm" :loading="loading"> 确定 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { addDelayTask } from '@/api/plan/delayTask'
import { DelayTaskForm } from '@/api/plan/delayTask/types'

// 导入各业务模块的API
import { getTaskAssignmentByTaskId as getAssignInspectionByTaskId, assignInspection } from '@/api/plan/assignInspection'
import { getTaskAssignmentByTaskId as getAssignMaintainByTaskId } from '@/api/plan/assignMaintain'
import { getTaskAssignmentByTaskId as getAssignSealingByTaskId } from '@/api/plan/assignSealing'
import { maintainAssign, sealingAssign } from '@/api/plan/task'
import { AssignInspectionFlowForm } from '@/api/plan/assignInspection/types'
import { AssignMaintainFlowForm } from '@/api/plan/assignMaintain/types'
import { AssignSealingFlowForm } from '@/api/plan/assignSealing/types'

// 定义组件的props
interface Props {
    modelValue: boolean
    projectId: string | number
    taskId: string | number
    currentDate?: string // 当前计划日期，用于初始化
    taskType?: string // 任务类型：inspect, curing, sealing
}

// 定义组件的emits
interface Emits {
    (e: 'update:modelValue', value: boolean): void
    (e: 'success', newDate: string): void // 变更成功后触发，返回新日期
    (e: 'navigate-to-list'): void // 需要跳转到列表页面时触发
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    currentDate: ''
})

const emit = defineEmits<Emits>()

// 响应式数据
const router = useRouter()
const visible = ref(false)
const loading = ref(false)
const changePlanFormRef = ref()
const changePlanForm = reactive({
    plannedDate: '',
    changeReason: ''
})

// 业务对象缓存
const businessObjects = ref<{
    inspection?: any
    maintain?: any
    sealing?: any
}>({})

// 计算属性
const isSpecialTask = computed(() => {
    return ['inspect', 'curing', 'sealing'].includes(props.taskType || '')
})

// 表单验证规则
const changePlanRules = reactive({
    plannedDate: [{ required: true, message: '请选择计划作业日期', trigger: 'change' }],
    changeReason: [
        { required: true, message: '请输入变更原因', trigger: 'blur' },
        { min: 1, message: '变更原因至少需要1个字符', trigger: 'blur' } as any
    ]
})

// 获取任务类型名称
const getTaskTypeName = () => {
    const typeMap = {
        'inspect': '巡检',
        'curing': '维养',
        'sealing': '封道'
    }
    return typeMap[props.taskType as keyof typeof typeMap] || props.taskType
}

// 监听modelValue变化
watch(
    () => props.modelValue,
    async (newVal) => {
        visible.value = newVal
        if (newVal) {
            // 初始化表单数据
            changePlanForm.plannedDate = props.currentDate || ''
            changePlanForm.changeReason = ''

            // 如果是特殊任务类型，预加载业务对象
            if (isSpecialTask.value) {
                await loadBusinessObject()
            }
        }
    }
)

// 监听visible变化
watch(visible, (newVal) => {
    emit('update:modelValue', newVal)
})

// 加载业务对象
const loadBusinessObject = async () => {
    try {
        console.log(`加载${props.taskType}业务对象，taskId: ${props.taskId}`)

        switch (props.taskType) {
            case 'inspect':
                const inspectionRes = await getAssignInspectionByTaskId(props.taskId)
                businessObjects.value.inspection = inspectionRes.data
                console.log('加载巡检业务对象成功:', inspectionRes.data)
                break

            case 'curing':
                const maintainRes = await getAssignMaintainByTaskId(props.taskId)
                businessObjects.value.maintain = maintainRes.data
                console.log('加载维养业务对象成功:', maintainRes.data)
                break

            case 'sealing':
                const sealingRes = await getAssignSealingByTaskId(props.taskId)
                businessObjects.value.sealing = sealingRes.data
                console.log('加载封道业务对象成功:', sealingRes.data)
                break
        }
    } catch (error) {
        console.error('加载业务对象失败:', error)
        ElMessage.warning('加载业务数据失败，将使用普通延期方式')
    }
}

// 关闭对话框
const handleClose = () => {
    visible.value = false
    // 重置表单
    changePlanForm.plannedDate = ''
    changePlanForm.changeReason = ''
    // 清除验证
    changePlanFormRef.value?.clearValidate()
    // 清除业务对象缓存
    businessObjects.value = {}
}

// 检查是否有业务对象
const hasBusinessObject = () => {
    switch (props.taskType) {
        case 'inspect':
            return !!businessObjects.value.inspection
        case 'curing':
            return !!businessObjects.value.maintain
        case 'sealing':
            return !!businessObjects.value.sealing
        default:
            return false
    }
}

// 使用assign操作处理延期
const processDelayWithAssign = async () => {
    const delayReason = `任务延期：${changePlanForm.changeReason}`

    switch (props.taskType) {
        case 'inspect':
            await processInspectionDelay(delayReason)
            break
        case 'curing':
            await processMaintainDelay(delayReason)
            break
        case 'sealing':
            await processSealingDelay(delayReason)
            break
    }

    ElMessage.success(`${getTaskTypeName()}任务延期处理成功，流程已回退到安全交底环节`)

    // 延期处理成功后跳转到对应的index页面
    navigateToIndexPage()
}

// 跳转到对应的index页面
const navigateToIndexPage = () => {
    let routePath = ''
    const random = new Date().getMilliseconds()

    switch (props.taskType) {
        case 'inspect':
            routePath = '/subProject/circle/inspection/list?r=' + random
            break
        case 'curing':
            routePath = '/subProject/circle/maintain/list?r=' + random
            break
        case 'sealing':
            routePath = '/subProject/circle/maintain/sealing?r=' + random
            break
        default:
            routePath = '/subProject/circle/maintain/list?r=' + random
    }

    console.log(`延期处理完成，跳转到: ${routePath}`)

    // 先通知父组件需要跳转
    emit('navigate-to-list')

    // 然后直接跳转
    router.push(routePath)
}

// 处理巡检延期
const processInspectionDelay = async (reason: string) => {
    const inspection = businessObjects.value.inspection!

    const assignData: AssignInspectionFlowForm = {
        task: {
            id: props.taskId,
            bgnDate: changePlanForm.plannedDate, // 修改开始时间
            projectId: props.projectId,
            currentStatus: inspection.task.currentStatus
        },
        assignInspection: inspection.assignInspection,
        nextAssignee: {
            wfOperation: 'ROLLBACK',
            // 不设置revokeType和targetActivityCode，由后端Controller设置
            opinion: reason
        },
        isDelay: true // 延期标识，触发后端设置回退参数
    }

    console.log('调用assignInspection延期API:', assignData)
    await assignInspection(assignData)
}

// 处理维养延期
const processMaintainDelay = async (reason: string) => {
    const maintain = businessObjects.value.maintain!

    const assignData: AssignMaintainFlowForm = {
        task: {
            id: props.taskId,
            bgnDate: changePlanForm.plannedDate,
            projectId: props.projectId,
            currentStatus: maintain.task.currentStatus
        },
        assignMaintain: maintain.assignMaintain,
        nextAssignee: {
            wfOperation: 'ROLLBACK',
            // 不设置revokeType和targetActivityCode，由后端Controller设置
            opinion: reason
        },
        isDelay: true // 延期标识
    }

    console.log('调用maintainAssign延期API:', assignData)
    await maintainAssign(assignData)
}

// 处理封道延期
const processSealingDelay = async (reason: string) => {
    const sealing = businessObjects.value.sealing!

    const assignData: AssignSealingFlowForm = {
        task: {
            id: props.taskId,
            bgnDate: changePlanForm.plannedDate,
            projectId: props.projectId,
            currentStatus: sealing.task.currentStatus
        },
        assignSealing: sealing.assignSealing,
        nextAssignee: {
            wfOperation: 'ROLLBACK',
            // 不设置revokeType和targetActivityCode，由后端Controller设置
            opinion: reason
        },
        isDelay: true // 延期标识
    }
    // 删除sealingItems字段，避免发送到后端
    delete sealing.assignSealing.sealingItems
    console.log('调用sealingAssign延期API:', assignData)
    await sealingAssign(assignData)
}

// 普通延期处理
const processDelayNormal = async () => {
    const delayTaskData: DelayTaskForm = {
        projectId: props.projectId,
        taskId: props.taskId,
        bgnDate: changePlanForm.plannedDate,
        remark: changePlanForm.changeReason
    }

    console.log('调用addDelayTask API:', delayTaskData)
    await addDelayTask(delayTaskData)

    ElMessage.success('计划日期变更成功')
}

// 确认变更计划日期
const handleConfirm = async () => {
    try {
        // 表单验证
        const valid = await changePlanFormRef.value?.validate()
        if (!valid) {
            return
        }

        loading.value = true

        if (isSpecialTask.value && hasBusinessObject()) {
            // 使用assign操作处理延期（包含工作流回退）
            await processDelayWithAssign()
        } else {
            // 使用原有的延期记录API
            await processDelayNormal()
        }

        // 触发成功事件，传递新日期
        emit('success', changePlanForm.plannedDate)
        handleClose()
    } catch (error) {
        console.error('变更计划日期失败:', error)
        ElMessage.error('变更计划日期失败，请重试')
    } finally {
        loading.value = false
    }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
    text-align: right;
}
</style>
