<!-- 任务列表（周-日视角）选择控件 -->
<template>
    <div class="task-month-list-container">
        <!-- 顶部日期选择区域 -->
        <div class="search-container">
            <div class="search-left">
                <el-date-picker
                    v-model="selectedMonth"
                    type="month"
                    format="YYYY年MM月"
                    value-format="YYYY-MM"
                    placeholder="选择年月"
                    @change="handleMonthChange"
                />
                <el-input v-model="planName" placeholder="请输入计划名称关键字" class="search-input" />
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            </div>
            <div class="search-right">
                <el-button type="primary" icon="Plus" v-if="buttonsEnabled.addWeekInspect" @click="handleAddInspection">新增巡检周计划</el-button>
                <el-button type="primary" icon="Plus" v-if="buttonsEnabled.addWeekMaintain" @click="handleAddMaintain">新增维养周计划</el-button>
                <el-button type="primary" icon="Plus" v-if="buttonsEnabled.addWeebSealing" @click="handleAddSealing">新增封道周计划</el-button>
            </div>
        </div>

        <!-- 周任务Tab页区域 -->
        <div class="week-tabs-container">
            <!-- 筛选按钮 - 绝对定位 -->
            <div class="task-filters-overlay">
                <div class="task-filters">
                    <!-- 任务类型筛选 -->
                    <div class="filter-group">
                        <div class="filter-buttons">
                            <button
                                v-for="option in taskTypeOptions"
                                :key="option.value"
                                :class="['filter-btn', { active: selectedTaskType === option.value }]"
                                @click="selectedTaskType = option.value"
                            >
                                {{ option.label }}
                            </button>
                        </div>
                    </div>

                    <!-- 任务状态筛选 -->
                    <div class="filter-group">
                        <div class="filter-buttons">
                            <button
                                v-for="option in taskStatusOptions"
                                :key="option.value"
                                :class="['filter-btn', { active: selectedTaskStatus === option.value }]"
                                @click="selectedTaskStatus = option.value"
                            >
                                {{ option.label }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tabs-header">
                <!-- Tab导航 -->
                <div class="tabs-left">
                    <el-tabs v-model="activeWeekTab" type="card" @tab-click="handleWeekChange">
                        <el-tab-pane
                            v-for="(week, weekIndex) in weeksList"
                            :key="weekIndex"
                            :label="`第${weekIndex + 1}周 ${week.startDate} 至 ${week.endDate}`"
                            :name="String(weekIndex)"
                        >
                            <!-- Loading 覆盖层 -->
                            <div v-if="loading" class="loading-overlay">
                                <el-icon class="loading-icon is-loading">
                                    <Loading />
                                </el-icon>
                                <span class="loading-text">正在加载任务数据...</span>
                            </div>

                            <!-- 周内日期列表 -->
                            <div class="days-container" :class="{ 'loading-blur': loading }">
                                <div v-for="(day, dayIndex) in week.days" :key="dayIndex" class="day-column">
                                    <div class="day-header">
                                        <span class="week-number">周{{ getDayName(dayIndex) }}</span>
                                        <span class="day-date">{{ day.date }}</span>
                                    </div>
                                    <!-- 白班任务区域 -->
                                    <div class="shift-label">白班</div>
                                    <div
                                        class="day-tasks day-shift"
                                        :data-week-index="weekIndex"
                                        :data-day-index="dayIndex"
                                        :data-shift-type="'day'"
                                        :ref="
                                            (el) => {
                                                if (el) {
                                                    if (!weekTaskRefs[weekIndex]) weekTaskRefs[weekIndex] = {}
                                                    if (!weekTaskRefs[weekIndex][dayIndex]) weekTaskRefs[weekIndex][dayIndex] = {}
                                                    weekTaskRefs[weekIndex][dayIndex]['day'] = el
                                                }
                                            }
                                        "
                                        @dragover.prevent
                                        @dragenter.prevent
                                        @drop="handleDrop($event, weekIndex, dayIndex, 'day')"
                                    >
                                        <div
                                            v-for="(task, taskIndex) in getDayTasks(day.tasks, 'day')"
                                            :key="task.id"
                                            :class="getTaskItemClass(task)"
                                            :draggable="!published"
                                            @dragstart="!published && handleDragStart($event, task, weekIndex, dayIndex, 'day')"
                                            @dragover.prevent="!published"
                                            @dragenter.prevent="!published"
                                            @drop="!published && handleDrop($event, weekIndex, dayIndex, 'day')"
                                            @contextmenu.prevent="!published && showContextMenu($event, task, weekIndex, dayIndex, 'day')"
                                            @click="handleView(task.id, task.taskType, task.taskStep)"
                                        >
                                            <!-- 只要是task状态，都是已发布 -->
                                            <div :title="task.name" class="task-content">
                                                <span v-if="task.canSplited" class="split-icon" title="可拆分">
                                                    <el-icon><Plus /></el-icon>&nbsp;
                                                </span>
                                                <span v-if="!task.canSplited" class="split-icon">
                                                    <el-icon><Minus /></el-icon>&nbsp;
                                                </span>
                                                <span
                                                    class="task-name"
                                                    :title="task.name"
                                                    :class="{
                                                        'task-name-plan': task.taskStep === 'plan',
                                                        'task-name-start': task.taskStep === 'task' && task.currentStatus === 'START',
                                                        'task-name-task': task.taskStep === 'task' && task.currentStatus !== 'START',
                                                        'task-name-complete': task.currentStatus == 'END'
                                                    }"
                                                    >{{ task.name }}</span
                                                >
                                            </div>

                                            <div class="task-icons">
                                                <!-- 班次图标 -->
                                                <div class="shift-icon" v-if="false">
                                                    <img
                                                        v-if="task.shift_type === 'day' || task.shiftType === 1 || task.shiftType === '1'"
                                                        src="@/assets/images/<EMAIL>"
                                                        alt="白班"
                                                        class="shift-img"
                                                    />
                                                    <img
                                                        v-else-if="task.shift_type === 'night' || task.shiftType === 2 || task.shiftType === '2'"
                                                        src="@/assets/images/<EMAIL>"
                                                        alt="晚班"
                                                        class="shift-img"
                                                    />
                                                </div>
                                                <!-- 可拆分状态 -->
                                                <div class="splitable-status" v-if="false">
                                                    <template v-if="task.canSplited" title="可拆分">
                                                        <img src="@/assets/images/<EMAIL>" class="status-icon" alt="可拆分" />
                                                        <span>可拆分</span>
                                                    </template>
                                                    <template v-if="!task.canSplited" title="不可拆分">
                                                        <img src="@/assets/images/<EMAIL>" class="status-icon" alt="不可拆分" />
                                                        <span>不可拆分</span>
                                                    </template>
                                                </div>
                                                <!-- 完成状态 -->
                                                <div class="completed-status">
                                                    <!-- 1. 最终状态优先：已完成 -->
                                                    <div v-if="task.currentStatus === 'END'">
                                                        <img src="@/assets/images/<EMAIL>" class="status-icon" alt="已完成" />
                                                        <span style="color: chartreuse">已完成</span>
                                                    </div>

                                                    <!-- 2. 最终状态优先：已终止 -->
                                                    <div v-else-if="task.currentStatus === 'TERMINATED'">
                                                        <img
                                                            src="@/assets/images/<EMAIL>"
                                                            class="status-icon schedulable"
                                                            alt="已终止"
                                                        />
                                                        <span style="color: red">已终止</span>
                                                    </div>

                                                    <!-- 3. 计划阶段（待发布） -->
                                                    <div v-else-if="task.taskStep === 'plan'" title="待发布">
                                                        <img src="@/assets/images/<EMAIL>" class="status-icon" alt="待发布" />
                                                        <span>待发布</span>
                                                    </div>

                                                    <!-- 4. 任务阶段（进行中，显示具体状态） -->
                                                    <div v-else-if="task.taskStep === 'task'" :title="getStatusDisplayName(task.currentStatus)">
                                                        <img src="@/assets/images/<EMAIL>" class="status-icon" alt="可排班" />
                                                        <span>{{ getStatusDisplayName(task.currentStatus) }}</span>
                                                    </div>

                                                    <!-- 5. 兜底状态 -->
                                                    <div v-else title="未知状态">
                                                        <img src="@/assets/images/<EMAIL>" class="status-icon" alt="未知" />
                                                        <span style="color: gray">未知状态</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 晚班任务区域 -->
                                    <div class="shift-label">晚班</div>
                                    <div
                                        class="day-tasks night-shift"
                                        :data-week-index="weekIndex"
                                        :data-day-index="dayIndex"
                                        :data-shift-type="'night'"
                                        :ref="
                                            (el) => {
                                                if (el) {
                                                    if (!weekTaskRefs[weekIndex]) weekTaskRefs[weekIndex] = {}
                                                    if (!weekTaskRefs[weekIndex][dayIndex]) weekTaskRefs[weekIndex][dayIndex] = {}
                                                    weekTaskRefs[weekIndex][dayIndex]['night'] = el
                                                }
                                            }
                                        "
                                        @dragover.prevent
                                        @dragenter.prevent
                                        @drop="handleDrop($event, weekIndex, dayIndex, 'night')"
                                    >
                                        <div
                                            v-for="(task, taskIndex) in getDayTasks(day.tasks, 'night')"
                                            :key="task.id"
                                            :class="getTaskItemClass(task)"
                                            :draggable="!published"
                                            @dragstart="!published && handleDragStart($event, task, weekIndex, dayIndex, 'night')"
                                            @dragover.prevent="!published"
                                            @dragenter.prevent="!published"
                                            @drop="!published && handleDrop($event, weekIndex, dayIndex, 'night')"
                                            @contextmenu.prevent="!published && showContextMenu($event, task, weekIndex, dayIndex, 'night')"
                                            @click="handleView(task.id, task.taskType, task.taskStep)"
                                        >
                                            <!-- 只要是task状态，都是已发布 -->
                                            <div>
                                                <span v-if="task.canSplited" class="split-icon" title="可拆分">
                                                    <el-icon><Plus /></el-icon>&nbsp;
                                                </span>
                                                <span v-if="!task.canSplited" class="split-icon">
                                                    <el-icon><Minus /></el-icon>&nbsp;
                                                </span>
                                                <span
                                                    class="task-name"
                                                    :title="task.name"
                                                    :class="{
                                                        'task-name-plan': task.taskStep === 'plan',
                                                        'task-name-start': task.taskStep === 'task' && task.currentStatus === 'START',
                                                        'task-name-task': task.taskStep === 'task' && task.currentStatus !== 'START',
                                                        'task-name-complete': task.taskStep === 'task' && task.currentStatus == 'END'
                                                    }"
                                                    >{{ task.name }}</span
                                                >
                                            </div>
                                            <div class="task-icons">
                                                <!-- 班次图标 -->
                                                <div class="shift-icon" v-if="false">
                                                    <img
                                                        v-if="task.shift_type === 'day' || task.shiftType === 1 || task.shiftType === '1'"
                                                        src="@/assets/images/<EMAIL>"
                                                        alt="白班"
                                                        class="shift-img"
                                                    />
                                                    <img
                                                        v-else-if="task.shift_type === 'night' || task.shiftType === 2 || task.shiftType === '2'"
                                                        src="@/assets/images/<EMAIL>"
                                                        alt="晚班"
                                                        class="shift-img"
                                                    />
                                                </div>
                                                <!-- 可拆分状态 -->
                                                <div class="splitable-status" v-if="false">
                                                    <template v-if="task.canSplited" title="可拆分">
                                                        <img
                                                            src="@/assets/images/<EMAIL>"
                                                            class="status-icon schedulable"
                                                            alt="可拆分"
                                                        />
                                                        <span>可拆分</span>
                                                    </template>
                                                    <template v-if="!task.canSplited" title="不可拆分">
                                                        <img src="@/assets/images/<EMAIL>" class="status-icon" alt="不可拆分" />
                                                        <span>不可拆分</span>
                                                    </template>
                                                </div>
                                                <!-- 完成状态 -->
                                                <div class="completed-status">
                                                    <!-- 1. 最终状态优先：已完成 -->
                                                    <div v-if="task.currentStatus === 'END'">
                                                        <img
                                                            src="@/assets/images/<EMAIL>"
                                                            class="status-icon schedulable"
                                                            alt="已完成"
                                                        />
                                                        <span style="color: chartreuse">已完成</span>
                                                    </div>

                                                    <!-- 2. 最终状态优先：已终止 -->
                                                    <div v-else-if="task.currentStatus === 'TERMINATED'">
                                                        <img
                                                            src="@/assets/images/<EMAIL>"
                                                            class="status-icon schedulable"
                                                            alt="已终止"
                                                        />
                                                        <span style="color: red">已终止</span>
                                                    </div>

                                                    <!-- 3. 计划阶段（待发布） -->
                                                    <div v-else-if="task.taskStep === 'plan'" title="待发布">
                                                        <img
                                                            src="@/assets/images/<EMAIL>"
                                                            class="status-icon schedulable"
                                                            alt="待发布"
                                                        />
                                                        <span>待发布</span>
                                                    </div>

                                                    <!-- 4. 任务阶段（进行中，显示具体状态） -->
                                                    <div v-else-if="task.taskStep === 'task'" :title="getStatusDisplayName(task.currentStatus)">
                                                        <img src="@/assets/images/<EMAIL>" class="status-icon schedulable" />
                                                        <span>{{ getStatusDisplayName(task.currentStatus) }}</span>
                                                    </div>

                                                    <!-- 5. 兜底状态 -->
                                                    <div v-else title="未知状态">
                                                        <img
                                                            src="@/assets/images/<EMAIL>"
                                                            class="status-icon schedulable"
                                                            alt="未知"
                                                        />
                                                        <span style="color: gray">未知状态</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
        </div>
        <div class="btn-container">
            <div class="publishState" :class="{ 'blue': published }">
                <span style="margin-right: 20px">发布状态：{{ published ? '已发布' : '未发布' }}</span>
                <el-button
                    type="primary"
                    style="width: 100px"
                    @click="handlePublish"
                    v-if="buttonsEnabled.publish"
                    :loading="isPublishing"
                    :disabled="isPublishing"
                >
                    {{ isPublishing ? '发布中...' : '发布' }}
                </el-button>
                <el-button type="primary" style="width: 100px; margin-left: 10px" @click="handleBatchBriefing" v-if="buttonsEnabled.batchBriefing"
                    >批量交底</el-button
                >
            </div>
        </div>
        <div class="remark">
            <ul>
                <!-- <li>说明：</li> -->

                <li>【拆分说明】：月计划发布之前进行拆分，" + "表示可拆分， " - "表示不可拆分；</li>
            </ul>
        </div>
        <!-- 右键菜单 -->
        <div v-show="contextMenuData.visible" class="context-menu" :style="{ left: contextMenuData.x + 'px', top: contextMenuData.y + 'px' }">
            <div v-if="contextMenuData.task && contextMenuData.task.canSplited" class="menu-item" @click="openSplitDialog">拆分</div>
        </div>

        <!-- 计划拆分组件 -->
        <PlanSplit ref="planSplitRef" @refresh="handleQuery" @split-success="handleSplitSuccess" />

        <!-- 批量交底对话框 -->
        <el-dialog title="批量交底" v-model="batchBriefingDialog.visible" width="1000px" append-to-body>
            <TaskListSeV2
                ref="taskListSelRef"
                :status-config="statusConfig"
                :project-id="appStore.projectContext.selectedProjectId"
                :date-filter="currentWeekDateFilter"
                :operation-type="'批量交底'"
                @selection-change="handleDialogSelectionChange"
                @confirm="handleDialogConfirm"
                @cancel="handleDialogCancel"
            />
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Loading, Check, Close, CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { listAllTaskWeekView } from '@/api/plan/taskWeekView/index'
import { updateTask, publishTask } from '@/api/plan/task/index'
import { getDicts } from '@/api/system/dict/data/index'
import { useAppStore } from '@/store/modules/app'
import AddMaintian from '../plan/year/addMaintian.vue'
import PlanSplit from './TaskMonthSpliter/index.vue'
import TaskListSeV2 from './TaskListSeV2.vue'
import type { TaskSplitResult } from '@/api/plan/task/types'
import type { DictDataVO } from '@/api/system/dict/data/types'

const appStore = useAppStore()
const route = useRoute()
const router = useRouter()
// 发布状态,true-已发布，false-未发布,只有未发布状态的任务可拆分和拖拽排序
// 全部项都是发布状态时，发布状态为true，否则为false
const published = ref(false)
// 发布按钮加载状态，防止重复点击
const isPublishing = ref(false)
// 当前选择的月份
const currentYear = new Date().getFullYear()
const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0')
const selectedMonth = ref(`${currentYear}-${currentMonth}`)

// 搜索条件
const planName = ref('')
const frequency = ref<number>()

// 当前激活的周Tab
const activeWeekTab = ref('0')

// 周列表数据
const weeksList = ref([])

// 加载状态
const loading = ref(false)

// 任务状态字典数据
const taskStatusDict = ref<DictDataVO[]>([])

// 筛选条件
const selectedTaskType = ref('all')
const selectedTaskStatus = ref('all')

// 任务类型筛选选项
const taskTypeOptions = [
    { label: '全部', value: 'all' },
    { label: '维养', value: 'curing' },
    { label: '维修', value: 'repair' },
    { label: '巡检', value: 'inspect' },
    { label: '封道', value: 'sealing' }
]

// 任务状态筛选选项
const taskStatusOptions = [
    { label: '全部', value: 'all' },
    { label: '已完成', value: 'END' },
    { label: '未完成', value: 'not_end' }
]

// 周任务DOM引用
const weekTaskRefs = ref({})

// 拆分组件引用
const planSplitRef = ref()

// 任务列表选择组件引用
const taskListSelRef = ref()

// 批量交底对话框状态
const batchBriefingDialog = ref({
    visible: false
})

// 状态配置
const statusConfig = ref({
    filterStatus: '',
    updateStatus: ''
})

// 拖拽数据
const dragData = reactive({
    task: null,
    sourceWeekIndex: -1,
    sourceDayIndex: -1,
    sourceShiftType: null // 新增：源班次类型（白班/晚班）
})
const buttonsEnabled = ref({
    publish: appStore.judgePermissionEnabled('weekplan:publish'),
    split: appStore.judgePermissionEnabled('weekplan:split'),
    addWeekMaintain: appStore.judgePermissionEnabled('weekplan:curing:add'),
    addWeekInspect: appStore.judgePermissionEnabled('weekplan:inspect:add'),
    addWeebSealing: appStore.judgePermissionEnabled('weekplan:sealing:add'),
    batchBriefing: appStore.judgePermissionEnabled('weekplan:safetybriefing'), //@todo 要绑定正确的权限 appStore.judgePermissionEnabled('weekplan:batch:briefing')
    arrange: appStore.judgePermissionEnabled('weekplan:arrange')
})
// 右键菜单数据
const contextMenuData = reactive({
    visible: false,
    x: 0,
    y: 0,
    task: null,
    weekIndex: -1,
    dayIndex: -1,
    shiftType: null // 新增：班次类型（白班/晚班）
})

// 获取指定班次的任务列表
const getDayTasks = (tasks, shiftType) => {
    return tasks.filter((task) => {
        // 班次筛选
        if (task.shift_type !== shiftType) return false

        // 任务类型筛选
        if (selectedTaskType.value !== 'all') {
            // 特殊处理：当选择维修(repair)时，过滤taskSubType为'repair'的任务
            if (selectedTaskType.value === 'repair') {
                if (task.taskSubType !== 'repair') {
                    return false
                }
            } else {
                // 其他类型照旧处理
                if (task.taskType !== selectedTaskType.value) {
                    return false
                }
            }
        }

        // 任务状态筛选
        if (selectedTaskStatus.value !== 'all') {
            if (selectedTaskStatus.value === 'END' && task.currentStatus !== 'END') {
                return false
            }
            if (selectedTaskStatus.value === 'not_end' && task.currentStatus === 'END') {
                return false
            }
        }

        return true
    })
}

// 获取任务状态显示名称
const getStatusDisplayName = (statusCode: string) => {
    const statusItem = taskStatusDict.value.find((item) => item.dictValue === statusCode)
    return statusItem ? statusItem.dictLabel : statusCode
}

// 计算任务项的样式类
const getTaskItemClass = computed(() => {
    return (task) => {
        const classes = ['task-item']

        // 根据任务类型添加背景色样式
        if (task.taskType === 'inspect') {
            classes.push('inspect')
        } else if (task.taskType === 'sealing') {
            classes.push('sealing')
        }
        // taskType === 'curing' 维养任务使用默认样式，不需要额外添加类

        // 添加班次样式（根据shift_type字段判断）
        if (task.shift_type === 'day' || task.shiftType === 1 || task.shiftType === '1') {
            classes.push('day-shift')
        } else if (task.shift_type === 'night' || task.shiftType === 2 || task.shiftType === '2') {
            classes.push('night-shift')
        }

        // 保持原有的disabled样式
        if (published.value) {
            classes.push('disabled-task')
        }

        return classes.join(' ')
    }
})

// 获取星期几的名称
const getDayName = (dayIndex) => {
    //const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    const dayNames = ['一', '二', '三', '四', '五', '六', '日']
    return dayNames[dayIndex]
}

// 根据日期计算天索引（0-6，对应周一到周日）
const getDayIndex = (taskDate) => {
    if (!taskDate) return 0

    // 如果传入的是字符串，创建Date对象
    const date = new Date(taskDate)

    // JavaScript的getDay()返回：0(周日), 1(周一), 2(周二), ..., 6(周六)
    // 我们需要转换为：0(周一), 1(周二), ..., 5(周六), 6(周日)
    const jsDay = date.getDay()

    // 转换逻辑：周一(1) -> 0, 周二(2) -> 1, ..., 周六(6) -> 5, 周日(0) -> 6
    return jsDay === 0 ? 6 : jsDay - 1
}

// 根据任务日期计算正确的周索引
const calculateCorrectWeekIndex = (taskDate, weeksList) => {
    if (!taskDate || !weeksList || weeksList.length === 0) return -1

    // 遍历所有周，查找包含该日期的周
    for (let weekIndex = 0; weekIndex < weeksList.length; weekIndex++) {
        const week = weeksList[weekIndex]
        for (let dayIndex = 0; dayIndex < week.days.length; dayIndex++) {
            if (week.days[dayIndex].fullDate === taskDate) {
                return weekIndex
            }
        }
    }
    return -1 // 未找到对应的周
}

// 根据任务ID查找任务对象
const findTaskById = (taskId: string | number) => {
    for (const week of weeksList.value) {
        for (const day of week.days) {
            const task = day.tasks.find((t) => t.id === taskId)
            if (task) {
                return task
            }
        }
    }
    return null
}

// 从日期时间字符串中提取时间部分
const extractTimeFromDateTime = (dateTimeStr: string) => {
    if (!dateTimeStr) return '00:00:00'

    // 处理各种可能的日期时间格式
    const dateTime = new Date(dateTimeStr)
    if (isNaN(dateTime.getTime())) {
        // 如果无法解析，返回默认时间
        return '00:00:00'
    }

    // 格式化时间为 HH:mm:ss
    const hours = String(dateTime.getHours()).padStart(2, '0')
    const minutes = String(dateTime.getMinutes()).padStart(2, '0')
    const seconds = String(dateTime.getSeconds()).padStart(2, '0')

    return `${hours}:${minutes}:${seconds}`
}
const handleAddInspection = () => {
    // 跳转到新增巡检页面
    router.push({
        path: '/subProject/circle/plan/addInspection',
        query: { isTemp: '1' }
    })
}
const handleAddMaintain = () => {
    // 跳转到新增维养页面
    router.push({
        path: '/subProject/circle/plan/addMaintian',
        query: { isTemp: '1' }
    })
}
const handleAddSealing = () => {
    // 跳转到新增封道页面
    router.push({
        path: '/subProject/circle/plan/addSealing',
        query: { isTemp: '1' }
    })
}

// 批量交底按钮处理
const handleBatchBriefing = () => {
    // 设置状态配置（与维养任务列表中的逻辑一致）
    statusConfig.value = {
        filterStatus: 'Safety_Briefing', // 筛选"安全交底"状态的任务
        updateStatus: 'Execute' // 目标状态为"执行"
    }

    // 打开对话框
    batchBriefingDialog.value.visible = true

    // 等待对话框渲染完成后加载数据
    nextTick(() => {
        console.log('TaskWeekListSel - 准备调用TaskListSeV2的getList方法')
        console.log('TaskWeekListSel - 传递的日期过滤器:', currentWeekDateFilter.value)
        if (taskListSelRef.value) {
            taskListSelRef.value.getList()
        }
    })
}

// 处理对话框选择变化
const handleDialogSelectionChange = (selectedIds: (string | number)[]) => {
    console.log('选中的任务ID:', selectedIds)
}

// 处理对话框确认
const handleDialogConfirm = async (data: any) => {
    try {
        if (data.success) {
            ElMessage.success(`批量交底操作成功，共处理 ${data.ids.length} 条记录`)
            batchBriefingDialog.value.visible = false
            // 刷新当前周的任务数据
            await loadWeekTasks()
        } else {
            ElMessage.error('批量交底操作失败')
        }
    } catch (error) {
        console.error('批量交底操作处理失败:', error)
        ElMessage.error('批量交底操作处理失败')
    }
}

// 处理对话框取消
const handleDialogCancel = () => {
    batchBriefingDialog.value.visible = false
}

// 计算当前日期所属的周索引
const getCurrentWeekIndex = (yearMonth) => {
    const today = new Date()
    const [year, month] = yearMonth.split('-').map(Number)

    // 检查当前日期是否在选择的月份内
    if (today.getFullYear() !== year || today.getMonth() + 1 !== month) {
        return 0 // 如果不是当前月份，默认返回第一周
    }

    // 计算当月的周数据
    const weeks = calculateWeeks(yearMonth)

    // 格式化今天的日期为 YYYY-MM-DD 格式
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`

    // 查找今天所属的周
    for (let weekIndex = 0; weekIndex < weeks.length; weekIndex++) {
        const week = weeks[weekIndex]
        for (let dayIndex = 0; dayIndex < week.days.length; dayIndex++) {
            if (week.days[dayIndex].fullDate === todayStr) {
                return weekIndex
            }
        }
    }

    return 0 // 如果没找到，默认返回第一周
}

// 获取指定周的日期范围
const getWeekDateRange = (weekIndex) => {
    if (!weeksList.value || !weeksList.value[weekIndex]) {
        return { startTaskDate: null, endTaskDate: null }
    }

    const week = weeksList.value[weekIndex]
    return {
        startTaskDate: week.days[0].fullDate, // 周一日期 YYYY-MM-DD
        endTaskDate: week.days[6].fullDate // 周日日期 YYYY-MM-DD
    }
}

// 当前周的日期过滤器
const currentWeekDateFilter = computed(() => {
    const currentWeekIndex = parseInt(activeWeekTab.value)
    const { startTaskDate, endTaskDate } = getWeekDateRange(currentWeekIndex)

    if (!startTaskDate || !endTaskDate) {
        return {
            startTaskDate: '',
            endTaskDate: ''
        }
    }

    // 计算实际的查询结束日期（结束日期+1天，确保包含结束日期当天的所有任务）
    // 与generateApiTasks方法保持一致
    const endDate = new Date(endTaskDate)
    endDate.setDate(endDate.getDate() + 1)
    const actualEndTaskDate = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`

    console.log('currentWeekDateFilter', startTaskDate, endTaskDate, actualEndTaskDate)

    return {
        startTaskDate,
        endTaskDate: actualEndTaskDate
    }
})

// 计算当月的周数和日期范围
const calculateWeeks = (yearMonth) => {
    const [year, month] = yearMonth.split('-').map(Number)
    const firstDay = new Date(year, month - 1, 1)
    const lastDay = new Date(year, month, 0)

    // 获取当月第一天是星期几 (0-6, 0是星期日)
    const firstDayOfWeek = firstDay.getDay()

    // 计算第一周的开始日期（如果第一天不是周一，则向后推到当月第一个周一）
    const firstWeekStart = new Date(firstDay)
    // 调整到当月第一个周一 (1是周一)
    // 如果第一天是周一(1)，不调整；否则向后推到第一个周一
    const adjust = firstDayOfWeek === 1 ? 0 : (8 - firstDayOfWeek) % 7
    firstWeekStart.setDate(firstWeekStart.getDate() + adjust)

    const weeks = []
    const currentDate = new Date(firstWeekStart)

    // 生成周数据，直到超过当月最后一天
    while (currentDate <= lastDay || weeks.length === 0) {
        const weekStartDate = new Date(currentDate)
        const weekEndDate = new Date(currentDate)
        weekEndDate.setDate(weekEndDate.getDate() + 6)

        // 格式化日期为 MM-DD 格式
        const formatDate = (date) => {
            return `${date.getMonth() + 1}-${date.getDate()}`
        }

        // 创建每周的日期数组（周一到周日）
        const days = []
        for (let i = 0; i < 7; i++) {
            const dayDate = new Date(weekStartDate)
            dayDate.setDate(dayDate.getDate() + i)
            days.push({
                date: formatDate(dayDate),
                fullDate: `${year}-${String(dayDate.getMonth() + 1).padStart(2, '0')}-${String(dayDate.getDate()).padStart(2, '0')}`,
                tasks: [] // 初始化空任务列表
            })
        }

        weeks.push({
            startDate: formatDate(weekStartDate),
            endDate: formatDate(weekEndDate),
            days: days
        })

        // 移动到下一周的周一
        currentDate.setDate(currentDate.getDate() + 7)
    }

    return weeks
}

const generateApiTasks = async (yearMonth) => {
    const projectId = appStore.projectContext.selectedProjectId
    const currentWeekIndex = parseInt(activeWeekTab.value)

    // 获取当前选中周的日期范围
    const { startTaskDate, endTaskDate } = getWeekDateRange(currentWeekIndex)

    if (!startTaskDate || !endTaskDate) {
        console.warn('无法获取周日期范围')
        return []
    }

    // 计算实际的查询结束日期（结束日期+1天，确保包含结束日期当天的所有任务）
    const endDate = new Date(endTaskDate)
    endDate.setDate(endDate.getDate() + 1)
    const actualEndTaskDate = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`

    // 构建查询参数
    const queryParams: any = {
        projectId: projectId,
        year: parseInt(yearMonth.split('-')[0]),
        month: parseInt(yearMonth.split('-')[1]),
        // 不传递 week 参数，改用任务日期范围
        // 使用 YYYY-MM-DD 格式，配合后端 @JsonFormat(pattern = "yyyy-MM-dd") 注解
        // 结束日期+1天确保包含结束日期当天的所有任务
        startTaskDate: startTaskDate, // 任务开始日期
        endTaskDate: actualEndTaskDate // 任务结束日期+1天
    }

    // 如果计划名称不为空，则添加到查询参数
    if (planName.value && planName.value.trim()) {
        queryParams.name = planName.value.trim()
    }

    // 如果频次不为空，则添加到查询参数
    if (frequency.value && frequency.value > 0) {
        queryParams.frequency = frequency.value
    }

    const res = await listAllTaskWeekView(queryParams)
    // @todo getRelatedTasksBySealing是封道任务的关联任务列表
    // 方法已经实现，尚未绑定到界面显示
    //getRelatedTasksBySealing(queryParams)

    //由于数据库里白班、晚班用1和2表示，所以需要转换为day和night
    const shiftTypes = ['day', 'day', 'night'] // 白班和晚班
    const tasks = []

    if (res.data && Array.isArray(res.data)) {
        // 如果有一项未发布，则发布状态为false
        let isPublished = true
        let checked = false
        res.data.forEach((item, index) => {
            // 将taskDate转换为Date对象，然后格式化为YYYY-MM-DD
            const taskDateObj = new Date(item.taskDate)
            const formattedDate = `${taskDateObj.getFullYear()}-${String(taskDateObj.getMonth() + 1).padStart(2, '0')}-${String(taskDateObj.getDate()).padStart(2, '0')}`

            const task = {
                id: item.id,
                name: item.name + '（' + item.frequency + '次/' + item.frequencyType + '）',
                week: item.week - 1, // 所在周索引，服务端返回的week从1开始，前端从0开始
                day: getDayIndex(formattedDate), // 所在天索引（0-6，对应周一到周日）
                sort: item.sort, // 排序号
                taskType: item.taskType,
                taskSubType: item.taskTypeSub,
                date: formattedDate, // 具体日期，格式化为YYYY-MM-DD
                shift_type: shiftTypes[parseInt(item.shiftType)], // 班次类型：day(白班)/night(晚班)
                publishState: item.publishState, // 发布状态
                currentStatus: item.currentStatus, // 任务状态
                taskStep: item.taskStep, // 任务阶段：plan或task
                bgnDate: item.bgnDate, // 开始时间
                endDate: item.endDate, // 结束时间
                canSplited: Boolean(item.splitable === 1 || item.splitable === true) && item.taskStep == 'plan', // 从API获取拆分状态
                defineId: item.defineId // 添加defineId字段，拆分时需要
            }
            checked = true
            if (item.publishState != 'published') {
                isPublished = false
            }

            tasks.push(task)
        })

        if (checked) {
            published.value = isPublished
        }
    }
    console.log('最终生成的tasks数组:', tasks)
    return tasks
}

const handleWeekChange = async () => {
    console.log('handleWeekChange', 'activeWeekTab:', activeWeekTab.value)
    loading.value = true
    try {
        await loadWeekTasks()
    } finally {
        loading.value = false
    }
}

// 加载当前周的任务数据
const loadWeekTasks = async () => {
    if (!selectedMonth.value) return

    // 添加短暂延迟以便看到loading效果（测试用，生产环境可移除）
    await new Promise((resolve) => setTimeout(resolve, 500))

    const currentWeekIndex = parseInt(activeWeekTab.value)

    // 清空当前周的任务数据
    if (weeksList.value[currentWeekIndex]) {
        weeksList.value[currentWeekIndex].days.forEach((day) => {
            day.tasks = []
        })
    }

    // 获取当前周的任务数据
    const tasks = await generateApiTasks(selectedMonth.value)

    // 将任务分配到对应的周和日 - 以实际日期为准重新计算周索引
    tasks.forEach((task) => {
        // 根据任务的实际日期计算正确的周索引，不依赖后端可能有误的week字段
        const correctWeekIndex = calculateCorrectWeekIndex(task.date, weeksList.value)
        const dayIndex = task.day

        if (correctWeekIndex >= 0 && correctWeekIndex < weeksList.value.length && dayIndex >= 0 && dayIndex < 7) {
            // 更新任务的周索引为正确的值
            task.week = correctWeekIndex
            weeksList.value[correctWeekIndex].days[dayIndex].tasks.push(task)

            //console.log(`任务 ${task.name} 日期 ${task.date} 分配到第${correctWeekIndex + 1}周第${dayIndex + 1}天`)
        } else {
            console.warn(`任务 ${task.name} 日期 ${task.date} 无法找到对应的周，跳过分配`)
        }
    })

    // 按排序号排序当前周每天的任务
    if (weeksList.value[currentWeekIndex]) {
        weeksList.value[currentWeekIndex].days.forEach((day) => {
            day.tasks.sort((a, b) => a.sort - b.sort)
        })
    }
}

// 处理月份变更
const handleMonthChange = async () => {
    if (!selectedMonth.value) return

    loading.value = true
    try {
        // 计算周列表
        weeksList.value = calculateWeeks(selectedMonth.value)

        // 设置默认激活的Tab为当前日期所属的周，如果不是当前月份则为第一周
        const currentWeekIndex = getCurrentWeekIndex(selectedMonth.value)
        activeWeekTab.value = String(currentWeekIndex)

        // 加载当前周的任务数据
        await loadWeekTasks()

        // 等待DOM更新后初始化拖拽
        nextTick(() => {
            initDragEvents()
        })
    } finally {
        loading.value = false
    }
}

// 初始化拖拽事件
const initDragEvents = () => {
    // 拖拽事件已在模板中通过@dragstart等指令绑定
}

// 处理拖拽开始
const handleDragStart = (event, task, weekIndex, dayIndex, shiftType) => {
    if (!buttonsEnabled.value.arrange) return
    dragData.task = task
    dragData.sourceWeekIndex = weekIndex
    dragData.sourceDayIndex = dayIndex
    dragData.sourceShiftType = shiftType

    // 设置拖拽数据
    event.dataTransfer.setData(
        'text/plain',
        JSON.stringify({
            taskId: task.id,
            sourceWeekIndex: weekIndex,
            sourceDayIndex: dayIndex,
            sourceShiftType: shiftType
        })
    )

    // 设置拖拽效果
    event.dataTransfer.effectAllowed = 'move'
}

// 处理拖拽放置
const handleDrop = async (event, targetWeekIndex, targetDayIndex, targetShiftType) => {
    event.preventDefault()

    if (!dragData.task) return

    const sourceWeekIndex = dragData.sourceWeekIndex
    const sourceDayIndex = dragData.sourceDayIndex
    const sourceShiftType = dragData.sourceShiftType
    const task = dragData.task

    // 如果是同一天同一班次内拖拽，只改变排序
    if (sourceWeekIndex === targetWeekIndex && sourceDayIndex === targetDayIndex && sourceShiftType === targetShiftType) {
        // 获取目标位置的任务索引
        const targetElement = event.target.closest('.task-item')
        if (targetElement) {
            const taskElements = Array.from(weekTaskRefs.value[targetWeekIndex][targetDayIndex][targetShiftType].querySelectorAll('.task-item'))
            const targetIndex = taskElements.indexOf(targetElement)

            // 获取当前班次的任务
            const shiftTasks = getDayTasks(weeksList.value[targetWeekIndex].days[targetDayIndex].tasks, targetShiftType)
            const allTasks = [...weeksList.value[targetWeekIndex].days[targetDayIndex].tasks]
            const sourceIndex = shiftTasks.findIndex((t) => t.id === task.id)

            if (sourceIndex !== -1) {
                // 找到任务在所有任务中的索引
                const taskIndexInAll = allTasks.findIndex((t) => t.id === task.id)
                const [movedTask] = allTasks.splice(taskIndexInAll, 1)

                // 计算插入位置
                let insertIndex = taskIndexInAll
                if (targetIndex !== -1) {
                    // 找到目标任务在所有任务中的位置
                    const targetTask = shiftTasks[targetIndex]
                    insertIndex = allTasks.findIndex((t) => t.id === targetTask.id)
                    if (insertIndex === -1) insertIndex = allTasks.length
                }

                // 插入任务
                allTasks.splice(insertIndex, 0, movedTask)

                // 保存原始排序号，用于比较变化
                const originalSortOrders = allTasks.map((t) => ({ id: t.id, originalSort: t.sort }))

                // 更新排序号
                allTasks.forEach((t, index) => {
                    t.sort = index + 1
                })

                // 输出排序变化的任务
                const changedTasks = allTasks.filter((t, index) => {
                    const original = originalSortOrders.find((o) => o.id === t.id)
                    return original && original.originalSort !== t.sort
                })

                if (changedTasks.length > 0) {
                    console.log(
                        '同班次内任务排序变化：',
                        changedTasks.map((t) => ({
                            id: t.id,
                            name: t.name,
                            newSort: t.sort
                        }))
                    )

                    // 批量更新排序变化的任务
                    for (const changedTask of changedTasks) {
                        await updateTaskSort(changedTask.id, null, null, null, changedTask.sort)
                    }
                }

                weeksList.value[targetWeekIndex].days[targetDayIndex].tasks = allTasks
            }
        }
    } else {
        // 跨天或跨班次拖拽
        // 从源天移除任务
        const sourceDayTasks = [...weeksList.value[sourceWeekIndex].days[sourceDayIndex].tasks]
        const taskIndex = sourceDayTasks.findIndex((t) => t.id === task.id)

        if (taskIndex !== -1) {
            const [movedTask] = sourceDayTasks.splice(taskIndex, 1)
            const originalWeek = movedTask.week
            const originalDay = movedTask.day
            const originalShift = movedTask.shift_type
            const originalSort = movedTask.sort

            weeksList.value[sourceWeekIndex].days[sourceDayIndex].tasks = sourceDayTasks

            // 更新任务的周数、天数和班次
            movedTask.week = targetWeekIndex
            movedTask.day = targetDayIndex
            movedTask.shift_type = targetShiftType
            movedTask.date = weeksList.value[targetWeekIndex].days[targetDayIndex].fullDate

            // 添加到目标天的指定位置
            const targetDayTasks = [...weeksList.value[targetWeekIndex].days[targetDayIndex].tasks]
            const targetShiftTasks = getDayTasks(targetDayTasks, targetShiftType)

            // 获取目标位置的任务索引
            let insertIndex = targetDayTasks.length // 默认添加到末尾

            // 检查目标班次是否有任务项
            if (targetShiftTasks.length > 0) {
                const targetElement = event.target.closest('.task-item')
                if (targetElement) {
                    const taskElements = Array.from(
                        weekTaskRefs.value[targetWeekIndex][targetDayIndex][targetShiftType].querySelectorAll('.task-item')
                    )
                    const targetIndex = taskElements.indexOf(targetElement)
                    if (targetIndex !== -1) {
                        // 找到目标任务在所有任务中的位置
                        const targetTask = targetShiftTasks[targetIndex]
                        insertIndex = targetDayTasks.findIndex((t) => t.id === targetTask.id)
                        if (insertIndex === -1) insertIndex = targetDayTasks.length
                    }
                }
            }

            // 在指定位置插入任务
            targetDayTasks.splice(insertIndex, 0, movedTask)

            // 更新排序号
            targetDayTasks.forEach((t, index) => {
                t.sort = index + 1
            })

            weeksList.value[targetWeekIndex].days[targetDayIndex].tasks = targetDayTasks

            // 输出跨天或跨班次移动的任务信息
            console.log('任务移动：', {
                id: movedTask.id,
                name: movedTask.name,
                fromWeek: originalWeek,
                fromDay: originalDay,
                fromShift: originalShift,
                toWeek: movedTask.week,
                toDay: movedTask.day,
                toShift: movedTask.shift_type,
                fromSort: originalSort,
                toSort: movedTask.sort
            })

            // 更新移动任务的位置和排序
            await updateTaskSort(movedTask.id, movedTask.week, movedTask.day, movedTask.shift_type, movedTask.sort)

            // 保存源天任务的原始排序号
            const originalSortOrders = sourceDayTasks.map((t) => ({ id: t.id, originalSort: t.sort }))

            // 更新源天的排序号
            sourceDayTasks.forEach((t, index) => {
                t.sort = index + 1
            })

            // 输出源天排序变化的任务
            const changedSourceTasks = sourceDayTasks.filter((t, index) => {
                const original = originalSortOrders.find((o) => o.id === t.id)
                return original && original.originalSort !== t.sort
            })

            // 批量更新源天排序变化的任务
            if (changedSourceTasks.length > 0) {
                console.log(
                    '源天任务排序变化：',
                    changedSourceTasks.map((t) => ({
                        id: t.id,
                        name: t.name,
                        newSort: t.sort
                    }))
                )

                for (const changedTask of changedSourceTasks) {
                    await updateTaskSort(changedTask.id, null, null, null, changedTask.sort)
                }
            }
        }
    }

    // 清除拖拽数据
    dragData.task = null
    dragData.sourceWeekIndex = -1
    dragData.sourceDayIndex = -1
    dragData.sourceShiftType = null
}

// 显示右键菜单
const showContextMenu = (event, task, weekIndex, dayIndex, shiftType) => {
    // 只有可拆分的任务才显示右键菜单
    if (task.canSplited && buttonsEnabled.value.split) {
        contextMenuData.visible = true
        contextMenuData.x = event.clientX
        contextMenuData.y = event.clientY
        contextMenuData.task = task
        contextMenuData.weekIndex = weekIndex
        contextMenuData.dayIndex = dayIndex
        contextMenuData.shiftType = shiftType

        // 添加全局点击事件，用于关闭右键菜单
        document.addEventListener('click', closeContextMenu)
    }
}

// 关闭右键菜单
const closeContextMenu = () => {
    contextMenuData.visible = false
    document.removeEventListener('click', closeContextMenu)
}

// 打开拆分对话框
const openSplitDialog = async () => {
    const task = contextMenuData.task
    if (!task) return

    // 关闭右键菜单
    closeContextMenu()

    // 构建计划信息
    const planInfo = {
        id: task.id,
        name: task.name,
        taskType: task.taskType,
        year: parseInt(selectedMonth.value.split('-')[0]),
        month: parseInt(selectedMonth.value.split('-')[1]),
        defineId: task.defineId,
        projectId: appStore.projectContext.selectedProjectId?.toString() || ''
    }

    // 调用拆分组件
    if (planSplitRef.value) {
        if (task.taskType === 'curing') {
            // 调用维养拆分，使用task模式
            await planSplitRef.value.openMaintenanceSplit(planInfo, 'task')
        } else if (task.taskType === 'inspect') {
            // 调用巡检拆分，使用task模式
            await planSplitRef.value.openInspectionSplit(planInfo, true, 'task')
        } else {
            ElMessage.warning('不支持的任务类型拆分')
        }
    }
}

// 处理拆分成功
const handleSplitSuccess = (result: TaskSplitResult) => {
    console.log('拆分成功:', result)
    ElMessage.success(`拆分成功！拆分出 ${result.splitCount} 个新任务`)

    // 刷新任务列表
    handleQuery()
}

// 更新任务排序和位置
const updateTaskSort = async (
    taskId: string | number,
    toWeek?: number | null,
    toDay?: number | null,
    toShift?: string | null,
    toSort?: number | null
) => {
    try {
        // 构建更新数据对象，只包含非null的字段
        const updateData: any = {
            id: taskId
        }

        // 如果周数不为null，则更新week字段
        if (toWeek !== null && toWeek !== undefined) {
            updateData.week = toWeek + 1 // 前端从0开始，后端从1开始
        }

        // 如果天索引不为null，则需要计算对应的日期
        if (toDay !== null && toDay !== undefined && toWeek !== null && toWeek !== undefined) {
            // 根据周数和天索引计算具体日期
            const weekData = weeksList.value[toWeek]
            if (weekData && weekData.days[toDay]) {
                // 将日期字符串转换为ISO 8601格式，后端Java可以正确解析
                const dateStr = weekData.days[toDay].fullDate
                updateData.taskDate = dateStr + ' 00:00:00' // YYYY-MM-DD HH:mm:ss格式

                // 获取当前任务的原始bgnDate和endDate，提取时间部分
                const currentTask = findTaskById(taskId)
                if (currentTask && currentTask.bgnDate && currentTask.endDate) {
                    // 提取原始时间部分
                    const originalBgnTime = extractTimeFromDateTime(currentTask.bgnDate)
                    const originalEndTime = extractTimeFromDateTime(currentTask.endDate)

                    // 组合新日期和原始时间
                    updateData.bgnDate = dateStr + ' ' + originalBgnTime
                    updateData.endDate = dateStr + ' ' + originalEndTime
                }
            }
        }

        // 如果班次类型不为null，则更新shiftType字段
        if (toShift !== null && toShift !== undefined) {
            // 将day/night转换为数字：day->1, night->2
            updateData.shiftType = toShift === 'day' ? 1 : 2
        }

        // 如果排序号不为null，则更新sort字段
        if (toSort !== null && toSort !== undefined) {
            updateData.sort = toSort
        }

        // 调用更新API
        await updateTask(updateData)

        console.log('任务更新成功:', {
            taskId: taskId,
            updateData: updateData
        })
        ElMessage.success('任务排序更新成功')
    } catch (error: any) {
        console.error('更新任务排序失败:', error)
        ElMessage.error('更新任务排序失败: ' + (error?.message || '未知错误'))
    }
}

// 查询按钮点击事件
const handleQuery = async () => {
    if (!selectedMonth.value) {
        ElMessage.warning('请先选择年月')
        return
    }
    loading.value = true
    try {
        // 重新加载当前周的数据
        await loadWeekTasks()
    } finally {
        loading.value = false
    }
}

const handleView = (taskId: string, taskType: string, taskStep: string) => {
    // const from = taskStep == 'task' ? 'task' : 'week'
    router.push('/subProject/circle/plan/baseInfo?id=' + taskId + '&taskType=' + taskType + '&from=task')
}

const handlePublish = async () => {
    // 防止重复点击
    if (isPublishing.value) {
        return
    }

    try {
        // 设置发布状态为进行中
        isPublishing.value = true

        // 检查必要参数
        if (!selectedMonth.value) {
            ElMessage.warning('请先选择年月')
            isPublishing.value = false
            return
        }

        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) {
            ElMessage.warning('请先选择项目')
            isPublishing.value = false
            return
        }

        // 解析年月和当前周
        const [year, month] = selectedMonth.value.split('-').map(Number)
        const currentWeek = parseInt(activeWeekTab.value) + 1 // activeWeekTab从0开始，接口需要从1开始

        // 检查是否有筛选条件
        const hasFilter = planName.value && planName.value.trim()
        let taskIds: string[] | undefined = undefined
        let confirmMessage = `确认发布 ${year}年${month}月第${currentWeek}周的任务吗？`

        // 如果有筛选条件，收集当前显示的任务ID
        if (hasFilter) {
            const currentWeekIndex = parseInt(activeWeekTab.value)
            const currentWeekData = weeksList.value[currentWeekIndex]

            if (currentWeekData && currentWeekData.days) {
                // 收集当前周所有天的任务ID
                taskIds = []
                currentWeekData.days.forEach((day) => {
                    if (day.tasks && day.tasks.length > 0) {
                        day.tasks.forEach((task) => {
                            taskIds!.push(task.id)
                        })
                    }
                })

                // 更新确认消息，显示筛选条件和任务数量
                confirmMessage = `当前筛选条件：计划名称包含"${planName.value.trim()}"
找到 ${taskIds.length} 个符合条件的任务
确认发布这些任务吗？`
            }
        }

        // 确认发布操作
        const confirmResult = await ElMessageBox.confirm(confirmMessage, '发布确认', {
            confirmButtonText: '确认发布',
            cancelButtonText: '取消',
            type: 'warning'
        }).catch(() => false)

        if (!confirmResult) {
            // 用户取消发布，重置状态
            isPublishing.value = false
            return
        }

        // 显示加载状态
        ElMessage.info('正在发布任务...')

        // 调用发布接口，传递taskIds参数
        await publishTask(projectId, year, month, currentWeek, taskIds)

        const successMessage = hasFilter ? `成功发布 ${taskIds?.length || 0} 个筛选任务！` : '任务发布成功！'
        ElMessage.success(successMessage)

        console.log('任务发布成功:', {
            projectId: projectId,
            year: year,
            month: month,
            week: currentWeek,
            hasFilter: hasFilter,
            taskIds: taskIds
        })
        // 发布成功后只需要重新加载当前周的任务数据，不需要重置Tab
        await loadWeekTasks()
    } catch (error: any) {
        console.error('发布任务失败:', error)
        ElMessage.error('发布任务失败: ' + (error?.message || '未知错误'))
    } finally {
        // 无论成功或失败，都要重置发布状态
        isPublishing.value = false
    }
}

// 组件挂载时初始化数据
onMounted(async () => {
    try {
        // 初始化任务状态字典数据
        const dictRes = await getDicts('task_status')
        taskStatusDict.value = dictRes.data || []
        console.log('任务状态字典加载成功:', taskStatusDict.value)
    } catch (error) {
        console.error('加载任务状态字典失败:', error)
        ElMessage.error('加载任务状态字典失败')
    }

    await handleMonthChange()
})

// 监听筛选条件变化，自动刷新显示
watch(
    [selectedTaskType, selectedTaskStatus],
    () => {
        // 筛选条件改变时，由于getDayTasks方法已经包含筛选逻辑，Vue会自动重新渲染
        console.log('筛选条件变化:', {
            taskType: selectedTaskType.value,
            taskStatus: selectedTaskStatus.value
        })
    },
    { immediate: false }
)
</script>

<style lang="scss" scoped>
.task-month-list-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    //padding: 16px;
    // background: red;
}

.disabled-task {
    //opacity: 0.6;
    //cursor: not-allowed;
    cursor: pointer;
    //pointer-events: none;
}

.search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .search-left {
        display: flex;
        gap: 10px;
        align-items: center;

        .search-input {
            width: 200px;
        }
    }

    .search-right {
        display: flex;
        gap: 5px;
        align-items: center;
    }
}

.week-tabs-container {
    overflow: visible;
    position: relative;

    // 筛选按钮覆盖层 - 绝对定位
    .task-filters-overlay {
        position: absolute;
        top: 5px;
        right: 0px;
        z-index: 100;
        pointer-events: none; // 让点击穿透到下层
        // background: red;

        .task-filters {
            pointer-events: auto; // 恢复筛选按钮的点击
            display: flex;
            gap: 8px;
            //background: rgba(255, 255, 255, 0.95);
            //backdrop-filter: blur(5px);
            border-radius: 8px;
            padding: 6px 10px;
            //box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            //border: 1px solid rgba(245, 242, 242, 0.1);
// background: red;
            .filter-group {
                .filter-buttons {
                    display: flex;
                    border-radius: 16px;
                    overflow: hidden;
                    border: 1px solid rgba(226, 221, 221, 0.2);
                    background: rgba(0, 0, 0, 0.05);
// background: red;
                    .filter-btn {
                        padding: 4px 10px;
                        background: transparent;
                        border: none;
                        color: #c8c3c3;
                        font-size: 12px;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        white-space: nowrap;

                        &:hover {
                            background: rgba(64, 158, 255, 0.1);
                            color: #409eff;
                        }

                        &.active {
                            background: #409eff;
                            color: white;
                        }
                    }
                }
            }
        }
    }

    .tabs-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 5px;
        width: 100%;
// background: red;
        .tabs-left {
            flex: 1;
            min-width: 0;
            overflow: hidden;
        }
    }

    :deep(.el-tab-pane) {
        position: relative;
    }
    :deep(.el-tabs__header.is-top) {
        margin-bottom: 0;
    }
    :deep(.el-tabs__nav-wrap) {
        overflow: visible;
    }
}

.days-container {
    display: flex;
    height: 100%;
    overflow-y: auto;
    border: 0px solid #e4e7ed;
    //background: rgba(50, 142, 234, 0.3);
    //background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #2d4a7a 100%);
    border-radius: 8px;
    // background: red;
}

.day-column {
    flex: 1;
    min-width: 150px;
    display: flex;
    flex-direction: column;
    border-right: 0px solid #e4e7ed;

    &:last-child {
        border-right: none;
    }
}

.day-header {

    font-weight: bold;
    font-size: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    // border-bottom: 0px solid #e4e7ed;
    color: #fff;
    background-image: url('@/assets/images/表头.png');
    // background: linear-gradient(0deg, rgba(8,24,61,0.8) 0%);
    background-size: 100% 100%;
    background-position: center bottom;
    background-repeat: no-repeat;
    //border-radius: 8px 8px 0 0;
    // border-radius: 12px;
    margin: 0 10px;
    // background: red;
    .week-number {
        font-size: 14px;
        color: rgba(129, 157, 191, 1);
    }
    .day-date {
        font-size: 32px;
        color: rgba(255, 255, 255);
    }
}

.day-tasks {
    flex: 1;
    padding: 6px;
    min-height: 300px;
    overflow-y: auto;
    // background: red;
    // 自定义滚动条样式
    &::-webkit-scrollbar {
        width: 2px; // 滚动条宽度
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f180; // 轨道背景
        border-radius: 2px; // 轨道圆角
    }

    &::-webkit-scrollbar-thumb {
        background: #ccc; // 滑块颜色
        border-radius: 2px; // 滑块圆角

        &:hover {
            background: #aaa; // 悬停时的滑块颜色
        }
    }
}

.shift-label {
    padding: 0px 8px;
    font-weight: bold;
    background-color: rgba(0, 0, 0, 0.2);
    color: #fff;
    text-align: center;
    font-size: 12px;
    // background: red;
}

.day-shift {
    border-bottom: 1px solid #e4e7ed;
}

.night-shift {
    background-color: rgba(0, 0, 0, 0.1);
    // background: red;
}

.task-item {
    margin-bottom: 4px;
    min-height: 50px;
    padding: 15px 10px;
    font-size: 14px;
    //font-weight: bold;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255);
    background-image: url('@/assets/images/<EMAIL>');
    background-size: 50% auto; /* 宽度缩小为50%，高度自动按比例缩放 */
    background-position: center bottom;
    background-repeat: repeat-x;
    cursor: move;
    transition: all 0.2s;
    position: relative;
    color: #fff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
// background: red;
margin-top: 15px;
    //白班
    &.day-shift {
        //background-color: rgba(255, 255, 255, 0.2);
    }
    //晚班
    &.night-shift {
        //background-color: rgba(109, 70, 27, 0.28);
    }

    //巡检背景色 - 放在班次样式之后，确保优先级更高
    &.inspect {
        background-color: rgba(5, 76, 79, 0.7) !important;
        color: rgba(0, 255, 255, 1);
        border-bottom: 1px solid rgba(0, 255, 255, 1);
    }
    //封道背景色 - 放在班次样式之后，确保优先级更高
    &.sealing {
        background-color: rgba(109, 70, 27, 0.28) !important;
        color: rgba(254, 192, 83, 1);
        border-bottom: 1px solid rgba(254, 192, 83, 1);
    }

    &:hover {
        background-color: rgba(97, 101, 95, 0.8);
        font-weight: bold;
    }

    &:last-child {
        margin-bottom: 0;
    }

    .task-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .split-icon {
            //position: absolute;
            // left: 2px;
            // top: 16px;
            font-size: 12px;
            font-weight: bold;
            color: #fff;
            &.circle {
                background-color: white;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }
        }
        .task-name {
            font-weight: normal;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
        }
    }

    // 任务图标区域
    .task-icons {
        //position: absolute;
        //bottom: 4px;
        //left: 8px;
        //right: 8px;
        color: #a4b3d5;
        font-weight: normal;
        margin-top: 10px;
        display: flex;
        align-items: center;
        //justify-content: space-between;
        gap: 8px;
        height: 16px;
        // background: red;

        .shift-icon {
            width: 22px;
            height: 22px;
            border-radius: 50%;
            //background-color: #ffffff30;
            position: relative;

            .shift-img {
                position: absolute;
                right: 2px;
                bottom: 2px;
                width: 16px;
                height: 16px;
            }
        }

        .splitable-status,
        .completed-status {
            margin-top: 5px;
            padding: 2px 7px;
            background-color: rgba(8, 24, 61, 0.3);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;

            span {
                margin: 0px 3px;
            }
            .status-icon {
                //margin-top: 4px;
                //padding-top: 2px;
                width: 12px;
                height: 12px;
                filter: brightness(0.8);

                &.schedulable {
                    color: #67c23a; // 绿色 - 可排班
                }

                &.non-schedulable {
                    color: #f56c6c; // 红色 - 不可排班
                }

                &.assigned {
                    color: #409eff; // 蓝色 - 已分配
                }

                &.unassigned {
                    color: #909399; // 灰色 - 未分配
                }
            }

            .status-text {
                font-size: 10px;
                line-height: 1;
                white-space: nowrap;

                &.schedulable {
                    color: #67c23a; // 绿色 - 可排班
                }

                &.non-schedulable {
                    color: #f56c6c; // 红色 - 不可排班
                }

                &.assigned {
                    color: #409eff; // 蓝色 - 已分配
                }

                &.unassigned {
                    color: #909399; // 灰色 - 未分配
                }
            }
        }
    }
}

// 任务名称颜色样式
// .task-name-plan {
//     color: white !important;
// }

// .task-name-start {
//     color: #409eff !important;
// }

// .task-name-task {
//     color: #409eff !important;
// }

// .task-name-complete {
//     color: gray !important;
// }

.context-menu {
    position: fixed;
    z-index: 1000;
    background-color: #2a2a2a;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
    padding: 5px 0;
}

.menu-item {
    padding: 8px 16px;
    cursor: pointer;
    color: #fff;

    &:hover {
        background-color: #3a3a3a;
    }
}

.remark {
    color: #ada9a9;

    ul,
    li {
        list-style: none;
        padding: 2px;
    }
    .shift {
        width: 16px;
        height: 16px;
        //vertical-align: middle;
        margin-right: 4px;
    }
}

.btn-container {
    margin-top: 10px;
    margin-left: auto;
    .publishState {
        color: #fff;
        font-size: 18px;
        font-weight: bold;

        &.blue {
            color: #409eff;
        }
    }
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    border-radius: 4px;

    .loading-icon {
        font-size: 24px;
        margin-bottom: 12px;
        color: #409eff;
    }

    .loading-text {
        color: #fff;
        font-size: 14px;
        font-weight: 500;
    }
}

.loading-blur {
    filter: blur(2px);
    pointer-events: none;
}
</style>
