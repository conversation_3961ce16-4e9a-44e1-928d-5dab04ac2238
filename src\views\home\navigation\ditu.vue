<!-- 项目级-MAP (百度地图版本) -->
<template>
    <div class="main-box">
        <div class="project-tabs" v-if="props.showProjectList">
            <div v-if="loading" class="loading-text">加载项目数据中...</div>
            <div
                v-else
                v-for="project in projects"
                :key="project.id"
                class="tab"
                :class="{ active: selectedProjectId === project.id }"
                @click="selectProject(project.id)"
            >
                {{ project.name }}
            </div>
        </div>
        <div class="map-container">
            <div id="baidu-map-container" class="map"></div>
            <img class="map-mask" src="@/assets/images/<EMAIL>" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { getAllProjects } from '@/api/project/project/index'
import { ProjectVO, ProjectMapItem } from '@/api/project/project/types'
import tunnelIcon from '@/assets/images/<EMAIL>'

// Props定义
interface Props {
    showProjectList?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    showProjectList: true
})

// 声明全局类型
declare global {
    interface Window {
        BMap: any
        initBaiduMapCallback: () => void
    }
}

// 响应式数据
const appStore = useAppStore()
const projects = ref<ProjectVO[]>([])
// 默认选中项：中环北路
const selectedProjectId = ref<string>('')
const loading = ref(true)
const mapInstance = ref<any>(null)
const mapMarkers = ref<any[]>([])
const mapPolylines = ref<any[]>([])

// 连线开关变量 - 暂时关闭坐标间的连线
const showConnectingLines = ref(true)

// 锚点图标显示开关变量
const showAnchorIcons = ref(false)

// zoom级别对应的字体大小和锚点图标大小配置
const zoomStyleConfig = ref({
    '>15': { fontSize: 16, iconSize: 40 }, // zoom > 15时，字体大小16px，图标大小40px
    '15': { fontSize: 14, iconSize: 35 }, // zoom = 15时，字体大小14px，图标大小35px
    '14': { fontSize: 12, iconSize: 30 }, // zoom = 14时，字体大小12px，图标大小30px
    '13': { fontSize: 10, iconSize: 25 }, // zoom = 13时，字体大小10px，图标大小25px
    '12': { fontSize: 8, iconSize: 20 }, // zoom = 12时，字体大小8px，图标大小20px
    '<12': { fontSize: 6, iconSize: 15 } // zoom < 12时，字体大小6px，图标大小15px
})

// 根据zoom级别获取对应的样式配置
const getStyleConfigByZoom = (zoom: number) => {
    if (zoom > 15) return zoomStyleConfig.value['>15']
    if (zoom === 15) return zoomStyleConfig.value['15']
    if (zoom === 14) return zoomStyleConfig.value['14']
    if (zoom === 13) return zoomStyleConfig.value['13']
    if (zoom === 12) return zoomStyleConfig.value['12']
    return zoomStyleConfig.value['<12'] // zoom < 12
}

// 更新所有标签和图标样式的方法
const updateAllElementsStyle = (styleConfig: { fontSize: number; iconSize: number }) => {
    mapMarkers.value.forEach((marker) => {
        if (marker instanceof window.BMap.Label) {
            // 更新标签字体大小
            marker.setStyle({
                fontSize: `${styleConfig.fontSize}px`
            })
        } else if (marker instanceof window.BMap.Marker) {
            // 更新图标大小
            const icon = marker.getIcon()
            if (icon) {
                const newIcon = new window.BMap.Icon(icon.imageUrl, new window.BMap.Size(styleConfig.iconSize, styleConfig.iconSize), {
                    imageSize: new window.BMap.Size(styleConfig.iconSize * 0.75, styleConfig.iconSize * 0.75)
                })
                marker.setIcon(newIcon)
            }
        }
    })
}

// 获取项目数据
const fetchProjects = async () => {
    try {
        loading.value = true
        const response = await getAllProjects()
        projects.value = response.data || []

        // 默认选中包含"中环北线"字样的项目，如果没有则选择第一个项目
        if (projects.value.length > 0) {
            const zhonghuanProject = projects.value.find((project) => project.name && project.name.includes('中环北线'))
            selectedProjectId.value = zhonghuanProject ? zhonghuanProject.id : projects.value[0].id
        }
    } catch (error) {
        console.error('获取项目数据失败:', error)
    } finally {
        loading.value = false
    }
}

// 解析项目地图数据
const parseProjectMapData = (mapJsonString: string): ProjectMapItem[] => {
    try {
        if (!mapJsonString) return []

        // 处理可能的多种JSON格式
        const mapData = JSON.parse(mapJsonString)

        // 如果是数组，直接返回
        if (Array.isArray(mapData)) {
            return mapData
        }

        // 如果是单个对象，转换为数组
        if (typeof mapData === 'object') {
            return [mapData]
        }

        return []
    } catch (error) {
        console.error('解析地图数据失败:', error)
        return []
    }
}

// 清除地图标记和线条（优化性能）
const clearMapElements = () => {
    if (mapInstance.value) {
        // 清除所有覆盖物
        mapInstance.value.clearOverlays()
        mapMarkers.value = []
        mapPolylines.value = []
    }
}

// 创建标记点击处理函数
const createMarkerClickHandler = (project: ProjectVO) => {
    return () => {
        // 设置当前项目到store
        appStore.projectContext.selectedProjectId = project.id
        appStore.projectContext.isPlatform = false

        // 跳转到navigation页面
        window.open('/navigation', '_blank')
    }
}

// 预定义的项目颜色数组 - 适合暗黑模式的冷色系
const projectColors = [
    //{ line: '#00ffff', start: '#00ffff', end: '#00ffff', name: 'rgba(0, 255, 255, 0.8)' }, // 亮青色
    { line: '#33c9f1', start: '#00ff88', end: '#00ff88', name: '#33c9f1' } // 翠绿色
    //{ line: '#4da6ff', start: '#4da6ff', end: '#4da6ff', name: 'rgba(77, 166, 255, 0.8)' }, // 亮蓝色
    //{ line: '#FF9326', start: '#00bfff', end: '#00bfff', name: 'rgba(0, 191, 255, 0.8)' }, // 深天蓝
    //{ line: '#FF73FF', start: '#7fffd4', end: '#7fffd4', name: 'rgba(127, 255, 212, 0.8)' } // 碧绿色
]

// 在地图上显示项目数据（百度地图版本）
const displayProjectOnMap = (project: ProjectVO, colorIndex: number = 0, clearMap: boolean = true) => {
    if (!mapInstance.value || !project.map) return

    // 根据参数决定是否清除现有标记和线条
    if (clearMap) {
        clearMapElements()
    }

    const mapItems = parseProjectMapData(project.map)
    if (mapItems.length === 0) return

    // 获取项目对应的颜色
    const colors = projectColors[colorIndex % projectColors.length]

    const allCoordinates: any[] = []

    // 按线段名称分组
    const segmentGroups = new Map<string, ProjectMapItem[]>()
    mapItems.forEach((item) => {
        if (item.name) {
            if (!segmentGroups.has(item.name)) {
                segmentGroups.set(item.name, [])
            }
            segmentGroups.get(item.name)!.push(item)
        }
    })

    // 为每个线段组创建连线和标记
    segmentGroups.forEach((segments, segmentName) => {
        if (segments.length === 0) return

        // 收集该线段组的所有坐标点（按顺序，去重）
        const segmentCoordinates: any[] = []
        const coordinateMap = new Map<string, any>() // 用于去重

        // 按顺序收集所有坐标点，避免重复
        segments.forEach((item) => {
            const beginLng = parseFloat(item.beginLongitude?.toString() || '0')
            const beginLat = parseFloat(item.beginLatitude?.toString() || '0')
            const endLng = parseFloat(item.endLongitude?.toString() || '0')
            const endLat = parseFloat(item.endLatitude?.toString() || '0')

            // 处理起点
            if (beginLng && beginLat) {
                const beginKey = `${beginLng},${beginLat}`
                if (!coordinateMap.has(beginKey)) {
                    const beginPos = new window.BMap.Point(beginLng, beginLat)
                    coordinateMap.set(beginKey, beginPos)
                    segmentCoordinates.push(beginPos)
                    allCoordinates.push(beginPos)
                }
            }

            // 处理终点（确保不与起点重复）
            if (endLng && endLat && (endLng !== beginLng || endLat !== beginLat)) {
                const endKey = `${endLng},${endLat}`
                if (!coordinateMap.has(endKey)) {
                    const endPos = new window.BMap.Point(endLng, endLat)
                    coordinateMap.set(endKey, endPos)
                    segmentCoordinates.push(endPos)
                    allCoordinates.push(endPos)
                }
            }
        })

        // 创建单一连续线段（如果有足够的坐标点，且开关开启）
        if (showConnectingLines.value && segmentCoordinates.length >= 2) {
            const polyline = new window.BMap.Polyline(segmentCoordinates, {
                strokeColor: colors.line,
                strokeWeight: 6,
                strokeStyle: 'solid',
                strokeOpacity: 0.8,
                strokeLineCap: 'round', // 设置线段端点为圆形，避免尖锐连接
                strokeLineJoin: 'round' // 设置线段连接处为圆形，避免尖锐角
            })

            mapInstance.value.addOverlay(polyline)
            mapPolylines.value.push(polyline)
        }

        // 为该线段组创建首尾标记
        if (segmentCoordinates.length >= 2) {
            // 起点标记（第一个坐标）- 根据开关控制显示
            if (showAnchorIcons.value) {
                const startPoint = segmentCoordinates[0]
                const currentZoom = mapInstance.value.getZoom()
                const styleConfig = getStyleConfigByZoom(currentZoom)
                const startMarker = new window.BMap.Marker(startPoint, {
                    icon: new window.BMap.Icon(tunnelIcon, new window.BMap.Size(styleConfig.iconSize, styleConfig.iconSize), {
                        imageSize: new window.BMap.Size(styleConfig.iconSize * 0.75, styleConfig.iconSize * 0.75)
                    })
                })
                startMarker.addEventListener('click', createMarkerClickHandler(project))
                mapInstance.value.addOverlay(startMarker)
                //起始点标记
                mapMarkers.value.push(startMarker)
            }

            // 计算标签显示位置：如果只有2个坐标显示在第1个，否则显示在中间节点
            let labelPointIndex = 0
            if (segmentCoordinates.length > 2) {
                labelPointIndex = Math.floor((segmentCoordinates.length - 1) / 2)
            }
            const labelPoint = segmentCoordinates[labelPointIndex]

            // 线段名称标签（显示在中间节点）
            const currentZoom = mapInstance.value.getZoom()
            const styleConfig = getStyleConfigByZoom(currentZoom)

            const nameLabel = new window.BMap.Label(segmentName, {
                position: labelPoint,
                offset: new window.BMap.Size(25, -10)
            })
            nameLabel.setStyle({
                background: '#0B2B37',
                border: '0px solid #4a9bb0',
                borderRadius: '8px',
                color: '#ffffff',
                fontSize: `${styleConfig.fontSize}px`,
                fontWeight: 'bold',
                padding: '6px 12px',
                whiteSpace: 'nowrap',
                boxShadow: 'inset 0px 0px 8px 0px rgba(0,217,255,0.75)',
                maxWidth: '200px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                cursor: 'pointer'
            })
            // 为线段名称标签添加点击事件
            nameLabel.addEventListener('click', createMarkerClickHandler(project))
            mapInstance.value.addOverlay(nameLabel)
            mapMarkers.value.push(nameLabel)

            // 终点标记（最后一个坐标）- 根据开关控制显示
            const endPoint = segmentCoordinates[segmentCoordinates.length - 1]
            // 避免起点和终点是同一个坐标的情况
            if (showAnchorIcons.value && (segmentCoordinates[0].lng !== endPoint.lng || segmentCoordinates[0].lat !== endPoint.lat)) {
                const currentZoom = mapInstance.value.getZoom()
                const styleConfig = getStyleConfigByZoom(currentZoom)
                const endMarker = new window.BMap.Marker(endPoint, {
                    icon: new window.BMap.Icon(tunnelIcon, new window.BMap.Size(styleConfig.iconSize, styleConfig.iconSize), {
                        imageSize: new window.BMap.Size(styleConfig.iconSize * 0.75, styleConfig.iconSize * 0.75)
                    })
                })
                endMarker.addEventListener('click', createMarkerClickHandler(project))
                mapInstance.value.addOverlay(endMarker)
                mapMarkers.value.push(endMarker)
            }
        }
    })

    // 注意：不同线段组之间不需要连线，每个线段组都是独立的路径

    // 调整地图视野 - 项目切换时固定zoom为13
    if (allCoordinates.length > 0) {
        //根据项目坐标调整视野
        //mapInstance.value.setViewport(allCoordinates, { margins: [50, 50, 50, 50] })
        // 计算项目坐标的中心点
        const lngs = allCoordinates.map((point) => point.lng)
        const lats = allCoordinates.map((point) => point.lat)
        const centerLng = (Math.min(...lngs) + Math.max(...lngs)) / 2
        const centerLat = (Math.min(...lats) + Math.max(...lats)) / 2
        const centerPoint = new window.BMap.Point(centerLng, centerLat)

        // 设置固定zoom为13
        mapInstance.value.centerAndZoom(centerPoint, 13)
        console.log('项目切换 - 设置zoom级别为:', 13)
    }
}

// 显示所有项目的线路
const displayAllProjectsOnMap = () => {
    if (!mapInstance.value || projects.value.length === 0) return

    // 清除现有标记和线条
    clearMapElements()

    // 遍历所有项目，为每个项目分配不同颜色
    projects.value.forEach((project, index) => {
        if (project.map) {
            // 第一个项目清除地图，后续项目不清除
            displayProjectOnMap(project, index, false)
        }
    })

    // 获取选中项目的坐标，用于调整视野
    const selectedProject = projects.value.find((p) => p.id === selectedProjectId.value)
    if (selectedProject && selectedProject.map) {
        const mapItems = parseProjectMapData(selectedProject.map)
        if (mapItems.length > 0) {
            const selectedCoordinates: any[] = []

            mapItems.forEach((item) => {
                const beginLng = parseFloat(item.beginLongitude?.toString() || '0')
                const beginLat = parseFloat(item.beginLatitude?.toString() || '0')
                const endLng = parseFloat(item.endLongitude?.toString() || '0')
                const endLat = parseFloat(item.endLatitude?.toString() || '0')

                if (beginLng && beginLat) {
                    selectedCoordinates.push(new window.BMap.Point(beginLng, beginLat))
                }
                if (endLng && endLat) {
                    selectedCoordinates.push(new window.BMap.Point(endLng, endLat))
                }
            })

            // 调整地图视野到选中项目的具体坐标范围 - 项目切换时固定zoom为13
            if (selectedCoordinates.length > 0) {
                // 计算选中项目坐标的中心点
                const lngs = selectedCoordinates.map((point) => point.lng)
                const lats = selectedCoordinates.map((point) => point.lat)
                const centerLng = (Math.min(...lngs) + Math.max(...lngs)) / 2
                const centerLat = (Math.min(...lats) + Math.max(...lats)) / 2
                const centerPoint = new window.BMap.Point(centerLng, centerLat)

                // 设置固定zoom为14
                mapInstance.value.centerAndZoom(centerPoint, 14)
                console.log('显示所有项目 - 设置zoom级别为:', 14)
            }
        }
    }
}

// 选择项目
const selectProject = (projectId: string) => {
    selectedProjectId.value = projectId
}

// 监听选中项目变化
watch(selectedProjectId, () => {
    // 重新显示所有项目，但视角会聚焦到选中项目
    displayAllProjectsOnMap()
})

// 初始化百度地图
const initBaiduMap = () => {
    if (window.BMap) {
        mapInstance.value = new window.BMap.Map('baidu-map-container')

        // 设置地图中心点和缩放级别
        const point = new window.BMap.Point(120.6, 31.38) // 苏州地区坐标
        mapInstance.value.centerAndZoom(point, 14)

        // 启用滚轮缩放
        mapInstance.value.enableScrollWheelZoom(true)

        // 输出初始zoom级别
        console.log('地图初始化 - 当前zoom级别:', mapInstance.value.getZoom())

        // 监听地图缩放事件
        mapInstance.value.addEventListener('zoomend', () => {
            const currentZoom = mapInstance.value.getZoom()
            const styleConfig = getStyleConfigByZoom(currentZoom)

            console.log(
                '地图缩放变化 - 当前zoom级别:',
                currentZoom,
                '字体大小:',
                styleConfig.fontSize + 'px',
                '图标大小:',
                styleConfig.iconSize + 'px'
            )

            // 更新所有标签和图标的样式
            updateAllElementsStyle(styleConfig)
        })

        // 设置地图样式（使用提供的样式ID）
        mapInstance.value.setMapStyleV2({
            styleId: 'a532aef0efd1ed8b7b0dfc38a67ea9bf'
        })

        // 显示所有项目的线路
        displayAllProjectsOnMap()
    }
}

onMounted(async () => {
    // 先获取项目数据
    await fetchProjects()

    // 动态加载百度地图API
    const script = document.createElement('script')
    script.src = 'https://api.map.baidu.com/api?v=3.0&ak=KRTlSNEe8zfVZSsr88CUle86l1o0bTJI&callback=initBaiduMapCallback'
    script.async = true
    window.initBaiduMapCallback = () => {
        initBaiduMap()
    }
    document.head.appendChild(script)
})
</script>

<style lang="scss" scoped>
.main-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: absolute;
    left: 0;
    top: 0;
    width: 35%;
    height: 100%;
    // background: red;

    .project-tabs {
        display: flex;
        margin-bottom: 10px;

        .loading-text {
            padding: 8px 15px;
            color: #0ff;
            font-size: 14px;
        }

        .tab {
            padding: 8px 15px;
            background-color: rgba(0, 100, 200, 0.3);
            border-radius: 4px;
            margin-right: 5px;
            cursor: pointer;
            font-size: 14px;

            &.active {
                background-color: rgba(0, 150, 255, 0.8);
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    right: 0;
                    border-width: 0 8px 8px 0;
                    border-style: solid;
                    border-color: transparent #ffcc00 transparent transparent;
                }
            }

            &:hover {
                background-color: rgba(0, 120, 220, 0.5);
            }
        }
    }

    .map-container {
        position: relative;
        margin-bottom: 10px;

        .map {
            width: 100%;
            height: calc(100vh - 10px);
            background-color: #0a1a2a;
            border-radius: 4px;
            overflow: hidden;
        }
        .map-mask {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 83px;
            height: 54px;
            z-index: 999;
        }
    }
}
</style>
