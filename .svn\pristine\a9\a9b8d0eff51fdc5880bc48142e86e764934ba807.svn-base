<!-- 维养任务取消 -->
<template>
    <div class="p-2" style="margin-top: -10px">
        <el-card class="box-card">
            <template v-slot:header>
                <div class="clearfix">
                    <span>作业单信息</span>
                </div>
            </template>
            <!-- 作业单任务基本信息 -->
            <BaseInfoMaintain :id="taskId" from="task" />
        </el-card>

        <el-card class="box-card">
            <template v-slot:header>
                <div class="clearfix">
                    <span>取消操作</span>
                </div>
            </template>
            <div class="text item">
                <el-form ref="projectFormRef" :model="form" :rules="rules" label-width="160px">
                    <el-row :gutter="gutter" v-if="form.task.currentStatus != 'Pending_Cancellation'">
                        <el-col :span="12">
                            <el-form-item label="取消原因" prop="nextAssignee.opinion">
                                <el-input v-model="form.nextAssignee.opinion" type="textarea" placeholder="请输入取消原因" :rows="4" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter" v-if="form.task.currentStatus == 'Pending_Cancellation'">
                        <el-col :span="12">
                            <el-form-item label="确认取消">
                                <el-radio-group v-model="form.nextAssignee.wfOperation">
                                    <el-radio v-for="option in approvalOptions" :key="option.value" :value="option.value">
                                        {{ option.label }}
                                    </el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter" v-if="form.task.currentStatus == 'Pending_Cancellation'">
                        <el-col :span="12">
                            <el-form-item label="意见" prop="nextAssignee.opinion">
                                <el-input v-model="form.nextAssignee.opinion" type="textarea" placeholder="请输入意见" :rows="4" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div class="text item">
                        <el-row :gutter="gutter">
                            <el-col :span="24">
                                <el-button type="primary" :loading="submitLoading" @click="handleSubmit">提交</el-button>
                                <el-button @click="handleCancel">返回</el-button>
                            </el-col>
                        </el-row>
                    </div>
                </el-form>
            </div>
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { maintainAssign } from '@/api/plan/task'
import { AssignMaintainFlowForm, AssignMaintainFlowVo, AssignMaintainResourceVo } from '@/api/plan/assignMaintain/types'
import { getTaskAssignmentByTaskId } from '@/api/plan/assignMaintain'
import { ElMessage } from 'element-plus'
import BaseInfoMaintain from '../../components/BaseInfoMaintain.vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const route = useRoute()
const router = useRouter()

const gutter = ref(50)
const taskId = ref((route.query.taskId as string) || '')
const projectFormRef = ref()
const submitLoading = ref(false)
// 审核结果选项
const approvalOptions = ref([
    { label: '终止', value: 'TERMINATE' },
    { label: '退回', value: 'ROLLBACK' }
    // { label: '延期', value: 'SUSPENDED' }
])
const actualResourceList = ref<AssignMaintainResourceVo[]>([
    {
        resourceId: '',
        quantity: 1
    } as AssignMaintainResourceVo
])

// 表单数据 - 只保留取消操作所需的字段
const form = reactive<AssignMaintainFlowForm>({
    task: {
        tempTask: 'no',
        tempResourceId: ''
    },
    assignMaintain: {
        taskId: taskId.value,
        resourceList: [],
        cars: '' as any
    },
    nextAssignee: {
        nextAssignees: undefined,
        wfOperation: 'APPROVE', // 🔥 修复拼写错误：ARRPOVE -> APPROVE
        opinion: '',
        businessData: {
            CANCEL_OPTION: 'CANCEL'
        }
    }
})

// 表单验证规则 - 只验证取消原因
const rules = reactive({
    'nextAssignee.opinion': [
        { required: true, message: '请输入取消原因', trigger: 'blur' },
        { min: 1, message: '取消原因至少1个字符', trigger: 'blur' }
    ]
} as any)

// 提交取消操作
const handleSubmit = async () => {
    try {
        // 表单验证
        const valid = await projectFormRef.value?.validate()
        if (!valid) {
            return
        }

        submitLoading.value = true

        // 创建处理后的表单数据副本
        const processedForm = {
            ...form
        }

        // 处理车辆数据：将数组转换为逗号分隔的字符串
        if (processedForm.assignMaintain) {
            if (Array.isArray(processedForm.assignMaintain.cars)) {
                processedForm.assignMaintain.cars = (processedForm.assignMaintain.cars as string[]).join(',')
                console.log('转换车辆数组为逗号分隔字符串:', processedForm.assignMaintain.cars)
            }

            // 处理resourceList数据：如果是空数组，确保转换为正确格式
            if (processedForm.assignMaintain.resourceList && processedForm.assignMaintain.resourceList.length === 0) {
                processedForm.assignMaintain.resourceList = []
            }
        }

        // 根据当前状态处理nextAssignee
        switch (form.task.currentStatus) {
            // 指派环节需要指定处理人
            case 'START':
                processedForm.nextAssignee = {
                    ...form.nextAssignee,
                    nextAssignees: form.nextAssignee?.nextAssignees
                        ? Array.isArray(form.nextAssignee.nextAssignees)
                            ? form.nextAssignee.nextAssignees
                            : [form.nextAssignee.nextAssignees]
                        : []
                }
                break
            default:
                // 其他环节不需要指定处理人
                processedForm.nextAssignee = {
                    ...form.nextAssignee,
                    nextAssignees: []
                }
                break
        }

        console.log('提交的取消数据:', processedForm)
        console.log('nextAssignees转换后:', processedForm.nextAssignee?.nextAssignees)

        // 调用后端API提交取消操作
        await maintainAssign(processedForm)

        ElMessage.success('任务取消成功')
        router.push('/subProject/circle/maintain/list')
    } catch (error) {
        console.error('任务取消失败:', error)
        ElMessage.error('任务取消失败，请重试')
    } finally {
        submitLoading.value = false
    }
}
// 获取任务分配数据
const getTaskAssignmentData = async () => {
    try {
        const response = await getTaskAssignmentByTaskId(taskId.value)
        if (response.data) {
            const data: AssignMaintainFlowVo = response.data

            // 初始化form数据
            if (data.task) {
                Object.assign(form.task, data.task)
                if (data.task.currentStatus == 'Execute') {
                    form.task.taskStartDate = form.task.bgnDate
                    form.task.taskFinishDate = form.task.endDate
                }
                console.log('任务信息:', {
                    id: data.task.id,
                    name: data.task.name,
                    taskType: data.task.taskType,
                    bgnDate: data.task.bgnDate,
                    endDate: data.task.endDate
                })
            }

            if (data.assignMaintain) {
                Object.assign(form.assignMaintain, data.assignMaintain)

                // 解析cars字段（逗号分隔的字符串转为数组）
                if (data.assignMaintain.cars && typeof data.assignMaintain.cars === 'string') {
                    ;(form.assignMaintain.cars as any) = data.assignMaintain.cars.split(',').filter((id) => id.trim() !== '')
                    console.log('解析车辆ID列表成功:', form.assignMaintain.cars)
                } else if (Array.isArray(data.assignMaintain.cars)) {
                    // 如果已经是数组，直接使用
                    ;(form.assignMaintain.cars as any) = data.assignMaintain.cars
                } else {
                    // 初始化为空数组
                    ;(form.assignMaintain.cars as any) = []
                }

                // 解析resource字段为actualResourceList
                if (data.assignMaintain.resource) {
                    try {
                        const parsedResource = JSON.parse(data.assignMaintain.resource)
                        if (Array.isArray(parsedResource)) {
                            actualResourceList.value = parsedResource
                            console.log('解析实际物资列表成功:', actualResourceList.value)
                        }
                    } catch (error) {
                        console.warn('解析resource字段失败，使用默认值:', error)
                        actualResourceList.value = [
                            {
                                resourceId: '',
                                quantity: 1
                            } as AssignMaintainResourceVo
                        ]
                    }
                } else {
                    // 如果没有resource数据，初始化为默认值
                    actualResourceList.value = [
                        {
                            resourceId: '',
                            quantity: 1
                        } as AssignMaintainResourceVo
                    ]
                }

                console.log('维养分配信息:', {
                    id: data.assignMaintain.id,
                    taskId: data.assignMaintain.taskId,
                    name: data.assignMaintain.name,
                    jobType: data.assignMaintain.jobType,
                    safeManId: data.assignMaintain.safeManId,
                    managerId: data.assignMaintain.managerId,
                    teams: data.assignMaintain.teams,
                    cars: data.assignMaintain.cars,
                    duration: data.assignMaintain.duration,
                    description: data.assignMaintain.description
                })
            }

            console.log('获取任务分配数据成功:', data)
        }
    } catch (error) {
        console.error('获取任务分配数据失败:', error)
        ElMessage.warning('获取任务数据失败，将使用默认数据')
    }
}
// 返回列表
const handleCancel = () => {
    router.push('/subProject/circle/maintain/list')
}

onMounted(async () => {
    await getTaskAssignmentData()
})
</script>

<style lang="scss" scoped>
.box-card {
    margin-top: 10px;
}
</style>
