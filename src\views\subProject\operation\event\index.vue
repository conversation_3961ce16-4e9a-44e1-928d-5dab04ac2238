<template>
    <div class="p-2 event-management-page">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <!-- <el-form-item label="所属项目" prop="projectId">
                            <el-input v-model="queryParams.projectId" placeholder="请输入所属项目" clearable
                                @keyup.enter="handleQuery" />
                        </el-form-item> -->
                        <el-form-item label="发生时间" style="width: 308px">
                            <el-date-picker
                                v-model="dateRangeHappenTime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                            />
                        </el-form-item>
                        <el-form-item label="事件类型" prop="emergencyLevel">
                            <el-tree-select
                                clearable
                                @change="handleEventTypeChange"
                                v-model="selectedEventCategoryId"
                                :data="categoryList"
                                :props="{ value: 'id', label: 'name', children: 'children' }"
                                value-key="id"
                                placeholder="请选择事件类型"
                                check-strictly
                            />
                        </el-form-item>
                        <!-- <el-form-item label="事件等级" prop="emergencyLevel">
                            <el-select v-model="queryParams.emergencyLevel" placeholder="请选择事件等级" clearable>
                                <el-option v-for="dict in emergency_level" :key="dict.value" :label="dict.label"
                                    :value="dict.value" />
                            </el-select>
                        </el-form-item> -->
                        <!-- <el-form-item label="天气情况" prop="fstrWeather">
                            <el-select v-model="queryParams.fstrWeather" placeholder="请选择天气情况" clearable>
                                <el-option v-for="dict in fstr_weather" :key="dict.value" :label="dict.label"
                                    :value="dict.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="占用总数" prop="roadUnavailable">
                            <el-select v-model="queryParams.roadUnavailable" placeholder="请选择占用总数" clearable>
                                <el-option v-for="dict in road_unavailable" :key="dict.value" :label="dict.label"
                                    :value="dict.value" />
                            </el-select>
                        </el-form-item> -->
                        <!-- <el-form-item label="死亡人数" prop="dies">
                            <el-input v-model="queryParams.dies" placeholder="请输入死亡人数" clearable
                                @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item label="受伤人数" prop="induries">
                            <el-input v-model="queryParams.induries" placeholder="请输入受伤人数" clearable
                                @keyup.enter="handleQuery" />
                        </el-form-item> -->

                        <!-- 🔥 新增：摄像头范围查询 -->
                        <el-form-item label="起始摄像机" label-width="100px">
                            <el-select
                                v-model="queryParams.startCameraTemp"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入摄像头名称搜索"
                                :remote-method="searchStartCameras"
                                :loading="startCameraLoading"
                                @change="handleStartCameraChange"
                                clearable
                                style="width: 240px"
                            >
                                <el-option
                                    v-for="camera in startCameraOptions"
                                    :key="camera.id"
                                    :label="`${camera.remark}${camera.bgnKilometer ? ' (' + camera.bgnKilometer + ')' : ''}`"
                                    :value="camera.id"
                                />
                            </el-select>
                        </el-form-item>

                        <el-form-item label="结束摄像机" label-width="100px">
                            <el-select
                                v-model="queryParams.endCameraTemp"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入摄像头名称搜索"
                                :remote-method="searchEndCameras"
                                :loading="endCameraLoading"
                                @change="handleEndCameraChange"
                                clearable
                                style="width: 240px"
                            >
                                <el-option
                                    v-for="camera in endCameraOptions"
                                    :key="camera.id"
                                    :label="`${camera.remark}${camera.bgnKilometer ? ' (' + camera.bgnKilometer + ')' : ''}`"
                                    :value="camera.id"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="里程号" prop="bgnKilometer">
                            <el-input v-model="queryParams.bgnKilometer" placeholder="请输入里程号" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">搜索</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <div class="btn-box">
                    <div class="filter">
                        <el-radio-group v-model="filterStatus" @change="handleFilterStatus">
                            <el-radio-button value="" v-if="false">全部</el-radio-button>
                            <el-radio-button value="Assign">待处理</el-radio-button>
                            <el-radio-button value="Accepted">已处理</el-radio-button>
                        </el-radio-group>
                        <!-- v-hasPermi="['operation:event:edit']" -->
                        <el-button type="success" v-if="false" plain icon="Edit" :disabled="single" @click="() => handleUpdate()">修改</el-button>
                        <!-- v-hasPermi="['operation:event:remove']" -->
                        <el-button type="danger" v-if="false" plain icon="Delete" :disabled="multiple" @click="handleDelete()">删除</el-button>
                    </div>
                    <div class="export">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
                        <el-button type="warning" v-if="false" plain icon="Download" @click="handleExport" v-hasPermi="['operation:event:export']"
                            >导出</el-button
                        >
                    </div>
                </div>
            </template>

            <el-table v-loading="loading" stripe :data="eventList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <!-- <el-table-column label="所属项目" align="center" prop="projectId" /> -->
                <el-table-column label="发生时间" align="center" prop="happenTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.happenTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="发现时间" align="center" prop="discoverTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.discoverTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column> -->
                <el-table-column label="事件分类" align="center" prop="eventClass">
                    <template #default="scope">
                        <span>{{ getCategoryName(scope.row.eventClass) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="事件类型" align="center" prop="eventType">
                    <template #default="scope">
                        <span>{{ getCategoryName(scope.row.eventType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="事件子类型" align="center" prop="eventSubtype">
                    <template #default="scope">
                        <span>{{ getCategoryName(scope.row.eventSubtype) }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="占用车道数" align="center" prop="roadUnavailable">
                    <template #default="scope">
                        <dict-tag :options="road_unavailable" :value="scope.row.roadUnavailable" />
                    </template>
                </el-table-column>
                <el-table-column label="摄像机号" align="center" width="200">
                    <template #default="scope">
                        <span>{{ formatCameraInfo(scope.row) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="里程号" align="center" prop="bgnKilometer" />

                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
                    <template #default="scope">
                        <div class="op-actions">
                            <el-button link type="primary" class="op-link op-info" @click="handleView(scope.row)">
                                <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="查看" />
                                查看
                            </el-button>
                            <el-button link type="primary" class="op-link op-edit" @click="handleUpdate(scope.row)">
                                <img class="op-icon" src="@/assets/images/edit-icon.png" alt="修改" />
                                修改
                            </el-button>
                            <el-button link type="danger" class="op-link op-delete" @click="handleDelete(scope.row)">
                                <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
    </div>
</template>

<script setup name="Event" lang="ts">
import { listEvent, listNoPageEvents, getEvent, delEvent, listEventLocations } from '@/api/subProject/operation/event'
import { EventVO, EventQuery } from '@/api/subProject/operation/event/types'
import { listCategory } from '@/api/common/category'
import { CategoryVO } from '@/api/common/category/types'
import { searchEquipmentByKeyword, batchGetEquipment } from '@/api/subProject/basic/equipment'
import { EquipmentVO } from '@/api/subProject/basic/equipment/types'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const router = useRouter()
const {
    fstr_weather,
    road_position,
    involve_car,
    road_num,
    road_leave,
    emergency_level,
    road_available,
    road_unavailable,
    event_bottleneck,
    congestion_length
} = toRefs<any>(
    proxy?.useDict(
        'fstr_weather',
        'road_position',
        'involve_car',
        'road_num',
        'road_leave',
        'emergency_level',
        'road_available',
        'road_unavailable',
        'event_bottleneck',
        'congestion_length'
    )
)
const categoryList = ref<CategoryVO[]>([])
const eventList = ref<EventVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRangeHappenTime = ref<[DateModelType, DateModelType]>(['', ''])
const queryFormRef = ref<ElFormInstance>()
const filterStatus = ref('Assign')
const appStore = useAppStore()
// 添加Category数据存储
const categoryMap = ref<Map<string, string>>(new Map())

// 🔥 事件类型选择相关
const selectedEventCategoryId = ref<string | undefined>(undefined)

// 状态缓存相关
const CACHE_KEY = 'event_list_search_state'

// 🔥 扩展查询参数类型，添加摄像头临时字段
interface ExtendedEventQuery extends EventQuery {
    startCameraTemp?: string // 临时存储起始摄像头ID，仅用于UI绑定
    endCameraTemp?: string // 临时存储结束摄像头ID，仅用于UI绑定

    // 🔥 新增：仅用于显示的摄像头信息
    startCameraDisplay?: string // 起始摄像头显示文本：remark + (bgnKilometer)
    endCameraDisplay?: string // 结束摄像头显示文本：remark + (bgnKilometer)
}

const queryParams = reactive<ExtendedEventQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    tunnelName: undefined,
    emergencyLevel: undefined,
    // 🔥 事件分类查询字段
    eventClass: undefined,
    eventType: undefined,
    eventSubtype: undefined,
    fstrWeather: undefined,
    roadUnavailable: undefined,
    dies: undefined,
    induries: undefined,
    // 🔥 新增摄像头查询相关字段
    addressType: undefined,
    bgnAddressNumber: undefined,
    endAddressNumber: undefined,
    roadwayId: undefined,
    startCameraTemp: undefined,
    endCameraTemp: undefined,
    params: {
        happenTime: undefined
    }
})

// 添加筛选状态参数
const currentStatus = ref('')

// 🔥 新增：摄像头查询相关响应式数据
const startCameraOptions = ref<EquipmentVO[]>([])
const endCameraOptions = ref<EquipmentVO[]>([])
const startCameraLoading = ref(false)
const endCameraLoading = ref(false)
const selectedStartCamera = ref<EquipmentVO | null>(null)
const selectedEndCamera = ref<EquipmentVO | null>(null)

// 🔥 新增：摄像机信息缓存
const cameraCache = ref<Map<string, EquipmentVO>>(new Map())

// 🔥 新增：批量加载摄像机信息
const loadCameraInfoForEvents = async (events: EventVO[]) => {
    // 提取所有摄像头设备ID
    const cameraIds = new Set<string>()

    events.forEach((event) => {
        if (event.addressType === 'camera_number') {
            if (event.bgnAddress) cameraIds.add(event.bgnAddress)
            if (event.endAddress) cameraIds.add(event.endAddress)
        }
    })

    if (cameraIds.size === 0) return

    try {
        console.log('批量查询摄像机信息，ID数量:', cameraIds.size)

        // 🔥 批量查询摄像机信息
        const response = await batchGetEquipment(Array.from(cameraIds))

        // 建立ID到设备信息的映射
        const newCameraMap = new Map<string, EquipmentVO>()
        response.data?.forEach((camera) => {
            newCameraMap.set(String(camera.id), camera)
        })

        // 更新缓存
        cameraCache.value = newCameraMap

        console.log('批量加载摄像机信息完成:', newCameraMap.size)
    } catch (error) {
        console.error('批量加载摄像机信息失败:', error)
    }
}

// 🔥 新增：格式化摄像机信息显示
const formatCameraInfo = (eventData: EventVO): string => {
    // 如果不是摄像头类型，返回空或其他信息
    if (eventData.addressType !== 'camera_number') {
        return '-'
    }

    // 优先显示起始摄像机信息
    if (eventData.bgnAddress) {
        const camera = cameraCache.value.get(eventData.bgnAddress)
        if (camera) {
            return `${camera.remark} (${camera.bgnKilometer})`
        }
    }

    // 备用方案：显示里程信息
    if (eventData.bgnKilometer) {
        return `摄像机 (${eventData.bgnKilometer})`
    }

    // 最后备用：显示设备ID
    return eventData.bgnAddress ? `设备ID: ${eventData.bgnAddress}` : '-'
}

// 🔥 新增：摄像头查询校验方法
const validateCameraQuery = (): { valid: boolean; message?: string } => {
    // 如果没有选择任何摄像头，跳过校验
    if (!selectedStartCamera.value && !selectedEndCamera.value) {
        return { valid: true }
    }

    // 如果只选择了一个摄像头，也是有效的
    if (!selectedStartCamera.value || !selectedEndCamera.value) {
        return { valid: true }
    }

    // 🔥 校验1：车道一致性检查
    const startRoadwayId = selectedStartCamera.value.roadwayId
    const endRoadwayId = selectedEndCamera.value.roadwayId

    if (startRoadwayId !== endRoadwayId) {
        return {
            valid: false,
            message: '起始摄像头和结束摄像头不在同一车道！'
        }
    }

    // 🔥 校验2：里程范围检查
    const bgnNumber = queryParams.bgnAddressNumber
    const endNumber = queryParams.endAddressNumber

    if (bgnNumber !== undefined && endNumber !== undefined) {
        if (endNumber < bgnNumber) {
            return {
                valid: false,
                message: `结束里程必须大于等于起始里程！\n起始里程：${bgnNumber}\n结束里程：${endNumber}`
            }
        }
    }

    return { valid: true }
}

// 🔥 新增：详细的摄像头信息校验（用于调试）
const validateCameraQueryDetailed = (): { valid: boolean; message?: string; details?: any } => {
    const validation = validateCameraQuery()

    if (!validation.valid) {
        const details = {
            startCamera: selectedStartCamera.value
                ? {
                      id: selectedStartCamera.value.id,
                      remark: selectedStartCamera.value.remark,
                      roadwayId: selectedStartCamera.value.roadwayId,
                      bgnKilometer: selectedStartCamera.value.bgnKilometer,
                      extractedNumber: queryParams.bgnAddressNumber
                  }
                : null,
            endCamera: selectedEndCamera.value
                ? {
                      id: selectedEndCamera.value.id,
                      remark: selectedEndCamera.value.remark,
                      roadwayId: selectedEndCamera.value.roadwayId,
                      bgnKilometer: selectedEndCamera.value.bgnKilometer,
                      extractedNumber: queryParams.endAddressNumber
                  }
                : null,
            queryParams: {
                addressType: queryParams.addressType,
                roadwayId: queryParams.roadwayId,
                bgnAddressNumber: queryParams.bgnAddressNumber,
                endAddressNumber: queryParams.endAddressNumber
            }
        }

        console.error('摄像头查询校验失败:', details)
        return { ...validation, details }
    }

    return validation
}

// 🔥 辅助方法：检查是否应该清空摄像头查询条件
const shouldClearCameraQueryConditions = (): boolean => {
    return !selectedStartCamera.value && !selectedEndCamera.value
}

// 🔥 辅助方法：获取当前有效的 roadwayId
const getCurrentRoadwayId = (): string | undefined => {
    if (selectedStartCamera.value) {
        return String(selectedStartCamera.value.roadwayId)
    }
    if (selectedEndCamera.value) {
        return String(selectedEndCamera.value.roadwayId)
    }
    return undefined
}

/** 获取Category数据并构建映射 */
const getCategoryData = async () => {
    try {
        const res = await listCategory({ kind: 'event' })
        const categoryData = proxy?.handleTree<CategoryVO>(res.data, 'id', 'parentId')

        // 构建ID到名称的映射
        const buildCategoryMap = (categories: CategoryVO[]) => {
            categories.forEach((category) => {
                categoryMap.value.set(category.id.toString(), category.name)
                if (category.children && category.children.length > 0) {
                    buildCategoryMap(category.children)
                }
            })
        }

        if (categoryData) {
            categoryList.value = categoryData
            buildCategoryMap(categoryData)
        }

        console.log('Category映射构建完成:', categoryMap.value)
    } catch (error) {
        console.error('获取Category数据失败:', error)
    }
}

/** 根据ID获取Category名称 */
const getCategoryName = (id: string | number | undefined): string => {
    if (!id) return ''
    const name = categoryMap.value.get(id.toString())
    return name || id.toString()
}

/** 🔥 处理事件类型选择变化 */
const handleEventTypeChange = (categoryId: string | undefined) => {
    console.log('事件类型选择变化:', categoryId)

    // 清空所有事件分类查询条件
    queryParams.eventClass = undefined
    queryParams.eventType = undefined
    queryParams.eventSubtype = undefined

    if (!categoryId) {
        console.log('清空事件类型选择')
        handleQuery()
        return
    }

    // 查找选中的分类信息
    const selectedCategory = findCategoryById(categoryId, categoryList.value)
    if (!selectedCategory) {
        console.warn('未找到选中的分类信息:', categoryId)
        handleQuery()
        return
    }

    console.log('选中的分类信息:', selectedCategory)

    // 根据分类层级设置对应的查询条件
    if (selectedCategory.level === 1) {
        // 一级分类
        queryParams.eventClass = categoryId
        console.log('设置一级分类查询条件:', categoryId)
    } else if (selectedCategory.level === 2) {
        // 二级分类
        queryParams.eventType = categoryId
        console.log('设置二级分类查询条件:', categoryId)
    } else if (selectedCategory.level === 3) {
        // 三级分类
        queryParams.eventSubtype = categoryId
        console.log('设置三级分类查询条件:', categoryId)
    } else {
        console.warn('未知的分类层级:', selectedCategory.level)
    }

    // 执行查询
    handleQuery()
}

/** 🔥 递归查找分类信息 */
const findCategoryById = (id: string, categories: CategoryVO[]): CategoryVO | undefined => {
    for (const category of categories) {
        if (category.id === id) {
            return category
        }
        if (category.children && category.children.length > 0) {
            const found = findCategoryById(id, category.children)
            if (found) {
                return found
            }
        }
    }
    return undefined
}

/** 🔥 验证和清理事件分类状态一致性 */
const validateEventCategoryState = () => {
    console.log('验证事件分类状态一致性...')

    // 统计有值的分类字段数量
    const activeFields = []
    if (queryParams.eventClass) activeFields.push('eventClass')
    if (queryParams.eventType) activeFields.push('eventType')
    if (queryParams.eventSubtype) activeFields.push('eventSubtype')

    console.log('当前活跃的分类字段:', activeFields)
    console.log('selectedEventCategoryId:', selectedEventCategoryId.value)

    // 如果有多个分类字段有值，这是不正确的状态，需要清理
    if (activeFields.length > 1) {
        console.warn('检测到多个事件分类字段有值，清理状态...')
        queryParams.eventClass = undefined
        queryParams.eventType = undefined
        queryParams.eventSubtype = undefined
        selectedEventCategoryId.value = undefined
        return
    }

    // 如果只有一个字段有值，确保selectedEventCategoryId与之一致
    if (activeFields.length === 1) {
        const activeField = activeFields[0]
        const activeValue = queryParams[activeField]

        if (selectedEventCategoryId.value !== activeValue) {
            console.log(`同步selectedEventCategoryId: ${selectedEventCategoryId.value} -> ${activeValue}`)
            selectedEventCategoryId.value = activeValue
        }
    }

    // 如果selectedEventCategoryId有值但查询字段都为空，清空selectedEventCategoryId
    if (selectedEventCategoryId.value && activeFields.length === 0) {
        console.log('清空无效的selectedEventCategoryId')
        selectedEventCategoryId.value = undefined
    }
}

// 🔥 增强的状态保存方法
const saveSearchState = () => {
    try {
        const state = {
            queryParams: {
                // 原有查询条件
                emergencyLevel: queryParams.emergencyLevel,
                fstrWeather: queryParams.fstrWeather,
                roadUnavailable: queryParams.roadUnavailable,
                dies: queryParams.dies,
                induries: queryParams.induries,

                // 🔥 事件分类查询条件
                eventClass: queryParams.eventClass,
                eventType: queryParams.eventType,
                eventSubtype: queryParams.eventSubtype,

                // 🔥 保持的摄像头查询条件
                addressType: queryParams.addressType,
                roadwayId: queryParams.roadwayId,
                bgnAddressNumber: queryParams.bgnAddressNumber,
                endAddressNumber: queryParams.endAddressNumber,
                bgnKilometer: queryParams.bgnKilometer,

                // 🔥 不保存 bgnAddress 和 endAddress
                // bgnAddress: undefined,  // 明确不保存
                // endAddress: undefined,  // 明确不保存

                // 🔥 仅用于显示的摄像头信息
                startCameraDisplay: queryParams.startCameraDisplay,
                endCameraDisplay: queryParams.endCameraDisplay
            },
            // 🔥 保存事件类型选择状态
            selectedEventCategoryId: selectedEventCategoryId.value,
            dateRangeHappenTime: dateRangeHappenTime.value,
            showSearch: showSearch.value,
            filterStatus: filterStatus.value
        }
        sessionStorage.setItem(CACHE_KEY, JSON.stringify(state))
        console.log('保存事件搜索状态（简化版）:', state)
    } catch (error) {
        console.error('保存事件搜索状态失败:', error)
    }
}

// 🔥 简化的状态恢复
const restoreSearchState = () => {
    try {
        const savedState = sessionStorage.getItem(CACHE_KEY)
        if (!savedState) return

        const state = JSON.parse(savedState)
        console.log('恢复事件搜索状态（简化版）:', state)

        // 恢复查询条件
        if (state.queryParams) {
            Object.assign(queryParams, state.queryParams)

            // 🔥 恢复显示文本后，清除临时ID字段
            queryParams.startCameraTemp = undefined
            queryParams.endCameraTemp = undefined
        }

        // 🔥 恢复事件类型选择状态
        if (state.selectedEventCategoryId !== undefined) {
            selectedEventCategoryId.value = state.selectedEventCategoryId
        } else {
            // 🔥 如果没有保存的selectedEventCategoryId，但有事件分类查询条件，需要同步
            if (queryParams.eventClass) {
                selectedEventCategoryId.value = queryParams.eventClass
            } else if (queryParams.eventType) {
                selectedEventCategoryId.value = queryParams.eventType
            } else if (queryParams.eventSubtype) {
                selectedEventCategoryId.value = queryParams.eventSubtype
            }
        }

        // 恢复其他状态
        if (state.dateRangeHappenTime) {
            dateRangeHappenTime.value = state.dateRangeHappenTime
        }

        if (state.showSearch !== undefined) {
            showSearch.value = state.showSearch
        }

        if (state.filterStatus) {
            filterStatus.value = state.filterStatus
        }

        // 🔥 验证和清理事件分类状态一致性
        validateEventCategoryState()

        console.log('恢复后的查询条件:', {
            roadwayId: queryParams.roadwayId,
            bgnAddressNumber: queryParams.bgnAddressNumber,
            endAddressNumber: queryParams.endAddressNumber,
            bgnKilometer: queryParams.bgnKilometer,
            startCameraDisplay: queryParams.startCameraDisplay,
            endCameraDisplay: queryParams.endCameraDisplay,
            // 🔥 新增事件分类状态
            selectedEventCategoryId: selectedEventCategoryId.value,
            eventClass: queryParams.eventClass,
            eventType: queryParams.eventType,
            eventSubtype: queryParams.eventSubtype
        })
    } catch (error) {
        console.error('恢复事件搜索状态失败:', error)
    }
}

// 清除缓存状态
const clearSearchState = () => {
    sessionStorage.removeItem(CACHE_KEY)
    console.log('已清除事件搜索状态缓存')
}

// 🔥 强制清理所有事件分类状态（调试用）
const forceCleanEventCategoryState = () => {
    console.log('强制清理事件分类状态...')
    selectedEventCategoryId.value = undefined
    queryParams.eventClass = undefined
    queryParams.eventType = undefined
    queryParams.eventSubtype = undefined
    clearSearchState()
    console.log('事件分类状态已清理完成')
}

/** 查询事件管理列表 */
const getList = async () => {
    loading.value = true
    queryParams.params = {}

    queryParams.projectId = appStore.projectContext.selectedProjectId
    proxy?.addDateRange(queryParams, dateRangeHappenTime.value, 'HappenTime')
    const res = await listEvent(queryParams)
    eventList.value = res.rows
    total.value = res.total

    // 🔥 批量加载当前页面事件的摄像机信息
    await loadCameraInfoForEvents(res.rows)

    loading.value = false
}

/** 搜索按钮操作 */
const handleQuery = () => {
    // 🔥 在查询前进行摄像头校验
    const validation = validateCameraQueryDetailed()

    if (!validation.valid) {
        // 显示错误信息
        proxy?.$modal.msgError(validation.message || '摄像头查询条件校验失败')
        return
    }

    // 校验通过，执行查询
    console.log('摄像头查询校验通过，开始查询')
    console.log('执行查询，查询条件:', {
        addressType: queryParams.addressType,
        bgnAddressNumber: queryParams.bgnAddressNumber,
        endAddressNumber: queryParams.endAddressNumber,
        roadwayId: queryParams.roadwayId
    })

    queryParams.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    dateRangeHappenTime.value = ['', '']
    queryFormRef.value?.resetFields()

    // 🔥 重置摄像头相关数据
    selectedStartCamera.value = null
    selectedEndCamera.value = null
    startCameraOptions.value = []
    endCameraOptions.value = []

    // 🔥 清除所有摄像头查询条件和显示文本
    queryParams.addressType = undefined
    queryParams.roadwayId = undefined
    queryParams.bgnAddressNumber = undefined
    queryParams.endAddressNumber = undefined
    queryParams.bgnKilometer = undefined
    queryParams.endKilometer = undefined
    queryParams.startCameraDisplay = undefined
    queryParams.endCameraDisplay = undefined
    queryParams.startCameraTemp = undefined
    queryParams.endCameraTemp = undefined

    // 🔥 重置事件类型相关数据
    selectedEventCategoryId.value = undefined
    queryParams.eventClass = undefined
    queryParams.eventType = undefined
    queryParams.eventSubtype = undefined

    // 重置筛选状态
    filterStatus.value = 'Assign'

    // 清除缓存状态
    clearSearchState()

    console.log('查询条件已重置')

    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: EventVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    router.push('eventForm')
}

/** 修改按钮操作 */
const handleUpdate = async (row?: EventVO) => {
    const _id = row?.id || ids.value[0]
    router.push('eventForm?id=' + _id)
}
const handleView = async (row?: EventVO) => {
    const _id = row?.id || ids.value[0]
    router.push('eventDetail?id=' + _id)
}

/** 删除按钮操作 */
const handleDelete = async (row?: EventVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除事件管理编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delEvent(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'operation/event/export',
        {
            ...queryParams
        },
        `event_${new Date().getTime()}.xlsx`
    )
}

/** 处理筛选状态改变 */
const handleFilterStatus = (status: string) => {
    currentStatus.value = status
    // 可以根据需要将状态参数添加到查询参数中
    // queryParams.someStatusField = status;
    queryParams.pageNum = 1 // 筛选时重置页码
    getList()
}

// 🔥 新增：复用form表单的里程数字提取方法
const extractKilometerNumber = (kilometer: string): number | undefined => {
    if (!kilometer) return undefined
    const match = kilometer.match(/\+(\d+(?:\.\d+)?)/)
    if (match) {
        return parseFloat(match[1])
    }
    const numberMatch = kilometer.match(/(\d+(?:\.\d+)?)/g)
    if (numberMatch && numberMatch.length > 0) {
        return parseFloat(numberMatch[numberMatch.length - 1])
    }
    return undefined
}

// 🔥 新增：起始摄像头搜索
const searchStartCameras = async (keyword: string) => {
    if (!keyword || keyword.length < 2) {
        startCameraOptions.value = []
        return
    }

    startCameraLoading.value = true
    try {
        const response = await searchEquipmentByKeyword({
            keyword,
            projectId: appStore.projectContext.selectedProjectId,
            categoryIdThird: '111',
            pageSize: 30
        })
        startCameraOptions.value = response.data || []
    } catch (error) {
        console.error('搜索起始摄像头失败:', error)
        proxy?.$modal.msgError('搜索摄像头失败')
    } finally {
        startCameraLoading.value = false
    }
}

// 🔥 新增：结束摄像头搜索
const searchEndCameras = async (keyword: string) => {
    if (!keyword || keyword.length < 2) {
        endCameraOptions.value = []
        return
    }

    endCameraLoading.value = true
    try {
        const response = await searchEquipmentByKeyword({
            keyword,
            projectId: appStore.projectContext.selectedProjectId,
            categoryIdThird: '111',
            pageSize: 30
        })
        endCameraOptions.value = response.data || []
    } catch (error) {
        console.error('搜索结束摄像头失败:', error)
        proxy?.$modal.msgError('搜索摄像头失败')
    } finally {
        endCameraLoading.value = false
    }
}

// 🔥 增强的起始摄像头选择处理
const handleStartCameraChange = (cameraId: string) => {
    if (!cameraId) {
        selectedStartCamera.value = null
        queryParams.bgnAddressNumber = undefined
        queryParams.startCameraDisplay = undefined

        // 🔥 方案A：智能清空策略
        if (shouldClearCameraQueryConditions()) {
            queryParams.roadwayId = undefined
            queryParams.addressType = undefined
        }

        saveSearchState()
        return
    }

    const camera = startCameraOptions.value.find((c) => c.id === cameraId)
    if (camera) {
        selectedStartCamera.value = camera

        // 设置查询条件
        queryParams.addressType = 'camera_number'
        queryParams.bgnAddressNumber = extractKilometerNumber(camera.bgnKilometer)
        queryParams.roadwayId = String(camera.roadwayId)
        queryParams.bgnKilometer = camera.bgnKilometer
        queryParams.startCameraDisplay = `${camera.remark} (${camera.bgnKilometer})`

        // 🔥 选择时的友好提示（不阻止选择）
        if (selectedEndCamera.value && selectedEndCamera.value.roadwayId !== camera.roadwayId) {
            console.warn('提示：起始和结束摄像头不在同一车道，查询时将会校验失败', {
                起始摄像头车道: camera.roadwayId,
                结束摄像头车道: selectedEndCamera.value.roadwayId
            })

            // 可选：显示友好提示
            proxy?.$modal.msgWarning(`提示：起始摄像头和结束摄像头不在同一车道，查询时可能失败`)
        }

        console.log('用户选择起始摄像头:', {
            id: camera.id,
            显示文本: queryParams.startCameraDisplay,
            查询条件: {
                bgnAddressNumber: queryParams.bgnAddressNumber,
                roadwayId: queryParams.roadwayId,
                bgnKilometer: queryParams.bgnKilometer
            }
        })

        saveSearchState()
    }
}

// 🔥 增强的结束摄像头选择处理
const handleEndCameraChange = (cameraId: string) => {
    if (!cameraId) {
        selectedEndCamera.value = null
        queryParams.endAddressNumber = undefined
        queryParams.endCameraDisplay = undefined

        // 🔥 方案A：智能清空策略
        if (shouldClearCameraQueryConditions()) {
            queryParams.roadwayId = undefined
            queryParams.addressType = undefined
        }

        saveSearchState()
        return
    }

    const camera = endCameraOptions.value.find((c) => c.id === cameraId)
    if (camera) {
        selectedEndCamera.value = camera

        // 设置查询条件
        queryParams.addressType = 'camera_number'
        queryParams.endAddressNumber = extractKilometerNumber(camera.bgnKilometer)
        queryParams.endKilometer = camera.bgnKilometer
        queryParams.endCameraDisplay = `${camera.remark} (${camera.bgnKilometer})`

        // 🔥 如果起始摄像头已选择，保持起始摄像头的 roadwayId
        // 如果起始摄像头未选择，使用当前摄像头的 roadwayId
        if (selectedStartCamera.value) {
            // 检查车道一致性并给出提示
            if (selectedStartCamera.value.roadwayId !== camera.roadwayId) {
                console.warn('提示：起始和结束摄像头不在同一车道，查询时将会校验失败', {
                    起始摄像头车道: selectedStartCamera.value.roadwayId,
                    结束摄像头车道: camera.roadwayId
                })

                // 可选：显示友好提示
                proxy?.$modal.msgWarning(`提示：起始摄像头和结束摄像头不在同一车道，查询时可能失败`)
            }
            // 保持起始摄像头的 roadwayId（不覆盖）
        } else {
            // 使用结束摄像头的 roadwayId
            queryParams.roadwayId = String(camera.roadwayId)
        }

        console.log('用户选择结束摄像头:', {
            id: camera.id,
            显示文本: queryParams.endCameraDisplay,
            查询条件: {
                endAddressNumber: queryParams.endAddressNumber,
                endKilometer: queryParams.endKilometer,
                roadwayId: queryParams.roadwayId
            }
        })

        saveSearchState()
    }
}

// 🔥 新增：清除摄像头查询条件
const clearCameraQueryParams = () => {
    queryParams.addressType = undefined
    queryParams.bgnAddressNumber = undefined
    queryParams.endAddressNumber = undefined
    queryParams.roadwayId = undefined
    queryParams.startCameraTemp = undefined
    queryParams.endCameraTemp = undefined
}

onMounted(async () => {
    loading.value = false
    await getCategoryData() // 先加载Category数据

    // 恢复搜索状态
    restoreSearchState()

    // 🔥 验证状态一致性（在Category数据加载完成后）
    validateEventCategoryState()

    // getList() // 启用列表自动加载
})

// 组件卸载前保存状态
onBeforeUnmount(() => {
    saveSearchState()
})

// 监听搜索条件变化，实时保存状态
watch(
    [
        () => queryParams.emergencyLevel,
        () => queryParams.fstrWeather,
        () => queryParams.roadUnavailable,
        () => queryParams.dies,
        () => queryParams.induries,
        // 🔥 事件分类查询条件监听
        () => queryParams.eventClass,
        () => queryParams.eventType,
        () => queryParams.eventSubtype,
        // 🔥 新增摄像头查询条件监听
        () => queryParams.addressType,
        () => queryParams.bgnAddressNumber,
        () => queryParams.endAddressNumber,
        () => queryParams.roadwayId,
        () => queryParams.startCameraTemp,
        () => queryParams.endCameraTemp,
        // 🔥 事件类型选择状态监听
        selectedEventCategoryId,
        dateRangeHappenTime,
        showSearch,
        filterStatus
    ],
    () => {
        // 延迟保存，避免频繁操作
        nextTick(() => {
            saveSearchState()
        })
    },
    { deep: true }
)

// 监听路由变化，当从表单页面返回时刷新列表
watch(
    () => router.currentRoute.value.path,
    async (newPath) => {
        if (newPath.endsWith('/event') || newPath.endsWith('/event/')) {
            // 延迟一下确保页面完全加载
            setTimeout(async () => {
                await getCategoryData() // 重新加载Category数据
                getList()
            }, 100)
        }
    }
)

// 页面激活时刷新列表（处理浏览器前进后退）
// onActivated(async () => {
//     await getCategoryData() // 重新加载Category数据
//     getList()
// })
</script>

<style lang="scss" scoped>
.event-management-page {
    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-tree-select__wrapper),
    :deep(.el-date-editor .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder),
    :deep(.el-tree-select__placeholder) {
        color: #8291a9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)),
    :deep(.el-tree-select .el-tree-select__selected-item > span:not(.el-tree-select__placeholder)) {
        color: #ffffff !important;
    }

    :deep(.el-select__wrapper.is-focused),
    :deep(.el-tree-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 日期选择器特殊样式 */
    :deep(.el-date-editor) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-date-editor .el-input__inner) {
        color: #ffffff !important;
        background: transparent !important;
    }

    :deep(.el-range-separator) {
        color: #8291a9 !important;
    }

    /* 树形选择器特殊样式 */
    :deep(.el-tree-select) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-tree-select__inner) {
        color: #ffffff !important;
        background: transparent !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286f3 !important;
        border-color: #4286f3 !important;
        color: #ffffff !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: '';
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #ffffff !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: '';
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #aed7f2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #ffffff !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 筛选按钮组样式 */
    .btn-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter {
            flex: 1;
        }

        .export {
            margin-left: auto;
        }
    }

    :deep(.el-radio-group .el-radio-button__inner) {
        background: #232d45 !important;
        border: 1px solid #4286f3 !important;
        color: #8291a9 !important;
        border-radius: 6px !important;
        margin-right: 10px;
        height: 36px;
        line-height: 34px;
        padding: 0 16px;
    }

    :deep(.el-radio-group .el-radio-button.is-active .el-radio-button__inner) {
        background: #4286f3 !important;
        border-color: #4286f3 !important;
        color: #ffffff !important;
    }

    /* 头部操作按钮样式 */
    :deep(.btn-box .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 36px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.btn-box .el-button.el-button--primary) {
        background-color: #4286f3 !important;
        border-color: #4286f3 !important;
        color: #ffffff !important;
    }

    :deep(.btn-box .el-button.el-button--success) {
        background-color: #67c23a !important;
        border-color: #67c23a !important;
        color: #ffffff !important;
    }

    :deep(.btn-box .el-button.el-button--warning) {
        background-color: #e6a23c !important;
        border-color: #e6a23c !important;
        color: #ffffff !important;
    }

    :deep(.btn-box .el-button.el-button--danger) {
        background-color: #f56c6c !important;
        border-color: #f56c6c !important;
        color: #ffffff !important;
    }

    /* 操作列图标按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286f3 !important;
    }

    .op-edit {
        color: #42f3e9 !important;
    }

    .op-delete {
        color: #d62121 !important;
    }
}
</style>
