<!-- 封道管理 维养管理完成后，修改这里，
 主逻辑和维养管理一致，planType：封道管理-->
<template>
    <div class="p-2 sealing-list-page">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item label="作业计划" style="width: 308px">
                            <!-- 作业单日期 -->
                            <el-date-picker
                                v-model="dateRangeTaskDate"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                                clearable
                            />
                        </el-form-item>
                        <el-form-item label="维修养护" prop="maintenanceContent">
                            <el-select placeholder="请选择维修养护类型" v-model="queryParams.maintenanceContent" clearable>
                                <el-option v-for="dict in maintenance_content" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>

                        <el-form-item label="作业状态" prop="currentStatus">
                            <el-select placeholder="请选择作业单状态" v-model="queryParams.currentStatus" clearable>
                                <el-option v-for="dict in task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="作业名称" prop="name">
                            <el-input v-model="queryParams.name" placeholder="请输入作业单名称" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">搜索</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <div class="btn-box">
                    <div class="filter">
                        <el-radio-group v-model="filterStatus" @change="handleFilterStatus">
                            <el-radio-button value="PENDING">待处理</el-radio-button>
                            <el-radio-button value="COMPLETED">已处理</el-radio-button>
                            <el-radio-button v-if="false" value="">全部</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div class="export">
                        <!-- @todo 验收后，工作流状态要同步调整 -->
                        <el-button v-if="false" type="primary" plain @click="handleConfirm('批量验收')">批量验收</el-button>
                        <!-- @todo 取消后，状态需要调整 -->
                        <el-button v-if="false" type="primary" plain @click="handleConfirm('批量取消')">批量取消</el-button>
                        <!-- @todo 确认验收后，状态需要调整 -->
                        <el-button v-if="false" type="primary" plain @click="handleConfirm('批量确认验收')">批量确认验收</el-button>
                        <el-button type="primary" v-if="false" plain @click="handleConfirm('批量交底')">批量交底</el-button>
                        <!-- <el-button type="primary" plain @click="handleConfirm('导出')">导出</el-button> -->
                    </div>
                </div>
            </template>

            <el-table v-loading="loading" stripe :data="taskList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <!-- <el-table-column label="" align="center" prop="id" v-if="true" /> -->
                <el-table-column label="作业计划开始时间" align="center">
                    <template #default="scope">
                        <span>{{ formatDate(scope.row.bgnDate) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="作业单名称" align="center" prop="name" />
                <el-table-column label="专业类型" align="center">
                    <template #default="scope">
                        <dict-tag :options="tnl_specialty" :value="scope.row.speciality" />
                    </template>
                </el-table-column>
                <!-- <el-table-column label="养护项目" align="center">
                    <template #default="scope">
                        <dict-tag :options="maintenance_content" :value="scope.row.maintenanceContent" />
                    </template>
                </el-table-column> -->
                <el-table-column label="作业单类型" align="center">
                    <template #default="scope">
                        <!-- @todo -->
                        <span>封道作业单</span>
                    </template>
                </el-table-column>
                <el-table-column label="实际开工日期" align="center">
                    <!-- @todo 替换成：实际开工日期 -->
                    <template #default="scope">
                        <span>{{ formatDate(scope.row.taskStartDate) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="实际完工日期" align="center">
                    <!-- @todo 替换成：实际验收时间 -->
                    <template #default="scope">
                        <span>{{ formatDate(scope.row.taskFinishDate) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="状态" align="center" prop="name">
                    <template #default="scope">
                        <dict-tag :options="task_status" :value="scope.row.currentStatus" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
                    <template #default="scope">
                        <div class="op-actions">
                            <el-button link type="primary" class="op-link op-info" @click="handleView(scope.row)">
                                <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="查看" />
                                查看
                            </el-button>
                            <!-- 已完成、取消中状态不显示取消按钮 -->
                            <el-button
                                link
                                type="danger"
                                class="op-link op-delete"
                                @click="handleCancel(scope.row)"
                                v-if="
                                    queryParams.todoStatus != 'COMPLETED' &&
                                    scope.row.currentStatus != 'END' &&
                                    scope.row.currentStatus != 'Pending_Cancellation' &&
                                    scope.row.currentStatus != 'TERMINATED' &&
                                    isCurrentUserAssignee(scope.row.assignee)
                                "
                            >
                                <img class="op-icon" src="@/assets/images/delete-icon.png" alt="取消" />
                                取消
                            </el-button>
                            <el-button
                                link
                                type="danger"
                                class="op-link op-delete"
                                @click="handleCancel(scope.row)"
                                v-if="
                                    queryParams.todoStatus != 'COMPLETED' &&
                                    scope.row.currentStatus == 'Pending_Cancellation' &&
                                    isCurrentUserAssignee(scope.row.assignee)
                                "
                            >
                                <img class="op-icon" src="@/assets/images/delete-icon.png" alt="确认取消" />
                                确认取消
                            </el-button>
                            <el-button
                                link
                                type="primary"
                                class="op-link op-edit"
                                @click="handleAssign(scope.row)"
                                v-if="
                                    queryParams.todoStatus != 'COMPLETED' &&
                                    scope.row.currentStatus != 'END' &&
                                    scope.row.currentStatus != 'Pending_Cancellation' &&
                                    scope.row.currentStatus != 'TERMINATED' &&
                                    isCurrentUserAssignee(scope.row.assignee)
                                "
                            >
                                <img class="op-icon" src="@/assets/images/edit-icon.png" alt="指派" />
                                {{ getButtonLabel(scope.row.currentStatus) }}
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <!-- 添加或修改年度计划目录对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="800px" append-to-body>
            <TaskListSel
                ref="taskListSelRef"
                :status-config="dialogStatusConfig"
                :project-id="queryParams.projectId"
                :operation-type="dialog.title"
                :task-type="'sealing'"
                @selection-change="handleDialogSelectionChange"
                @confirm="handleDialogConfirm"
                @cancel="cancel"
            />
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, onBeforeUnmount, watch, getCurrentInstance, ComponentInternalInstance, nextTick } from 'vue'
import TaskListSel from '../../components/TaskListSel.vue'
import { listTaskView, listPageAllTaskView, getTaskView } from '@/api/plan/taskView'
import { TaskViewVO, TaskViewForm, TaskViewQuery } from '@/api/plan/taskView/types'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import { useRouter, useRoute } from 'vue-router'
const appStore = useAppStore()
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const { proxy } = getCurrentInstance() as ComponentInternalInstance

/** 检查当前用户是否为指定的处理人 */
const isCurrentUserAssignee = (assignee: string | undefined | null) => {
    if (!assignee || !userStore.userId) {
        return false
    }

    // 将assignee按逗号分割，去除空格，检查是否包含当前用户ID
    const assigneeList = assignee
        .split(',')
        .map((id) => id.trim())
        .filter((id) => id)
    const currentUserId = userStore.userId.toString()

    const isAssignee = assigneeList.includes(currentUserId)

    // console.log('检查用户权限:', {
    //     currentUserId,
    //     assignee,
    //     assigneeList,
    //     isAssignee
    // })

    return isAssignee
}
const { task_status, tnl_specialty, maintenance_content } = toRefs<any>(proxy?.useDict('task_status', 'tnl_specialty', 'maintenance_content'))

// 状态缓存相关
const CACHE_KEY = 'sealing_list_search_state'

// 日期格式化函数
const formatDate = (date: string) => {
    if (!date) return ''
    const d = new Date(date)
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
}

const taskList = ref<TaskViewVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const queryFormRef = ref<ElFormInstance>()
const taskFormRef = ref<ElFormInstance>()
const taskListSelRef = ref()

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

// 弹窗任务项状态配置
const dialogStatusConfig = reactive({
    filterStatus: '', //要筛选的状态
    updateStatus: '' //确认后要更新的状态
})

const filterStatus = ref('PENDING')

const initFormData: TaskViewForm = {
    id: undefined
}

const data = reactive<PageData<TaskViewForm, TaskViewQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        projectId: undefined,
        parentId: undefined,
        name: undefined,
        taskType: 'sealing',
        defineId: undefined,
        weekTaskId: undefined,
        year: undefined,
        month: undefined,
        week: undefined,
        shiftType: undefined,
        taskDate: undefined,
        taskStep: 'task',
        bgnDate: undefined,
        endDate: undefined,
        teamId: undefined,
        sort: undefined,
        publishState: undefined,
        status: undefined,
        currentStatus: undefined,
        speciality: undefined,
        maintenanceContent: undefined
    },
    rules: {
        id: [{ required: true, message: '不能为空', trigger: 'blur' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

const dateRangeTaskDate = ref<[string, string]>(['', ''])

/** 查询月度任务列表 */
const getList = async () => {
    loading.value = true
    proxy?.addDateRange(queryParams.value, dateRangeTaskDate.value, 'BgnDate')

    try {
        // 确保项目ID已设置
        if (!queryParams.value.projectId) {
            queryParams.value.projectId = appStore.projectContext.selectedProjectId
        }

        queryParams.value.taskType = 'sealing'
        queryParams.value.taskStep = 'task'
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
        queryParams.value.todoStatus = filterStatus.value
        //queryParams.value.assignee = userStore.userId;

        const res = await listPageAllTaskView(queryParams.value)
        taskList.value = res.rows
        total.value = res.total
    } catch (error) {
        console.error('获取任务列表失败:', error)
    } finally {
        loading.value = false
    }
}

// 缓存搜索状态
const saveSearchState = () => {
    try {
        const state = {
            queryParams: {
                maintenanceContent: queryParams.value.maintenanceContent,
                speciality: queryParams.value.speciality,
                currentStatus: queryParams.value.currentStatus,
                name: queryParams.value.name
            },
            dateRangeTaskDate: dateRangeTaskDate.value,
            showSearch: showSearch.value,
            filterStatus: filterStatus.value
        }
        sessionStorage.setItem(CACHE_KEY, JSON.stringify(state))
        console.log('保存封道管理搜索状态:', state)
    } catch (error) {
        console.error('保存封道管理搜索状态失败:', error)
    }
}

// 恢复搜索状态
const restoreSearchState = () => {
    try {
        const cached = sessionStorage.getItem(CACHE_KEY)
        if (cached) {
            const state = JSON.parse(cached)

            // 恢复查询参数
            if (state.queryParams) {
                queryParams.value.maintenanceContent = state.queryParams.maintenanceContent
                queryParams.value.speciality = state.queryParams.speciality
                queryParams.value.currentStatus = state.queryParams.currentStatus
                queryParams.value.name = state.queryParams.name
            }

            // 恢复日期范围
            if (state.dateRangeTaskDate) {
                dateRangeTaskDate.value = state.dateRangeTaskDate
            }

            // 恢复搜索框显示状态
            if (state.showSearch !== undefined) {
                showSearch.value = state.showSearch
            }

            // 恢复筛选状态
            if (state.filterStatus) {
                filterStatus.value = state.filterStatus
            }

            console.log('恢复封道管理搜索状态:', state)
        }
    } catch (error) {
        console.error('恢复封道管理搜索状态失败:', error)
    }
}

// 清除缓存状态
const clearSearchState = () => {
    sessionStorage.removeItem(CACHE_KEY)
}

/** 取消按钮 */
const cancel = () => {
    //reset();
    dialog.visible = false
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    taskFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    queryParams.value.projectId = appStore.projectContext.selectedProjectId
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    dateRangeTaskDate.value = ['', '']
    queryFormRef.value?.resetFields()

    // 清除缓存状态
    clearSearchState()

    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: TaskViewVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    dialog.visible = true
    dialog.title = '添加月度任务'
}

/** 修改按钮操作 */
const handleUpdate = async (row?: TaskViewVO) => {
    reset()
    const _id = row?.id || ids.value[0]
    const res = await getTaskView(String(_id))
    Object.assign(form.value, res.data)
    dialog.visible = true
    dialog.title = '修改月度任务'
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'plan/task/export',
        {
            ...queryParams.value
        },
        `task_${new Date().getTime()}.xlsx`
    )
}

// 导航到查看、指派页面
const handleAssign = (row?: TaskViewVO) => {
    router.push('sealingAssign?taskId=' + row?.id)
}

// 导航到取消页面
const handleCancel = (row?: TaskViewVO) => {
    router.push('cancelSealing?taskId=' + row?.id)
    //router.push('sealingAssign?taskId=' + row?.id + '&status=Pending_Cancellation');
    //router.push('/subProject/circle/maintain/sealing/cancelSealing?taskId=' + row?.id);
}

// 导航到查看页面
const handleView = (row?: TaskViewVO) => {
    router.push('sealingTaskDetail?taskId=' + row?.id)
}

/** 审批按钮操作 */
const handleConfirm = (operationName: string) => {
    dialog.visible = true
    dialog.title = operationName
    // @todo设置状态配置，因为要涉及工作流状态的调整，所以暂不实现
    if (operationName === '批量验收') {
        dialogStatusConfig.filterStatus = 'START'
        dialogStatusConfig.updateStatus = 'Accepted'
    } else if (operationName === '批量取消') {
        dialogStatusConfig.filterStatus = 'START'
        dialogStatusConfig.updateStatus = 'Cancelled'
    } else if (operationName === '批量确认验收') {
        dialogStatusConfig.filterStatus = 'START'
        dialogStatusConfig.updateStatus = 'Confirmed'
    } else if (operationName === '批量交底') {
        dialogStatusConfig.filterStatus = 'Safety_Briefing'
        dialogStatusConfig.updateStatus = 'Execute'
    }

    // 等待对话框渲染完成后加载数据
    nextTick(() => {
        if (taskListSelRef.value) {
            taskListSelRef.value.getList()
        }
    })
}

/** 处理弹窗中的选择变化 */
const handleDialogSelectionChange = (selectedIds: (string | number)[]) => {
    // 可以在这里处理选中项的变化
    console.log('选中的ID:', selectedIds)
}

/** 处理弹窗确认 */
const handleDialogConfirm = async (data: { ids: (string | number)[]; updateStatus: string }) => {
    // 在这里处理确认逻辑
    console.log('确认选中的ID:', data.ids)
    console.log('要更新的状态:', data.updateStatus)
    // 处理完成后关闭对话框
    dialog.visible = false
    // 刷新主列表
    await getList()
}

/** 处理筛选状态改变 */
const handleFilterStatus = (status: string) => {
    filterStatus.value = status

    // 根据前端筛选状态设置对应的todo_status查询条件
    if (status === 'PENDING') {
        // 待处理：对应 todo_status = 'ACTIVE' OR todo_status IS NULL
        queryParams.value.todoStatus = 'ACTIVE'
    } else if (status === 'COMPLETED') {
        // 已处理：对应 todo_status = 'COMPLETED'
        queryParams.value.todoStatus = 'COMPLETED'
    } else {
        // 全部：不传 todo_status 值
        queryParams.value.todoStatus = undefined
    }

    // 重置页码并查询
    queryParams.value.pageNum = 1
    getList()
}

/**
 * 根据状态值从task_status数据字典中获取按钮标题
 * @param statusValue 状态值
 * @returns 按钮标题
 */
const getButtonLabel = (statusValue: string) => {
    // 从task_status数据字典中查找对应的标签
    const dictItem = task_status.value?.find((item: any) => item.value === statusValue)

    // 如果找到字典项，返回其标签；否则返回原始值
    return dictItem?.label || statusValue
}

// 监听路由变化，当查询参数变化时重新加载数据
watch(
    () => route.query.r,
    (newR, oldR) => {
        if (newR && newR !== oldR) {
            console.log('检测到路由刷新参数变化，重新加载封道任务列表')
            getList()
        }
    }
)

onMounted(() => {
    queryParams.value.projectId = appStore.projectContext.selectedProjectId

    // 恢复搜索状态
    restoreSearchState()

    getList()
})

// 组件卸载前保存状态
onBeforeUnmount(() => {
    saveSearchState()
})

// 监听搜索条件变化，实时保存状态
watch(
    [
        () => queryParams.value.maintenanceContent,
        () => queryParams.value.speciality,
        () => queryParams.value.currentStatus,
        () => queryParams.value.name,
        dateRangeTaskDate,
        showSearch,
        filterStatus
    ],
    () => {
        // 延迟保存，避免频繁操作
        nextTick(() => {
            saveSearchState()
        })
    },
    { deep: true }
)
</script>
<style lang="scss" scoped>
.sealing-list-page {
    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-date-editor .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder) {
        color: #8291a9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)) {
        color: #ffffff !important;
    }

    :deep(.el-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 日期选择器特殊样式 */
    :deep(.el-date-editor) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-date-editor .el-input__inner) {
        color: #ffffff !important;
        background: transparent !important;
    }

    :deep(.el-range-separator) {
        color: #8291a9 !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286f3 !important;
        border-color: #4286f3 !important;
        color: #ffffff !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: '';
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #ffffff !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: '';
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #aed7f2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #ffffff !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 筛选按钮组样式 */
    .btn-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter {
            flex: 1;
        }

        .export {
            margin-left: auto;
        }
    }

    :deep(.el-radio-group .el-radio-button__inner) {
        background: #232d45 !important;
        border: 1px solid #4286f3 !important;
        color: #8291a9 !important;
        border-radius: 6px !important;
        margin-right: 10px;
        height: 36px;
        line-height: 34px;
        padding: 0 16px;
    }

    :deep(.el-radio-group .el-radio-button.is-active .el-radio-button__inner) {
        background: #4286f3 !important;
        border-color: #4286f3 !important;
        color: #ffffff !important;
    }

    /* 操作列图标按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286f3 !important;
    }

    .op-edit {
        color: #42f3e9 !important;
    }

    .op-delete {
        color: #d62121 !important;
    }
}
</style>
