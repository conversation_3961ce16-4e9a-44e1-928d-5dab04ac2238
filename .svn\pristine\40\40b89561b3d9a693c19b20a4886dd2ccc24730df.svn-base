<template>
  <div class="p-2 statistics-defect-page">
    <!-- 搜索条件 -->
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px">
            <!-- 统计类型选择 - 独立一行 -->
            <div>
              <el-form-item label="统计类型">
                <el-radio-group v-model="queryParams.statisticsType" @change="handleStatisticsTypeChange">
                  <el-radio label="equipment">设备缺陷统计</el-radio>
                  <el-radio label="facility">设施缺陷统计</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>

            <!-- 时间范围 -->
            <el-form-item label="统计时间" style="width: 400px">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="~"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleDateChange">
              </el-date-picker>
            </el-form-item>

            <!-- 设备/设施名称 -->
            <el-form-item label="设备/设施名称" prop="deviceName" label-width="100px">
              <el-input
                v-model="queryParams.deviceName"
                placeholder="请输入设备/设施名称"
                clearable
              />
            </el-form-item>

            <!-- 起始里程号 -->
            <el-form-item label="起始里程号" prop="startKilometer" label-width="100px">
              <el-input
                v-model="queryParams.startKilometer"
                placeholder="请输入起始里程号"
                clearable
              />
            </el-form-item>

            <!-- 结束里程号 -->
            <el-form-item label="结束里程号" prop="endKilometer" label-width="120px">
              <el-input
                v-model="queryParams.endKilometer"
                placeholder="请输入结束里程号"
                clearable
              />
            </el-form-item>

            <el-form-item class="filter-actions">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <!-- 工具栏 -->
    <el-card shadow="never">
      <template #header>
        <div class="btn-box" v-if="false">
          <div class="filter"></div>
          <div class="export">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['subProject:statistics:defect:export']">导出</el-button>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table
        ref="defectStatisticsRef"
        v-loading="loading"
        :data="defectStatisticsList"
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="缺陷类型" align="center" prop="defectCategoryName" />
        <el-table-column label="设备/设施名称" align="center" prop="deviceName" />
        <el-table-column label="里程号" align="center" prop="bgnKilometer" />
        <el-table-column label="房间名称" align="center" prop="roomName" />
        <el-table-column label="缺陷数量" align="center" prop="countNumber">
          <template #default="scope">
            <el-tag type="primary" size="large">{{ scope.row.countNumber }}</el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
import {
  listEquipmentDefectStatistics,
  listFacilityDefectStatistics,
  getEquipmentDefectSummary,
  getFacilityDefectSummary
} from '@/api/subProject/statistics/defect';
import type { DefectStatisticsQuery, DefectStatisticsVO, DefectStatisticsSummary } from '@/api/subProject/statistics/defect/types';
import { useAppStore } from '@/store/modules/app';

defineOptions({
  name: 'DefectStatistics',
  inheritAttrs: false
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const appStore = useAppStore();
const route = useRoute();

// 响应式数据
const loading = ref(false);
const showSearch = ref(true);
const defectStatisticsList = ref<DefectStatisticsVO[]>([]);
const total = ref(0);
const dateRange = ref<[string, string]>(['', '']);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const summaryData = ref<DefectStatisticsSummary>({
  totalDefects: 0,
  totalTypes: 0,
  specialtyStats: {},
  defectTypeStats: {},
  deviceStats: {}
});

const queryFormRef = ref<ElFormInstance>();
const defectStatisticsRef = ref<ElTableInstance>();

// 查询参数
const queryParams = reactive<DefectStatisticsQuery>({
  pageNum: 1,
  pageSize: 100,
  statisticsType: 'equipment', // 默认设备统计
  projectId: '', // 需要从路由或store获取
  startDate: undefined,
  endDate: undefined,
  deviceName: undefined,
  roomName: undefined,
  defectCategoryName: undefined,
  startKilometer: undefined,
  endKilometer: undefined
});

// 获取列表数据
const getList = async () => {
  loading.value = true;

  // 强制要求传入项目ID
  const currentProjectId = appStore.projectContext.selectedProjectId;
  if (!currentProjectId) {
    proxy?.$modal.msgError('请先选择项目');
    loading.value = false;
    return;
  }

  // 设置项目ID
  queryParams.projectId = currentProjectId;

  // 处理日期范围
  if (dateRange.value && dateRange.value.length === 2 && dateRange.value[0] && dateRange.value[1]) {
    queryParams.startDate = dateRange.value[0];
    queryParams.endDate = dateRange.value[1];
  } else {
    queryParams.startDate = undefined;
    queryParams.endDate = undefined;
  }

  try {
    const api = queryParams.statisticsType === 'equipment'
      ? listEquipmentDefectStatistics
      : listFacilityDefectStatistics;

    const response = await api(queryParams);
    defectStatisticsList.value = response.rows || [];
    total.value = response.total || 0;

    // 获取汇总数据
    await getSummaryData();
  } catch (error) {
    console.error('获取缺陷统计数据失败:', error);
    proxy?.$modal.msgError('获取缺陷统计数据失败');
  } finally {
    loading.value = false;
  }
};

// 获取汇总数据
const getSummaryData = async () => {
  try {
    const api = queryParams.statisticsType === 'equipment'
      ? getEquipmentDefectSummary
      : getFacilityDefectSummary;

    const { data } = await api(queryParams);
    summaryData.value = data;
  } catch (error) {
    console.error('获取汇总数据失败:', error);
  }
};

// 统计类型变化
const handleStatisticsTypeChange = () => {
  queryParams.pageNum = 1;
  getList();
};

// 日期范围变化
const handleDateChange = (dates: [string, string] | null) => {
  if (dates) {
    queryParams.startDate = dates[0];
    queryParams.endDate = dates[1];
  } else {
    queryParams.startDate = undefined;
    queryParams.endDate = undefined;
  }
};

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  dateRange.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: DefectStatisticsVO[]) => {
  ids.value = selection.map((item, index) => `${item.defectCategoryName || ''}-${item.deviceName || ''}-${item.bgnKilometer || ''}-${item.roomName || ''}-${index}`);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 导出按钮操作 */
const handleExport = () => {
  // 检查是否选择了项目
  const currentProjectId = appStore.projectContext.selectedProjectId;
  if (!currentProjectId) {
    proxy?.$modal.msgError('请先选择项目');
    return;
  }

  proxy?.$modal.confirm('是否确认导出所有缺陷统计数据项？').then(async () => {
    try {
      // 确保导出时也包含项目ID
      const exportParams = { ...queryParams };
      exportParams.projectId = currentProjectId;

      // 这里需要实现导出功能
      proxy?.$modal.msgSuccess('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      proxy?.$modal.msgError('导出失败');
    }
  });
};

// 初始化
onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.statistics-defect-page {
  /* 头部卡片与分隔 */
  :deep(.el-card) {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }

  :deep(.el-card__header) {
    border-bottom: none !important;
    padding-top: 0 !important;
  }

  :deep(.el-card__body) {
    background: transparent !important;
    padding-bottom: 0 !important;
  }

  /* 输入/下拉统一深色皮肤 */
  :deep(.el-input__wrapper),
  :deep(.el-select__wrapper),
  :deep(.el-tree-select__wrapper),
  :deep(.el-date-editor .el-input__wrapper) {
    background: #232d45 !important;
    border-radius: 6px !important;
    box-shadow: none !important;
    min-height: 36px;
    height: 36px;
    padding: 5px 10px;
  }

  :deep(.el-input__inner::placeholder),
  :deep(.el-select__placeholder),
  :deep(.el-tree-select__placeholder) {
    color: #8291A9 !important;
    opacity: 1;
  }

  :deep(.el-input__inner),
  :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)),
  :deep(.el-tree-select .el-tree-select__selected-item > span:not(.el-tree-select__placeholder)) {
    color: #FFFFFF !important;
  }

  :deep(.el-select__wrapper.is-focused),
  :deep(.el-tree-select__wrapper.is-focused) {
    box-shadow: none !important;
  }

  /* 日期选择器特殊样式 */
  :deep(.el-date-editor) {
    background: #232d45 !important;
    border-radius: 6px !important;
    border: none !important;
  }

  :deep(.el-date-editor .el-input__inner) {
    color: #FFFFFF !important;
    background: transparent !important;
  }

  :deep(.el-range-separator) {
    color: #8291A9 !important;
  }

  /* 搜索/重置按钮 */
  :deep(.filter-actions .el-button:not(.is-link)) {
    border-radius: 6px !important;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 14px !important;
    margin-right: 10px;
  }

  :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
    background-color: #4286F3 !important;
    border-color: #4286F3 !important;
    color: #FFFFFF !important;
  }

  :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url('@/assets/images/search-icon.png') no-repeat center/contain;
    margin-right: 6px;
  }

  :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
    background-color: #808892 !important;
    border-color: #808892 !important;
    color: #FFFFFF !important;
  }

  :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
    margin-right: 6px;
  }

  /* 表格透明化与去边框 */
  :deep(.el-table) {
    --el-table-bg-color: transparent;
    --el-table-header-bg-color: transparent;
    --el-table-tr-bg-color: transparent;
    --el-table-border-color: rgba(255, 255, 255, 0.08);
    background-color: transparent !important;
    color: #ffffff;
  }

  :deep(.el-table__inner-wrapper::before),
  :deep(.el-table__inner-wrapper::after),
  :deep(.el-table::before),
  :deep(.el-table--border::after),
  :deep(.el-table__border-left-patch) {
    display: none !important;
    background: transparent !important;
  }

  :deep(.el-table__header th) {
    background-color: transparent !important;
    border-bottom: none !important;
    height: 44px;
    text-align: center;
  }

  :deep(.el-table__header th .cell) {
    color: #AED7F2 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    text-align: center;
    position: relative;
    top: -3px;
  }

  :deep(.el-table td) {
    text-align: center !important;
    height: 48px;
    background-color: transparent !important;
  }

  :deep(.el-table .cell) {
    color: #FFFFFF !important;
    font-size: 13px !important;
    line-height: 1.4;
    padding: 8px 12px;
  }

  /* 整行隔行渐变 */
  :deep(.el-table__body tr) {
    background: transparent !important;
  }

  :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
    background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
  }

  :deep(.el-table__body tr:hover > td) {
    background-color: rgba(66, 134, 243, 0.08) !important;
  }

  /* 表头整行背景 */
  :deep(.el-table__header-wrapper) {
    position: relative;
  }

  :deep(.el-table__header-wrapper)::before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    height: 44px;
    background: rgb(31, 43, 78);
    border-radius: 6px;
    pointer-events: none;
    z-index: 0;
  }

  :deep(.el-table thead),
  :deep(.el-table th.el-table__cell) {
    position: relative;
    z-index: 1;
  }

  /* 表体每行间隔线 */
  :deep(.el-table__body tr > td) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
  }

  /* 筛选按钮组样式（列表卡片头部预留） */
  .btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .filter {
      flex: 1;
    }

    .export {
      margin-left: auto;
    }
  }

  :deep(.el-radio-group .el-radio-button__inner) {
    background: #232d45 !important;
    border: 1px solid #4286F3 !important;
    color: #8291A9 !important;
    border-radius: 6px !important;
    margin-right: 10px;
    height: 36px;
    line-height: 34px;
    padding: 0 16px;
  }

  :deep(.el-radio-group .el-radio-button.is-active .el-radio-button__inner) {
    background: #4286F3 !important;
    border-color: #4286F3 !important;
    color: #FFFFFF !important;
  }

  /* 标签强调 */
  .el-tag {
    font-weight: bold;
  }
}

.statistic-card {
  text-align: center;
}

.statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
